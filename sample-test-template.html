<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Sample Test Template</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.5;
            color: #333;
        }
        
        .header {
            text-align: center;
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 30px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .content {
            margin: 20px 0;
        }
        
        .info-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #3498db;
            margin: 20px 0;
        }
        
        .info-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .details-table th,
        .details-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .details-table th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        
        .details-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        .signature {
            margin-top: 50px;
            text-align: right;
        }
        
        .signature-box {
            border: 1px solid #ddd;
            padding: 20px;
            margin-top: 20px;
            background-color: #f9f9f9;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10pt;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        {{documentTitle}}
    </div>
    
    <div class="content">
        <div class="info-section">
            <h3>Agent Information</h3>
            <p>Dear <span class="highlight">{{agentName}}</span>,</p>
            <p>This document confirms your registration details with our agency system.</p>
        </div>
        
        <table class="details-table">
            <tr>
                <th>Field</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Agent Code</td>
                <td>{{agentCode}}</td>
            </tr>
            <tr>
                <td>Registration Date</td>
                <td>{{registrationDate}}</td>
            </tr>
            <tr>
                <td>Location</td>
                <td>{{location}}</td>
            </tr>
            <tr>
                <td>Status</td>
                <td><span class="status-active">{{status}}</span></td>
            </tr>
            <tr>
                <td>Email</td>
                <td>{{email}}</td>
            </tr>
            <tr>
                <td>Phone</td>
                <td>{{phone}}</td>
            </tr>
        </table>
        
        <div class="info-section">
            <h3>Important Notes</h3>
            <ul>
                <li>Please keep this document for your records</li>
                <li>Your agent code is required for all future transactions</li>
                <li>Contact support if you have any questions about your registration</li>
                <li>This document was generated on {{currentDate}}</li>
            </ul>
        </div>
        
        <div class="content">
            <p><strong>Additional Information:</strong></p>
            <p>{{additionalInfo}}</p>
        </div>
    </div>
    
    <div class="signature">
        <div class="signature-box">
            <p><strong>{{location}}, {{currentDate}}</strong></p>
            <br/><br/>
            <p>_________________________</p>
            <p><strong>{{signerName}}</strong></p>
            <p>{{signerTitle}}</p>
            <p>{{companyName}}</p>
        </div>
    </div>
    
    <div class="footer">
        <p>This is an automatically generated document. For questions, contact {{supportEmail}} or call {{supportPhone}}.</p>
        <p>Document ID: {{documentId}} | Generated: {{currentDate}}</p>
    </div>
</body>
</html>
