resource "aws_ecs_service" "services" {
  name          = "keagenan"
  cluster       = "arn:aws:ecs:ap-southeast-3:850995538171:cluster/superapp-ecs"
  launch_type   = "FARGATE"
  enable_ecs_managed_tags = true
  desired_count = 1
  task_definition = "${aws_ecs_task_definition.task.family}:${aws_ecs_task_definition.task.revision}"
  load_balancer {
    target_group_arn = "arn:aws:elasticloadbalancing:ap-southeast-3:850995538171:targetgroup/keagenan-tg/d2b3460748403c71"

    container_name = "keagenan"
    container_port = 8080
  }

  network_configuration {
    subnets = [
      "subnet-0373567487a6598c0",
      "subnet-04a739e47d7fb315f"
    ]
    assign_public_ip = false
    security_groups  = [
        "sg-0d8553b015cd7fa94"
    ]
  }

  lifecycle {
    ignore_changes = [
      desired_count
    ]
  }
}

resource "aws_ecs_task_definition" "task" {
  family                   = "keagenan-task-def"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = "2048"
  memory                   = "4096"
  task_role_arn            = "arn:aws:iam::850995538171:role/keagenan-task-role"
  execution_role_arn       = "arn:aws:iam::850995538171:role/keagenan-task-role"

  runtime_platform {
    cpu_architecture        = "X86_64"
    operating_system_family = "LINUX"
  }

  container_definitions = jsonencode([
    {
      name      = "keagenan"
      image     = var.image_name
      cpu       = 0
      essential = true
      portMappings = [
        {
          name          = "keagenan-80-tcp"
          containerPort = 8080
          hostPort      = 8080
          protocol      = "tcp"
          appProtocol   = "http"
        }
      ]
      environment     = []
      mountPoints     = []
      volumesFrom     = []
      systemControls  = []
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = "/aws/ecs/keagenan-logs"
          awslogs-create-group  = "true"
          awslogs-region        = "ap-southeast-3"
          awslogs-stream-prefix = "keagenan"
        }
      }
    }
  ])
}