# ---------- Build Stage ----------
FROM eclipse-temurin:21-jdk as build

WORKDIR /app

COPY mvnw pom.xml ./
COPY .mvn .mvn

RUN chmod +x mvnw
RUN ./mvnw dependency:go-offline

COPY . ./

RUN ./mvnw clean package -DskipTests

 
# ---------- Runtime Stage ----------
FROM eclipse-temurin:21-jre as runtime

WORKDIR /app

# Download Elastic APM Agent
ADD https://repo1.maven.org/maven2/co/elastic/apm/elastic-apm-agent/1.53.0/elastic-apm-agent-1.53.0.jar /app/elastic-apm-agent.jar

# Copy the built application JAR
COPY --from=build /app/target/*.jar app.jar

EXPOSE 8080

# Run the application with APM agent
ENTRYPOINT ["java", "-Xmx3g", "-Xms1g", \
  "-javaagent:/app/elastic-apm-agent.jar", \
  "-Delastic.apm.service_name=superapp-agent", \
  "-Delastic.apm.server_urls=http://***********:8200", \
  "-Delastic.apm.secret_token=7110eda4d09e062aa5e4a390b0a572ac0d2c0220", \
  "-Delastic.apm.environment=uat", \
  "-Delastic.apm.capture_body=all", \
  "-Delastic.apm.transaction_sample_rate=1.0", \
  "-Delastic.apm.application_packages=id.co.panindaiichilife.superapp.agent", \
  "-jar", "app.jar"]

