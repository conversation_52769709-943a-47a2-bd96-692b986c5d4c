# ========================
# ð§ Application Metadata
# ========================
# Name of the application (used for tagging, logging, etc.)
APP_NAME=superapp-agent
# Deployment environment (e.g., DEV, STAGE, PROD)
APP_ENV=DEV
# SonarQube project name for static code analysis
SONAR_PROJECT_NAME=superapp-agent
# ========================
# ð Deployment Configuration
# ========================
# ECS Task Definition name (container specs, resource limits)
TASK_DEFINITION=keagenan-task-def
TASK_VCPU=2000
TASK_RAM=4096
# ECS Cluster name for deployment
ECS_CLUSTER=superapp-ecs
# ECS Service name managing the app lifecycle
ECS_SERVICE=keagenan
# ========================
# ð¦ Container Registry
# ========================
# AWS ECR repository path for Docker image storage
REGISTRY_PATH=superapp-keagenan-ecr
# ========================
# ð Security & Compliance
# ========================
# Comma-separated list of security scans to run (e.g., sonar for code, trivy for image)
REQUIRED_SECURITY_SCAN=sonar,trivy
# Scans that must pass before deployment (optional, currently empty)
MANDATORY_SECURITY_SCAN=
# ========================
# ð Vault Configuration
# ========================
# Path to secrets in HashiCorp Vault (used during runtime or build)
VAULT_SECRET_PATH=superapp-agent
# Path where secrets will be injected or loaded
VAULT_ENV_PATH=src/main/resources/application.properties
