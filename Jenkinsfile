pipeline {
    agent any
    environment {
        AWS_REGION = "ap-southeast-3"
        app_name = "superapp-agent"
        app_env = "DEV"
        imageRegistryHost = "850995538171.dkr.ecr.ap-southeast-3.amazonaws.com"
        registry = "${imageRegistryHost}/superapp-keagenan-ecr"
        task_definition = "keagenan-task-def"
        ecs_cluster = "superapp-ecs"
        ecs_service = "keagenan"
        VAULT_ADDR = 'https://localhost:8200'
        VAULT_TOKEN = credentials('hcv-token') // stored in Jenkins credentials
    }
    stages {
        stage('Extract Tag') {
            steps {
                script {
                    env.GIT_TAG = env.GIT_BRANCH.replace("refs/tags/", "").trim()
                    currentBuild.displayName = "#${env.BUILD_NUMBER} - ${env.GIT_TAG}"
                    echo "Building image with Tag '${env.GIT_TAG}'."
                }
            }
        }

        stage('Fetch secrets from Vault') {
            steps {
                sh '''
                export VAULT_SKIP_VERIFY=true
                export VAULT_ADDR=https://localhost:8200
                vault login $VAULT_TOKEN
                vault kv get -format=json -mount="superapp-pdl" "superapp-agent" | jq -r '.data.data | to_entries | map([.key,.value] | join("=")) |.[]' > src/main/resources/application.properties
                '''
            }
        }

        stage('SonarQube Analysis') {
            steps {
                script {
                    def mvn = tool 'pdl-mvn' // your configured Maven installation name
                    withSonarQubeEnv('sonarqube-jenkins') {
                        sh "${mvn}/bin/mvn clean verify sonar:sonar -Dsonar.projectKey=superapp-agent -Dsonar.projectName='superapp-agent' -Dsonar.projectVersion=${env.GIT_TAG}"
                    }
                }
            }
        }

        stage('Quality Gate') {
            steps {
                script {
                    if (env.MANDATORY_SECURITY_SCAN.contains("sonar") && qualityGate.status != 'OK') {
                        timeout(time: 2, unit: 'MINUTES') {
                            def qualityGate = waitForQualityGate()
                                error("❌ Quality Gate failed and 'sonar' is required in MANDATORY_SECURITY_SCAN")
                        }
                    }
                }
            }
        }

        stage('Build Docker Image') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'aws-fahmi', usernameVariable: 'AWS_ACCESS_KEY_ID', passwordVariable: 'AWS_SECRET_ACCESS_KEY')]) {
                        sh "docker build -t ${registry}:${env.GIT_TAG} ."
                    }
                }
            }
        }

        stage('Trivy Image Scan') {
            steps {
                script {
                    echo "Running Trivy Image scan..."
                    def result = sh(
                        script: """
                            trivy image --exit-code 0 --severity CRITICAL,HIGH --no-progress --output trivy-report-image.txt ${registry}:${GIT_TAG}
                        """,
                        returnStatus: true
                    )

                    if (result != 0 && env.MANDATORY_SECURITY_SCAN.contains("trivy")) {
                        error("❌ High or Critical vulnerabilities found in Docker image and 'trivy' is required in MANDATORY_SECURITY_SCAN")
                    }
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'trivy-report-image.txt', fingerprint: true
                }
            }
        }

        stage('Push Docker Image') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'aws-fahmi', usernameVariable: 'AWS_ACCESS_KEY_ID', passwordVariable: 'AWS_SECRET_ACCESS_KEY')]) {
                        sh "aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${imageRegistryHost}"
                        sh "docker push ${registry}:${env.GIT_TAG}"
                    }
                }
            }
        }

        stage('Deploy') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'aws-fahmi', usernameVariable: 'AWS_ACCESS_KEY_ID', passwordVariable: 'AWS_SECRET_ACCESS_KEY')]) {
                        def image_id = "${registry}:${env.GIT_TAG}"
                        
                        sh """
                        export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
                        export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
                        export AWS_REGION=${AWS_REGION}
                        
                        ansible-playbook playbook.yml --extra-vars \\
                        "task_family=${task_definition} new_image=${image_id} ecs_cluster=${ecs_cluster} ecs_service=${ecs_service}"
                        """
                    }
                }
            }
        }
    }
    post {
        success {
            emailext(
                subject: "✅ Jenkins Build Succeeded - ${app_name} - ${env.GIT_TAG}", 
                body: """
                    <h3>✅ Build Success</h3>
                    <p>The Jenkins pipeline has successfully completed for <b>${app_name}</b> - <b>${env.GIT_TAG}</b>.</p>
                    <p>Check the Jenkins UI for details.</p>
                    <p>🌍 <b>Environment:</b> ${app_env}</p>
                    <p>🔗 <a href="${env.BUILD_URL}">Jenkins Build Log</a></p>
                """,
                to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                mimeType: 'text/html'
            )
        }
        failure {
            emailext(
                subject: "❌ Jenkins Build Failed - ${app_name} - ${env.GIT_TAG}", 
                body: """
                    <h3>❌ Build Failure</h3>
                    <p>The Jenkins pipeline has failed for <b>${app_name}</b> - <b>${env.GIT_TAG}</b>.</p>
                    <p>❗ Please check the logs for details.</p>
                    <p>🌍 <b>Environment:</b> ${app_env}</p>
                    <p>🔗 <a href="${env.BUILD_URL}">Jenkins Artifact Output</a></p>
                    <p>🔗 <a href="${env.BUILD_URL}console">Jenkins Console Output</a></p>
                """,
                to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                mimeType: 'text/html',
                attachmentsPattern: "trivy-*.txt"
            )
        }
    }
}