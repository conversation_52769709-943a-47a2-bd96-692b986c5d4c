insert into agent.approval_levels (id, deleted, trx_type, channel, is_active, approver_role, level_number, min_approvers, is_direct_upline, requester_role) values
-- REJOIN_BP | requester BM
(375,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_BD', 1, 1, true, 'ROLE_AGE_BM'),
(376,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_BDM', 2, 1, false, 'ROLE_AGE_BM'),
(377,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_ABDD', 3, 1, false, 'ROLE_AGE_BM'),
(378,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_BDD', 4, 1, false, 'ROLE_AGE_BM'),
(379,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 5, 1, false, 'ROLE_AGE_BM'),
(380,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 6, 1, false, 'ROLE_AGE_BM'),
-- REJOIN_BP | requester BD
(381,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_BDM', 1, 1, false, 'ROLE_AGE_BD'),
(382,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_ABDD', 2, 1, false, 'ROLE_AGE_BD'),
(383,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_BDD', 3, 1, false, 'ROLE_AGE_BD'),
(384,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 4, 1, false, 'ROLE_AGE_BD'),
(385,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 5, 1, false, 'ROLE_AGE_BD'),
-- REJOIN_BP | requester BDM
(386,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_ABDD', 1, 1, false, 'ROLE_AGE_BDM'),
(387,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_BDD', 2, 1, false, 'ROLE_AGE_BDM'),
(388,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 3, 1, false, 'ROLE_AGE_BDM'),
(389,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 4, 1, false, 'ROLE_AGE_BDM'),
-- REJOIN_BP | requester ABDD
(390,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_AGE_BDD', 1, 1, false, 'ROLE_AGE_ABDD'),
(391,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_ABDD'),
(392,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_ABDD'),
-- REJOIN_BP | requester BDD
(393,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 1, 1, false, 'ROLE_AGE_BDD'),
(394,  false, 'REJOIN_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 2, 1, false, 'ROLE_AGE_BDD'),
