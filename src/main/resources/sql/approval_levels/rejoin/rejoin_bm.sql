insert into agent.approval_levels (id, deleted, trx_type, channel, is_active, approver_role, level_number, min_approvers, is_direct_upline, requester_role) values
-- REJOIN_BM | requester BD
(395,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_AGE_BDM', 1, 1, false, 'ROLE_AGE_BD'),
(396,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_AGE_ABDD', 2, 1, false, 'ROLE_AGE_BD'),
(397,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_AGE_BDD', 3, 1, false, 'ROLE_AGE_BD'),
(398,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 4, 1, false, 'ROLE_AGE_BD'),
(399,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 5, 1, false, 'ROLE_AGE_BD'),
-- REJOIN_BM | requester BDM
(400,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_AGE_ABDD', 1, 1, false, 'ROLE_AGE_BDM'),
(401,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_AGE_BDD', 2, 1, false, 'ROLE_AGE_BDM'),
(402,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 3, 1, false, 'ROLE_AGE_BDM'),
(403,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 4, 1, false, 'ROLE_AGE_BDM'),
-- REJOIN_BM | requester ABDD
(404,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_AGE_BDD', 1, 1, false, 'ROLE_AGE_ABDD'),
(405,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_ABDD'),
(406,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_ABDD'),
-- REJOIN_BM | requester BDD
(407,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 1, 1, false, 'ROLE_AGE_BDD'),
(408,  false, 'REJOIN_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 2, 1, false, 'ROLE_AGE_BDD'),
