insert into agent.approval_levels (id, deleted, trx_type, channel, is_active, approver_role, level_number, min_approvers, is_direct_upline, requester_role) values
-- TERMINASI_BD | requester BD
(353,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_BDM', 1, 1, false, 'ROLE_AGE_BD'),
(354,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_ABDD', 2, 1, false, 'ROLE_AGE_BD'),
(355,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_BDD', 3, 1, false, 'ROLE_AGE_BD'),
(356,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 4, 1, false, 'ROLE_AGE_BD'),
(357,  false, 'TERMINASI_BD', 'AGE', true, 'R<PERSON><PERSON>_CAS_APPROVAL_AGE', 5, 1, false, 'ROLE_AGE_BD'),
-- TERMINASI_BD | requester BDM
(358,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_ABDD', 1, 1, false, 'ROLE_AGE_BDM'),
(359,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_BDD', 2, 1, false, 'ROLE_AGE_BDM'),
(360,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 3, 1, false, 'ROLE_AGE_BDM'),
(361,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 4, 1, false, 'ROLE_AGE_BDM'),
-- TERMINASI_BD | requester ABDD
(362,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_BDD', 1, 1, false, 'ROLE_AGE_ABDD'),
(363,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_ABDD'),
(364,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_ABDD'),
-- TERMINASI_BD | requester BDD | approver HOS
(365,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_HOS', 1, 1, false, 'ROLE_AGE_BDD'),
(366,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_BDD'),
(367,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_BDD'),
-- TERMINASI_BD | requester BDD | approver CAO
(368,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_CAO', 1, 1, false, 'ROLE_AGE_BDD'),
(369,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_BDD'),
(370,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_BDD'),
-- TERMINASI_BD | requester HOS
(371,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_AGE_CAO', 1, 1, false, 'ROLE_AGE_HOS'),
(372,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_HOS'),
(373,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_HOS'),
-- TERMINASI_BD | requester CAO
(374,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 1, 1, false, 'ROLE_AGE_CAO'),
(375,  false, 'TERMINASI_BD', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 2, 1, false, 'ROLE_AGE_CAO');
