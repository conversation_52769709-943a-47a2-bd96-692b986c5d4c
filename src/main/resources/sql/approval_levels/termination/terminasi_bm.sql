insert into agent.approval_levels (id, deleted, trx_type, channel, is_active, approver_role, level_number, min_approvers, is_direct_upline, requester_role) values
-- TERMINASI_BM | requester BM
(324,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_BD', 1, 1, true, 'ROLE_AGE_BM'),
(325,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_BDM', 2, 1, false, 'ROLE_AGE_BM'),
(326,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_ABDD', 3, 1, false, 'ROLE_AGE_BM'),
(327,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_BDD', 4, 1, false, 'ROLE_AGE_BM'),
(328,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 5, 1, false, 'ROLE_AGE_BM'),
(329,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 6, 1, false, 'ROLE_AGE_BM'),
-- TERMINASI_BM | requester BD
(330,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_BDM', 1, 1, false, 'ROLE_AGE_BD'),
(331,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_ABDD', 2, 1, false, 'ROLE_AGE_BD'),
(332,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_BDD', 3, 1, false, 'ROLE_AGE_BD'),
(333,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 4, 1, false, 'ROLE_AGE_BD'),
(334,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 5, 1, false, 'ROLE_AGE_BD'),
-- TERMINASI_BM | requester BDM
(335,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_ABDD', 1, 1, false, 'ROLE_AGE_BDM'),
(336,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_BDD', 2, 1, false, 'ROLE_AGE_BDM'),
(337,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 3, 1, false, 'ROLE_AGE_BDM'),
(338,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 4, 1, false, 'ROLE_AGE_BDM'),
-- TERMINASI_BM | requester ABDD
(339,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_BDD', 1, 1, false, 'ROLE_AGE_ABDD'),
(340,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_ABDD'),
(341,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_ABDD'),
-- TERMINASI_BM | requester BDD | approver HOS
(342,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_HOS', 1, 1, false, 'ROLE_AGE_BDD'),
(343,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_BDD'),
(344,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_BDD'),
-- TERMINASI_BM | requester BDD | approver CAO
(345,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_CAO', 1, 1, false, 'ROLE_AGE_BDD'),
(346,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_BDD'),
(347,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_BDD'),
-- TERMINASI_BM | requester HOS
(348,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_AGE_CAO', 1, 1, false, 'ROLE_AGE_HOS'),
(349,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_HOS'),
(350,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_HOS'),
-- TERMINASI_BM | requester CAO
(351,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 1, 1, false, 'ROLE_AGE_CAO'),
(352,  false, 'TERMINASI_BM', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 2, 1, false, 'ROLE_AGE_CAO');
