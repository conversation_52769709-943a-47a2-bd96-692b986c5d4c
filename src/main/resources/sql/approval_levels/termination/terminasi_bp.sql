insert into agent.approval_levels (id, deleted, trx_type, channel, is_active, approver_role, level_number, min_approvers, is_direct_upline, requester_role) values
-- TERMINASI_BP | requester BP
(288,  false,'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BM', 1, 1, true, 'ROLE_AGE_BP'),
(289,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BD', 2, 1, true, 'ROLE_AGE_BP'),
(290,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BDM', 3, 1, false, 'ROLE_AGE_BP'),
(291,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_ABDD', 4, 1, false, 'ROLE_AGE_BP'),
(292,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BDD', 5, 1, false, 'ROLE_AGE_BP'),
(293,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 6, 1, false, 'ROLE_AGE_BP'),
(294,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 7, 1, false, 'ROLE_AGE_BP'),
-- TERMINASI BP | requester BM
(295,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BD', 1, 1, true, 'ROLE_AGE_BM'),
(296,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BDM', 2, 1, false, 'ROLE_AGE_BM'),
(297,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_ABDD', 3, 1, false, 'ROLE_AGE_BM'),
(298,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BDD', 4, 1, false, 'ROLE_AGE_BM'),
(299,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 5, 1, false, 'ROLE_AGE_BM'),
(300,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 6, 1, false, 'ROLE_AGE_BM'),
-- TERMINASI BP | requester BD
(301,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BDM', 1, 1, false, 'ROLE_AGE_BD'),
(302,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_ABDD', 2, 1, false, 'ROLE_AGE_BD'),
(303,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BDD', 3, 1, false, 'ROLE_AGE_BD'),
(304,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 4, 1, false, 'ROLE_AGE_BD'),
(305,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 5, 1, false, 'ROLE_AGE_BD'),
-- TERMINASI BP | requester BDM
(306,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_ABDD', 1, 1, false, 'ROLE_AGE_BDM'),
(307,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BDD', 2, 1, false, 'ROLE_AGE_BDM'),
(308,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 3, 1, false, 'ROLE_AGE_BDM'),
(309,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 4, 1, false, 'ROLE_AGE_BDM'),
-- TERMINASI BP | requester ABDD
(310,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_BDD', 1, 1, false, 'ROLE_AGE_ABDD'),
(311,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_ABDD'),
(312,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_ABDD'),
-- TERMINASI BP | requester BDD | approver HOS
(313,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_HOS', 1, 1, false, 'ROLE_AGE_BDD'),
(314,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_BDD'),
(315,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_BDD'),
-- TERMINASI BP | requester BDD | approver CAO
(316,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_CAO', 1, 1, false, 'ROLE_AGE_BDD'),
(317,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_BDD'),
(318,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_BDD'),
-- TERMINASI BP | requester HOS
(319,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_AGE_CAO', 1, 1, false, 'ROLE_AGE_HOS'),
(320,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 2, 1, false, 'ROLE_AGE_HOS'),
(321,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 3, 1, false, 'ROLE_AGE_HOS'),
-- TERMINASI BP | requester CAO
(322,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_REVIEW_AGE', 1, 1, false, 'ROLE_AGE_CAO'),
(323,  false, 'TERMINASI_BP', 'AGE', true, 'ROLE_CAS_APPROVAL_AGE', 2, 1, false, 'ROLE_AGE_CAO');
