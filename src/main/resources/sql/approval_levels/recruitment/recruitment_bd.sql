
-- Recruitment BD approval levels
-- BD as recruiter
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_BD', 'RECRUITMENT_BD', 1, 1, true, 'ROLE_AGE_BD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_BDM', 'RECRUITMENT_BD', 2, 1, true, 'ROLE_AGE_BD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_ABDD', 'RECRUITMENT_BD', 3, 1, true, 'ROLE_AGE_BD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_BDD', 'RECRUITMENT_BD', 4, 1, false, 'ROLE_AGE_BD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_ARA', 'RECRUITMENT_BD', 5, 1, false, 'ROLE_AGE_BD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_HOS', 'RECRUITMENT_BD', 6, 1, false, 'ROLE_AGE_BD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_CAO', 'RECRUITMENT_BD', 7, 1, false, 'ROLE_AGE_BD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_REVIEW_AGE', 'RECRUITMENT_BD', 8, 1, false, 'ROLE_AGE_BD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_APPROVAL_AGE', 'RECRUITMENT_BD', 9, 1, false, 'ROLE_AGE_BD', true, false, 'AGE');

-- ARA as recruiter
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_ARA', 'RECRUITMENT_BD', 1, 1, false, 'ROLE_AGE_ARA', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_HOS', 'RECRUITMENT_BD', 2, 1, false, 'ROLE_AGE_ARA', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_CAO', 'RECRUITMENT_BD', 3, 1, false, 'ROLE_AGE_ARA', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_REVIEW_AGE', 'RECRUITMENT_BD', 4, 1, false, 'ROLE_AGE_ARA', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_APPROVAL_AGE', 'RECRUITMENT_BD', 5, 1, false, 'ROLE_AGE_ARA', true,false, 'AGE');


-- BDM as recruiter
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_BDM', 'RECRUITMENT_BD', 1, 1, false, 'ROLE_AGE_BDM', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_ABDD', 'RECRUITMENT_BD', 2, 1, false, 'ROLE_AGE_BDM', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_BDD', 'RECRUITMENT_BD', 3, 1, false, 'ROLE_AGE_BDM', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_ARA', 'RECRUITMENT_BD', 4, 1, false, 'ROLE_AGE_BDM', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_HOS', 'RECRUITMENT_BD', 5, 1, false, 'ROLE_AGE_BDM', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_CAO', 'RECRUITMENT_BD', 6, 1, false, 'ROLE_AGE_BDM', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_REVIEW_AGE', 'RECRUITMENT_BD', 7, 1, false, 'ROLE_AGE_BDM', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_APPROVAL_AGE', 'RECRUITMENT_BD', 8, 1, false, 'ROLE_AGE_BDM', true,false, 'AGE');


-- ABDD as recruiter
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_ABDD', 'RECRUITMENT_BD', 1, 1, false, 'ROLE_AGE_ABDD', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_BDD', 'RECRUITMENT_BD', 2, 1, false, 'ROLE_AGE_ABDD', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_ARA', 'RECRUITMENT_BD', 3, 1, false, 'ROLE_AGE_ABDD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_HOS', 'RECRUITMENT_BD', 4, 1, false, 'ROLE_AGE_ABDD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_CAO', 'RECRUITMENT_BD', 5, 1, false, 'ROLE_AGE_ABDD', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_REVIEW_AGE', 'RECRUITMENT_BD', 6, 1, false, 'ROLE_AGE_ABDD', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_APPROVAL_AGE', 'RECRUITMENT_BD', 7, 1, false, 'ROLE_AGE_ABDD', true,false, 'AGE');


-- BDD as recruiter
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_BDD', 'RECRUITMENT_BD', 1, 1, false, 'ROLE_AGE_BDD', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_ARA', 'RECRUITMENT_BD', 2, 1, false, 'ROLE_AGE_BDD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active, deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_HOS', 'RECRUITMENT_BD', 3, 1, false, 'ROLE_AGE_BDD', true, false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_CAO', 'RECRUITMENT_BD', 4, 1, false, 'ROLE_AGE_BDD', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_REVIEW_AGE', 'RECRUITMENT_BD', 5, 1, false, 'ROLE_AGE_BDD', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_APPROVAL_AGE', 'RECRUITMENT_BD', 6, 1, false, 'ROLE_AGE_BDD', true,false, 'AGE');


-- HOS as recruiter
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_HOS', 'RECRUITMENT_BD', 1, 1, false, 'ROLE_AGE_HOS', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_CAO', 'RECRUITMENT_BD', 2, 1, false, 'ROLE_AGE_HOS', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_REVIEW_AGE', 'RECRUITMENT_BD', 3, 1, false, 'ROLE_AGE_HOS', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_APPROVAL_AGE', 'RECRUITMENT_BD', 4, 1, false, 'ROLE_AGE_HOS', true,false, 'AGE');


-- CAO as recruiter
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_AGE_CAO', 'RECRUITMENT_BD', 1, 1, false, 'ROLE_AGE_CAO', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_REVIEW_AGE', 'RECRUITMENT_BD', 2, 1, false, 'ROLE_AGE_CAO', true,false, 'AGE');
INSERT INTO agent.approval_levels (id,approver_role, trx_type, level_number, min_approvers, is_direct_upline, requester_role, is_active,deleted, channel)
VALUES (nextval('agent.approval_levels_id_seq'),'ROLE_CAS_APPROVAL_AGE', 'RECRUITMENT_BD', 3, 1, false, 'ROLE_AGE_CAO', true,false, 'AGE');


