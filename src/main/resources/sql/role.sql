CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    description TEXT,
    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);


INSERT INTO agent.roles ("id", "code", "name") VALUES
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_BP', 'Business Partner'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_BM', 'Business Manager'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_BD', 'Business Developer'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_SECRETARY_BP', 'Sekretaris Business Partner'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_SECRETARY_BM', '<PERSON><PERSON><PERSON>ris Business Manager'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_SECRETARY_BD', 'Sekretaris Business Developer'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_ADMIN_BP', 'Admin Business Partner'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_ADMIN_BM', 'Admin Business Manager'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_ADMIN_BD', 'Admin Business Developer'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_BDM', 'Business Development Manager'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_ABDD', 'Associate Business Development Director'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_BDD', 'Business Development Director'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_HOS', 'Head of Sales'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_CAO', 'Chief Agency Officer'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_ARA', 'Agency Recruitment & Activation'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_ADMIN_ARA', 'Admin Agency Recruitment & Activation'),
  (nextval('agent.roles_id_seq'), 'ROLE_AGE_HEAD_ARA', 'Head Agency Recruitment & Activation'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_BO', 'Bancassurance Office'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_ASM', 'Area Sales Manager'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_RSM', 'Regional Sales Manager'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_HOS', 'Head of Sales'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_HOR', 'Head of Recruitment'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_HOB', 'Head of BAN'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_BS', 'BAN Support'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_SR', 'Staff Recruitment'),
  (nextval('agent.roles_id_seq'), 'ROLE_BAN_BTR', 'Trainer Banca'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_REVIEW_BAN', 'CAS Keagenan - Reviewer BAN'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_APPROVAL_BAN', 'CAS Keagenan - Approval BAN'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_REVIEW_AGE', 'CAS Keagenan - Reviewer AGE'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_APPROVAL_AGE', 'CAS Keagenan - Approval AGE'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_LISENSI_REVIEW_BAN', 'CAS Lisensi – Reviewer BAN'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_LISENSI_REVIEW_AGE', 'CAS Lisensi – Reviewer AGE'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_LISENSI_APPROVAL', 'CAS Lisensi – Approval'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_AML_REVIEW', 'CAS AML – Reviewer'),
  (nextval('agent.roles_id_seq'), 'ROLE_CAS_AML_APPROVAL', 'CAS AML – Approval'),
  (nextval('agent.roles_id_seq'), 'ROLE_ADMIN_MCC', 'Admin CMS MCC'),
  (nextval('agent.roles_id_seq'), 'ROLE_ADMIN_AGE', 'Admin CMS AGE'),
  (nextval('agent.roles_id_seq'), 'ROLE_ADMIN_BAN', 'Admin CMS BAN'),
  (nextval('agent.roles_id_seq'), 'ROLE_SUPER_ADMIN_AGE', 'Super Admin AGE'),
  (nextval('agent.roles_id_seq'), 'ROLE_SUPER_ADMIN_BAN', 'Super Admin BAN'),
  (nextval('agent.roles_id_seq'), 'ROLE_SUPER_ADMIN_ITE', 'Super Admin ITE'),
  (nextval('agent.roles_id_seq'), 'ROLE_SUPER_ADMIN', 'Super Admin');





