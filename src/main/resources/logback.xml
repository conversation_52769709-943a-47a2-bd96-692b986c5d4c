<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property resource='application.properties'/>
    <!--  <property resource='application-local.properties'/>-->

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date %-5level %class:%line - [%X{Username}] [%X{X-Transaction-ID}] %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.hibernate.SQL" level="info" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="id.co.panindaiichilife.superapp.agent" level="debug" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="okhttp3.logging" level="trace" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <root level="warn">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>