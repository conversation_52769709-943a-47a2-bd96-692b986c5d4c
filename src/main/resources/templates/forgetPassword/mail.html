<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="initial-scale=1.0">
  <meta name="format-detection" content="telephone=no">
  <title>MOSAICO Responsive Email Designer</title>
  <style type="text/css">.socialLinks {
    font-size: 6px;
  }

  .socialLinks a {
    display: inline-block;
  }

  .socialIcon {
    display: inline-block;
    vertical-align: top;
    padding-bottom: 0px;
    border-radius: 100%;
  }

  table.vb-row, table.vb-content {
    border-collapse: separate;
  }

  table.vb-row {
    border-spacing: 9px;
  }

  table.vb-row.halfpad {
    border-spacing: 0;
    padding-left: 9px;
    padding-right: 9px;
  }

  table.vb-row.fullwidth {
    border-spacing: 0;
    padding: 0;
  }

  table.vb-container.fullwidth {
    padding-left: 0;
    padding-right: 0;
  }</style>
  <style type="text/css">
    /* yahoo, hotmail */
    .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
      line-height: 100%;
    }

    .yshortcuts a {
      border-bottom: none !important;
    }

    .vb-outer {
      min-width: 0 !important;
    }

    .RMsgBdy, .ExternalClass {
      width: 100%;
      background-color: #3f3f3f;
      background-color: #3f3f3f
    }

    /* outlook */
    table {
      mso-table-rspace: 0pt;
      mso-table-lspace: 0pt;
    }

    #outlook a {
      padding: 0;
    }

    img {
      outline: none;
      text-decoration: none;
      border: none;
      -ms-interpolation-mode: bicubic;
    }

    a img {
      border: none;
    }

    @media screen and (max-device-width: 600px), screen and (max-width: 600px) {
      table.vb-container, table.vb-row {
        width: 95% !important;
      }

      .mobile-hide {
        display: none !important;
      }

      .mobile-textcenter {
        text-align: center !important;
      }

      .mobile-full {
        float: none !important;
        width: 100% !important;
        max-width: none !important;
        padding-right: 0 !important;
        padding-left: 0 !important;
      }

      img.mobile-full {
        width: 100% !important;
        max-width: none !important;
        height: auto !important;
      }
    }
  </style>
  <style
      type="text/css">#ko_singleArticleBlock_3 .links-color a:visited, #ko_singleArticleBlock_3 .links-color a:hover {
    color: #3f3f3f;
    color: #3f3f3f;
    text-decoration: underline;
  }

  #ko_footerBlock_2 .links-color a:visited, #ko_footerBlock_2 .links-color a:hover {
    color: #ccc;
    color: #ccc;
    text-decoration: underline;
  }</style>
</head>
<body style="margin: 0;padding: 0;background-color: #3f3f3f;color: #919191;" text="#919191"
      vlink="#cccccc" alink="#cccccc" bgcolor="#3f3f3f">

<center>

  <!-- preheaderBlock -->

  <!-- /preheaderBlock -->

  <table id="ko_singleArticleBlock_3" style="background-color: #f8f8f8;" class="vb-outer"
         cellpadding="0" cellspacing="0" width="100%" bgcolor="#f8f8f8" border="0">
    <tbody>
    <tr>
      <td style="padding-left: 9px;padding-right: 9px;background-color: #f8f8f8;" class="vb-outer"
          valign="top" align="center" bgcolor="#f8f8f8">

        <!--[if (gte mso 9)|(lte ie 8)]>
        <table align="center" border="0" cellspacing="0" cellpadding="0" width="570">
          <tr>
            <td align="center" valign="top"><![endif]-->
        <div class="oldwebkit" style="max-width: 570px;">
          <table
              style="border: 1px solid ddd;border-collapse: separate;border-spacing: 18px;padding-left: 0;padding-right: 0;width: 100%;max-width: 570px;background-color: #fff;"
              class="vb-container fullpad" cellpadding="0" cellspacing="18" width="570"
              bgcolor="#ffffff" border="0">
            <tbody>
            <tr>
              <td class="links-color" valign="top" width="100%" align="left">
                <img src="cid:banner"
                     style="border: 0px;display: block;vertical-align: top;max-width: 534px;width: 100%;height: auto;"
                     class="mobile-full" alt="" hspace="0" vspace="0" width="534" border="0"></td>
            </tr>
            <tr>
              <td>
                <table cellpadding="0" cellspacing="0" width="100%" align="left" border="0">
                  <tbody>
                  <tr>
                    <td style="font-size: 18px; font-family: Arial, Helvetica, sans-serif; color: #3f3f3f; text-align: left;">
                      <span style="color: #3f3f3f;">Password Reset Request<br
                          data-mce-bogus="1"></span>
                    </td>
                  </tr>
                  <tr>
                    <td style="font-size: 1px; line-height: 1px;" height="9"></td>
                  </tr>
                  <tr>
                    <td style="text-align: left; font-size: 13px; font-family: Arial, Helvetica, sans-serif; color: #3f3f3f;"
                        class="long-text links-color" align="left">
                      <p style="margin: 1em 0px;margin-top: 0px;">
                        A request has been made to reset your account password.
                      </p>
                      <p style="margin: 1em 0px;">
                        To reset your password, follow this link and enter
                        your desired new password.
                      </p>
                      <p style="margin: 1em 0px;margin-bottom: 0px;">
                        <a th:href="${url}" th:text="${url}"
                           style="color: #3f3f3f;text-decoration: underline;">
                          link</a><br data-mce-bogus="1">
                      </p>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <!--[if (gte mso 9)|(lte ie 8)]></td></tr></table><![endif]-->
      </td>
    </tr>
    </tbody>
  </table>
</center>

</body>
</html>
