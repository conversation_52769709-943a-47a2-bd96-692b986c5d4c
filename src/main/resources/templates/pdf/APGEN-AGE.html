<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>Aplik<PERSON>nan</title>
    <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        @page {
          width: 800px;
          height: 792px;
          margin: 0.3in;
          bleed: 0;
          marks: none;
        }

        body {
          font-family: "Times New Roman", serif;
          font-size: 6pt;
          line-height: 1.5;
          color: #000;
          background: #fff;
          width: 800px;
          margin: 0 auto;
        }

        /* FORM CONTAINER - REPLACED FLEX WITH FLOAT */
        .form-container {
          width: 100%;
          overflow: hidden; /* Clearfix */
        }

        .form-column {
          float: left;
          width: 30%;
          box-sizing: border-box;
          margin-right: 2%;
          padding: 0 5px;
        }

        .form-column:last-child {
          margin-right: 0;
        }

        /* FORM LINE - REPLACED FLEX WITH FLOAT */
        .form-line {
          margin-bottom: 8px;
          overflow: hidden;
        }

        .form-label {
          float: left;
          width: 100px;
          margin-top: 3px;
        }

        .form-input {
          margin-left: 95px;
          border-bottom: 1px solid #000;
          min-height: 20px;
          display: block;
          text-align: left;
          padding-top: 4px;
        }

        /* SPECIAL ELEMENTS */
        .header {
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 20px;
          padding-top: 50px;
          width: 100%;
        }

        .photo-box {
          margin: 20px auto;
          width: 120px;
          height: 160px;
          border: 1px solid #000;
          position: relative;
        }

        .photo-text {
          position: absolute;
          top: 20px;
          width: 100%;
          text-align: center;
          font-size: 10px;
          color: #333;
          font-weight: bold;
        }

        .company-name {
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          margin: 20px 0;
          padding-top: 50px;
        }

        .agent-note {
          font-size: 10px;
          margin-bottom: 20px;
        }

        .checkbox-container {
          margin-left: 60px;
          margin-top: 2px;
        }

        .checkbox-row {
          display: inline-block;
        }

        .checkbox-box {
          width: 16px;
          height: 16px;
          border: 1px solid #000;
          display: inline-block;
          margin-right: 2px;
          text-align: center;
        }

        .checkbox-label {
          font-size: 10px;
          margin-top: -8px;
        }

        /* CLEARFIX HELPER */
        .clearfix::after {
          content: "";
          display: table;
          clear: both;
        }

        /* ADDITIONAL STYLES FROM ORIGINAL */
        table {
          border-collapse: collapse;
          width: 100%;
        }
        th,
        td {
          border: 1px solid black;
          padding: 10px;
          text-align: left;
        }
        th {
          font-weight: bold;
          text-align: center;
        }
        .header-row th {
          text-align: center;
        }
        .empty-cell {
          height: 50px;
        }

        .statement-title {
          text-align: center;
          font-weight: bold;
          margin-bottom: 20px;
          font-size: 18px;
          width: 100%;
        }

        .statement-content {
          margin-bottom: 30px;
          text-align: justify;
        }

        .signature-section {
          margin-top: 0;
          display: block;
          height: 100%;
        }

        .signature-place {
          margin-left: 30px;
        }

        .signature-label {
          border: 2px solid #000;
          font-weight: bold;
          margin-top: 15px;
          font-size: 10px;
          margin-left: 30px;
          text-align: center;
          padding-top: 10px;
          width: 70%;
        }

        @media print {
          body {
            width: 800px;
            height: 800px;
            margin: auto;
            padding: auto;
            background: none;
          }

          .page {
            width: 800px;
            height: 1000px;
            margin: auto;
            padding: auto;
            border: none;
            box-shadow: none;
          }
        }

        .checked-icon {
          display: inline-block;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          position: relative;
        }

        .checked-icon::after {
          content: "X";
          position: absolute;
          left: 6px;
          width: 5px;
          height: 10px;
          border-width: 0 3px 3px 0;
        }
    </style>
</head>
<body>
<div class="form-container">
    <!-- Column 1: Header and form fields -->
    <div class="form-column">
        <div class="header">APLIKASI KEAGENAN</div>

        <div class="form-line">
            <div class="form-label">Distribution Channel :</div>
            <div class="form-input" style="margin-top: -6px" th:text="${distributionChannel}"></div>
        </div>

        <div class="form-line">
            <div class="form-label">Group/BD :</div>
            <div class="form-input" style="margin-top: -7px" th:text="${groupBD}"></div>
        </div>

        <div class="form-line">
            <div class="form-label">
                Sales Office/GA : <br/>
                (Penempatan Agent)
            </div>
            <div class="form-input" style="margin-top: -8px" th:text="${salesOfficeGA}"></div>
        </div>
    </div>

    <!-- Column 2: Photo -->
    <div class="form-column" style="margin-left: -20px">
        <div class="photo-container">
            <div class="photo-box">
                <div class="photo-text">
                    <div th:if="${passPhoto != null and passPhoto != ''}">
                        <img
                                alt="selfie photo"
                                style="width: 100px;"
                                th:src="${passPhoto}"
                        />
                    </div>
                    <div th:unless="${passPhoto != null and passPhoto != ''}">
                        FOTO <br/>3 X 4
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Column 3: Company name and agent info -->
    <div class="form-column" style="margin-left: -40px">
        <div class="company-name" style="margin-top: -1px; margin-left: 40px">
            <img
                    alt=""
                    src="https://upload.wikimedia.org/wikipedia/commons/b/b2/Panin_Dai-Ichi_Life.png"
                    width="300px"
            />
        </div>

        <div class="form-line">
            <div class="form-label" style="width: 60px !important">
                Kode Agen :
            </div>
            <div class="checkbox-container">
                <div class="checkbox-row">
                    <div class="checkbox-box"
                         th:text="${agentCode != null and agentCode.length() > 0 ? agentCode.charAt(0) : ''}"></div>
                    <div class="checkbox-box"
                         th:text="${agentCode != null and agentCode.length() > 1 ? agentCode.charAt(1) : ''}"></div>
                    <div class="checkbox-box"
                         th:text="${agentCode != null and agentCode.length() > 2 ? agentCode.charAt(2) : ''}"></div>
                    <div class="checkbox-box"
                         th:text="${agentCode != null and agentCode.length() > 3 ? agentCode.charAt(3) : ''}"></div>
                    <div class="checkbox-box"
                         th:text="${agentCode != null and agentCode.length() > 4 ? agentCode.charAt(4) : ''}"></div>
                    <div class="checkbox-box"
                         th:text="${agentCode != null and agentCode.length() > 5 ? agentCode.charAt(5) : ''}"></div>
                    <div class="checkbox-box"
                         th:text="${agentCode != null and agentCode.length() > 6 ? agentCode.charAt(6) : ''}"></div>
                    <div class="checkbox-box"
                         th:text="${agentCode != null and agentCode.length() > 7 ? agentCode.charAt(7) : ''}"></div>
                    <div class="checkbox-label" style="margin-top: 0.1px">
                        (Diisi oleh Kantor Pusat)
                    </div>
                </div>
            </div>
        </div>

        <div class="form-line">
            <div class="form-label" style="width: 60px !important">
                Level Agen :
            </div>
            <div class="checkbox-container">
                <div class="checkbox-row" style="width: 100%">
                    <div
                            class="form-input"
                            style="margin-left: 0px; margin-top: -10px"
                            th:text="${agentLevel}"
                    ></div>
                    <div class="checkbox-label" style="margin-top: 0.1px">
                        (wajib Diisi)
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div>
    <p>
        Bersama ini Saya mengajukan untuk menjadi Agen Asuransi Jiwa di PT Panin
        Dai-ichi Life dengan data sebagai berikut :
    </p>
</div>
<div style="font-weight: bold">
    <p>Data di bawah wajib diisi dengan lengkap</p>
    <p>
        * Alamat Rumah dan No. Telepon Rumah tidak diperkenankan menggunakan
        alamat dan nomor telepon kantor pemasaran PT. Panin Dai-ichi Life
    </p>
    <p>
        ** No. Hp dan Alamat e-mail tidak diperkenankan menggunakan No. Hp dan
        Alamat e-mail yang pernah/sudah terdaftar di PT. Panin Dai-ichi Life
    </p>
</div>
<!-- Data Pribadi -->
<div style="width: 100%; margin-top: 4px">
    <p
            style="background-color: #00ffff; font-weight: bold; margin-bottom: 5px"
    >
        I. Data Pribadi
    </p>
    <div class="form-line">
        <div class="form-label">Nama Lengkap</div>
        <div class="form-input" th:text="${fullName}"></div>
    </div>
    <div class="form-line">
        <div class="form-label" style="width: 100px">
            Tempat &amp; Tanggal Lahir
        </div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
                th:text="${birthPlace}"
        ></div>
        <span style="margin-right: 10px">tgl</span>
        <div
                class="form-input"
                style="
            padding-left: 50px !important;
            text-indent: -50px;
            display: inline-block;
            margin-left: -5px;
          "
                th:text="${birthDate != null and birthDate.contains('/') and birthDate.split('/').length > 0 ? birthDate.split('/')[0] : ''}"
        ></div>
        <span style="margin-right: 10px">bln</span>
        <div
                class="form-input"
                style="
            padding-left: 50px !important;
            text-indent: -50px;
            display: inline-block;
            margin-left: -5px;
          "
                th:text="${birthDate != null and birthDate.contains('/') and birthDate.split('/').length > 1 ? birthDate.split('/')[1] : ''}"
        ></div>
        <span style="margin-right: 10px">thn</span>
        <div
                class="form-input"
                style="
            padding-left: 50px !important;
            display: inline-block;
            text-indent: -50px;
            margin-left: -5px;
            margin-right: 20px;
          "
                th:text="${birthDate != null and birthDate.contains('/') and birthDate.split('/').length > 2 ? birthDate.split('/')[2] : ''}"
        ></div>

        <span style="vertical-align: middle; margin-right: 8px"
        >Jenis Kelamin</span
        >
        <div
                style="
            border: 1px solid #000;
            width: 18px;
            height: 13px;
            display: inline-block;
            vertical-align: middle;
          "
        >
            <div class="checked-icon" th:if="${gender == 'M'}"></div>
        </div>
        <span style="margin-top: 2px">Pria</span>
        <div
                style="
            border: 1px solid #000;
            width: 18px;
            height: 13px;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
          "
        >
            <div class="checked-icon" th:if="${gender == 'F'}"></div>
        </div>
        <span style="margin-top: 2px; margin-right: 35px">Wanita</span>
    </div>
    <div class="form-line">
        <div class="form-label">No.KTP</div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
                th:text="${ktpNumber}"
        ></div>
        <span style="margin-right: 20px; margin-left: 20px"
        >Masa Berlaku KTP</span
        >
        <span style="margin-right: 10px">tgl</span>
        <div
                class="form-input"
                style="
            padding-left: 50px !important;
            text-indent: -50px;
            display: inline-block;
            margin-left: -5px;
            margin-right: 20px;
          "
                th:text="${ktpValidityDate != null and ktpValidityDate.contains('/') and ktpValidityDate.split('/').length > 0 ? ktpValidityDate.split('/')[0] : ''}"
        ></div>
        <span style="margin-right: 10px">Bln</span>
        <div
                class="form-input"
                style="
            padding-left: 50px !important;
            text-indent: -50px;
            display: inline-block;
            margin-left: -5px;
            margin-right: 20px;
          "
                th:text="${ktpValidityDate != null and ktpValidityDate.contains('/') and ktpValidityDate.split('/').length > 1 ? ktpValidityDate.split('/')[1] : ''}"
        ></div>
        <span style="margin-right: 10px">Thn</span>
        <div
                class="form-input"
                style="
            padding-left: 50px !important;
            text-indent: -50px;
            display: inline-block;
            margin-left: -5px;
            margin-right: 20px;
          "
                th:text="${ktpValidityDate != null and ktpValidityDate.contains('/') and ktpValidityDate.split('/').length > 2 ? ktpValidityDate.split('/')[2] : ''}"
        ></div>
    </div>
    <div class="form-line">
        <div class="form-label">Status Perkawinan</div>
        <div
                style="
            border: 1px solid #000;
            width: 18px;
            height: 13px;
            display: inline-block;
            vertical-align: middle;
          "
        >
            <div class="checked-icon" th:if="${maritalStatus == 'BELUM_KAWIN'}"></div>
        </div>
        <span style="margin-top: 2px; margin-right: 20px">Belum Menikah</span>
        <div
                style="
            border: 1px solid #000;
            width: 18px;
            height: 13px;
            display: inline-block;
            vertical-align: middle;
          "
        >
            <div class="checked-icon" th:if="${maritalStatus == 'KAWIN'}"></div>
        </div>
        <span style="margin-top: 2px; margin-right: 20px">Menikah</span>
        <div
                style="
            border: 1px solid #000;
            width: 18px;
            height: 13px;
            display: inline-block;
            vertical-align: middle;
          "
        >
            <div class="checked-icon" th:if="${maritalStatus == 'CERAI' or maritalStatus == 'CERAI_MATI'}"></div>
        </div>
        <span style="margin-top: 2px">Cerai</span>
    </div>
    <div class="form-line">
        <div class="form-label">Alamat Rumah <sup>*</sup></div>
        <div class="form-input" th:text="${homeAddress}"></div>
    </div>
    <div class="form-line">
        <div class="form-line" style="width: 100%">
            <div class="form-label"></div>
            <div
                    class="form-input"
                    style="
              padding-left: 250px !important;
              display: inline-block;
              text-indent: -250px;
              margin-left: -5px;
            "
                    th:text="${homeAddress}"
            ></div>
            <span style="margin-right: 10px">Kota</span>
            <div
                    class="form-input"
                    style="
              padding-left: 250px !important;
              display: inline-block;
              margin-left: -5px;
              text-indent: -250px;
            "
                    th:text="${city}"
            ></div>
            <span style="margin-right: 10px">Kode Pos</span>
            <div
                    class="form-input"
                    style="
              padding-left: 140px !important;
              display: inline-block;
              margin-left: -5px;
              text-indent: -140px;
            "
                    th:text="${postalCode}"
            ></div>
        </div>
    </div>
    <div class="form-line">
        <div class="form-label">No. Telepon Rumah</div>
        <div class="form-line" style="margin-left: 90px">
            <div
                    class="form-input"
                    style="
              padding-left: 50px !important;
              display: inline-block;
              margin-left: -5px;
              text-indent: -38px;
            "
                    th:text="${homePhone != null and homePhone.contains('-') ? homePhone.split('-')[0] : (homePhone != null ? homePhone : '')}"
            ></div>
            <span>-</span>
            <div
                    class="form-input"
                    style="
              padding-left: 200px !important;
              display: inline-block;
              margin-left: -5px;
              text-indent: -190px;
            "
                    th:text="${homePhone != null and homePhone.contains('-') and homePhone.split('-').length > 1 ? homePhone.split('-')[1] : ''}"
            ></div>
            <span style="margin-right: 10px">No. Fax</span>
            <div
                    class="form-input"
                    style="
              padding-left: 415px !important;
              display: inline-block;
              margin-left: -5px;
              text-indent: -415px;
            "
                    th:text="${faxNumber}"
            ></div>
        </div>
    </div>
    <div class="form-line">
        <div class="form-label">No. Hp <sup>**</sup></div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
                th:text="${mobilePhone}"
        ></div>
        <span style="margin-right: 20px">Alamat e-mail <sup>**</sup></span>
        <div
                class="form-input"
                style="
            padding-left: 380px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -380px;
          "
                th:text="${email}"
        ></div>
    </div>
    <div class="form-line">
        <div class="form-label">Kewarganegaraan</div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
        >
            Indonesia
        </div>
        <span style="margin-right: 20px">Jumlah Tanggungan</span>
        <div
                class="form-input"
                style="
            padding-left: 368px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -368px;
          "
                th:text="${numberOfDependents}"
        ></div>
    </div>
    <div class="form-line">
        <div class="form-label">Pekerjaan</div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
                th:text="${occupation}"
        ></div>
        <span style="margin-right: 15px">Bindang Usaha</span>
        <div
                class="form-input"
                style="
            padding-left: 388px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -388px;
          "
        ></div>
    </div>
    <div class="form-line">
        <div class="form-label">Jabatan</div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
        ></div>
        <span style="margin-right: 12px">Tipe Pekerjaan</span>
        <div
                class="form-input"
                style="
            padding-left: 388px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -388px;
          "
        ></div>
    </div>
    <div class="form-line">
        <div class="form-label">Nomor NPWP</div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
                th:text="${npwpNumber}"
        ></div>
        <span style="margin-right: 10px">Tipe PTKP</span>
        <div
                class="form-input"
                style="
            padding-left: 406px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -406px;
          "
        ></div>
    </div>
    <div class="form-line">
        <div class="form-label">Sumber Penghasilan</div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
                th:text="${incomeSource}"
        ></div>
        <span style="margin-right: 12px">Pendapatan Gross Tahunan</span>
        <div
                class="form-input"
                style="
            padding-left: 340px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -340px;
          "
        ></div>
    </div>
</div>
<div class="form-line">
    <div class="form-label">Alamat NPWP <sup>*</sup></div>
    <div class="form-input" th:text="${npkpAddress}"></div>
</div>
<div class="form-line">
    <div class="form-line" style="width: 100%">
        <div class="form-label"></div>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
                th:text="${educationCity}"
        ></div>
        <span style="margin-right: 10px">Kota</span>
        <div
                class="form-input"
                style="
            padding-left: 250px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -250px;
          "
        ></div>
        <span style="margin-right: 10px">Kode Pos</span>
        <div
                class="form-input"
                style="
            padding-left: 140px !important;
            display: inline-block;
            margin-left: -5px;
            text-indent: -140px;
          "
                th:text="${educationPostalCode}"
        ></div>
    </div>
</div>
<div class="form-line" style="margin-top: -10px">
    <div class="form-label" style="margin-top: 10px">Pendidikan Terakhir</div>
    <div
            style="
          border: 1px solid #000;
          width: 18px;
          height: 13px;
          display: inline-block;
          vertical-align: middle;
        "
    >
        <div class="checked-icon" th:if="${isEducationSD}"></div>
    </div>
    <span style="margin-right: 20px">SD</span>
    <div
            style="
          border: 1px solid #000;
          width: 18px;
          height: 13px;
          display: inline-block;
          vertical-align: middle;
        "
    >
        <div class="checked-icon" th:if="${isEducationSMP}"></div>
    </div>
    <span style="margin-right: 20px">SMP</span>
    <div
            style="
          border: 1px solid #000;
          width: 18px;
          height: 13px;
          display: inline-block;
          vertical-align: middle;
        "
    >
        <div class="checked-icon" th:if="${isEducationSMA}"></div>
    </div>
    <span style="margin-right: 20px">SMA/SMK</span>
    <div
            style="
          border: 1px solid #000;
          width: 18px;
          height: 13px;
          display: inline-block;
          vertical-align: middle;
        "
    >
        <div class="checked-icon" th:if="${isEducationAkademi}"></div>
    </div>
    <span style="margin-right: 20px">Akademik</span>
    <div
            style="
          border: 1px solid #000;
          width: 18px;
          height: 13px;
          display: inline-block;
          vertical-align: middle;
        "
    >
        <div class="checked-icon" th:if="${isEducationUniversitas}"></div>
    </div>
    <span style="margin-right: 20px">Universitas</span>
    <div
            style="
          border: 1px solid #000;
          width: 18px;
          height: 13px;
          display: inline-block;
          vertical-align: middle;
        "
    >
        <div class="checked-icon" th:if="${isEducationLainLain}"></div>
    </div>
    <span style="margin-right: 20px">Lain lain</span>
    <div
            class="form-input"
            style="
          padding-left: 230px !important;
          display: inline-block;
          margin-left: -5px;
          text-indent: -230px;
        "
            th:text="${educationLainLainDetail}"
    ></div>
</div>

<div class="form-line">
    <div class="form-label" style="width: 250px">
        Pernah menjadi Agen Asuransi Jiwa dalam 6 bulan terakhir?
    </div>
    <div
            style="
          border: 1px solid #000;
          width: 18px;
          height: 13px;
          display: inline-block;
          vertical-align: middle;
        "
    >
        <div class="checked-icon" th:if="${hasInsuranceExperienceYa}"></div>
    </div>
    <span style="margin-right: 20px">Ya</span>
    <div
            style="
          border: 1px solid #000;
          width: 18px;
          height: 13px;
          display: inline-block;
          vertical-align: middle;
        "
    >
        <div class="checked-icon" th:if="${hasInsuranceExperienceTidak}"></div>
    </div>
    <span style="margin-right: 20px">Tidak</span>
</div>

<div style="page-break-after: always"></div>
<div style="width: 93%">
    <p
            style="background-color: #00ffff; font-weight: bold; margin-bottom: 5px"
    >
        II. Data Keluarga yang dapat dihubungi
    </p>
    <table>
        <tr class="header-row">
            <th rowspan="2">KETERANGAN</th>
            <th colspan="2">NAMA ORANG TUA KANDUNG</th>
            <th rowspan="2">NAMA SUAMI/ISTRI</th>
        </tr>
        <tr>
            <th>Ayah</th>
            <th>Ibu</th>
        </tr>
        <tr>
            <td>Nama Lengkap</td>
            <td class="empty-cell" th:text="${fatherName}"></td>
            <td class="empty-cell" th:text="${motherName}"></td>
            <td class="empty-cell" th:text="${spouseName}"></td>
        </tr>
        <tr>
            <td>Tanggal Lahir</td>
            <td class="empty-cell" th:text="${fatherBirthDate}"></td>
            <td class="empty-cell" th:text="${motherBirthDate}"></td>
            <td class="empty-cell" th:text="${spouseBirthDate}"></td>
        </tr>
        <tr>
            <td>Alamat Rumah</td>
            <td class="empty-cell" th:text="${fatherAddress}"></td>
            <td class="empty-cell" th:text="${motherAddress}"></td>
            <td class="empty-cell" th:text="${spouseAddress}"></td>
        </tr>
        <tr>
            <td>No. Telp / No. Hp</td>
            <td class="empty-cell" th:text="${fatherPhone}"></td>
            <td class="empty-cell" th:text="${motherPhone}"></td>
            <td class="empty-cell" th:text="${spousePhone}"></td>
        </tr>
    </table>
</div>
<div style="width: 93%; margin-top: 10px">
    <p
            style="background-color: #00ffff; font-weight: bold; margin-bottom: 5px"
    >
        III. Data Rekning Bank
    </p>
    <table>
        <tr class="header-row">
            <th>Nama Pada Rekening Bank</th>
            <th>Nomor Rekening Bank</th>
            <th>NAMA Bank</th>
            <th>Cabang/ Capem &amp; Kota</th>
        </tr>

        <tr>
            <td class="empty-cell" th:text="${accountHolderName}"></td>
            <td class="empty-cell" th:text="${accountNumber}"></td>
            <td class="empty-cell" th:text="${bankName}"></td>
            <td class="empty-cell" th:text="${branchLocation}"></td>
        </tr>
    </table>
</div>
<div style="width: 93%; margin-top: 10px; text-align: justify">
    <p
            style="background-color: #00ffff; font-weight: bold; margin-bottom: 5px"
    >
        IV. Pendaftaran User Name ADS (Advisor Development System)
    </p>
    <p>
        Dengan mengajukan permintaan user name &amp; password untuk digunakan
        pada ADS maka saya menyatakan bahwa saya bersedia untuk user name dan
        password saya dikirimkan ke alamat email yang tercantum pada data
        pribadi dan saya menyatakan bahwa user name dan password tersebut adalah
        benar hanya akan dipergunakan oleh saya yang bertanda tangan di bawah
        dan saya akan menjaga kerahasiaan data yang terdapat dalam ADS tersebut.
    </p>
    <p><br/></p>
    <p>
        Saya mengerti dan memahami bahwa apabila terjadi perbedaan data antara
        data yang ditampilkan di dalam ADS dengan data yang terdapat di Kantor
        Pusat PT Panin Dai-ichi Life, maka data yang benar dan diakui oleh PT
        Panin Dai-ichi Life adalah data yang terdapat di Kantor Pusat PT Panin
        Dai-ichi Life. Sehubungan dengan hal tersebut, saya membebaskan dan
        melepaskan PT Panin Dai-ichi Life dari segala tuntunan dalam bentuk dan
        rupa apapun.
    </p>
</div>

<div style="width: 100%; margin-top: 10px; margin-left: -10px; margin-right: -10px">
    <table>
        <tr>
            <td style="width: 50%; border: none">
                <p
                        style="
                font-weight: bold;
                background-color: #00ffff;
                margin-top: -80px;
              "
                >
                    Pernyataan
                </p>
                <p>
                    Dengan ini saya menyatakan bahwa data dan informasi yang saya
                    berikan diatas adalah benar adanya dan saya menyadari bahwa jika
                    saya memberikan data dan informasi yang tidak benar atau palsu
                    maka perjanjian keagenan saya akan diakhiri oleh PT. Panin
                    Dai-ichi Life.
                </p>
            </td>
            <td style="border: none">
                <span>Ditandatangani di (Kota &amp; Tanggal):</span>
                <div
                        class="form-input"
                        style="
                padding-left: 215px !important;
                display: inline-block;
                margin-left: 5px;
                text-indent: -215px;
              "
                        th:text="${applicationLocation + ' / ' + applicationDate}"
                ></div>
                <div class="signature-label">
                    <div th:if="${candidateSignature != null and candidateSignature != ''}">
                        <img
                                alt="candidate signature"
                                style="width: 100px"
                                th:src="${candidateSignature}"
                        />
                    </div>
                    <p th:text="${fullName}"></p>
                </div>
            </td>
        </tr>
    </table>
</div>
</body>
</html>
