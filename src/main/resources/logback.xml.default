<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <property resource='application.properties'/>
  <property resource='application-local.properties'/>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%date %-5level %class:%line - [%X{Username}] [%X{X-Transaction-ID}] %msg%n</pattern>
    </encoder>
  </appender>

  <appender name="API" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${log.dir}/api.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${log.dir}/api.log.%d{yyyy-MM-dd}</fileNamePattern>
      <maxHistory>7</maxHistory>
    </rollingPolicy>
    <encoder>
      <pattern>%date %-5level %class:%line - [%X{Username}] [%X{X-Transaction-ID}] %msg%n</pattern>
    </encoder>
  </appender>

  <appender name="WEBHOOK" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${log.dir}/webhook.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${log.dir}/webhook.log.%d{yyyy-MM-dd}</fileNamePattern>
      <maxHistory>7</maxHistory>
    </rollingPolicy>
    <encoder>
      <pattern>%date %-5level %class:%line - %msg%n</pattern>
    </encoder>
  </appender>


  <logger name="org.hibernate.SQL" level="debug" additivity="false">
    <appender-ref ref="API"/>
  </logger>

  <logger name="org.zohar" level="debug" additivity="false">
    <appender-ref ref="API"/>
  </logger>

  <logger name="id.co.thebodyshop.me" level="debug" additivity="false">
    <appender-ref ref="API"/>
  </logger>

  <logger name="id.co.thebodyshop.me.core.service.CatalogueService" level="debug" additivity="false">
    <appender-ref ref="WEBHOOK"/>
  </logger>

  <logger name="id.co.thebodyshop.me.cat.api.controller.WebhookCatalogueController" level="debug" additivity="false">
    <appender-ref ref="WEBHOOK"/>
  </logger>

  <!--
    info  -> logs request and response lines
    debug -> logs request and response lines and their respective headers
    trace -> logs request and response lines and their respective headers and bodies (if present)
  -->
  <logger name="okhttp3.logging" level="trace" additivity="false">
    <appender-ref ref="API"/>
  </logger>

  <root level="warn">
    <appender-ref ref="API"/>
  </root>

</configuration>