package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.PolicyDto;
import id.co.panindaiichilife.superapp.agent.api.filter.PolicyFilter;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalPolicyDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalPolicyResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PolicyService {

    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    private final PortalProvider portalProvider;

    /**
     * Get policy list for an agent (original method without pagination)
     *
     * @param username The username of the agent
     * @param filter   The filter parameters
     * @return List of policy DTOs
     */
    @CacheableWithTTL(cacheName = "policyListCache",
            key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.valueStatus) ? #filter.valueStatus : 'ALL') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.policyNumber) ? #filter.policyNumber : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.spajNumber) ? #filter.spajNumber : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.policyHolderName) ? #filter.policyHolderName : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.startDate) ? #filter.startDate : 'default') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.endDate) ? #filter.endDate : 'default')+ ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.withDownline) ? #filter.withDownline : 0)",
            ttl = 1600, db = 7)
    public List<PolicyDto> getPolicyList(String username, PolicyFilter filter) {
        // Get the agent information from the username
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // If agentCode is not provided in the filter, use the agent's code
        String agentCode = StringUtils.isBlank(filter.getAgentCode())
                ? agent.getAgentCode()
                : filter.getAgentCode();

        // Trim -D suffix from agentCode if present
        agentCode = AgentCodeUtil.trimDSuffix(agentCode);

        // Create the request DTO for the portal API
        PortalPolicyDto requestDto = new PortalPolicyDto();
        requestDto.setAgentCode(agentCode);

        // Set policy status (active by default)
        requestDto.setValueStatus(filter.getValueStatus() != null ? filter.getValueStatus().name() : "ALL");

        // Set twisting filter (ALL by default)
        requestDto.setTwist("ALL");

        // Set NLG status (ALL by default)
        requestDto.setNlg("ALL");

        // Set default values for other required fields
        int bitFilter = 0;
        String valString = "";

        if (StringUtils.isNotBlank(filter.getPolicyNumber())) {
            bitFilter = 1;
            valString = filter.getPolicyNumber();
        } else if (StringUtils.isNotBlank(filter.getSpajNumber())) {
            bitFilter = 2;
            valString = filter.getSpajNumber();
        } else if (StringUtils.isNotBlank(filter.getPolicyHolderName())) {
            bitFilter = 3;
            valString = filter.getPolicyHolderName();
        }

        requestDto.setBitFilter(bitFilter);
        requestDto.setValFilter(valString);
        requestDto.setPageIndex(1);
        requestDto.setPageSize(1000); // Large page size for original method
        requestDto.setCountRow(true);
        requestDto.setSortIndex(2);
        requestDto.setSortBy(0);
        requestDto.setBrCode("");
        requestDto.setChannel(agent.getDistributionCode().name());
        requestDto.setWithDownline(filter.getWithDownline() != null && filter.getWithDownline() == 1);
        requestDto.setMonthFrom(filter.getStartDate().getMonthValue());
        requestDto.setYearFrom(filter.getStartDate().getYear());
        requestDto.setMonthTo(filter.getEndDate().getMonthValue());
        requestDto.setYearTo(filter.getEndDate().getYear());

        // Call the portal API
        Call<PortalPolicyResponseDto> call = portalProvider.getPolicyList(requestDto);

        try {
            Response<PortalPolicyResponseDto> response = call.execute();
            if (response.isSuccessful()) {
                PortalPolicyResponseDto responseDto = response.body();
                if (responseDto != null && responseDto.getData() != null) {
                    // Convert the response data to PolicyDto objects
                    return responseDto.getData().stream()
                            .map(this::convertToPolicyDto)
                            .collect(Collectors.toList());
                }
            }
            // Return empty list if response is not successful or body is null
            return new ArrayList<>();
        } catch (IOException e) {
            log.error("Error occurred while retrieving policy list", e);
            throw new InternalServerErrorException("Error occurred while retrieving policy list");
        }
    }

    /**
     * Get policy list for an agent with pagination support
     *
     * @param username The username of the agent
     * @param filter   The filter parameters
     * @param pageable The pagination parameters
     * @return Page of policy DTOs
     */
    @CacheableWithTTL(cacheName = "policyListPageableCache",
            key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.valueStatus) ? #filter.valueStatus : 'ALL') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.policyNumber) ? #filter.policyNumber : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.spajNumber) ? #filter.spajNumber : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.policyHolderName) ? #filter.policyHolderName : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.startDate) ? #filter.startDate : 'default') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.endDate) ? #filter.endDate : 'default')+ ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.withDownline) ? #filter.withDownline : 0) + ':' + " +
                    "#pageable.pageNumber + ':' + #pageable.pageSize",
            ttl = 1600, db = 7)
    public Page<PolicyDto> getPolicyListPageable(String username, PolicyFilter filter, Pageable pageable) {
        // Get the agent information from the username
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // If agentCode is not provided in the filter, use the agent's code
        String agentCode = StringUtils.isBlank(filter.getAgentCode())
                ? agent.getAgentCode()
                : filter.getAgentCode();

        // Trim -D suffix from agentCode if present
        agentCode = AgentCodeUtil.trimDSuffix(agentCode);

        // Create the request DTO for the portal API
        PortalPolicyDto requestDto = new PortalPolicyDto();
        requestDto.setAgentCode(agentCode);

        // Set policy status (active by default)
        requestDto.setValueStatus(filter.getValueStatus() != null ? filter.getValueStatus().name() : "ALL");

        // Set twisting filter (ALL by default)
        requestDto.setTwist("ALL");

        // Set NLG status (ALL by default)
        requestDto.setNlg("ALL");

        // Set default values for other required fields
        int bitFilter = 0;
        String valString = "";

        if (StringUtils.isNotBlank(filter.getPolicyNumber())) {
            bitFilter = 1;
            valString = filter.getPolicyNumber();
        } else if (StringUtils.isNotBlank(filter.getSpajNumber())) {
            bitFilter = 2;
            valString = filter.getSpajNumber();
        } else if (StringUtils.isNotBlank(filter.getPolicyHolderName())) {
            bitFilter = 3;
            valString = filter.getPolicyHolderName();
        }

        requestDto.setBitFilter(bitFilter);
        requestDto.setValFilter(valString);
        requestDto.setPageIndex(pageable.getPageNumber() + 1); // Portal API uses 1-based indexing
        requestDto.setPageSize(pageable.getPageSize());
        requestDto.setCountRow(true);
        requestDto.setSortIndex(2);
        requestDto.setSortBy(0);
        requestDto.setBrCode("");
        requestDto.setChannel(agent.getDistributionCode().name());
        requestDto.setWithDownline(filter.getWithDownline() != null && filter.getWithDownline() == 1);
        requestDto.setMonthFrom(filter.getStartDate().getMonthValue());
        requestDto.setYearFrom(filter.getStartDate().getYear());
        requestDto.setMonthTo(filter.getEndDate().getMonthValue());
        requestDto.setYearTo(filter.getEndDate().getYear());

        // Call the portal API
        Call<PortalPolicyResponseDto> call = portalProvider.getPolicyList(requestDto);

        try {
            Response<PortalPolicyResponseDto> response = call.execute();
            if (response.isSuccessful()) {
                PortalPolicyResponseDto responseDto = response.body();
                if (responseDto != null && responseDto.getData() != null) {
                    // Convert the response data to PolicyDto objects
                    List<PolicyDto> policyDtos = responseDto.getData().stream()
                            .map(this::convertToPolicyDto)
                            .collect(Collectors.toList());

                    // Create Page object with total count from portal response
                    return new PageImpl<>(policyDtos, pageable, responseDto.getTotalData());
                }
            }
            // Return empty page if response is not successful or body is null
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        } catch (IOException e) {
            log.error("Error occurred while retrieving policy list", e);
            throw new InternalServerErrorException("Error occurred while retrieving policy list");
        }
    }

    /**
     * Convert PortalPolicyResponseDto.PolicyDataDto to PolicyDto
     */
    private PolicyDto convertToPolicyDto(PortalPolicyResponseDto.PolicyDataDto policyData) {
        PolicyDto policyDto = new PolicyDto();
        BeanUtils.copyProperties(policyData, policyDto);
        return policyDto;
    }
}
