package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;

@Entity
@Table(name = "trx_terminations")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@SQLDelete(sql = "UPDATE trx_terminations SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class TrxTermination extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "trx_terminations_id_seq")
    @SequenceGenerator(name = "trx_terminations_id_seq", sequenceName = "trx_terminations_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    private String reason;

    @Audited
    @OneToOne(targetEntity = TrxApprovalHeader.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "trx_approval_header_id")
    private TrxApprovalHeader approvalHeader;

    @Audited
    @Enumerated(EnumType.STRING)
    private TrxStatus status;

    @Audited
    @Enumerated(EnumType.STRING)
    private TrxType trxType;

    @Audited
    @Enumerated(EnumType.STRING)
    private ApprovalStatus approvalStatus;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requester_id", nullable = false)
    private User requester;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_id", nullable = false)
    private User target;

    @Audited
    @OneToOne(targetEntity = TrxPolicyTransfer.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "trx_policy_transfer_id")
    private TrxPolicyTransfer trxPolicyTransfer;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

    @Audited
    private Instant effectiveDate;

    @Audited
    private Instant lastActionAt;
}
