package id.co.panindaiichilife.superapp.agent.api.filter;

import id.co.panindaiichilife.superapp.agent.enums.ProductivityGroupBy;
import id.co.panindaiichilife.superapp.agent.enums.ProductivityRecruitmentType;
import id.co.panindaiichilife.superapp.agent.enums.ProductivityScope;
import lombok.Data;

import java.time.LocalDate;

/**
 * Filter for productivity recruitment data
 */
@Data
public class ProductivityRecruitmentFilter {

    /**
     * Filter by month (1-12). If specified, returns 5 months of data (current month + 4 previous months)
     */
    private Integer month;

    /**
     * Filter by year. If null, defaults to current year. Returns 5 years of data (current year + 4 previous years)
     */
    private Integer year;

    /**
     * Filter by recruitment type:
     * - REKRUT_BERKODE_AGEN: Approved recruitments with agent code but license not ACTIVE
     * - REKRUT_BARU_BERLISENSI: Approved recruitments with agent code and license ACTIVE
     * - null: Returns both types
     */
    private ProductivityRecruitmentType recruitmentType;

    /**
     * Filter scope:
     * - SELF: Only current user's recruitments
     * - TEAM: Current user and their team's recruitments
     * - null: Defaults to SELF
     */
    private ProductivityScope scope;

    /**
     * Start date for custom date range filtering
     */
    private LocalDate startDate;

    /**
     * End date for custom date range filtering
     */
    private LocalDate endDate;

    /**
     * Group data by:
     * - MONTHLY: Group by month
     * - YEARLY: Group by year
     * - null: Defaults to MONTHLY
     */
    private ProductivityGroupBy groupBy;
}
