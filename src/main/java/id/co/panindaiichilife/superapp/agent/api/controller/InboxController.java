package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.InboxCountByTrxTypeDto;
import id.co.panindaiichilife.superapp.agent.api.dto.InboxDto;
import id.co.panindaiichilife.superapp.agent.api.filter.InboxFilter;
import id.co.panindaiichilife.superapp.agent.api.form.BulkInboxForm;
import id.co.panindaiichilife.superapp.agent.service.InboxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;

@RestController("inboxController")
@RequestMapping("/api/inbox")
@Tag(name = "Inbox", description = "API Inbox")
@Slf4j
@RequiredArgsConstructor
public class InboxController {

    private final InboxService inboxService;

    @Operation(summary = "List current user inbox")
    @GetMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'view')")
    public Page<InboxDto> index(Principal principal, @ParameterObject @ModelAttribute("filter") InboxFilter filter,
                                @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        filter.setUser(principal.getName());
        return inboxService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific inbox")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'view')")
    public InboxDto view(Principal principal, @PathVariable long id) {
        return inboxService.findOne(principal.getName(), id);
    }

    @Operation(summary = "Read inbox")
    @PostMapping(value = "read/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'read')")
    public void read(Principal principal, @PathVariable long id) {
        inboxService.read(principal.getName(), id);
    }

    @Operation(summary = "Archive inbox")
    @PostMapping(value = "archive/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'archive')")
    public void archive(Principal principal, @PathVariable long id) {
        inboxService.archive(principal.getName(), id);
    }

    @Operation(summary = "Soft Delete inbox")
    @PostMapping(value = "soft-delete/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'delete')")
    public void softDelete(Principal principal, @PathVariable long id) {
        inboxService.softDelete(principal.getName(), id);
    }

    @Operation(summary = "Hard Delete inbox")
    @DeleteMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'delete')")
    public void delete(Principal principal, @PathVariable long id) {
        inboxService.hardDelete(principal.getName(), id);
    }

    @Operation(summary = "Bulk read inbox")
    @PostMapping(value = "bulk/read")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'read')")
    public void bulkRead(Principal principal, @Valid @RequestBody BulkInboxForm form) {
        inboxService.bulkRead(principal.getName(), form);
    }

    @Operation(summary = "Bulk archive inbox")
    @PostMapping(value = "bulk/archive")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'archive')")
    public void bulkArchive(Principal principal, @Valid @RequestBody BulkInboxForm form) {
        inboxService.bulkArchive(principal.getName(), form);
    }

    @Operation(summary = "Bulk soft delete inbox")
    @PostMapping(value = "bulk/soft-delete")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'delete')")
    public void bulkSoftDelete(Principal principal, @Valid @RequestBody BulkInboxForm form) {
        inboxService.bulkSoftDelete(principal.getName(), form);
    }

    @Operation(summary = "Bulk hard delete inbox")
    @DeleteMapping(value = "bulk")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'delete')")
    public void bulkHardDelete(Principal principal, @Valid @RequestBody BulkInboxForm form) {
        inboxService.bulkHardDelete(principal.getName(), form);
    }

    @Operation(summary = "Get unread inbox count by transaction type")
    @GetMapping(value = "count/unread-by-trx-type")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Inbox', 'view')")
    public List<InboxCountByTrxTypeDto> getUnreadCountByTrxType(Principal principal) {
        return inboxService.getUnreadCountByTrxType(principal.getName());
    }
}
