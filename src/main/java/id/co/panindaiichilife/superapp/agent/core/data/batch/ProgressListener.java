package id.co.panindaiichilife.superapp.agent.core.data.batch;

import id.co.panindaiichilife.superapp.agent.core.view.Progress;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ChunkListener;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.util.function.Supplier;

@Slf4j
public class ProgressListener
        implements JobExecutionListener, ChunkListener {

    private String topic;

    private int total;

    private long totalStep;

    private long currentStep;

    private String stepName;

    private long sleepMillis;

    private Supplier<Integer> totalSupplier;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private SimpMessagingTemplate simp;

    public ProgressListener(String resourceUri) {
        this(resourceUri, 50);
    }

    public ProgressListener(String resourceUri, long sleepMillis) {
        this(new CsvItemCounter(resourceUri), sleepMillis);
    }

    public ProgressListener(Supplier<Integer> totalSupplier) {
        this(totalSupplier, 50);
    }

    public ProgressListener(Supplier<Integer> totalSupplier, long sleepMillis) {
        this.totalSupplier = totalSupplier;
        this.sleepMillis = sleepMillis;
    }

    @Override
    public void beforeJob(JobExecution jobExecution) {
        topic = "/topic/monitoring/job/" + jobExecution.getId();
        Long totalStepParam = jobExecution.getJobParameters().getLong("totalStep");
        totalStep = totalStepParam != null ? totalStepParam : 1; // Default to 1 if null
        if (totalStep == 0) totalStep = 1;
        currentStep = 0;

        total = totalSupplier.get();

        if (total < 1000) {
            //if size is too small, do initial sleep
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
                log.debug("Thread interrupted during initial sleep");
            }
        }
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        try {
            Progress payload = new Progress();
            payload.setFinished(true);

            simp.convertAndSend(topic, payload);
        } catch (Exception ex) {
            log.warn("Unable to send finished signal");
        }
    }

    @Override
    public void beforeChunk(ChunkContext context) {
        // No pre-chunk processing required for progress monitoring
        // Progress updates are handled in afterChunk method
    }

    @Override
    public void afterChunk(ChunkContext context) {
        try {
            if (total > 0) {
                long itemCount = context.getStepContext().getStepExecution().getReadCount();
                log.trace("Item read: {}/{}", itemCount, total);
                String currentStepName = context.getStepContext().getStepExecution().getStepName();
                if (!currentStepName.equals(stepName)) {
                    stepName = currentStepName;
                    currentStep = currentStep + 1;
                }

                Progress payload = new Progress();
                payload.setCurrent(itemCount);
                payload.setTotal(total);
                payload.setStepName(stepName);
                payload.setTotalStep(totalStep);
                payload.setCurrentStep(currentStep);
                simp.convertAndSend(topic, payload);
            }
        } catch (Exception ex) {
            log.warn("Unable to retrieve item count");
        }

        if (sleepMillis > 0) {
            try {
                Thread.sleep(sleepMillis);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
                log.debug("Thread interrupted during progress sleep");
            }
        }
    }

    @Override
    public void afterChunkError(ChunkContext context) {
        // No specific error handling required for progress monitoring
        // Error handling is managed by the batch framework
    }

    // @Override
    // public void beforeStep(StepExecution stepExecution) {
    //   currentStepName = stepExecution.getStepName();
    //   currentStep = currentStep++;
    // }

    // @Override
    // @Nullable
    // public ExitStatus afterStep(StepExecution stepExecution) {
    //   return stepExecution.getExitStatus();
    // }


}