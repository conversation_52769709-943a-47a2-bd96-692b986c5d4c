package id.co.panindaiichilife.superapp.agent.service.agency.validator;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationBankAccountDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationBankAccountDtoBuilder;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationBankAccountResponseDto;
import id.co.panindaiichilife.superapp.agent.enums.ValidationBankAccountStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;

/**
 * Validator for checking bank account data
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BankAccountValidator extends AbstractRecruitmentValidator {

    private final PortalProvider portalProvider;
    private final GlobalConfigService globalConfigService;

    @Override
    public boolean canValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        return entity.getBank() != null && StringUtils.isNotBlank(entity.getBankAccountNumber());
    }

    @Override
    protected TrxRecruitment doValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        PortalValidationBankAccountResponseDto response = validateBankAccountData(entity);

        if (response != null) {
            // Determine bank account validation status based on response
            if ("200".equals(response.getStatusCode()) && "SUCCESS".equals(response.getCode())) {
                // Check if the account name matches the candidate's name
                String accountName = response.getData().getAccountName();
                String candidateName = entity.getFullName();

                if (StringUtils.isNotBlank(accountName) && StringUtils.isNotBlank(candidateName)) {
                    // Store the account name from the validation
                    entity.setBankAccountName(accountName);

                    // Simple name matching logic - could be enhanced
                    if (accountName.equalsIgnoreCase(candidateName) ||
                            candidateName.contains(accountName) ||
                            accountName.contains(candidateName)) {
                        entity.setValidationBankAccountStatus(ValidationBankAccountStatus.PASS);
                        log.info("Bank account validation passed for account {}: {} matches {}",
                                entity.getBankAccountNumber(), accountName, candidateName);
                    } else {
                        entity.setValidationBankAccountStatus(ValidationBankAccountStatus.POSTPONED);
                        log.info("Bank account validation postponed for account {}: {} does not match {}",
                                entity.getBankAccountNumber(), accountName, candidateName);
                    }
                } else {
                    entity.setValidationBankAccountStatus(ValidationBankAccountStatus.POSTPONED);
                    log.info("Bank account validation postponed for account {}: missing account name or candidate name",
                            entity.getBankAccountNumber());
                }
            } else {
                entity.setValidationBankAccountStatus(ValidationBankAccountStatus.POSTPONED);
                log.info("Bank account validation postponed for account {}: {}",
                        entity.getBankAccountNumber(), response.getMessage());
            }
        } else {
            log.warn("Bank account validation skipped or failed for account {}", entity.getBankAccountNumber());
        }

        return entity;
    }

    @Override
    public String getValidatorName() {
        return "Bank Account Validator";
    }

    /**
     * Validates bank account data for a recruitment candidate
     *
     * @param trxRecruitment The recruitment entity containing bank data
     * @return The bank account validation response from the portal
     */
    private PortalValidationBankAccountResponseDto validateBankAccountData(TrxRecruitment trxRecruitment) {
        try {
            String user = globalConfigService.getGlobalConfig("advance-ai.user", "UAT-QX");
            boolean isActive = globalConfigService.getGlobalConfig("advance-ai.active", "false").equals("true");

            if (!isActive) {
                log.error("Failed to validate bank account data: {}", "Advance AI is not active");
                // Return an empty response to avoid blocking the recruitment process
                PortalValidationBankAccountResponseDto emptyResponse = new PortalValidationBankAccountResponseDto();
                emptyResponse.setStatusCode("500");
                emptyResponse.setCode("ERROR");
                emptyResponse.setMessage("Advance AI is not active");
                return emptyResponse;
            }

            // Create a transaction number using a timestamp and random UUID
            Long trxNum = trxRecruitment.getId();

            // Get bank code from the recruitment entity
            String bankCode = trxRecruitment.getBank() != null ? trxRecruitment.getBank().getBankAdvanceAiCode() : null;

            if (null == bankCode) {
                PortalValidationBankAccountResponseDto emptyResponse = new PortalValidationBankAccountResponseDto();
                emptyResponse.setStatusCode("500");
                emptyResponse.setCode("ERROR");
                emptyResponse.setMessage("Bank advance ai code is null");
                return emptyResponse;
            }

            // Create the bank account validation request using the builder
            PortalValidationBankAccountDto request = new PortalValidationBankAccountDtoBuilder()
                    .withTrxNum(trxNum.toString())
                    .withBankCode(bankCode)
                    .withBankAccount(trxRecruitment.getBankAccountNumber())
                    .withCaller(user)
                    .build();

            // Call the portal API to validate bank account data
            Call<PortalValidationBankAccountResponseDto> call = portalProvider.validateBankAccount(request);
            Response<PortalValidationBankAccountResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                return response.body();
            } else {
                log.error("Failed to validate bank account data: {}", response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                // Return an empty response to avoid blocking the recruitment process
                PortalValidationBankAccountResponseDto emptyResponse = new PortalValidationBankAccountResponseDto();
                emptyResponse.setStatusCode("500");
                emptyResponse.setCode("ERROR");
                emptyResponse.setMessage("Failed to validate bank account data");
                return emptyResponse;
            }
        } catch (Exception e) {
            log.error("Error validating bank account data", e);
            // Return an empty response to avoid blocking the recruitment process
            PortalValidationBankAccountResponseDto emptyResponse = new PortalValidationBankAccountResponseDto();
            emptyResponse.setStatusCode("500");
            emptyResponse.setCode("ERROR");
            emptyResponse.setMessage("Error validating bank account data: " + e.getMessage());
            return emptyResponse;
        }
    }
}
