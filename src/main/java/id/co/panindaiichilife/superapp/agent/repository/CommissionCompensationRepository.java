package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.CommissionCompensation;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface CommissionCompensationRepository extends BaseRepository<CommissionCompensation, Long> {

    // Find by agent code
    CommissionCompensation findByAgentCode(String agentCode);

    // Find by leader code
    List<CommissionCompensation> findByLeaderCode(String leaderCode);

    // Find by agent type
    List<CommissionCompensation> findByType(String type);

    // Find by branch code
    List<CommissionCompensation> findByBranchCode(String branchCode);

    // Find by year and month
    List<CommissionCompensation> findByYearAndMonth(Integer year, Integer month);

    List<CommissionCompensation> findByAgentCodeAndYearAndMonth(String agentCode, int year, int month);

    @Query("SELECT DISTINCT c.periode FROM CommissionCompensation c WHERE c.agentCode = :agentCode AND c.year = :year AND c.month = :month")
    String findPeriodeByAgentAndYearAndMonth(@Param("agentCode") String agentCode,
                                             @Param("year") Integer year,
                                             @Param("month") Integer month);

    @Query("SELECT SUM(c.amount) FROM CommissionCompensation c WHERE c.agentCode = :agentCode AND c.year = :year AND c.month = :month AND c.type = :type")
    Double sumAmountByPeriodeAndType(@Param("agentCode") String agentCode,
                                     @Param("year") Integer year,
                                     @Param("month") Integer month,
                                     @Param("type") String type);

    @Query("SELECT c.policyYear as policyYear, SUM(c.amount) as amount FROM CommissionCompensation c " +
            "WHERE c.agentCode = :agentCode AND c.year = :year AND c.month = :month " +
            "GROUP BY c.policyYear ORDER BY c.policyYear")
    List<Object[]> findAmountByAgentAndPolicyYear(@Param("agentCode") String agentCode,
                                                  @Param("year") Integer year,
                                                  @Param("month") Integer month);

    @Query("SELECT c.policyYear as policyYear, SUM(c.amount) as amount FROM CommissionCompensation c " +
            "WHERE c.agentCode = :agentCode AND c.year = :year AND c.month = :month AND c.type = :type " +
            "GROUP BY c.policyYear ORDER BY c.policyYear")
    List<Object[]> findAmountByAgentAndPolicyYearAndType(@Param("agentCode") String agentCode,
                                                         @Param("year") Integer year,
                                                         @Param("month") Integer month,
                                                         @Param("type") String type);

    @Modifying
    @Transactional
    @Query("DELETE FROM CommissionCompensation c WHERE c.year = :year AND c.month = :month")
    void deleteByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

    long countByYearAndMonth(Integer year, Integer month);

}
