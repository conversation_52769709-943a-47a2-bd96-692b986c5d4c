package id.co.panindaiichilife.superapp.agent.api.controller.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.agency.EmailVerificationResponseDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.ProductivityRecruitmentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRecruitmentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccountDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ProductivityRecruitmentFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.TrxRecruitmentFilter;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentInterviewForm;
import id.co.panindaiichilife.superapp.agent.api.validation.TrxRecruitmentValidator;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.service.EmailVerificationService;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRecruitmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;

@RestController("agencyRecruitmentController")
@RequestMapping("/api/agency/recruitment")
@Tag(name = "Agency - Recruitment", description = "API Agency Recruitment")
@Slf4j
@RequiredArgsConstructor
public class TrxRecruitmentController {

    private final TrxRecruitmentService trxRecruitmentService;

    private final TrxRecruitmentValidator profileTrxRecruitmentValidator;

    private final UserService userService;

    private final EmailVerificationService emailVerificationService;

    @Operation(summary = "List Recruitment")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public Page<TrxRecruitmentDto> findAll(Principal principal, @ParameterObject @ModelAttribute("filter") TrxRecruitmentFilter filter,
                                           @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        AccountDto user = userService.findByUsername(principal.getName());
        filter.setRecruiter(user.getId());

        return trxRecruitmentService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific recruitment by uuid")
    @GetMapping(value = "{uuid}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public TrxRecruitmentDto view(@PathVariable String uuid) {
        return trxRecruitmentService.findByUuid(uuid);
    }

    @Operation(summary = "View specific recruitment by id")
    @GetMapping(value = "detail/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public TrxRecruitmentDto findById(@PathVariable Long id) {
        return trxRecruitmentService.findOne(id);
    }

    @Operation(summary = "Draft Recruitment")
    @PostMapping(value = "draft")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'draft')")
    public TrxRecruitmentDto draft(Principal principal, @Valid @RequestBody TrxRecruitmentForm profileTrxRecruitmentForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        profileTrxRecruitmentValidator.validateForDraft(profileTrxRecruitmentForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        TrxRecruitment trxRecruitment = trxRecruitmentService.saveDraft(principal.getName(), profileTrxRecruitmentForm);
        return BaseDto.of(TrxRecruitmentDto.class, trxRecruitment);
    }

    @Operation(summary = "Submit Recruitment")
    @PostMapping(value = "submit")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'submit')")
    public void submit(Principal principal, @Valid @RequestBody TrxRecruitmentForm profileTrxRecruitmentForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        profileTrxRecruitmentValidator.validate(profileTrxRecruitmentForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        trxRecruitmentService.submit(principal.getName(), profileTrxRecruitmentForm);
    }

    @Operation(summary = "Revise Recruitment")
    @PatchMapping(value = "revise/{uuid}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'revise')")
    public void reviseUpdate(@PathVariable String uuid, Principal principal, @Valid @RequestBody TrxRecruitmentForm profileTrxRecruitmentForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        profileTrxRecruitmentValidator.validateForRevise(profileTrxRecruitmentForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        // Find recruitment by UUID first
        TrxRecruitmentDto recruitment = trxRecruitmentService.findByUuid(uuid);
        trxRecruitmentService.revise(principal.getName(), recruitment.getId(), profileTrxRecruitmentForm);
    }

    @Operation(summary = "Cancel Recruitment")
    @PostMapping(value = "cancel/{uuid}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'cancel')")
    public void cancelRequestUpdate(Principal principal, @PathVariable String uuid) {
        // Find recruitment by UUID first
        TrxRecruitmentDto recruitment = trxRecruitmentService.findByUuid(uuid);
        trxRecruitmentService.cancel(principal.getName(), recruitment.getId());
    }

    @Operation(summary = "Upload Documents")
    @PostMapping(value = "upload/{path}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'upload')")
    public FileinputResponse uploadDocument(@PathVariable String path, Principal principal, @RequestParam("file") MultipartFile file) {
        return trxRecruitmentService.upload(principal.getName(), path, file);
    }

    @Operation(summary = "Submit Interview Result")
    @PostMapping(value = "{uuid}/interview")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'interview')")
    public TrxRecruitmentDto submitInterviewResult(
            @PathVariable String uuid,
            Principal principal,
            @Valid @RequestBody TrxRecruitmentInterviewForm interviewForm) {
        // Find recruitment by UUID first
        TrxRecruitmentDto recruitment = trxRecruitmentService.findByUuid(uuid);
        return trxRecruitmentService.submitInterviewResult(principal.getName(), recruitment.getId(), interviewForm);
    }

    @Operation(summary = "Send Email Verification Link")
    @PostMapping(value = "{uuid}/send-verification-email")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public ResponseEntity<EmailVerificationResponseDto> sendVerificationEmail(
            @PathVariable String uuid,
            Principal principal) {
        try {
            String email = emailVerificationService.sendVerificationEmailByUuid(uuid);
            if (null != email) {
                return ResponseEntity.ok(EmailVerificationResponseDto.success(
                        "Verification email sent successfully", email, uuid));
            } else {
                return ResponseEntity.ok(EmailVerificationResponseDto.error(
                        "Failed to send verification email. Please try again later."));
            }
        } catch (BadRequestException e) {
            return ResponseEntity.ok(EmailVerificationResponseDto.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error sending verification email", e);
            return ResponseEntity.ok(EmailVerificationResponseDto.error(
                    "An unexpected error occurred. Please try again later."));
        }
    }

    @Operation(summary = "Check Email Verified by UUID")
    @GetMapping(value = "check-email-verified")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public ResponseEntity<Boolean> verifyEmail(@RequestParam String uuid) {
        return ResponseEntity.ok(emailVerificationService.checkEmailVerified(uuid));
    }

    @Operation(summary = "Expire stale recruitments")
    @PutMapping("/expire")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'update')")
    public void expireRecruitments() {
        trxRecruitmentService.expireStaleRecruitments();
    }

    @Operation(summary = "Get productivity recruitment data for charts and analysis")
    @GetMapping("/productivity")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public ProductivityRecruitmentDto getProductivityData(
            Principal principal,
            @ParameterObject @ModelAttribute("filter") ProductivityRecruitmentFilter filter) {
        return trxRecruitmentService.getProductivityRecruitmentData(principal.getName(), filter);
    }
}
