package id.co.panindaiichilife.superapp.agent.api.filter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import id.co.panindaiichilife.superapp.agent.core.data.filter.FieldFilter;
import id.co.panindaiichilife.superapp.agent.core.data.filter.FilterParam;
import id.co.panindaiichilife.superapp.agent.core.view.FilterMode;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalDetail;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
@Slf4j
public class HistoryApprovalFilter extends FieldFilter<TrxApprovalDetail> {

    @FilterParam(value = "approvalHeader.trxType", modes = FilterMode.FK)
    private List<TrxType> trxType;

    @FilterParam
    private ApprovalStatus approvalStatus;

    @Parameter(hidden = true)
    @FilterParam(value = "actionBy.username", modes = FilterMode.FK)
    private String user;
}
