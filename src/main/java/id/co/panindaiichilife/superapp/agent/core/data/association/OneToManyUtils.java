package id.co.panindaiichilife.superapp.agent.core.data.association;

import org.apache.commons.beanutils.PropertyUtils;
import org.springframework.beans.BeanUtils;

import jakarta.persistence.OneToMany;
import java.lang.reflect.Field;
import java.util.*;

public class OneToManyUtils {

  /**
   * Helper function to save <code>@OneToMany</code> relationship.
   *
   * @param form form object
   * @param model model object
   * @param field field in both form object and model object
   * @param targetClass type of each model child object
   * @param listener a listener that can be implemented to add additional logic for each saved
   * child
   */
  public static void save(Object form, Object model, String field, Class targetClass,
      AssociationListener listener) {
    save(form, model, field, field, targetClass, listener);
  }

  /**
   * Helper function to save <code>@OneToMany</code> relationship.
   *
   * @param form form object
   * @param model model object
   * @param formField source field in form object
   * @param modelField destination field in model object
   * @param targetClass type of each model child object
   * @param listener a listener that can be implemented to add additional logic for each associated
   * child
   */
  @SuppressWarnings("unchecked")
  public static void save(Object form, Object model, String formField, String modelField,
      Class targetClass, AssociationListener listener) {
    try {
      Collection assocs = (Collection) PropertyUtils.getProperty(model, modelField);
      Collection values = (Collection) PropertyUtils.getProperty(form, formField);

      String parentField = getParentField(model, modelField);
      assocs = initializeAssociationsIfNull(assocs);

      if (values == null) {
        assocs.clear();
      } else {
        removeMissingAssociations(assocs, values);
        processValues(assocs, values, targetClass, parentField, model, listener);
        PropertyUtils.setProperty(model, modelField, assocs);
      }
    } catch (ReflectiveOperationException ex) {
      throw new RuntimeException(ex);
    }
  }

  private static String getParentField(Object model, String modelField) throws ReflectiveOperationException {
    Field otmField = model.getClass().getDeclaredField(modelField);
    OneToMany otmAnnotation = otmField.getAnnotation(OneToMany.class);
    return otmAnnotation.mappedBy();
  }

  @SuppressWarnings("unchecked")
  private static Collection initializeAssociationsIfNull(Collection assocs) {
    return assocs == null ? new LinkedHashSet() : assocs;
  }

  @SuppressWarnings("unchecked")
  private static void removeMissingAssociations(Collection assocs, Collection values) throws ReflectiveOperationException {
    for (Iterator i = assocs.iterator(); i.hasNext(); ) {
      Object assoc = i.next();
      String assocIdField = AssociationUtils.getIdField(assoc);
      Object assocId = PropertyUtils.getProperty(assoc, assocIdField);

      if (!isValuePresent(values, assocId)) {
        i.remove();
      }
    }
  }

  private static boolean isValuePresent(Collection values, Object assocId) throws ReflectiveOperationException {
    for (Object value : values) {
      String valueIdField = AssociationUtils.getIdField(value);
      Object valueId = PropertyUtils.getProperty(value, valueIdField);
      if (assocId.equals(valueId)) {
        return true;
      }
    }
    return false;
  }

  @SuppressWarnings("unchecked")
  private static void processValues(Collection assocs, Collection values, Class targetClass,
      String parentField, Object model, AssociationListener listener) throws ReflectiveOperationException {
    for (Object value : values) {
      String valueIdField = AssociationUtils.getIdField(value);
      Object valueId = PropertyUtils.getProperty(value, valueIdField);

      Object target = findExistingTarget(assocs, valueId);
      if (target == null) {
        target = createNewTarget(targetClass, parentField, model, assocs);
      }

      BeanUtils.copyProperties(value, target, valueIdField);

      if (listener != null) {
        listener.associate(target, value);
      }
    }
  }

  private static Object findExistingTarget(Collection assocs, Object valueId) throws ReflectiveOperationException {
    if (valueId != null) {
      for (Object assoc : assocs) {
        String assocIdField = AssociationUtils.getIdField(assoc);
        Object assocId = PropertyUtils.getProperty(assoc, assocIdField);
        if (assocId.equals(valueId)) {
          return assoc;
        }
      }
    }
    return null;
  }

  @SuppressWarnings("unchecked")
  private static Object createNewTarget(Class targetClass, String parentField, Object model,
      Collection assocs) throws ReflectiveOperationException {
    Object target = targetClass.newInstance();
    String differentiatorField = AssociationUtils.getDifferentiatorField(target);
    PropertyUtils.setProperty(target, differentiatorField, UUID.randomUUID().toString());
    PropertyUtils.setProperty(target, parentField, model);
    assocs.add(target);
    return target;
  }

  /**
   * Helper function to populate <code>@OneToMany</code> relationship.
   *
   * @param model model object
   * @param form form object
   * @param field field in both model object and form object
   * @param targetClass type of each form child object
   * @param listener a listener that can be implemented to add additional logic for each associated
   * child
   */
  public static void populate(Object model, Object form, String field, Class targetClass,
      AssociationListener listener) {
    populate(model, form, field, field, targetClass, listener);
  }

  /**
   * Helper function to populate <code>@OneToMany</code> relationship.
   *
   * @param model model object
   * @param form form object
   * @param modelField destination field in model object
   * @param formField source field in form object
   * @param targetClass type of each form child object
   * @param listener a listener that can be implemented to add additional logic for each associated
   * child
   */
  @SuppressWarnings("unchecked")
  public static void populate(Object model, Object form, String modelField, String formField,
      Class targetClass, AssociationListener listener) {
    try {
      Collection assocs = (Collection) PropertyUtils.getProperty(model, modelField);
      Collection values = new ArrayList();

      if (assocs != null) {
        for (Object assoc : assocs) {
          Object value = targetClass.newInstance();
          BeanUtils.copyProperties(assoc, value);

          if (listener != null) {
            listener.associate(assoc, value);
          }

          values.add(value);
        }
      }

      PropertyUtils.setProperty(form, formField, values);
    } catch (ReflectiveOperationException ex) {
      throw new RuntimeException(ex);
    }
  }
}
