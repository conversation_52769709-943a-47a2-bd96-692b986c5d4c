package id.co.panindaiichilife.superapp.agent.core.security;

import id.co.panindaiichilife.superapp.agent.repository.AccessRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Slf4j
@Component
@RequiredArgsConstructor
public class AccessEvaluator implements PermissionEvaluator {

    private final AccessRepository accessRepository;

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject,
                                 Object permission) {
        log.trace("auth = {}, domain = {}, action = {}",
                authentication, targetDomainObject, permission);

        if (authentication == null || targetDomainObject == null || permission == null
                || !(targetDomainObject instanceof String) || !(permission instanceof String)) {
            return false;
        }

        return accessRepository.hasAccess(
                authentication.getName(),
                (String) targetDomainObject,
                (String) permission);
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId,
                                 String targetType, Object permission) {
        log.trace("auth = {}, domain = {}, action = {}",
                authentication, targetType, permission);

        if (authentication == null || targetType == null || permission == null
                || !(permission instanceof String)) {
            return false;
        }

        return accessRepository.hasAccess(
                authentication.getName(),
                targetType,
                (String) permission);
    }
}
