package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.model.Role;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;


@EqualsAndHashCode(callSuper = true)
@Data
public class RoleDto extends BaseDto<Role> {

    private Long id;

    private String code;

    private String name;

    private Set<AccessDto> accesses;

    @Override
    public void copy(Role data) {
        super.copy(data);
        accesses = BaseDto.of(AccessDto.class, data.getAccesses());
    }
}
