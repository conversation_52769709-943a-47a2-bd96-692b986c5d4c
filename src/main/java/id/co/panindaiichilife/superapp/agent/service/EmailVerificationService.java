package id.co.panindaiichilife.superapp.agent.service;

import com.sendgrid.helpers.mail.Mail;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.MailService;
import id.co.panindaiichilife.superapp.agent.core.support.HttpUtils;
import id.co.panindaiichilife.superapp.agent.model.EmailVerificationToken;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.repository.EmailVerificationTokenRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRecruitmentRepository;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class EmailVerificationService {

    // Token expiration time in minutes
    private static final int TOKEN_EXPIRATION_MINUTES = 30;
    private final MailService mailService;
    private final TrxRecruitmentRepository trxRecruitmentRepository;
    private final EmailVerificationTokenRepository emailVerificationTokenRepository;
    private final GlobalConfigService globalConfigService;
    @Value("${mail.base-url:}")
    private String baseUrl;

    /**
     * Generate a verification token for a recruitment
     *
     * @param recruitment The recruitment to generate a token for
     * @return The generated token
     */
    @Transactional
    public EmailVerificationToken generateVerificationToken(TrxRecruitment recruitment) {
        // Create a new token
        EmailVerificationToken token = new EmailVerificationToken();
        token.setToken(UUID.randomUUID().toString());
        token.setRecruitment(recruitment);
        token.setExpiryDate(LocalDateTime.now().plusMinutes(TOKEN_EXPIRATION_MINUTES));
        token.setUsed(false);

        return emailVerificationTokenRepository.save(token);
    }

    /**
     * Send a verification email to a candidate (public endpoint)
     *
     * @param recruitmentId The ID of the recruitment
     * @return True if the email was sent successfully, false otherwise
     */
    @Transactional
    public String sendVerificationEmail(Long recruitmentId) {
        return sendVerificationEmail(recruitmentId, false);
    }

    /**
     * Send a verification email to a candidate with option to use agent endpoint using UUID
     *
     * @param uuid The UUID of the recruitment
     * @return True if the email was sent successfully, false otherwise
     */
    @Transactional
    public String sendVerificationEmailByUuid(String uuid) {
        TrxRecruitment recruitment = trxRecruitmentRepository.findByUuid(uuid)
                .orElseThrow(() -> new NotFoundException("Recruitment not found with UUID: " + uuid));

        // Check if email is already verified
        if (Boolean.TRUE.equals(recruitment.getIsEmailVerified())) {
            throw new BadRequestException("Email is already verified");
        }

        // Check if email is provided
        if (StringUtils.isBlank(recruitment.getEmail())) {
            throw new BadRequestException("Recruitment has no email address");
        }

        // Generate a new token
        EmailVerificationToken verificationToken = generateVerificationToken(recruitment);

        // Send the verification email
        return sendVerificationEmail(recruitment, verificationToken);
    }

    /**
     * Send a verification email to a candidate with option to use agent endpoint
     *
     * @param recruitmentId   The ID of the recruitment
     * @param isAgentEndpoint Whether to use the agent endpoint (true) or public endpoint (false)
     * @return True if the email was sent successfully, false otherwise
     */
    @Transactional
    public String sendVerificationEmail(Long recruitmentId, boolean isAgentEndpoint) {
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(recruitmentId)
                .orElseThrow(() -> new NotFoundException("Recruitment not found with ID: " + recruitmentId));

        // Check if email is already verified
        if (Boolean.TRUE.equals(recruitment.getIsEmailVerified())) {
            throw new BadRequestException("Email is already verified");
        }

        // Check if email is provided
        if (StringUtils.isBlank(recruitment.getEmail())) {
            throw new BadRequestException("Recruitment has no email address");
        }

        // Generate a new token
        EmailVerificationToken verificationToken = generateVerificationToken(recruitment);

        // Send the verification email
        return sendVerificationEmail(recruitment, verificationToken);
    }

    /**
     * Send a verification email to a candidate
     *
     * @param recruitment The recruitment
     * @param token       The verification token
     * @return True if the email was sent successfully, false otherwise
     */
    private String sendVerificationEmail(TrxRecruitment recruitment, EmailVerificationToken token) {
        if (StringUtils.isBlank(baseUrl)) {
            baseUrl = HttpUtils.getBaseUrl();
        }

        String verificationPath = "https://nrwvafwa4f.execute-api.ap-southeast-3.amazonaws.com/dev/agent/api/public/agency/recruitment/verify-email?token=";

        String verificationUrl = verificationPath + token.getToken();

        Map<String, Object> data = new HashMap<>();
        data.put("name", recruitment.getFullName());
        data.put("email", recruitment.getEmail());
        data.put("url", verificationUrl);
        data.put("expiryMinutes", TOKEN_EXPIRATION_MINUTES);

        try {
            String template = getEmailVerificationTemplate();
            Mail email = mailService.createEmail(template, data, recruitment.getEmail());
            mailService.sendEmail(email);
            return recruitment.getEmail();
        } catch (MessagingException ex) {
            log.error("Error sending verification email", ex);
            return null;
        }
    }

    /**
     * Get the email verification template ID
     *
     * @return The template ID
     */
    protected String getEmailVerificationTemplate() {
        return globalConfigService.getGlobalConfig("mail.recruitment.verify-email.template.id", "d-email-verification-template-id");
    }

    /**
     * Verify an email using a token
     *
     * @param token The token
     * @return The verified recruitment
     */
    @Transactional
    public TrxRecruitment verifyEmail(String token) {
        EmailVerificationToken verificationToken = emailVerificationTokenRepository.findByToken(token)
                .orElseThrow(() -> new BadRequestException("Invalid verification token"));

        // Check if token is already used
        if (Boolean.TRUE.equals(verificationToken.getUsed())) {
            throw new BadRequestException("Verification token has already been used");
        }

        // Check if token is expired
        if (verificationToken.isExpired()) {
            throw new BadRequestException("Verification token has expired. Please request a new one.");
        }

        // Get the recruitment
        TrxRecruitment recruitment = verificationToken.getRecruitment();

        // Mark the email as verified
        recruitment.setIsEmailVerified(true);
        recruitment.setEmailVerifiedDate(LocalDateTime.now());
        trxRecruitmentRepository.save(recruitment);

        // Mark the token as used
        verificationToken.setUsed(true);
        emailVerificationTokenRepository.save(verificationToken);

        return recruitment;
    }

    @Transactional
    public Boolean checkEmailVerified(String uuid) {
        // Get the recruitment
        TrxRecruitment recruitment = trxRecruitmentRepository.findByUuid(uuid).orElseThrow(() -> new NotFoundException("Recruitment not found with UUID: " + uuid));

        return recruitment.getIsEmailVerified();
    }
}
