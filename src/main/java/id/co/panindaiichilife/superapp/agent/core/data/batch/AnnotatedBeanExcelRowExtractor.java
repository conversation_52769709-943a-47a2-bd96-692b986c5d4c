package id.co.panindaiichilife.superapp.agent.core.data.batch;

import id.co.panindaiichilife.superapp.agent.core.support.AnnotatedBeanUtils;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.Hyperlink;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFHyperlink;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.NumberFormat;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.Temporal;
import java.util.*;


@Slf4j
public class AnnotatedBeanExcelRowExtractor<T> implements ExcelRowExtractor<T> {

    private Class<T> type;
    private List<Field> exportedFields;
    private Sheet sheet;
    private DataFormat dataFormat;
    private int rowIndex;
    private Map<String, CellStyle> cellStyleMap;

    public AnnotatedBeanExcelRowExtractor(Class<T> type, Sheet sheet) {
        this.type = type;
        this.sheet = sheet;
        rowIndex = 1; // Row index 0 is used for header, so we start at 1
        exportedFields = AnnotatedBeanUtils.getExportedFields(type);

        // Write header
        List<AnnotatedBeanUtils.ExportedHeader> headers = AnnotatedBeanUtils.getExportedFieldNames(type);
        Row row = sheet.createRow(0);

        for (int i = 0; i < headers.size(); i++) {
            Cell cell = row.createCell(i);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(headers.get(i).getValue());
            if (!headers.get(i).getComment().isEmpty()) {
                addComment(cell, "system", headers.get(i).getComment());
            }
        }
    }

    @Override
    public void extract(T item) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        Row row = sheet.createRow(rowIndex++);
        log.debug("Writing row at index: {}", rowIndex - 1);

        for (int i = 0; i < exportedFields.size(); i++) {
            Field field = exportedFields.get(i);
            Cell cell = row.createCell(i);

            log.debug("Writing field: {} with value: {}", field.getName(), PropertyUtils.getProperty(item, field.getName()));
            writeCell(field, cell, item);

            if (field.isAnnotationPresent(Hyperlink.class)) {
                XSSFHyperlink link = (XSSFHyperlink) sheet.getWorkbook().getCreationHelper()
                        .createHyperlink(HyperlinkType.URL);
                link.setAddress(cell.getStringCellValue());
                cell.setHyperlink(link);
            }
        }
    }

    private void writeCell(Field field, Cell cell, T item) {
        try {
            Object value = PropertyUtils.getProperty(item, field.getName());

            if (value == null) {
                // Do nothing
            } else if (value instanceof String) {
                cell.setCellValue((String) value);
            } else if (value instanceof Boolean) {
                cell.setCellValue((Boolean) value);
            } else if (value instanceof Number) {
                writeNumeric(field, cell, (Number) value);
            } else if (value instanceof Temporal) {
                writeTemporal(field, cell, (Temporal) value);
            } else if (value instanceof Date) {
                writeTemporal(field, cell, ((Date) value).toInstant());
            } else if (value instanceof Map) {
                // Handle nested Map (e.g., JSON)
                cell.setCellValue(value.toString()); // Convert Map to string
            } else {
                cell.setCellValue(value.toString());
            }
        } catch (ReflectiveOperationException ex) {
            ReflectionUtils.handleReflectionException(ex);
        }
    }

    private void writeNumeric(Field field, Cell cell, Number value) {
        String pattern;
        if (field.isAnnotationPresent(NumberFormat.class)) {
            NumberFormat numberFormat = field.getAnnotation(NumberFormat.class);
            pattern = numberFormat.pattern();
        } else if (value instanceof Double || value instanceof Float) {
            pattern = "#,##0.00";
        } else {
            pattern = "#,##0";
        }

        cell.setCellStyle(getStyle(pattern));
        cell.setCellValue(value.doubleValue());
    }

    private CellStyle getStyle(String pattern) {
        if (dataFormat == null) {
            dataFormat = sheet.getWorkbook().createDataFormat();
        }

        CellStyle cellStyle;
        if (cellStyleMap != null && cellStyleMap.containsKey(pattern)) {
            cellStyle = cellStyleMap.get(pattern);
        } else {
            if (cellStyleMap == null) {
                cellStyleMap = new HashMap<>();
            }

            cellStyle = sheet.getWorkbook().createCellStyle();
            cellStyleMap.put(pattern, cellStyle);
        }

        cellStyle.setDataFormat(dataFormat.getFormat(pattern));
        return cellStyle;
    }

    private void writeTemporal(Field field, Cell cell, Temporal value) {
        String pattern = null;

        if (field.isAnnotationPresent(DateTimeFormat.class)) {
            DateTimeFormat dateTimeFormat = field.getAnnotation(DateTimeFormat.class);
            pattern = dateTimeFormat.pattern();
        }

        Calendar cal = Calendar.getInstance();
        if (value instanceof LocalDate) {
            LocalDate v = (LocalDate) value;
            cal.set(v.getYear(), v.getMonthValue() - 1, v.getDayOfMonth(), 0, 0, 0);
            pattern = pattern == null ? "mmm d, yyyy" : pattern;
        } else if (value instanceof LocalTime) {
            LocalTime v = (LocalTime) value;
            cal.set(1970, Calendar.JANUARY, 1, v.getHour(), v.getMinute(), v.getSecond());
            pattern = pattern == null ? "hh:mm" : pattern;
        } else if (value instanceof LocalDateTime) {
            LocalDateTime v = (LocalDateTime) value;
            cal.set(v.getYear(), v.getMonthValue() - 1, v.getDayOfMonth(),
                    v.getHour(), v.getMinute(), v.getSecond());
            pattern = pattern == null ? "mmm d, yyyy hh:mm" : pattern;
        } else if (value instanceof Instant) {
            Instant v = (Instant) value;
            cal.setTimeInMillis(v.toEpochMilli());
            pattern = pattern == null ? "mmm d, yyyy hh:mm" : pattern;
        } else {
            throw new UnsupportedOperationException("Unsupported temporal type: " + field.getType());
        }

        cell.setCellStyle(getStyle(pattern));
        cell.setCellValue(cal.getTime());
    }

    public void addComment(Cell cell, String author, String commentText) {
        CreationHelper factory = sheet.getWorkbook().getCreationHelper();

        ClientAnchor anchor = factory.createClientAnchor();
        anchor.setCol1(cell.getColumnIndex() + 1);
        anchor.setCol2(cell.getColumnIndex() + 3);
        anchor.setRow1(cell.getRowIndex() + 1);
        anchor.setRow2(cell.getRowIndex() + 5);

        Drawing drawing = sheet.createDrawingPatriarch();
        Comment comment = drawing.createCellComment(anchor);
        comment.setString(factory.createRichTextString(commentText));
        comment.setAuthor(author);

        cell.setCellComment(comment);
    }
}
