package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalLoginResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("needPassReset")
    @JsonProperty("needPassReset")
    private Boolean needPassReset;

    @SerializedName("remainingDays")
    @JsonProperty("remainingDays")
    private Integer remainingDays;
}
