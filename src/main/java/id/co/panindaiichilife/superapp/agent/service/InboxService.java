package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.InboxCountByTrxTypeDto;
import id.co.panindaiichilife.superapp.agent.api.dto.InboxDto;
import id.co.panindaiichilife.superapp.agent.api.dto.TrxEditProfileDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRecruitmentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRejoinApplicationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxTerminationDto;
import id.co.panindaiichilife.superapp.agent.api.filter.InboxFilter;
import id.co.panindaiichilife.superapp.agent.api.form.BulkInboxForm;
import id.co.panindaiichilife.superapp.agent.api.form.InboxForm;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.Inbox;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class InboxService {

    private final InboxRepository inboxRepository;

    private final UserRepository userRepository;

    private final TrxRecruitmentRepository trxRecruitmentRepository;

    private final TrxEditProfileRepository trxEditProfileRepository;
    private final TrxTerminationRepository trxTerminationRepository;
    private final TrxRejoinApplicationRepository trxRejoinApplicationRepository;

    public Page<InboxDto> findAll(Pageable pageable, InboxFilter filter) {
        Page<Inbox> inboxes = inboxRepository.findAll(filter, pageable);
        return BaseDto.of(InboxDto.class, inboxes, pageable);
    }

    public InboxDto findOne(String username, Long id) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Inbox data = inboxRepository.findById(id).orElseThrow(NotFoundException::new);

        //Check ownership
        validateOwnership(user, data);

        InboxDto dto = BaseDto.of(InboxDto.class, data);
        // Set detail data based on transaction type and ID
        if (data.getTrxId() != null && data.getTrxType() != null) {
            dto.setDetailData(getDetailDataByType(data.getTrxType(), data.getTrxId()));
        }

        return dto;
    }

    public void add(User user, InboxForm form) {
        Inbox data = new Inbox();
        BeanUtils.copyProperties(form, data);
        data.setUser(user);
        inboxRepository.save(data);
    }

    public void read(String username, Long id) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Inbox data = inboxRepository.findById(id).orElseThrow(NotFoundException::new);

        //Check ownership
        validateOwnership(user, data);

        if (!data.getIsRead()) {
            data.setIsRead(Boolean.TRUE);
        } else {
            data.setIsRead(Boolean.FALSE);
        }
        inboxRepository.save(data);
    }

    public void archive(String username, Long id) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Inbox data = inboxRepository.findById(id).orElseThrow(NotFoundException::new);

        //Check ownership
        validateOwnership(user, data);

        if (!data.getIsArchived()) {
            data.setIsArchived(Boolean.TRUE);
        } else {
            data.setIsArchived(Boolean.FALSE);
        }

        inboxRepository.save(data);
    }

    public void hardDelete(String username, Long id) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Inbox data = inboxRepository.findById(id).orElseThrow(NotFoundException::new);
        //Check ownership
        validateOwnership(user, data);
        inboxRepository.deleteById(id);
    }

    public void softDelete(String username, Long id) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Inbox data = inboxRepository.findById(id).orElseThrow(NotFoundException::new);
        //Check ownership
        validateOwnership(user, data);
        if (!data.isDeleted()) {
            data.setDeleted(true);
            data.setDeletedAt(LocalDateTime.now());
        } else {
            data.setDeleted(false);
            data.setDeletedAt(null);
        }
        inboxRepository.save(data);
    }

    public void validateOwnership(User user, Inbox inbox) {
        if (!user.getId().equals(inbox.getUser().getId())) {
            throw new BadRequestException("Notifikasi ini bukan milik Anda.");
        }
    }

    @Transactional
    public void bulkRead(String username, BulkInboxForm form) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        List<Inbox> inboxes = inboxRepository.findAllById(form.getInboxIds());

        for (Inbox inbox : inboxes) {
            // Check ownership for each inbox
            validateOwnership(user, inbox);

            // Toggle read status
            if (!inbox.getIsRead()) {
                inbox.setIsRead(Boolean.TRUE);
            } else {
                inbox.setIsRead(Boolean.FALSE);
            }
        }

        inboxRepository.saveAll(inboxes);
    }

    @Transactional
    public void bulkArchive(String username, BulkInboxForm form) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        List<Inbox> inboxes = inboxRepository.findAllById(form.getInboxIds());

        for (Inbox inbox : inboxes) {
            // Check ownership for each inbox
            validateOwnership(user, inbox);

            // Toggle archive status
            if (!inbox.getIsArchived()) {
                inbox.setIsArchived(Boolean.TRUE);
            } else {
                inbox.setIsArchived(Boolean.FALSE);
            }
        }

        inboxRepository.saveAll(inboxes);
    }

    @Transactional
    public void bulkSoftDelete(String username, BulkInboxForm form) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        List<Inbox> inboxes = inboxRepository.findAllById(form.getInboxIds());

        for (Inbox inbox : inboxes) {
            // Check ownership for each inbox
            validateOwnership(user, inbox);

            // Toggle soft delete status
            if (!inbox.isDeleted()) {
                inbox.setDeleted(true);
                inbox.setDeletedAt(LocalDateTime.now());
            } else {
                inbox.setDeleted(false);
                inbox.setDeletedAt(null);
            }
        }

        inboxRepository.saveAll(inboxes);
    }

    @Transactional
    public void bulkHardDelete(String username, BulkInboxForm form) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        List<Inbox> inboxes = inboxRepository.findAllById(form.getInboxIds());

        for (Inbox inbox : inboxes) {
            // Check ownership for each inbox
            validateOwnership(user, inbox);
        }

        // Delete all validated inboxes
        inboxRepository.deleteAllById(form.getInboxIds());
    }

    public List<InboxCountByTrxTypeDto> getUnreadCountByTrxType(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        return inboxRepository.countUnreadInboxByTrxTypeAndUser(user);
    }

    private Object getDetailDataByType(TrxType trxType, Long trxId) {
        switch (trxType) {
            case EDIT_PROFILE:
                return BaseDto.of(TrxEditProfileDto.class, trxEditProfileRepository.findById(trxId).orElse(null));
            case RECRUITMENT_BP:
            case RECRUITMENT_BM:
            case RECRUITMENT_BD:
                return BaseDto.of(TrxRecruitmentDto.class, trxRecruitmentRepository.findById(trxId).orElse(null));
            case TERMINASI_BP:
            case TERMINASI_BM:
            case TERMINASI_BD:
                return BaseDto.of(TrxTerminationDto.class, trxTerminationRepository.findById(trxId)
                        .orElse(null));
            case REJOIN_BP:
            case REJOIN_BM:
                return BaseDto.of(TrxRejoinApplicationDto.class, trxRejoinApplicationRepository
                        .findById(trxId).orElse(null));
            default:
                return null;
        }
    }
}
