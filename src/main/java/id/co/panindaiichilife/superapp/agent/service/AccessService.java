package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccessDto;
import id.co.panindaiichilife.superapp.agent.api.filter.AccessFilter;
import id.co.panindaiichilife.superapp.agent.config.retrofit.superappcommon.SuperAppCommonProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.superappcommon.dto.ScanAccessDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.model.Access;
import id.co.panindaiichilife.superapp.agent.repository.AccessRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.reflections.scanners.MethodAnnotationsScanner;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
@RequiredArgsConstructor
public class AccessService {

    private final Pattern accessPattern = Pattern
            .compile("hasPermission\\s*\\(\\s*'([^']*)'\\s*,\\s*'([^']*)'\\s*\\)");

    private final AccessRepository accessRepository;

    private final SuperAppCommonProvider superAppCommonProvider;

    public void scanPackages(String packageName) {
        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage(packageName))
                .setScanners(new SubTypesScanner(), new TypeAnnotationsScanner(),
                        new MethodAnnotationsScanner()));

        Set<Class<?>> types = reflections.getTypesAnnotatedWith(PreAuthorize.class);
        for (Class<?> type : types) {
            log.trace("class = {}", type.getName());

            PreAuthorize preAuthorize = type.getAnnotation(PreAuthorize.class);
            List<Access> accesses = extractFromString(preAuthorize.value());
            createIfNotExist(accesses);
        }

        Set<Method> methods = reflections.getMethodsAnnotatedWith(PreAuthorize.class);
        for (Method method : methods) {
            log.trace("class = {}, method = {}", method.getDeclaringClass().getName(), method.getName());

            PreAuthorize preAuthorize = method.getAnnotation(PreAuthorize.class);
            List<Access> accesses = extractFromString(preAuthorize.value());
            createIfNotExist(accesses);
        }

        //Scan Common Package
        scanCommonAccess(packageName);
    }

    public void createIfNotExist(List<Access> accesses) {
        for (Access access : accesses) {
            createIfNotExist(access);
        }
    }


    public void createIfNotExist(Access access) {
        if (!accessRepository.isDefined(access.getDomain(), access.getAction())) {
            String category = StringUtils.substringAfterLast(access.getDomain(), ".");
            category = String.join(" ", StringUtils.splitByCharacterTypeCamelCase(category));
            access.setCategory(category);

            String privilege = access.getAction();
            privilege = String.join(" ", StringUtils.splitByCharacterTypeCamelCase(privilege));
            access.setPrivilege(StringUtils.capitalize(privilege));

            accessRepository.save(access);
        }
    }

    public List<Access> extractFromString(String string) {
        List<Access> result = new ArrayList<>();

        int start = 0;
        Matcher m = accessPattern.matcher(string);
        while (m.find(start)) {
            start = m.end();

            Access row = new Access();
            row.setDomain(m.group(1));
            row.setAction(m.group(2));

            result.add(row);
        }

        return result;
    }

    public Page<AccessDto> findAll(AccessFilter accessFilter, Pageable pageable) {
        Page<Access> accesses = accessRepository.findAll(accessFilter, pageable);
        return BaseDto.of(AccessDto.class, accesses, pageable);
    }

    public void scanCommonAccess(String packageName) {
        Call<ScanAccessDto> call = superAppCommonProvider.scanAccess(packageName);

        try {
            Response<ScanAccessDto> response = call.execute();
            if (response.isSuccessful()) {
                ScanAccessDto scanAccessDto = response.body();
                for (String type : scanAccessDto.getTypes()) {
                    List<Access> accesses = extractFromString(type);
                    createIfNotExist(accesses);
                }

                for (String method : scanAccessDto.getMethods()) {
                    List<Access> accesses = extractFromString(method);
                    createIfNotExist(accesses);
                }
            }
        } catch (IOException e) {
            throw new InternalServerErrorException("Error occurred while scan common package");
        }
    }
}
