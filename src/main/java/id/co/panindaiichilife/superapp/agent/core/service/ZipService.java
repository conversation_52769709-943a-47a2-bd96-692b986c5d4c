package id.co.panindaiichilife.superapp.agent.core.service;

import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class ZipService {

  public void zipIt(OutputStream out, Map<File, ZipEntry> map) {

    byte[] buffer = new byte[1024];

    try (ZipOutputStream zos = new ZipOutputStream(out)) {

      for (File key : map.keySet()) {
        File file = key;
        ZipEntry ze = map.get(key);
        zos.putNextEntry(ze);

        try (FileInputStream in = new FileInputStream(file.toString())) {
          int len;
          while ((len = in.read(buffer)) > 0) {
            zos.write(buffer, 0, len);
          }

        } catch (IOException ex) {
          ex.printStackTrace();
        }
      }

      zos.closeEntry();
      System.out.println("Folder successfully compressed");

    } catch (IOException ex) {
      ex.printStackTrace();
    }
  }
}
