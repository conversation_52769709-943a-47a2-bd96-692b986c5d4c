package id.co.panindaiichilife.superapp.agent.config;

import org.springframework.core.convert.converter.Converter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class CustomJwtAuthenticationConverter implements Converter<Jwt, Collection<GrantedAuthority>> {

    private static final String CLAIM_ROLES = "roles";  // Change this to your JWT claim for roles if different
    private static final String CLAIM_SCOPES = "scope"; // Change this if your scopes are stored in another claim

    @Override
    public Collection<GrantedAuthority> convert(Jwt jwt) {
        Collection<GrantedAuthority> authorities = new ArrayList<>();

        // Extract roles from the JWT claims
        List<String> roles = jwt.getClaimAsStringList(CLAIM_ROLES);

        if (roles != null) {
            roles.forEach(role -> authorities.add(new SimpleGrantedAuthority("ROLE_" + role)));
        }

        // Extract scopes from the JWT claims
        String scope = jwt.getClaimAsString(CLAIM_SCOPES);
        if (scope != null) {
            String[] scopes = scope.split(" ");
            for (String s : scopes) {
                authorities.add(new SimpleGrantedAuthority("SCOPE_" + s));
            }
        }

        return authorities;
    }
}
