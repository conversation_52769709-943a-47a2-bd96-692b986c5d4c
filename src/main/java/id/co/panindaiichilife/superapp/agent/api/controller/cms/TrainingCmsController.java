package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.TrainingDto;
import id.co.panindaiichilife.superapp.agent.api.filter.TrainingFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.TrainingForm;
import id.co.panindaiichilife.superapp.agent.service.TrainingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController("cmsTrainingController")
@RequestMapping("/api/cms/training")
@Tag(name = "Training - CMS", description = "API CMS Training")
@Slf4j
@RequiredArgsConstructor
public class TrainingCmsController {

    private final TrainingService trainingService;

    @Operation(summary = "List trainings")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Training', 'view')")
    public Page<TrainingDto> index(@ParameterObject @ModelAttribute("filter") TrainingFilter filter,
                                   @ParameterObject @PageableDefault(sort = "code") Pageable pageable) {
        return trainingService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific training")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Training', 'view')")
    public TrainingDto view(@PathVariable long id) {
        return trainingService.findOne(id);
    }

    @Operation(summary = "Modify existing training")
    @PutMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Training', 'edit')")
    public TrainingDto edit(@PathVariable long id,
                            @Valid @RequestBody TrainingForm trainingForm) {
        return trainingService.update(id, trainingForm);
    }

    @Operation(summary = "Delete existing training")
    @DeleteMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Training', 'delete')")
    public void delete(@PathVariable long id) {
        trainingService.delete(id);
    }

    @Operation(summary = "Run Sync Training from API")
    @GetMapping(value = "sync")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Training', 'sync')")
    public void syncTraining() {
        trainingService.syncTraining();
    }
}
