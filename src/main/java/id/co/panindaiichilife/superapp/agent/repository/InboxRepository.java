package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.api.dto.InboxCountByTrxTypeDto;
import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.Inbox;
import id.co.panindaiichilife.superapp.agent.model.User;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface InboxRepository extends BaseRepository<Inbox, Long> {

    @Query("SELECT new id.co.panindaiichilife.superapp.agent.api.dto.InboxCountByTrxTypeDto(i.trxType, COUNT(i)) " +
           "FROM Inbox i " +
           "WHERE i.user = :user " +
           "AND i.isRead = false " +
           "AND i.deleted = false " +
           "AND i.trxType IS NOT NULL " +
           "GROUP BY i.trxType " +
           "ORDER BY i.trxType")
    List<InboxCountByTrxTypeDto> countUnreadInboxByTrxTypeAndUser(@Param("user") User user);
}
