package id.co.panindaiichilife.superapp.agent.api.form.agency;

import id.co.panindaiichilife.superapp.agent.enums.*;
import lombok.Data;

/**
 * Form for updating validation statuses of a recruitment
 */
@Data
public class TrxRecruitmentValidationForm {
    private ValidationBlacklistStatus validationBlacklistStatus;
    private ValidationKtpStatus validationKtpStatus;
    private ValidationBankAccountStatus validationBankAccountStatus;
    private ValidationHirarkiStatus validationHirarkiStatus;
    private ValidationAmlStatus validationAmlStatus;
    private ValidationAdministrationAgentStatus validationAdministrationAgentStatus;
    private ValidationLicenseAajiStatus validationLicenseAajiStatus;
    private ValidationLicenseAasiStatus validationLicenseAasiStatus;
}
