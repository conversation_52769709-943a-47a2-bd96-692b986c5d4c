package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.batch.*;
import id.co.panindaiichilife.superapp.agent.service.BatchService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController("cmsBatchController")
@RequestMapping("/api/cms/monitoring/batch")
@Tag(name = "Monitoring Batch - CMS", description = "API CMS Monitoring Batch")
@Slf4j
@RequiredArgsConstructor
public class BatchCmsController {

    private final BatchService batchService;

    @GetMapping("job/list")
    public ResponseEntity<List<BatchJobBasicDto>> getLast30Jobs() {
        return ResponseEntity.ok(batchService.getLast30JobExecutions());
    }

    @GetMapping("job/names")
    public ResponseEntity<List<String>> getJobNames() {
        return ResponseEntity.ok(batchService.getAllJobNames());
    }

    @GetMapping("/{id}")
    public ResponseEntity<BatchJobBasicDto> getBatchDetails(@PathVariable long id) {
        return ResponseEntity.ok(batchService.getBasicJob(id));
    }

    @GetMapping("/import/{id}")
    public ResponseEntity<ImportJobDto> getImportDetails(@PathVariable long id) {
        return ResponseEntity.ok(batchService.getImportJob(id));
    }

    @GetMapping("/export/{id}")
    public ResponseEntity<ExportJobDto> getExportDetails(@PathVariable long id) {
        return ResponseEntity.ok(batchService.getExportJob(id));
    }

    @PostMapping("/{id}/stop")
    public ResponseEntity<JobOperationDto> stopBatchJob(@PathVariable long id) {
        return ResponseEntity.ok(batchService.stop(id));
    }

    @GetMapping("/{id}/status")
    public ResponseEntity<JobStatusDto> checkStatus(@PathVariable long id) {
        return ResponseEntity.ok(batchService.getJobStatus(id));
    }
}