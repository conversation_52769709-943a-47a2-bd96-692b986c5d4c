package id.co.panindaiichilife.superapp.agent.config;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig {

    @Value("${cors.base-url}")
    private String corsBaseUrl;

    @Value("${cors.allow-credentials:false}")
    private Boolean allowCredentials;

    @PostConstruct
    public void validateCorsConfig() {
        if (corsBaseUrl == null || corsBaseUrl.isBlank()) {
            throw new IllegalStateException("cors.base-url must be configured");
        }
    }

    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**")
                        .allowedOriginPatterns(corsBaseUrl)
                        .allowedMethods("*")
                        .allowedHeaders("*")
                        .allowCredentials(allowCredentials)
                        .maxAge(86400); // 24 hours
            }
        };
    }
}