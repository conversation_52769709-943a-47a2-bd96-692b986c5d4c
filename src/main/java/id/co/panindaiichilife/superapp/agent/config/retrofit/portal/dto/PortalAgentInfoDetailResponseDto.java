package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PortalAgentInfoDetailResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("agentInfo")
    @JsonProperty("agentInfo")
    private AgentInfoDto agentInfo;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AgentInfoDto {
        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("agentLevel")
        @JsonProperty("agentLevel")
        private String agentLevel;

        @SerializedName("agentName")
        @JsonProperty("agentName")
        private String agentName;

        @SerializedName("branchCode")
        @JsonProperty("branchCode")
        private String branchCode;

        @SerializedName("joinDate")
        @JsonProperty("joinDate")
        private String joinDate;

        @SerializedName("endDate")
        @JsonProperty("endDate")
        private String endDate;

        @SerializedName("dob")
        @JsonProperty("dob")
        private String dob;

        @SerializedName("idNumber")
        @JsonProperty("idNumber")
        private String idNumber;

        @SerializedName("bankName")
        @JsonProperty("bankName")
        private String bankName;

        @SerializedName("bankAccountName")
        @JsonProperty("bankAccountName")
        private String bankAccountName;

        @SerializedName("bankAccountNo")
        @JsonProperty("bankAccountNo")
        private String bankAccountNo;

        @SerializedName("email")
        @JsonProperty("email")
        private String email;

        @SerializedName("noLicense")
        @JsonProperty("noLicense")
        private String noLicense;

        @SerializedName("tglLicenseExpired")
        @JsonProperty("tglLicenseExpired")
        private String tglLicenseExpired;

        @SerializedName("licenseType")
        @JsonProperty("licenseType")
        private String licenseType;

        @SerializedName("noLicenseAASI")
        @JsonProperty("noLicenseAASI")
        private String noLicenseAASI;

        @SerializedName("licenseTypeAASI")
        @JsonProperty("licenseTypeAASI")
        private String licenseTypeAASI;

        @SerializedName("tglLicenseExpiredAASI")
        @JsonProperty("tglLicenseExpiredAASI")
        private String tglLicenseExpiredAASI;

        @SerializedName("jenisLicenseAASI")
        @JsonProperty("jenisLicenseAASI")
        private String jenisLicenseAASI;
    }
}
