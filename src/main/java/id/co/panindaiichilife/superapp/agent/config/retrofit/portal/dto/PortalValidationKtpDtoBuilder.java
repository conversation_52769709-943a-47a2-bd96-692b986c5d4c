package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

/**
 * Builder class for PortalValidationKtpDto
 */
public class PortalValidationKtpDtoBuilder {
    private final PortalValidationKtpDto dto;
    
    public PortalValidationKtpDtoBuilder() {
        dto = new PortalValidationKtpDto();
    }
    
    public PortalValidationKtpDtoBuilder withTrxNum(String trxNum) {
        dto.setTrxNum(trxNum);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withDesc1(String distributionCode, String agentLevel) {
        dto.setDesc1(distributionCode + "-" + agentLevel);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withFaceImage(String faceImage) {
        dto.setFaceImage(faceImage);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withKtpImage(String ktpImage) {
        dto.setKtpImage(ktpImage);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withIdNumber(String idNumber) {
        dto.setIdNumber(idNumber);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withEmail(String email) {
        dto.setEmail(email);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withPhoneNumber(String phoneNumber) {
        dto.setPhoneNumber(phoneNumber);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withName(String name) {
        dto.setName(name);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withBirthDate(String birthDate) {
        dto.setBirthDate(birthDate);
        return this;
    }
    
    public PortalValidationKtpDtoBuilder withCaller(String caller) {
        dto.setCaller(caller);
        return this;
    }
    
    public PortalValidationKtpDto build() {
        return dto;
    }
}
