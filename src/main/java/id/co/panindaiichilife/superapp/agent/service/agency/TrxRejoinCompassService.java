package id.co.panindaiichilife.superapp.agent.service.agency;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalAgentInfoDetailResponseDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalAgentReactivationValidationResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Service
@RequiredArgsConstructor
@Slf4j
public class TrxRejoinCompassService {

    private final PortalProvider portalProvider;

    @CacheableWithTTL(
            cacheName = "rejoinValidationCompassApiCache",
            key = "#form.agentCode + ':' + #form.proposedLevel + ':' + #form.proposedLeaderCode + ':' +" +
                    "#form.branchCode",
            db = 7)
    public Response<PortalAgentReactivationValidationResponseDto> validateRejoinRequestWithPortal(
            TrxRejoinApplicationForm form) {
        Call<PortalAgentReactivationValidationResponseDto> call = portalProvider.validateAgentReactivation(
                form.getAgentCode(), form.getProposedLeaderCode(), form.getBranchCode(), form.getProposedLevel());
        try {
            return call.execute();
        } catch (Exception e) {
            log.error("Failed when verifying agent reactivation eligibility with portal", e);
            throw new InternalServerErrorException(e.getMessage());
        }
    }

    public void enrichDataFromCompass(TrxRejoinApplication application) {
        try {
            Call<PortalAgentInfoDetailResponseDto> call = portalProvider.getDetailedAgentInfo(
                    application.getAgent().getAgentCode());
            Response<PortalAgentInfoDetailResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalAgentInfoDetailResponseDto body = response.body();
                switch (body.getStatusCode()) {
                    case "200" -> {
                        log.info("Detailed agent info was successfully retrieved.");
                        if (body.getAgentInfo() == null) {
                            log.warn("Got status 200 but agentInfo was null, hence data was not enriched");
                            return;
                        }

                        PortalAgentInfoDetailResponseDto.AgentInfoDto agentInfoDto = body.getAgentInfo();

                        if (agentInfoDto.getDob() != null) {
                            application.setDob(LocalDate.parse(agentInfoDto.getDob(),
                                    DateTimeFormatter.ISO_OFFSET_DATE_TIME));
                        }
                        application.setIdNumber(agentInfoDto.getIdNumber().trim());
                        application.setBankAccountName(agentInfoDto.getBankAccountName());
                        application.setBankAccountNumber(agentInfoDto.getBankAccountName());
                    }
                    case "400" -> log.warn("receive status 400 upon retrieving detailed agent info");
                    default -> {
                        log.warn("receive unknown status upon retrieving detailed agent info");
                    }
                }
            } else {
                log.warn("response of retrieving detailed agent info was not successful");
            }
        } catch (Exception e) {
            log.warn("Unable to enrich rejoin data from compass due to {}", e.getMessage());
        }
    }
}
