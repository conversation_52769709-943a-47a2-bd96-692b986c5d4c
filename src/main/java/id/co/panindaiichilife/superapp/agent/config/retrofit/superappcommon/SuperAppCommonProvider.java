package id.co.panindaiichilife.superapp.agent.config.retrofit.superappcommon;


import id.co.panindaiichilife.superapp.agent.config.retrofit.superappcommon.dto.GlobalConfigDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.superappcommon.dto.ScanAccessDto;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface SuperAppCommonProvider {

    @GET("/api/public/access/scan")
    Call<ScanAccessDto> scanAccess(@Query("packageName") String packageName);

    @GET("/api/public/globalConfig/{key}")
    Call<GlobalConfigDto> getConfigByKey(@Path("key") String key);

}
