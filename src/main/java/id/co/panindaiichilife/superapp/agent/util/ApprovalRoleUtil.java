package id.co.panindaiichilife.superapp.agent.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class ApprovalRoleUtil {

    /**
     * Parses a comma-separated role string into a set of individual roles.
     * Handles both single roles and comma-separated multiple roles.
     *
     * @param roleConfig The role configuration string (e.g., "ROLE_AGE_HOS,ROLE_AGE_CAO")
     * @return Set of individual role codes
     */
    public static Set<String> parseRoles(String roleConfig) {
        if (roleConfig == null || roleConfig.trim().isEmpty()) {
            return Collections.emptySet();
        }

        return Arrays.stream(roleConfig.split(","))
                .map(String::trim)
                .filter(role -> !role.isEmpty())
                .collect(Collectors.toSet());
    }

    /**
     * Checks if any of the user's roles match the approver role(s) defined in the approval level.
     * Supports both single roles and comma-separated multiple roles.
     *
     * @param approverRoleConfig The approver role configuration (can be single role or comma-separated)
     * @param userRoles The set of roles the user has
     * @return true if there's a match, false otherwise
     */
    public static boolean hasMatchingRole(String approverRoleConfig, Set<String> userRoles) {
        if (approverRoleConfig == null || userRoles == null || userRoles.isEmpty()) {
            return false;
        }

        Set<String> configuredRoles = parseRoles(approverRoleConfig);
        
        // Check if any of the user's roles match any of the configured roles
        boolean hasMatch = configuredRoles.stream().anyMatch(userRoles::contains);
        
        log.debug("Checking role match: configured={}, user={}, match={}", 
                configuredRoles, userRoles, hasMatch);
        
        return hasMatch;
    }

    /**
     * Checks if the approver role configuration matches any of the requester's roles.
     * Used for recruitment transactions to avoid self-approval scenarios.
     *
     * @param approverRoleConfig The approver role configuration
     * @param requesterRoles The requester's roles
     * @return true if there's a match (indicating potential self-approval), false otherwise
     */
    public static boolean approverRoleMatchesRequesterRole(String approverRoleConfig, Set<String> requesterRoles) {
        return hasMatchingRole(approverRoleConfig, requesterRoles);
    }

    /**
     * Formats multiple roles into a comma-separated string for storage.
     *
     * @param roles Set of role codes
     * @return Comma-separated role string
     */
    public static String formatRoles(Set<String> roles) {
        if (roles == null || roles.isEmpty()) {
            return "";
        }
        
        return roles.stream()
                .filter(role -> role != null && !role.trim().isEmpty())
                .map(String::trim)
                .collect(Collectors.joining(","));
    }
}
