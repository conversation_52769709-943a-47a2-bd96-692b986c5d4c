package id.co.panindaiichilife.superapp.agent.config.retrofit.superapp;


import id.co.panindaiichilife.superapp.agent.config.retrofit.superapp.dto.TokenResponseDto;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface SuperAppProvider {

    @POST("oauth/token")
    @FormUrlEncoded
    Call<TokenResponseDto> getToken(
            @Field("username") String username,
            @Field("password") String password,
            @Field("grant_type") String grantType,
            @Field("scopes") String scopes
    );

    @POST("oauth/token")
    @FormUrlEncoded
    Call<TokenResponseDto> getToken(
            @Field("username") String username,
            @Field("password") String password,
            @Field("grant_type") String grantType,
            @Field("scopes") String scopes,
            @Field("rememberMe") Boolean rememberMe
    );
}
