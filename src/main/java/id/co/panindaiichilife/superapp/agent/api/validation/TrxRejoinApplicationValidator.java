package id.co.panindaiichilife.superapp.agent.api.validation;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class TrxRejoinApplicationValidator implements Validator {
    @Override
    public boolean supports(Class<?> clazz) {
        return TrxRejoinApplicationForm.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        TrxRejoinApplicationForm form = (TrxRejoinApplicationForm) target;

        // validate required fields
        if (form.getAgentCode() == null || form.getAgentCode().trim().isEmpty()) {
            errors.rejectValue("agentCode", "agentCode.required", "Agent code is required");
        }

        if (form.getProposedLevel() == null || form.getProposedLevel().trim().isEmpty()) {
            errors.rejectValue("proposedLevel", "proposedLevel.required", "Proposed level is required");
        }

        if (form.getProposedLeaderCode() == null || form.getProposedLeaderCode().trim().isEmpty()) {
            errors.rejectValue("proposedLeaderCode", "proposedLeaderCode.required", "Proposed leader code is required");
        }

        if (form.getBranchCode() == null || form.getBranchCode().trim().isEmpty()) {
            errors.rejectValue("branchCode", "branchCode.required", "Branch code is required");
        }
    }
}
