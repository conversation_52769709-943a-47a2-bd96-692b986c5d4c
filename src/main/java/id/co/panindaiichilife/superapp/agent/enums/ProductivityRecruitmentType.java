package id.co.panindaiichilife.superapp.agent.enums;

/**
 * Enum for productivity recruitment filtering by type
 */
public enum ProductivityRecruitmentType {
    /**
     * Approved recruitments with agent code but license validation statuses (AAJI/AASI) are NOT ACTIVE
     */
    REKRUT_BERKODE_AGEN,
    
    /**
     * Approved recruitments with agent code and either AAJI or AASI license validation status is ACTIVE
     */
    REKRUT_BARU_BERLISENSI
}
