package id.co.panindaiichilife.superapp.agent.api.dto.pdf;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * Utility class for PDF document generation with common helper methods.
 * This class provides shared functionality for all PDF DTO classes to reduce code duplication.
 */
public final class PdfDocumentUtils {

    private PdfDocumentUtils() {
        // Utility class - prevent instantiation
    }

    /**
     * Get Indonesian day name from DayOfWeek.
     *
     * @param dayOfWeek the day of week
     * @return Indonesian day name
     */
    public static String getIndonesianDayName(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY:
                return "Senin";
            case TUESDAY:
                return "Selasa";
            case WEDNESDAY:
                return "Rabu";
            case THURSDAY:
                return "Kamis";
            case FRIDAY:
                return "Jumat";
            case SATURDAY:
                return "Sabtu";
            case SUNDAY:
                return "<PERSON>gu";
            default:
                return "";
        }
    }

    /**
     * Get Indonesian month name from Month.
     *
     * @param month the month
     * @return Indonesian month name
     */
    public static String getIndonesianMonthName(Month month) {
        switch (month) {
            case JANUARY:
                return "Januari";
            case FEBRUARY:
                return "Februari";
            case MARCH:
                return "Maret";
            case APRIL:
                return "April";
            case MAY:
                return "Mei";
            case JUNE:
                return "Juni";
            case JULY:
                return "Juli";
            case AUGUST:
                return "Agustus";
            case SEPTEMBER:
                return "September";
            case OCTOBER:
                return "Oktober";
            case NOVEMBER:
                return "November";
            case DECEMBER:
                return "Desember";
            default:
                return "";
        }
    }

    /**
     * Utility method to get string value or empty string if null.
     *
     * @param value the string value to check
     * @return the value if not null, empty string otherwise
     */
    public static String getStringOrEmpty(String value) {
        return value != null ? value : "";
    }

    /**
     * Utility method to get string value or default if null.
     *
     * @param value        the string value to check
     * @param defaultValue the default value to return if null
     * @return the value if not null, default value otherwise
     */
    public static String getStringOrDefault(String value, String defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * Utility method to get boolean value or false if null.
     *
     * @param value the Boolean value to check
     * @return true if value is Boolean.TRUE, false otherwise
     */
    public static boolean getBooleanOrFalse(Boolean value) {
        return Boolean.TRUE.equals(value);
    }

    /**
     * Add standard date and time variables to the variables map.
     *
     * @param variables the map to add variables to
     */
    public static void addStandardDateTimeVariables(Map<String, Object> variables) {
        LocalDate now = LocalDate.now();
        variables.put("dayName", getIndonesianDayName(now.getDayOfWeek()));
        variables.put("date", now.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        variables.put("month", getIndonesianMonthName(now.getMonth()));
        variables.put("year", String.valueOf(now.getYear()));
    }

    /**
     * Add additional variables to the map if they are not null.
     *
     * @param variables           the target map
     * @param additionalVariables the additional variables to add
     */
    public static void addAdditionalVariables(Map<String, Object> variables, Map<String, Object> additionalVariables) {
        if (additionalVariables != null) {
            variables.putAll(additionalVariables);
        }
    }
}
