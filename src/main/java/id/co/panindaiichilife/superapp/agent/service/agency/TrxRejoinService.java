package id.co.panindaiichilife.superapp.agent.service.agency;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRejoinApplicationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRejoinEligibleCandidateDto;
import id.co.panindaiichilife.superapp.agent.api.filter.agency.CandidateRejoinFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.agency.RejoinListFilter;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinCancellationForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinValidationForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalAgentReactivationValidationResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.core.service.MailService;
import id.co.panindaiichilife.superapp.agent.core.support.TimeUtils;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.Branch;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import id.co.panindaiichilife.superapp.agent.model.TrxTermination;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.BranchRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRejoinApplicationRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxTerminationRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.ApprovalService;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Response;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class TrxRejoinService {
    private final TrxRejoinApplicationRepository rejoinApplicationRepository;
    private final UserRepository userRepository;
    private final TrxTerminationRepository terminationRepository;
    private final ApprovalService approvalService;
    private final AgentRepository agentRepository;
    private final TrxRejoinCompassService rejoinCompassService;
    private final AmazonS3Service amazonS3Service;
    private final BranchRepository branchRepository;
    private final GlobalConfigService globalConfigService;
    private final MailService mailService;
    private final RejoinValidationService validationService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public Page<TrxRejoinEligibleCandidateDto> retrieveEligibleCandidates(
            String username, CandidateRejoinFilter filter, Pageable pageable) {

        User currentUser = userRepository.findByUsername(username).orElseThrow(() ->
                new NotFoundException(String.format("User %s not found", username)));

        Page<AgentWithBranchInfo> queryResult;
        if (currentUser.getIsAgent()) {
            // check if user is agent, if yes assume it's leader
            queryResult = agentRepository.findAllEligibleAgentsForRejoinBasedOnLeader(
                    currentUser.getAgent().getAgentCode(),
                    TimeUtils.zonedNow().minusYears(2).toInstant(),
                    filter.getSearchQuery(),
                    pageable);

            return new PageImpl<>(
                    queryResult.getContent()
                            .stream()
                            .map(TrxRejoinEligibleCandidateDto::of).toList(),
                    pageable,
                    queryResult.getTotalElements());
        }

        if (filter.getBranchCode() == null || filter.getBranchCode().isEmpty()) {
            throw new BadRequestException("Branch code not supplied");
        }

        queryResult = agentRepository.findAllEligibleAgentsForRejoinBasedOnBranch(
                filter.getBranchCode(),
                TimeUtils.zonedNow().minusYears(2).toInstant(),
                filter.getSearchQuery(),
                pageable);

        return new PageImpl<>(
                queryResult.getContent()
                        .stream()
                        .map(TrxRejoinEligibleCandidateDto::of).toList(),
                pageable,
                queryResult.getTotalElements());
    }

    private void validateRejoinEligibility(TrxRejoinApplicationForm form) {
        // verify eligibility with portal
        try {
            Response<PortalAgentReactivationValidationResponseDto> response =
                    rejoinCompassService.validateRejoinRequestWithPortal(form);

            if (response.isSuccessful() && response.body() != null) {
                PortalAgentReactivationValidationResponseDto body = response.body();
                switch (body.getStatusCode()) {
                    case "200" -> log.info("Rejoin eligibility was successfully validated.");
                    case "400" -> throw new BadRequestException(body.getAgentReactivationValidationDTO().getRemark());
                    default -> {
                        log.warn("Response from reactivation validation: {}", objectMapper.writeValueAsString(body));
                        throw new BadRequestException("Rejoin validation failed");
                    }
                }
            } else {
                log.warn("Response from reactivation validation: {}", objectMapper.writeValueAsString(response));
                throw new InternalServerErrorException("Error from Portal: Rejoin validation failed");
            }
        } catch (Exception e) {
            log.error("Failed when verifying agent reactivation eligibility with portal", e);
            throw new InternalServerErrorException(e.getMessage());
        }
    }

    public TrxRejoinApplicationDto submitApplication(String username, TrxRejoinApplicationForm form) {
        User submitter = userRepository.findByUsername(username)
                .orElseThrow((() -> new NotFoundException(String.format("User %s not found", username))));

        // validate agentCode existence
        Agent onboardAgent = agentRepository.findTopByAgentCode(form.getAgentCode())
                .orElseThrow((() -> new NotFoundException(String.format("Agent %s not found", form.getAgentCode()))));

        TrxRejoinApplication rejoinApplication = rejoinApplicationRepository
                .findTopByAgentAndStatusInOrderByUpdatedAtDesc(onboardAgent,
                        List.of(TrxStatus.DRAFT, TrxStatus.IN_PROGRESS, TrxStatus.DIKEMBALIKAN)).orElse(null);

        if (rejoinApplication != null) {
            throw new BadRequestException(String.format("There is an ongoing rejoin application for agent %s",
                    form.getAgentCode()));
        }

        // get branch data
        Branch branch = branchRepository.findByBranchCode(form.getBranchCode())
                .orElseThrow((() -> new NotFoundException(String.format("Branch %s not found", form.getBranchCode()))));

        // validate rejoin eligibility
        validateRejoinEligibility(form);

        // get last termination data
        TrxTermination lastTermination = terminationRepository
                .findTopByTargetOrderByEffectiveDateDesc(onboardAgent.getUser())
                .orElse(null);

        if (lastTermination == null) {
            log.info("Last termination information not found in the system");
        }

        TrxType trxType = TrxType.valueOf("REJOIN_" + form.getProposedLevel());

        TrxRejoinApplication newApplication = new TrxRejoinApplication();
        BeanUtils.copyProperties(form, newApplication);
        newApplication.setAgent(onboardAgent);
        newApplication.setBranch(branch);
        newApplication.setTrxType(trxType);
        newApplication.setStatus(TrxStatus.IN_PROGRESS);
        newApplication.setSubmittedBy(submitter);
        newApplication.setLastTermination(lastTermination);
        rejoinCompassService.enrichDataFromCompass(newApplication);

        newApplication = rejoinApplicationRepository.save(newApplication);

        // Run all validations asynchronously using the validation service
        log.info("Starting validation process for rejoin application ID: {}", newApplication.getId());
        validationService.validateRejoin(form, newApplication);

        // request approval
        TrxApprovalHeader approvalHeader = approvalService.requestApproval(trxType, newApplication.getId(), submitter,
                form.getRemarks(), submitter.getChannel(), false);
        // set approval entity to rejoin app and save again
        newApplication.setApprovalHeader(approvalHeader);
        newApplication.setApprovalStatus(approvalHeader.getApprovalStatus());
        rejoinApplicationRepository.save(newApplication);

        return TrxRejoinApplicationDto.of(TrxRejoinApplicationDto.class, newApplication);
    }

    public Page<TrxRejoinApplicationDto> getAllRejoinApplicationsByCurrentUser(
            String username, RejoinListFilter filter, Pageable pageable) {
        User currentUser = userRepository.findByUsername(username)
                .orElseThrow((() -> new NotFoundException(String.format("User %s not found", username))));

        Page<TrxRejoinApplication> queryResult = rejoinApplicationRepository
                .findRejoinApplicationsBySubmitter(
                        currentUser.getId(),
                        filter.getSearchQuery(),
                        filter.getTrxStatuses(),
                        pageable);

        return new PageImpl<>(
                queryResult.getContent()
                        .stream()
                        .map(appl -> TrxRejoinApplicationDto.of(TrxRejoinApplicationDto.class, appl) ).toList(),
                pageable,
                queryResult.getTotalElements());
    }

    public TrxRejoinApplicationDto getDetailApplication(Long id) {
        TrxRejoinApplication application = rejoinApplicationRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format("Application %s not found", id)));
        return TrxRejoinApplicationDto.of(TrxRejoinApplicationDto.class, application);
    }

    @Transactional
    public void cancelApplication(String username, TrxRejoinCancellationForm form) {
        if (form.getId() == null || form.getId() == 0) {
            throw new BadRequestException("Invalid rejoin application id");
        }

        Long id = form.getId();

        User submitter = userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException(String.format("User %s not found", username)));
        TrxRejoinApplication application = rejoinApplicationRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format("Rejoin application %d not found", id)));

        // verify submitter validity
        if (!application.getSubmittedBy().getId().equals(submitter.getId())) {
            throw new BadRequestException("Invalid submitter");
        }

        if (application.getStatus() == TrxStatus.CANCELLED) {
            throw new BadRequestException("Rejoin application has been cancelled");
        }

        application.setApprovalStatus(ApprovalStatus.DIBATALKAN);
        application.setStatus(TrxStatus.CANCELLED);
        rejoinApplicationRepository.save(application);

        // Cancel Approval
        approvalService.cancelApproval(application.getTrxType(), id, form.getRemarks());
    }

    @Transactional
    public void expireStaleApplications() {
        // select all applications that has no update since 30 days
        List<TrxRejoinApplication> expiredEntries = rejoinApplicationRepository
                .findAllByUpdatedAtBeforeAndStatusNotIn(
                        TimeUtils.zonedNow().minusDays(30L).toInstant(),
                        List.of(
                                TrxStatus.COMPLETE,
                                TrxStatus.EXPIRED,
                                TrxStatus.CANCELLED,
                                TrxStatus.REJECTED
                        )
                );

        Set<TrxRejoinApplication> notificationEntries = new HashSet<>();
        List<TrxRejoinApplication> expiredApplications = expiredEntries.stream().peek((application) -> {
            application.setStatus(TrxStatus.EXPIRED);

            TrxApprovalHeader approvalHeader = application.getApprovalHeader();
            approvalHeader.setApprovalStatus(ApprovalStatus.DIBATALKAN);
            approvalService.cancelApproval(application.getTrxType(), application.getId());

            notificationEntries.add(application);
        }).toList();

        rejoinApplicationRepository.saveAll(expiredApplications);
    }

    @Transactional
    public TrxRejoinApplicationDto revise(String username, Long originalApplicationId, TrxRejoinApplicationForm form) {
        // check original application
        TrxRejoinApplication application = rejoinApplicationRepository.findById(originalApplicationId)
                .orElseThrow(() -> new NotFoundException(String.format("No rejoin application was found with id %d",
                        originalApplicationId)));
        // verify submitter
        User submitter = userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException(String.format("User %s not found", username)));

        if (application.getSubmittedBy().getId().equals(submitter.getId())) {
            throw new BadRequestException("Invalid submitter");
        }

        // validate only application with status TERTUNDA can be revised
        if (application.getApprovalStatus() != ApprovalStatus.TERTUNDA || application.getStatus() != TrxStatus.DIKEMBALIKAN) {
            throw new BadRequestException("Invalid rejoin application status");
        }

        // update existing application with submitted form
        BeanUtils.copyProperties(form, application);

        // resend/reset approval flows
        TrxApprovalHeader approvalHeader = approvalService.resendApproval(application.getTrxType(),
                originalApplicationId);
        application.setApprovalStatus(approvalHeader.getApprovalStatus());
        application.setApprovalHeader(approvalHeader);
        application.setStatus(TrxStatus.IN_PROGRESS);

        // persist application to db
        return TrxRejoinApplicationDto.of(TrxRejoinApplicationDto.class, rejoinApplicationRepository.save(application));
    }

    @Transactional
    public TrxRejoinApplication resolveRejoinApplicationStatusOnEventReceived(
            TrxRejoinApplication application, ApprovalStatus approvalStatus) {
        log.info("Updating rejoin application status upon event received for id {} to {}", application.getId(),
                approvalStatus);

        application.setApprovalStatus(approvalStatus);

        // Update termination status based on approval status
        switch (approvalStatus) {
            case DISETUJUI:
                application.setStatus(TrxStatus.COMPLETE);
                log.info("Rejoin application {} completed", application.getId());
                break;

            case DITOLAK:
                application.setStatus(TrxStatus.REJECTED);
                log.info("Rejoin application {} rejected", application.getId());
                break;

            case TERTUNDA:
                application.setStatus(TrxStatus.DIKEMBALIKAN);
                log.info("Rejoin application {} returned for revision and marked as DIKEMBALIKAN", application.getId());
                break;

            case DIBATALKAN:
                application.setStatus(TrxStatus.CANCELLED);
                log.info("Rejoin application {} cancelled and marked as CANCELLED", application.getId());
                break;

            case MENUNGGU_PERSETUJUAN:
            case BARU:
                // Keep the current transaction status for these approval statuses
                log.info("Rejoin application {} status updated to {} without changing transaction status",
                        application.getId(), approvalStatus);
                break;
            default:
                log.warn("No handler for action {}", approvalStatus);
                break;
        }

        // Persist updated termination record
        return rejoinApplicationRepository.save(application);
    }

    public FileinputResponse upload(String username, MultipartFile file) {
        final String folder = "rejoin";
        User user = userRepository.findByUsername(username).orElseThrow(() ->
                new NotFoundException(String.format("User %s not found", username)));

        String filePath = "/assets/rejoin/" + user + "/" + folder + "/" + UUID.randomUUID() + "."
                + FilenameUtils.getExtension(file.getOriginalFilename());
        if (amazonS3Service.store(file, filePath)) {
            return FileinputResponse.success(amazonS3Service.getUrl(filePath));
        } else {
            throw new BadRequestException("Upload failed");
        }
    }

    public void sendEmail(List<String> recipients,
                          String key,
                          String defaultTemplate,
                          Map<String, Object> data,
                          Attachments... attachments) {

        String templateId = globalConfigService.getGlobalConfig(key, defaultTemplate);
        recipients.forEach((r) -> {
            try {
                Mail mail = mailService.createEmailWithAttachments(templateId, data, Set.of(attachments), r);
                mailService.sendEmail(mail);
            } catch (Exception e) {
                log.warn("Unable to send {} email notification to {}, due to error {}",
                        key, r, e.getMessage());
            }
        });
    }

    @Transactional
    public TrxRejoinApplicationDto updateValidationStatuses(
            String username, Long id, TrxRejoinValidationForm form) {
        // Validate user
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException("User not found: " + username));

        TrxRejoinApplication application = rejoinApplicationRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format("Rejoin not found with ID: %d", id)));

        // Update validation statuses if provided in the form
        if (form.getValidationBlacklistStatus() != null) {
            application.setValidationBlacklistStatus(form.getValidationBlacklistStatus());
        }

        if (form.getValidationKtpStatus() != null) {
            application.setValidationKtpStatus(form.getValidationKtpStatus());
        }

        if (form.getValidationBankAccountStatus() != null) {
            application.setValidationBankAccountStatus(form.getValidationBankAccountStatus());
        }

        if (form.getValidationHirarkiStatus() != null) {
            application.setValidationHirarkiStatus(form.getValidationHirarkiStatus());
        }

        if (form.getValidationAmlStatus() != null) {
            application.setValidationAmlStatus(form.getValidationAmlStatus());
        }

        if (form.getValidationAdministrationAgentStatus() != null) {
            application.setValidationAdministrationAgentStatus(form.getValidationAdministrationAgentStatus());
        }

        if (form.getValidationLicenseAajiStatus() != null) {
            application.setValidationLicenseAajiStatus(form.getValidationLicenseAajiStatus());
        }

        if (form.getValidationLicenseAasiStatus() != null) {
            application.setValidationLicenseAasiStatus(form.getValidationLicenseAasiStatus());
        }

        // Persist updated entity
        application = rejoinApplicationRepository.save(application);

        log.info("Validation statuses updated for rejoin id: {} by user: {}", id, username);

        return TrxRejoinApplicationDto.of(TrxRejoinApplicationDto.class, application);
    }
}
