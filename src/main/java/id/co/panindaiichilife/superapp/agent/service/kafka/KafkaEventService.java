package id.co.panindaiichilife.superapp.agent.service.kafka;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.config.kafka.KafkaConfig;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.*;
import id.co.panindaiichilife.superapp.agent.model.event.AgentReactivationEvent;
import id.co.panindaiichilife.superapp.agent.model.event.AgentTerminationEvent;
import id.co.panindaiichilife.superapp.agent.model.event.EditProfileEvent;
import id.co.panindaiichilife.superapp.agent.model.event.RecruitmentStatusEvent;
import id.co.panindaiichilife.superapp.agent.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaEventService {

    private final KafkaTemplate<String, Object> kafkaTemplate;

    private final TrxRecruitmentRepository trxRecruitmentRepository;

    private final TrxEditProfileRepository trxEditProfileRepository;

    private final TrxTerminationRepository trxTerminationRepository;

    private final TrxRejoinApplicationRepository rejoinApplicationRepository;

    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    /**
     * Sends an event to Kafka based on the transaction type and approval header
     *
     * @param header The approval header containing transaction information
     * @param status The approval status
     * @return A CompletableFuture that will be completed when the send operation completes
     */
    public CompletableFuture<SendResult<String, Object>> sendApprovalEvent(TrxApprovalHeader header, ApprovalStatus status) {
        return sendApprovalEvent(header, status, null, null);
    }

    /**
     * Sends an event to Kafka based on the transaction type and approval header
     *
     * @param header       The approval header containing transaction information
     * @param status       The approval status
     * @param recipients   The notification recipients
     * @param notification The notification payload
     * @return A CompletableFuture that will be completed when the send operation completes
     */
    public CompletableFuture<SendResult<String, Object>> sendApprovalEvent(
            TrxApprovalHeader header,
            ApprovalStatus status,
            List<User> recipients,
            NotificationDto notification) {
        log.info("Preparing to send Kafka event for transaction type: {}, ID: {}, status: {}",
                header.getTrxType(), header.getTrxId(), status);

        // Route to the appropriate event creator based on transaction type
        switch (header.getTrxType()) {
            case EDIT_PROFILE:
                return sendEditProfileEvent(header, status, recipients, notification);
            case RECRUITMENT_BP:
            case RECRUITMENT_BM:
            case RECRUITMENT_BD:
                return sendRecruitmentEvent(header, status, recipients, notification);
            // Add more cases for other transaction types as needed
            case TERMINASI_BP:
            case TERMINASI_BM:
            case TERMINASI_BD:
                return sendTerminationEvent(header, status, recipients, notification);
            case REJOIN_BP:
            case REJOIN_BM:
                return sendRejoinEvent(header, status, recipients, notification);
            // case PROMOTION:
            //     return sendPromotionEvent(header, status, recipients, notification);
            default:
                log.warn("No Kafka event handler for transaction type: {}", header.getTrxType());
                return CompletableFuture.completedFuture(null);
        }
    }

    /**
     * Creates and sends a recruitment event to Kafka
     *
     * @param header       The approval header
     * @param status       The approval status
     * @param recipients   The notification recipients
     * @param notification The notification payload
     * @return A CompletableFuture that will be completed when the send operation completes
     */
    private CompletableFuture<SendResult<String, Object>> sendRecruitmentEvent(
            TrxApprovalHeader header,
            ApprovalStatus status,
            List<User> recipients,
            NotificationDto notification) {
        // Find the recruitment transaction
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(header.getTrxId())
                .orElse(null);

        if (recruitment == null) {
            log.error("Could not find recruitment with ID: {} for Kafka event", header.getTrxId());
            return CompletableFuture.completedFuture(null);
        }

        // Get next approver user IDs
        List<String> nextApproverUserIds = new ArrayList<>();

        // If status is MENUNGGU_PERSETUJUAN and we have recipients, use their user IDs
        if ((status == ApprovalStatus.BARU ||
                status == ApprovalStatus.MENUNGGU_PERSETUJUAN) && recipients != null && !recipients.isEmpty()) {
            // Extract user IDs from recipients
            nextApproverUserIds = recipients.stream()
                    .map(User::getUsername)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("Using {} recipients as next approvers", nextApproverUserIds.size());
        }
        // If no recipients provided, try to determine from the approval header
        else if ((status == ApprovalStatus.BARU ||
                status == ApprovalStatus.MENUNGGU_PERSETUJUAN) && header.getApproverRole() != null) {
            // Check if this is an upline approval
            if (header.getApproverRole().startsWith("UPLINE:")) {
                // Extract the upline agent code
                String uplineAgentCode = header.getApproverRole().substring("UPLINE:".length());

                // Find the upline agent and their user
                Agent uplineAgent = agentRepository.findTopByAgentCode(uplineAgentCode).orElse(null);
                if (uplineAgent != null && uplineAgent.getUser() != null) {
                    User uplineUser = uplineAgent.getUser();
                    nextApproverUserIds.add(uplineUser.getUsername());
                }
            }
            // Check if this is a recruiter approval
            else if (header.getApproverRole().startsWith("RECRUITER:")) {
                // Extract the recruiter username
                String recruiterUsername = header.getApproverRole().substring("RECRUITER:".length());

                // Find the recruiter user
                User recruiter = userRepository.findByUsername(recruiterUsername).orElse(null);
                if (recruiter != null) {
                    nextApproverUserIds.add(recruiter.getUsername());
                }
            }
            // Standard role-based approval
            else {
                // For role-based approvals, find all users with the role
                List<User> usersWithRole = userRepository.findUsersByRoleCode(header.getApproverRole());
                if (usersWithRole != null && !usersWithRole.isEmpty()) {
                    // Extract user IDs from users with the role
                    nextApproverUserIds = usersWithRole.stream()
                            .map(User::getUsername)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    log.info("Found {} users with role {}", usersWithRole.size(), header.getApproverRole());
                } else {
                    log.warn("No users found with role {}", header.getApproverRole());
                }
            }
        }

        // Create the recruitment event
        RecruitmentStatusEvent event = RecruitmentStatusEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .recruitmentId(recruitment.getId())
                .recruitmentUuId(recruitment.getUuid())
                .recruiterCode(recruitment.getRecruiterCode())
                .candidateName(recruitment.getFullName())
                .candidateEmail(recruitment.getEmail())
                .candidatePhone(recruitment.getPhoneNumber())
                .trxType(header.getTrxType())
                .positionLevel(recruitment.getPositionLevel() != null ?
                        recruitment.getPositionLevel().name() : null)
                .leaderCode(recruitment.getLeaderCode())
                .approvalStatus(status)
                .trxStatus(getTrxStatusFromApprovalStatus(status))
                .timestamp(Instant.now())
                .nextApproverUserIds(nextApproverUserIds)
                .approvalHeaderId(header.getId())
                .inboxType(notification != null ? notification.getInboxType() : null)
                .notificationTitle(notification != null ? notification.getTitle() : null)
                .notificationBody(notification != null ? notification.getBody() : null)
                .build();

        // Send the event to Kafka
        String key = event.getRecruitmentId().toString();
        log.info("Sending recruitment event to Kafka for ID: {}, status: {}", event.getRecruitmentId(), status);

        CompletableFuture<SendResult<String, Object>> future =
                kafkaTemplate.send(KafkaConfig.RECRUITMENT_TOPIC, key, event);

        future.whenComplete((result, ex) -> {
            if (ex == null) {
                log.info("Sent recruitment event for ID: {} with offset: {}",
                        event.getRecruitmentId(), result.getRecordMetadata().offset());
            } else {
                log.error("Unable to send recruitment event for ID: {} due to: {}",
                        event.getRecruitmentId(), ex.getMessage(), ex);
            }
        });

        return future;
    }

    /**
     * Converts an ApprovalStatus to the corresponding TrxStatus
     *
     * @param approvalStatus The approval status
     * @return The corresponding transaction status
     */
    /**
     * Creates and sends an edit profile event to Kafka
     *
     * @param header       The approval header
     * @param status       The approval status
     * @param recipients   The notification recipients
     * @param notification The notification payload
     * @return A CompletableFuture that will be completed when the send operation completes
     */
    private CompletableFuture<SendResult<String, Object>> sendEditProfileEvent(
            TrxApprovalHeader header,
            ApprovalStatus status,
            List<User> recipients,
            NotificationDto notification) {
        // Find the edit profile transaction
        TrxEditProfile editProfile = trxEditProfileRepository.findById(header.getTrxId())
                .orElse(null);

        if (editProfile == null) {
            log.error("Could not find edit profile with approval header ID: {} for Kafka event", header.getId());
            return CompletableFuture.completedFuture(null);
        }

        // Get next approver user IDs
        List<String> nextApproverUserIds = new ArrayList<>();

        if ((status == ApprovalStatus.BARU ||
                status == ApprovalStatus.MENUNGGU_PERSETUJUAN) && recipients != null && !recipients.isEmpty()) {
            // Extract user IDs from recipients
            nextApproverUserIds = recipients.stream()
                    .map(User::getUsername)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("Using {} recipients as next approvers for edit profile", nextApproverUserIds.size());
        }
        // If no recipients provided, try to determine from the approval header
        else if (status == ApprovalStatus.MENUNGGU_PERSETUJUAN && header.getApproverRole() != null) {
            // Check if this is an upline approval
            if (header.getApproverRole().startsWith("UPLINE:")) {
                // Extract the upline agent code
                String uplineAgentCode = header.getApproverRole().substring("UPLINE:".length());

                // Find the upline agent and their user
                Agent uplineAgent = agentRepository.findTopByAgentCode(uplineAgentCode).orElse(null);
                if (uplineAgent != null && uplineAgent.getUser() != null) {
                    User uplineUser = uplineAgent.getUser();
                    nextApproverUserIds.add(uplineUser.getUsername());
                }
            }
            // Standard role-based approval
            else {
                // For role-based approvals, find all users with the role
                List<User> usersWithRole = userRepository.findUsersByRoleCode(header.getApproverRole());
                if (usersWithRole != null && !usersWithRole.isEmpty()) {
                    // Extract user IDs from users with the role
                    nextApproverUserIds = usersWithRole.stream()
                            .map(User::getUsername)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    log.info("Found {} users with role {} for edit profile", usersWithRole.size(), header.getApproverRole());
                } else {
                    log.warn("No users found with role {} for edit profile", header.getApproverRole());
                }
            }
        }

        // Parse the changed fields from the data JSON
        Map<String, String> changedFields = new HashMap<>();
        if (editProfile.getData() != null && !editProfile.getData().isEmpty()) {
            try {
                // Parse the JSON data to extract changed fields
                changedFields = new Gson().fromJson(
                        editProfile.getData(),
                        new TypeToken<Map<String, String>>() {
                        }.getType()
                );
            } catch (Exception e) {
                log.error("Error parsing edit profile data: {}", e.getMessage(), e);
            }
        }

        // Get agent information
        Agent agent = editProfile.getAgent();
        String agentCode = agent != null ? agent.getAgentCode() : null;
        String agentName = agent != null && agent.getUser() != null ? agent.getUser().getName() : null;
        String agentEmail = agent != null && agent.getUser() != null ? agent.getUser().getEmail() : null;

        // Get requester information
        String requestByUsername = header.getRequestBy() != null ? header.getRequestBy().getUsername() : null;
        String requestByName = header.getRequestBy() != null ? header.getRequestBy().getName() : null;

        // Create the edit profile event
        EditProfileEvent event = EditProfileEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .editProfileId(editProfile.getId())
                .agentCode(agentCode)
                .agentName(agentName)
                .agentEmail(agentEmail)
                .requestByUsername(requestByUsername)
                .requestByName(requestByName)
                .changedFields(changedFields)
                .approvalStatus(status)
                .trxType(header.getTrxType())
                .trxStatus(getTrxStatusFromApprovalStatus(status))
                .timestamp(Instant.now())
                .nextApproverUserIds(nextApproverUserIds)
                .approvalHeaderId(header.getId())
                .notificationTitle(notification != null ? notification.getTitle() : null)
                .notificationBody(notification != null ? notification.getBody() : null)
                .build();

        // Send the event to Kafka
        String key = event.getEditProfileId().toString();
        log.info("Sending edit profile event to Kafka for ID: {}, status: {}", event.getEditProfileId(), status);

        CompletableFuture<SendResult<String, Object>> future =
                kafkaTemplate.send(KafkaConfig.EDIT_PROFILE_TOPIC, key, event);

        future.whenComplete((result, ex) -> {
            if (ex == null) {
                log.info("Sent edit profile event for ID: {} with offset: {}",
                        event.getEditProfileId(), result.getRecordMetadata().offset());
            } else {
                log.error("Unable to send edit profile event for ID: {} due to: {}",
                        event.getEditProfileId(), ex.getMessage(), ex);
            }
        });

        return future;
    }

    private CompletableFuture<SendResult<String, Object>> sendTerminationEvent(
            TrxApprovalHeader header,
            ApprovalStatus status,
            List<User> recipients,
            NotificationDto notification) {
        TrxTermination termination = trxTerminationRepository.findById(header.getTrxId())
                .orElse(null);
        if (termination == null) {
            log.error("Could not find termination request with ID: {} for Kafka event", header.getTrxId());
            return CompletableFuture.completedFuture(null);
        }

        // Get next approver user IDs
        List<String> nextApproverUserIds = new ArrayList<>();

        // If status is MENUNGGU_PERSETUJUAN and we have recipients, use their user IDs
        if (status == ApprovalStatus.MENUNGGU_PERSETUJUAN && recipients != null && !recipients.isEmpty()) {
            // Extract user IDs from recipients
            nextApproverUserIds = recipients.stream()
                    .map(User::getUsername)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("Using {} recipients as next approvers", nextApproverUserIds.size());
        }

        // If no recipients provided, try to determine from the approval header
        else if (status == ApprovalStatus.MENUNGGU_PERSETUJUAN && header.getApproverRole() != null) {
            // Check if this is an upline approval
            if (header.getApproverRole().startsWith("UPLINE:")) {
                // Extract the upline agent code
                String uplineAgentCode = header.getApproverRole().substring("UPLINE:".length());

                // Find the upline agent and their user
                Agent uplineAgent = agentRepository.findTopByAgentCode(uplineAgentCode).orElse(null);
                if (uplineAgent != null && uplineAgent.getUser() != null) {
                    User uplineUser = uplineAgent.getUser();
                    nextApproverUserIds.add(uplineUser.getUsername());
                }
            }
            // Standard role-based approval
            else {
                // For role-based approvals, find all users with the role
                List<User> usersWithRole = userRepository.findUsersByRoleCode(header.getApproverRole());
                if (usersWithRole != null && !usersWithRole.isEmpty()) {
                    // Extract user IDs from users with the role
                    nextApproverUserIds = usersWithRole.stream()
                            .map(User::getUsername)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    log.info("Found {} users with role {}", usersWithRole.size(), header.getApproverRole());
                } else {
                    log.warn("No users found with role {}", header.getApproverRole());
                }
            }
        }

        // Create termination event
        String eventId = UUID.randomUUID().toString();
        AgentTerminationEvent event = AgentTerminationEvent.builder()
                .eventId(eventId)
                .terminationId(termination.getId())
                .targetAgentCode(termination.getTarget().getAgent().getAgentCode())
                .trxType(header.getTrxType())
                .approvalStatus(status)
                .trxStatus(termination.getStatus())
                .timestamp(Instant.now())
                .nextApproverUserIds(nextApproverUserIds)
                .approvalHeaderId(header.getId())
                .notificationTitle(notification != null ? notification.getTitle() : null)
                .notificationBody(notification != null ? notification.getBody() : null)
                .inboxType(InboxType.INBOX)
                .build();


        log.info("Sending termination event to kafka with id: {}, status: {}", eventId, status);
        CompletableFuture<SendResult<String, Object>> future =
                kafkaTemplate.send(KafkaConfig.TERMINATION_TOPIC, eventId, event);

        future.whenComplete((result, ex) -> {
            if (ex == null) {
                log.info("Sent termination event for ID: {} with offset: {}",
                        eventId, result.getRecordMetadata().offset());
            } else {
                log.error("Unable to send termination event for ID: {} due to: {}",
                        eventId, ex.getMessage(), ex);
            }
        });

        return future;
    }

    private CompletableFuture<SendResult<String, Object>> sendRejoinEvent(
            TrxApprovalHeader header,
            ApprovalStatus status,
            List<User> recipients,
            NotificationDto notification) {
        TrxRejoinApplication application = rejoinApplicationRepository.findById(header.getTrxId())
                .orElse(null);
        if (application == null) {
            log.error("Could not find rejoin application with ID: {} for Kafka event", header.getTrxId());
            return CompletableFuture.completedFuture(null);
        }

        // Get next approver user IDs
        List<String> nextApproverUserIds = new ArrayList<>();

        // If status is MENUNGGU_PERSETUJUAN and we have recipients, use their user IDs
        if ((status == ApprovalStatus.MENUNGGU_PERSETUJUAN || status == ApprovalStatus.BARU)
                && recipients != null && !recipients.isEmpty()) {
            // Extract user IDs from recipients
            nextApproverUserIds = recipients.stream()
                    .map(User::getUsername)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("Using {} recipients as next approvers", nextApproverUserIds.size());
        }

        // If no recipients provided, try to determine from the approval header
        else if (status == ApprovalStatus.MENUNGGU_PERSETUJUAN && header.getApproverRole() != null) {
            // Check if this is an upline approval
            if (header.getApproverRole().startsWith("UPLINE:")) {
                // Extract the upline agent code
                String uplineAgentCode = header.getApproverRole().substring("UPLINE:".length());

                // Find the upline agent and their user
                Agent uplineAgent = agentRepository.findTopByAgentCode(uplineAgentCode).orElse(null);
                if (uplineAgent != null && uplineAgent.getUser() != null) {
                    User uplineUser = uplineAgent.getUser();
                    nextApproverUserIds.add(uplineUser.getUsername());
                }
            }
            // Standard role-based approval
            else {
                // For role-based approvals, find all users with the role
                List<User> usersWithRole = userRepository.findUsersByRoleCode(header.getApproverRole());
                if (usersWithRole != null && !usersWithRole.isEmpty()) {
                    // Extract user IDs from users with the role
                    nextApproverUserIds = usersWithRole.stream()
                            .map(User::getUsername)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    log.info("Found {} users with role {}", usersWithRole.size(), header.getApproverRole());
                } else {
                    log.warn("No users found with role {}", header.getApproverRole());
                }
            }
        }

        // Create rejoin event
        String eventId = UUID.randomUUID().toString();
        AgentReactivationEvent event = AgentReactivationEvent.builder()
                .eventId(eventId)
                .applicationId(application.getId())
                .trxType(header.getTrxType())
                .approvalStatus(status)
                .nextApproverUserIds(nextApproverUserIds)
                .approvalHeaderId(header.getId())
                .notificationTitle(notification != null ? notification.getTitle() : null)
                .notificationBody(notification != null ? notification.getBody() : null)
                .inboxType(InboxType.INBOX)
                .build();


        log.info("Sending rejoin event to kafka with id: {}, status: {}", eventId, status);
        CompletableFuture<SendResult<String, Object>> future =
                kafkaTemplate.send(KafkaConfig.REJOIN_TOPIC, eventId, event);

        future.whenComplete((result, ex) -> {
            if (ex == null) {
                log.info("Sent rejoin event for ID: {} with offset: {}",
                        eventId, result.getRecordMetadata().offset());
            } else {
                log.error("Unable to send rejoin event for ID: {} due to: {}",
                        eventId, ex.getMessage(), ex);
            }
        });

        return future;
    }

    /**
     * Converts an ApprovalStatus to the corresponding TrxStatus
     *
     * @param approvalStatus The approval status
     * @return The corresponding transaction status
     */
    private TrxStatus getTrxStatusFromApprovalStatus(ApprovalStatus approvalStatus) {
        switch (approvalStatus) {
            case DISETUJUI:
                return TrxStatus.COMPLETE;
            case DITOLAK:
                return TrxStatus.REJECTED;
            case TERTUNDA:
                return TrxStatus.DIKEMBALIKAN;
            case DIBATALKAN:
                return TrxStatus.CANCELLED;
            case MENUNGGU_PERSETUJUAN:
            case BARU:
            default:
                return TrxStatus.DRAFT;
        }
    }
}
