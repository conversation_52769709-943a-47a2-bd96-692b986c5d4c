package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.UserType;
import id.co.panindaiichilife.superapp.agent.model.User;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.Collection;


@EqualsAndHashCode(callSuper = true)
@Data
public class UserDto extends BaseDto<User> {

    private Long id;

    private String username;

    private String name;

    private String email;

    private String phone;

    private Channel channel;

    private String agentCode;

    private String agentLevel;

    private String picture;

    private Collection<RoleDto> roles;

    private Collection<BranchDto> branches;

    private User.Status status;

    private String timezone;

    private Boolean isAgent;

    private UserType userType;

    private Instant lastLogin;

    private Instant lastChangePassword;

    private Instant createdAt;

    private Instant updatedAt;

    @Override
    public void copy(User data) {
        super.copy(data);
        roles = BaseDto.of(RoleDto.class, data.getRoles());
        branches = BaseDto.of(BranchDto.class, data.getBranches());

        if (null != data.getIsAgent()) {
            if (data.getIsAgent()) {
                agentCode = data.getAgent().getAgentCode();
                agentLevel = data.getAgent().getLevel();
            }
        }
    }
}
