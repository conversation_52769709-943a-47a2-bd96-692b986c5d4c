package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalBranchResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("mainBranches")
    @JsonProperty("mainBranches")
    private List<MainBranchDto> mainBranches;


    @Data
    public static class MainBranchDto {

        @SerializedName("branchCode")
        @JsonProperty("branchCode")
        private String branchCode;

        @SerializedName("branchName")
        @JsonProperty("branchName")
        private String branchName;

        @SerializedName("bdmCode")
        @JsonProperty("bdmCode")
        private String bdmCode;

        @SerializedName("bdmRegion")
        @JsonProperty("bdmRegion")
        private String bdmRegion;

        @SerializedName("abddCode")
        @JsonProperty("abddCode")
        private String abddCode;

        @SerializedName("abddRegion")
        @JsonProperty("abddRegion")
        private String abddRegion;

        @SerializedName("bddCode")
        @JsonProperty("bddCode")
        private String bddCode;

        @SerializedName("bddRegion")
        @JsonProperty("bddRegion")
        private String bddRegion;

        @SerializedName("hosCode")
        @JsonProperty("hosCode")
        private String hosCode;

        @SerializedName("hosRegion")
        @JsonProperty("hosRegion")
        private String hosRegion;

        @SerializedName("phoneNumber")
        @JsonProperty("phoneNumber")
        private String phoneNumber;

        @SerializedName("address1")
        @JsonProperty("address1")
        private String address1;

        @SerializedName("address2")
        @JsonProperty("address2")
        private String address2;

        @SerializedName("address3")
        @JsonProperty("address3")
        private String address3;

        @SerializedName("city")
        @JsonProperty("city")
        private String city;

        @SerializedName("status")
        @JsonProperty("status")
        private String status;

        @SerializedName("subBranches")
        @JsonProperty("subBranches")
        private List<SubBranchDto> subBranches;
    }

    @Data
    public static class SubBranchDto {

        @SerializedName("branchCode")
        @JsonProperty("branchCode")
        private String branchCode;

        @SerializedName("branchName")
        @JsonProperty("branchName")
        private String branchName;

        @SerializedName("phoneNumber")
        @JsonProperty("phoneNumber")
        private String phoneNumber;

        @SerializedName("address1")
        @JsonProperty("address1")
        private String address1;

        @SerializedName("address2")
        @JsonProperty("address2")
        private String address2;

        @SerializedName("address3")
        @JsonProperty("address3")
        private String address3;

        @SerializedName("city")
        @JsonProperty("city")
        private String city;

        @SerializedName("status")
        @JsonProperty("status")
        private String status;

        @SerializedName("staffCount")
        @JsonProperty("staffCount")
        private int staffCount;
    }
}
