package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class SimpleAgentV2Dto extends BaseDto<Agent> {

    private String agentCode;

    private String agentName;

    private String agentPhoto;

    private String agentLevel;

    @Override
    public void copy(Agent data) {
        agentCode = data.getAgentCode();
        agentName = data.getAgentName();
        agentPhoto = data.getPhoto();
        agentLevel = data.getLevel();
    }

}
