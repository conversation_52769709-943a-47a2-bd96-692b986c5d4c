package id.co.panindaiichilife.superapp.agent.config.apm;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

@Data
@ConfigurationProperties(prefix = "apm.response.capture")
public class ApmResponseCaptureProperties {

    /**
     * Enable or disable APM response capture
     */
    private boolean enabled = true;

    /**
     * Maximum response body size to capture (in bytes)
     */
    private int maxBodySize = 10240; // 10KB default

    /**
     * URL patterns to include for response capture
     */
    private List<String> includePatterns = Arrays.asList("/api/**");

    /**
     * URL patterns to exclude from response capture
     */
    private List<String> excludePatterns = Arrays.asList(
            "/api/health/**",
            "/docs/**",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/actuator/**"
    );

    /**
     * Whether to capture response headers
     */
    private boolean captureHeaders = true;

    /**
     * List of sensitive headers to exclude from capture
     */
    private List<String> sensitiveHeaders = Arrays.asList(
            "authorization",
            "x-api-key",
            "cookie",
            "set-cookie"
    );

    /**
     * Check if a URL path should be captured
     */
    public boolean shouldCapture(String path) {
        if (!enabled) {
            return false;
        }

        // Check exclude patterns first
        for (String excludePattern : excludePatterns) {
            if (matchesPattern(path, excludePattern)) {
                return false;
            }
        }

        // Check include patterns
        for (String includePattern : includePatterns) {
            if (matchesPattern(path, includePattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Simple pattern matching (supports * wildcard)
     */
    private boolean matchesPattern(String path, String pattern) {
        if (pattern.equals("/**")) {
            return true;
        }

        if (pattern.endsWith("/**")) {
            String prefix = pattern.substring(0, pattern.length() - 3);
            return path.startsWith(prefix);
        }

        if (pattern.endsWith("/*")) {
            String prefix = pattern.substring(0, pattern.length() - 2);
            return path.startsWith(prefix) && !path.substring(prefix.length()).contains("/");
        }

        return path.equals(pattern);
    }

    /**
     * Check if a header should be captured
     */
    public boolean shouldCaptureHeader(String headerName) {
        if (!captureHeaders) {
            return false;
        }

        return sensitiveHeaders.stream()
                .noneMatch(sensitive -> sensitive.equalsIgnoreCase(headerName));
    }
}
