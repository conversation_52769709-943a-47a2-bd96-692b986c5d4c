package id.co.panindaiichilife.superapp.agent.service.agency.validator.rejoin;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;

import java.util.concurrent.CompletableFuture;

public interface RejoinValidator {
    boolean canValidate(TrxRejoinApplicationForm form, TrxRejoinApplication entity);

    CompletableFuture<TrxRejoinApplication> validate(TrxRejoinApplicationForm form, TrxRejoinApplication entity);

    String getValidatorName();
}
