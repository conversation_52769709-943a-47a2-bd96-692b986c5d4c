package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * DTO for the response from the agency admin validation API
 */
@Data
public class PortalValidationAdminAgentResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("idNumber")
    @JsonProperty("idNumber")
    private String idNumber;

    @SerializedName("adminName")
    @JsonProperty("adminName")
    private String adminName;

    @SerializedName("position")
    @JsonProperty("position")
    private String position;

    @SerializedName("leaders")
    @JsonProperty("leaders")
    private List<Leader> leaders;

    @SerializedName("ga")
    @JsonProperty("ga")
    private String ga;

    @Data
    public static class Leader {
        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("agentName")
        @JsonProperty("agentName")
        private String agentName;
    }
}
