package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.ValidasiPerHirarkiImportDto;
import id.co.panindaiichilife.superapp.agent.model.ValidasiPerHirarki;
import id.co.panindaiichilife.superapp.agent.repository.ValidasiPerHirarkiRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class ValidasiPerHirarkiImportProcessor implements ItemProcessor<ValidasiPerHirarkiImportDto, ValidasiPerHirarki> {

    private final ValidasiPerHirarkiRepository validasiPerHirarkiRepository;
    
    @Override
    public ValidasiPerHirarki process(ValidasiPerHirarkiImportDto item) {
        return findOrCreateValidasiPerHirarki(item);
    }

    private ValidasiPerHirarki findOrCreateValidasiPerHirarki(ValidasiPerHirarkiImportDto item) {
        ValidasiPerHirarki validasiPerHirarki = new ValidasiPerHirarki();

        // Copy properties
        BeanUtils.copyProperties(item, validasiPerHirarki);
        return validasiPerHirarki;
    }
}