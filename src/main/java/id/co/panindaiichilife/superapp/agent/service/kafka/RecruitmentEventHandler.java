package id.co.panindaiichilife.superapp.agent.service.kafka;

import com.sendgrid.helpers.mail.Mail;
import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.core.service.MailService;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.model.event.RecruitmentStatusEvent;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.ContractDocumentService;
import id.co.panindaiichilife.superapp.agent.service.FirebaseService;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRecruitmentService;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class RecruitmentEventHandler {

    private final MailService mailService;

    private final GlobalConfigService globalConfigService;

    private final FirebaseService firebaseService;

    private final UserRepository userRepository;

    private final TrxRecruitmentService trxRecruitmentService;

    private final ContractDocumentService contractDocumentService;

    /**
     * Handles recruitment events based on their approval status
     *
     * @param event The recruitment event to handle
     */
    public void handleRecruitmentEvent(RecruitmentStatusEvent event) {
        log.info("Handling recruitment event for ID: {}", event.getRecruitmentId());

        try {
            TrxRecruitment trxRecruitment = trxRecruitmentService.updateRecruitmentStatus(event.getRecruitmentId(), event.getApprovalStatus());

            TrxStatus trxStatus = trxRecruitment != null ? trxRecruitment.getTrxStatus() : event.getTrxStatus();
            // First check if we need to notify the next approvers
            if ((event.getApprovalStatus() == ApprovalStatus.BARU ||
                    event.getApprovalStatus() == ApprovalStatus.MENUNGGU_PERSETUJUAN) &&
                    event.getNextApproverUserIds() != null && !event.getNextApproverUserIds().isEmpty()) {
                // Send notification to the next approvers
                log.info("Sending notification to {} next approvers for recruitment ID: {} with status: {}",
                        event.getNextApproverUserIds().size(), event.getRecruitmentId(), event.getApprovalStatus());
                sendNextApproverNotifications(event);
            }

            // Process the event based on the transaction status
            switch (trxStatus) {
                case COMPLETE:
                    // Handle approved recruitment
                    log.info("Processing approved recruitment for ID: {}", event.getRecruitmentId());

                    // Send approval email
                    sendApprovalEmail(event);

                    // Note: Portal system update is now handled synchronously in ApprovalService
                    // Generate contract documents: PKAJ, PMKAJ (only for BM and BD), ANTI TWISTING, and Kode Etik
                    // Find the recruitment to get agent code for document generation
                    TrxRecruitment recruitment = trxRecruitmentService.findById(event.getRecruitmentId());
                    if (recruitment != null && recruitment.getAgentCode() != null) {
                        try {
                            log.info("Generating contract documents for recruitment ID: {} with agent code: {}",
                                    event.getRecruitmentId(), recruitment.getAgentCode());

                            // Generate all contract documents
                            Map<String, String> documentUrls = contractDocumentService.generateAllContractDocuments(recruitment);

                            log.info("Successfully generated {} contract documents for recruitment ID: {}",
                                    documentUrls.size(), event.getRecruitmentId());
                        } catch (Exception e) {
                            log.error("Error generating contract documents for recruitment ID: {}", event.getRecruitmentId(), e);
                        }
                    } else {
                        log.warn("Cannot generate contract documents - recruitment or agent code is null for ID: {}",
                                event.getRecruitmentId());
                    }
                    break;

                case REJECTED:
                    // Handle rejected recruitment
                    log.info("Processing rejected recruitment for ID: {}", event.getRecruitmentId());

                    // Send rejection email
                    sendRejectionEmail(event);
                    break;

                case DIKEMBALIKAN:
                    // Handle pending recruitment
                    log.info("Processing return recruitment for ID: {}", event.getRecruitmentId());

                    // Send return notification
                    sendReturnNotification(event);
                    break;

                case CANCELLED:
                    // Handle cancelled recruitment
                    log.info("Processing cancelled recruitment for ID: {}", event.getRecruitmentId());
                    break;

                case EXPIRED:
                    log.info("Processing draft recruitment for ID: {}", event.getRecruitmentId());
                    break;

                default:
                    log.info("No specific action needed for status: {}", event.getApprovalStatus());
                    break;
            }
        } catch (Exception e) {
            log.error("Error handling recruitment event: {}", e.getMessage(), e);
        }
    }


    /**
     * Sends an approval email to the candidate
     *
     * @param event The recruitment status event
     */
    private void sendApprovalEmail(RecruitmentStatusEvent event) throws MessagingException {
        User recruiter = userRepository.findByUsername(event.getRecruiterCode()).orElse(null);
        if (null != recruiter && recruiter.getEmail() != null && !recruiter.getEmail().isEmpty()) {
            // Get the template ID from global config or use a default
            String templateId = globalConfigService.getGlobalConfig(
                    "mail.recruitment.approved.recruiter.template.id",
                    "d-recruitment-approved-template");

            // Prepare the data for the template
            Map<String, Object> data = new HashMap<>();
            data.put("recruiter_name", recruiter.getName());
            data.put("candidate_name", event.getCandidateName());
            data.put("agent_code", event.getAgentCode());

            // Create and send the email
            Mail email = mailService.createEmail(templateId, data, recruiter.getEmail());
            mailService.sendEmail(email);

            log.info("Approval email sent to recruiter: {}", recruiter.getEmail());
        }

        if (recruiter != null) {
            // Send Firebase notification if the user has a device token
            NotificationDto notification = new NotificationDto();
            notification.setTitle(event.getNotificationTitle());
            notification.setBody(event.getNotificationBody());
            notification.setData(Map.of(
                    "approvalId", event.getApprovalHeaderId().toString(),
                    "trxId", event.getRecruitmentId().toString(),
                    "trxUuId", event.getRecruitmentUuId(),
                    "trxType", event.getTrxType().name(),
                    "status", event.getApprovalStatus().name()
            ));
            notification.setInboxType(event.getInboxType());

            firebaseService.sendNotification(List.of(recruiter), notification);

            log.info("Approved request notification sent to recruiter: {}", recruiter.getUsername());
        } else {
            log.warn("Could not find recruiter with ID: {} ", recruiter.getId());
        }
    }

    /**
     * Sends a rejection email to the candidate
     *
     * @param event The recruitment status event
     */
    private void sendRejectionEmail(RecruitmentStatusEvent event) throws MessagingException {
        User recruiter = userRepository.findByUsername(event.getRecruiterCode()).orElse(null);
        if (null != recruiter && recruiter.getEmail() != null && !recruiter.getEmail().isEmpty()) {
            // Get the template ID from global config or use a default
            String templateId = globalConfigService.getGlobalConfig(
                    "mail.recruitment.rejected.recruiter.template.id",
                    "d-recruitment-rejected-template");

            // Prepare the data for the template
            Map<String, Object> data = new HashMap<>();
            data.put("recruiter_name", recruiter.getName());
            data.put("candidate_name", event.getCandidateName());
            data.put("reviewer", event.getReviewer());
            data.put("notes", event.getNotes());

            // Create and send the email
            Mail email = mailService.createEmail(templateId, data, recruiter.getEmail());
            mailService.sendEmail(email);

            log.info("Rejection email sent to recruiter: {}", recruiter.getEmail());
        }
        if (recruiter != null) {
            // Send Firebase notification if the user has a device token
            NotificationDto notification = new NotificationDto();
            notification.setTitle(event.getNotificationTitle());
            notification.setBody(event.getNotificationBody());
            notification.setData(Map.of(
                    "approvalId", event.getApprovalHeaderId().toString(),
                    "trxId", event.getRecruitmentId().toString(),
                    "trxUuId", event.getRecruitmentUuId(),
                    "trxType", event.getTrxType().name(),
                    "status", event.getApprovalStatus().name()
            ));
            notification.setInboxType(event.getInboxType());

            firebaseService.sendNotification(List.of(recruiter), notification);

            log.info("Rejection request notification sent to recruiter: {}", recruiter.getUsername());
        } else {
            log.warn("Could not find recruiter with ID: {} ", recruiter.getId());
        }
    }

    /**
     * Sends a pending notification to the recruiter
     *
     * @param event The recruitment status event
     */
    private void sendReturnNotification(RecruitmentStatusEvent event) throws MessagingException {
        User recruiter = userRepository.findByUsername(event.getRecruiterCode()).orElse(null);
        if (null != recruiter && recruiter.getEmail() != null && !recruiter.getEmail().isEmpty()) {
            // Get the template ID from global config or use a default
            String templateId = globalConfigService.getGlobalConfig(
                    "mail.recruitment.return.recruiter.template.id",
                    "d-recruitment-return-template");

            // Prepare the data for the template
            Map<String, Object> data = new HashMap<>();
            data.put("recruiter_name", recruiter.getName());
            data.put("candidate_name", event.getCandidateName());
            data.put("reviewer", event.getReviewer());
            data.put("notes", event.getNotes());

            // Create and send the email
            Mail email = mailService.createEmail(templateId, data, recruiter.getEmail());
            mailService.sendEmail(email);

            log.info("Rejection email sent to recruiter: {}", recruiter.getEmail());
        }

        if (recruiter != null) {
            // Send Firebase notification if the user has a device token
            NotificationDto notification = new NotificationDto();
            notification.setTitle(event.getNotificationTitle());
            notification.setBody(event.getNotificationBody());
            notification.setData(Map.of(
                    "approvalId", event.getApprovalHeaderId().toString(),
                    "trxId", event.getRecruitmentId().toString(),
                    "trxUuId", event.getRecruitmentUuId(),
                    "trxType", event.getTrxType().name(),
                    "status", event.getApprovalStatus().name()
            ));
            notification.setInboxType(event.getInboxType());

            firebaseService.sendNotification(List.of(recruiter), notification);

            log.info("Return request notification sent to recruiter: {} ({})", recruiter.getUsername(), recruiter.getEmail());
        } else {
            log.warn("Could not find recruiter with ID: {} ", recruiter.getId());
        }
    }

    /**
     * Sends notifications to the next approvers in the approval chain
     *
     * @param event The recruitment status event
     */
    private void sendNextApproverNotifications(RecruitmentStatusEvent event) throws MessagingException {
        // Get the list of next approver user IDs
        List<String> approverUserIds = event.getNextApproverUserIds();

        if (approverUserIds != null && !approverUserIds.isEmpty()) {
            // For each approver user ID
            for (String userId : approverUserIds) {
                // Find the user by username
                User approver = userRepository.findByUsername(userId).orElse(null);

                if (approver != null) {
                    // Send Firebase notification if the user has a device token
                    NotificationDto notification = new NotificationDto();
                    notification.setTitle(event.getNotificationTitle());
                    notification.setBody(event.getNotificationBody());
                    notification.setData(Map.of(
                            "approvalId", event.getApprovalHeaderId().toString(),
                            "trxId", event.getRecruitmentId().toString(),
                            "trxUuId", event.getRecruitmentUuId(),
                            "trxType", event.getTrxType().name(),
                            "status", event.getApprovalStatus().name()
                    ));
                    notification.setInboxType(event.getInboxType());

                    firebaseService.sendNotification(List.of(approver), notification);

                    log.info("Approval request notification sent to approver: {} ({})", approver.getUsername(), approver.getEmail());
                } else {
                    log.warn("Could not find approver with ID: {} or email is missing", userId);
                }
            }
        } else {
            log.warn("No next approver user IDs found for recruitment ID: {}", event.getRecruitmentId());
        }
    }


}
