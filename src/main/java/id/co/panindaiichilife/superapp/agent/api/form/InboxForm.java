package id.co.panindaiichilife.superapp.agent.api.form;

import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class InboxForm {

    @NotNull
    private TrxType trxType;

    @NotNull
    private InboxType inboxType;

    private Long trxId;
    
    private Long approvalId;

    @NotBlank
    private String title;

    @NotBlank
    private String body;

    private String footer;

    private Boolean isArchived = Boolean.FALSE;

    private Boolean isRead = Boolean.FALSE;
}
