package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.BranchDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BranchFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.BranchForm;
import id.co.panindaiichilife.superapp.agent.service.BranchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController("cmsBranchController")
@RequestMapping("/api/cms/branch")
@Tag(name = "Branch - CMS", description = "API CMS Branch")
@Slf4j
@RequiredArgsConstructor
public class BranchCmsController {

    private final BranchService branchService;

    @Operation(summary = "List branches")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Branch', 'view')")
    public Page<BranchDto> index(@ParameterObject @ModelAttribute("filter") BranchFilter filter,
                                 @ParameterObject @PageableDefault(sort = "createdAt") Pageable pageable) {
        return branchService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific branch")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Branch', 'view')")
    public BranchDto view(@PathVariable long id) {
        return branchService.findOne(id);
    }

    @Operation(summary = "Add new branch")
    @PostMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Branch', 'add')")
    public BranchDto insert(@Valid @RequestBody BranchForm branchForm) {
        return branchService.add(branchForm);
    }

    @Operation(summary = "Modify existing branch")
    @PutMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Branch', 'edit')")
    public BranchDto edit(@PathVariable long id,
                          @Valid @RequestBody BranchForm branchForm) {
        return branchService.update(id, branchForm);
    }

    @Operation(summary = "Delete existing branch")
    @DeleteMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Branch', 'delete')")
    public void delete(@PathVariable long id) {
        branchService.delete(id);
    }

    @Operation(summary = "Run Sync Branch from API")
    @GetMapping(value = "sync")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Branch', 'sync')")
    public void syncBranch() {
        branchService.syncBranch();
    }

    @Operation(summary = "Get List City Branch")
    @GetMapping(value = "city")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Branch', 'view')")
    public List<String> getCity() {
        return branchService.getCity();
    }
}
