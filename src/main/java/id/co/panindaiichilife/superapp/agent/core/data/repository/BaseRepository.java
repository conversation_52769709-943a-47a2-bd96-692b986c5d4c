package id.co.panindaiichilife.superapp.agent.core.data.repository;

import jakarta.persistence.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.Optional;
import java.util.function.Consumer;

@NoRepositoryBean
public interface BaseRepository<E, I extends Serializable>
        extends JpaRepository<E, I>, JpaSpecificationExecutor<E> {

    /**
     * @deprecated Replaced by findById
     */
    @Deprecated(since = "1.0", forRemoval = true)
    E findOne(I id);

    Optional<E> findByIdThenInitialize(I id, String... attributes);

    Optional<E> findByIdThenInitialize(I id, Consumer<EntityGraph<E>> consumer);

    E findUnique(String field, Object value);

    boolean exists(String field, Object value);

    boolean exists(String field, Object value, I excludeId);

    void refresh(E entity);

    /**
     * @deprecated Replaced by deleteById
     */
    @Deprecated(since = "1.0", forRemoval = true)
    void delete(I id);
}
