package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "persistency")
@Data
@ToString(of = {"id", "agentCode"})
public class Persistency {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "persistency_id_seq")
    @SequenceGenerator(name = "persistency_id_seq", sequenceName = "persistency_id_seq", allocationSize = 1)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    private DistributionCode distributionCode;

    private String agentCode;

    private String branchCode;

    private String mainBranchCode;

    private String bdmCode;

    private String bdmName;

    private String abddCode;

    private String abddName;

    private String bddCode;

    private String bddName;

    private String hosCode;

    private String hosName;

    private Integer year;

    private String type;

    private String persistencyType;

    private Double persistency;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
