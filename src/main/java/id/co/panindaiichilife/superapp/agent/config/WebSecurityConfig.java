package id.co.panindaiichilife.superapp.agent.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String[] AUTH_WHITELIST = {
            "/sys/forgetPassword/**",
            // swagger ui
            "/",
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/swagger-resources/**",
            "/v2/api-docs",
            "/v3/api-docs/**",
            "/webjars/**",
            // OAuth2 Endpoints
            "/oauth2/**",
            "/login/oauth2/**",
            "/oauth/token",
            "/oauth/check_token",
            "/oauth/jwks",
            "/api/public/**",
            "/api/cms/auth/login",
            "/api/auth/check-access",
            "/api/auth/check-user-channel",
            "/api/auth/forget-password",
            "/api/auth/reset-password/**",
            "/api/auth/login"
    };
    @Qualifier("customAuthenticationProvider")
    private final AuthenticationProvider customAuthenticationProvider;
    private final JwtDecoder jwtDecoder;
    private final SecurityErrorHandler securityErrorHandler;


    @Bean
    @Order(1)
    public SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity http) throws Exception {
        OAuth2AuthorizationServerConfigurer authorizationServerConfigurer = new OAuth2AuthorizationServerConfigurer();

        // Apply default OAuth2AuthorizationServerConfigurer settings
        http.apply(authorizationServerConfigurer);

        http.authenticationProvider(customAuthenticationProvider);

        // Configure token endpoint and link to authentication provider
        authorizationServerConfigurer.tokenEndpoint(token ->
                token.accessTokenRequestConverter(new PasswordGrantAuthenticationConverter()));
        http
                .securityMatcher(authorizationServerConfigurer.getEndpointsMatcher())
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(AUTH_WHITELIST).permitAll()
                )
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(exception -> exception
                        .authenticationEntryPoint(securityErrorHandler.customAuthenticationEntryPoint())
                        .accessDeniedHandler(securityErrorHandler.customAccessDeniedHandler()));
        return http.build();
    }

    // Security configuration for API requests
    @Bean
    @Order(2)
    public SecurityFilterChain apiSecurityFilterChain(HttpSecurity http) throws Exception {
        http
                .securityMatcher("/api/**")
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(HttpMethod.OPTIONS, "/api/**").permitAll()  // Allow all OPTIONS requests
                        .requestMatchers(AUTH_WHITELIST).permitAll()  // Adjust whitelist paths as needed
                        .anyRequest().authenticated()  // Require authentication for all other API paths
                )
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS)) // Stateless session
                .csrf(AbstractHttpConfigurer::disable)
                .oauth2ResourceServer(oauth2 ->
                        oauth2.jwt(jwt ->
                                jwt.decoder(jwtDecoder) // Explicitly set the JwtDecoder
                        )
                )// Enable JWT validation
                .exceptionHandling(exception -> exception
                        .authenticationEntryPoint(securityErrorHandler.customAuthenticationEntryPoint())
                        .accessDeniedHandler(securityErrorHandler.customAccessDeniedHandler())
                );
        return http.build();
    }

    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverter() {
        JwtAuthenticationConverter converter = new JwtAuthenticationConverter();
        converter.setJwtGrantedAuthoritiesConverter(new CustomJwtAuthenticationConverter());
        return converter;
    }
}
