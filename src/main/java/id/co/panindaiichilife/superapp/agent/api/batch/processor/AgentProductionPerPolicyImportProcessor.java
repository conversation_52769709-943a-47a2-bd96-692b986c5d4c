package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.AgentProductionPerPolicyImportDto;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.AgentProductionPerPolicy;
import id.co.panindaiichilife.superapp.agent.repository.AgentProductionPerPolicyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class AgentProductionPerPolicyImportProcessor implements ItemProcessor<AgentProductionPerPolicyImportDto, AgentProductionPerPolicy> {

    private final AgentProductionPerPolicyRepository agentProductionPerPolicyRepository;
    
    @Override
    public AgentProductionPerPolicy process(AgentProductionPerPolicyImportDto item) {
        return findOrCreateAgentProductionPerPolicy(item);
    }

    private AgentProductionPerPolicy findOrCreateAgentProductionPerPolicy(AgentProductionPerPolicyImportDto item) {
        AgentProductionPerPolicy agentProductionPerPolicy = new AgentProductionPerPolicy();

        BeanUtils.copyProperties(item, agentProductionPerPolicy);
        agentProductionPerPolicy.setDistributionCode(DistributionCode.valueOf(item.getDistributionCode()));
        return agentProductionPerPolicy;
    }
}