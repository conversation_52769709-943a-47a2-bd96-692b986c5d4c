package id.co.panindaiichilife.superapp.agent.api.dto.agency;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.PolicyTransferStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.TrxPolicyTransfer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

@EqualsAndHashCode(callSuper = true)
@Data
public class TrxPolicyTransferDto extends BaseDto<TrxPolicyTransfer> {
    private String assignedTo;
    private String assignedAgentCode;
    private String assignedAgentPicture;
    private String level;
    private String sourceAgentCode;
    private String sourceAgentName;
    private String sourceAgentPicture;
    private String sourceAgentLevel;
    private String branchCode;
    private String branchName;
    private Long totalPolicies;

    private String submittedBy;
    private String submitterAgentCode;
    private PolicyTransferStatus status;
    private Instant requestedAt;

    private Long trxTerminationId;

    @Override
    public void copy(TrxPolicyTransfer data) {
        super.copy(data);

        Agent target = data.getTarget().getAgent();
        Agent sourceAgent = data.getSource().getAgent();

        this.assignedTo = target.getAgentName();
        this.assignedAgentCode = target.getAgentCode();
        this.level = target.getLevel();
        this.assignedAgentPicture = target.getPhoto();

        this.sourceAgentCode = sourceAgent.getAgentCode();
        this.sourceAgentName = sourceAgent.getAgentName();
        this.sourceAgentLevel = sourceAgent.getLevel();
        this.sourceAgentPicture = sourceAgent.getPhoto();

        this.submittedBy = data.getAssignedBy().getName();
        this.submitterAgentCode = data.getAssignedBy().getAgent().getAgentCode();
        this.status = data.getStatus();

        this.requestedAt = data.getCreatedAt();
    }
}
