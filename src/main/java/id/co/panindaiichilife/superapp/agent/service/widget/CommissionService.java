package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.CommissionDetailDto;
import id.co.panindaiichilife.superapp.agent.api.dto.widget.CommissionDto;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.AllowanceRepository;
import id.co.panindaiichilife.superapp.agent.repository.CommissionCompensationRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;
import java.time.format.TextStyle;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class CommissionService {
    // Commission type constants
    private static final String COMMISSION_TYPE = "Commission";
    private static final String OVERRIDING_TYPE = "Overriding";
    private static final String BONUS_TYPE = "Bonus";
    private static final String REFERENSI_AGENT_TYPE = "ReferensiAgent";

    // Commission type array
    private static final String[] COMMISSION_TYPES = {COMMISSION_TYPE, OVERRIDING_TYPE, BONUS_TYPE, REFERENSI_AGENT_TYPE};

    private final UserRepository userRepository;
    private final AgentRepository agentRepository;
    private final CommissionCompensationRepository commissionCompensationRepository;
    private final AllowanceRepository allowanceRepository;

    @CacheableWithTTL(cacheName = "commissionCache",
            key = "#username + ':' + " +
                    "#month + ':' + " +
                    "#year",
            ttl = 1200, db = 7)
    public CommissionDto getCommissionAgent(String username, Integer year, Integer month) {
        if (month == null || year == null) {
            LocalDate now = LocalDate.now();
            month = month != null ? month : now.getMonthValue();
            year = year != null ? year : now.getYear();
        }

        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);
        String agentCode = agent.getAgentCode();

        // Trim -D suffix from agentCode if present
        agentCode = AgentCodeUtil.trimDSuffix(agentCode);

        if (agent.getDistributionCode().equals(DistributionCode.A)) {
            return this.getCommissionSummary(agentCode, year, month);
        } else {
            return this.getAllowanceSummary(agentCode, year, month);
        }
    }

    public CommissionDto getCommissionSummary(String agentCode, Integer year, Integer month) {
        if (month == null || year == null) {
            LocalDate now = LocalDate.now();
            month = month != null ? month : now.getMonthValue();
            year = year != null ? year : now.getYear();
        }

        // Get the period for this agent, year and month
        String periode = commissionCompensationRepository.findPeriodeByAgentAndYearAndMonth(agentCode, year, month);
        if (periode == null) {
            periode = ""; // Handle case where no data exists
        }

        CommissionDto response = new CommissionDto();
        response.setYear(year);
        response.setMonth(month);
        response.setPeriode(periode);
        response.setPeriodDate(formatPeriodDate(periode, month, year));

        List<CommissionDto.CommissionType> commissions = new ArrayList<>();
        double totalCommission = 0.0;

        // Get amounts for each commission type
        Map<String, String> typeNames = getCommissionTypeNames();

        for (Map.Entry<String, String> entry : typeNames.entrySet()) {
            String type = entry.getKey();
            String displayName = entry.getValue();

            Double amount = commissionCompensationRepository.sumAmountByPeriodeAndType(agentCode, year, month, type);
            if (amount == null) {
                amount = 0.0;
            }

            CommissionDto.CommissionType commission = new CommissionDto.CommissionType();
            commission.setName(displayName);
            commission.setAmount(amount);
            commissions.add(commission);

            totalCommission += amount;
        }

        response.setCommissions(commissions);
        response.setTotalCommission(totalCommission);

        return response;
    }

    public CommissionDto getAllowanceSummary(String agentCode, Integer year, Integer month) {
        // Get the period for this agent, year and month
        String periode = allowanceRepository.findPeriodeByAgentAndYearAndMonth(agentCode, year, month);
        if (periode == null) {
            periode = ""; // Handle case where no data exists
        }

        CommissionDto response = new CommissionDto();
        response.setYear(year);
        response.setMonth(month);
        response.setPeriode(periode);
        response.setPeriodDate(formatPeriodDate(periode, month, year));

        List<CommissionDto.CommissionType> commissions = new ArrayList<>();
        double totalCommission = 0.0;

        // Get amounts for each commission type
        Map<String, String> typeNames = getCommissionTypeNames();

        for (Map.Entry<String, String> entry : typeNames.entrySet()) {
            String type = entry.getKey();
            String displayName = entry.getValue();

            Double amount = allowanceRepository.sumAmountByPeriodeAndType(agentCode, year, month, type);
            if (amount == null) {
                amount = 0.0;
            }

            CommissionDto.CommissionType commission = new CommissionDto.CommissionType();
            commission.setName(displayName);
            commission.setAmount(amount);
            commissions.add(commission);

            totalCommission += amount;
        }

        response.setCommissions(commissions);
        response.setTotalCommission(totalCommission);

        return response;
    }

    @CacheableWithTTL(cacheName = "commissionDetailCache",
            key = "#username + ':' + " +
                    "#month + ':' + " +
                    "#year",
            ttl = 1200, db = 7)
    public CommissionDetailDto getCommissionDetail(String username, Integer year, Integer month) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);
        String agentCode = agent.getAgentCode();
        if (month == null || year == null) {
            LocalDate now = LocalDate.now();
            month = month != null ? month : now.getMonthValue();
            year = year != null ? year : now.getYear();
        }

        // Get the period for this agent, year and month
        String periode = commissionCompensationRepository.findPeriodeByAgentAndYearAndMonth(agentCode, year, month);
        if (periode == null) {
            periode = ""; // Handle case where no data exists
        }

        CommissionDetailDto response = new CommissionDetailDto();
        response.setYear(year);
        response.setMonth(month);
        response.setPeriod(periode);
        response.setPeriodDate(formatPeriodDate(periode, month, year));

        // Process policy year data
        PolicyYearData policyYearData = processPolicyYearData(agentCode, year, month);
        Double total = policyYearData.getTotal();
        Map<Integer, Long> amountsByYear = policyYearData.getAmountsByYear();

        // Process commission type data by policy year
        Map<String, Map<Integer, Long>> typeAmountsByYear = processCommissionTypeData(agentCode, year, month);

        // Create the list of policy year details
        List<CommissionDetailDto.PolicyYearDetail> details = new ArrayList<>();
        for (int i = 1; i <= 7; i++) {
            CommissionDetailDto.PolicyYearDetail detail = new CommissionDetailDto.PolicyYearDetail();
            detail.setYear(i == 7 ? "Y-7+" : "Y-" + i);
            detail.setAmount(amountsByYear.get(i));
            detail.setCommission(typeAmountsByYear.get(COMMISSION_TYPE).get(i));
            detail.setOverriding(typeAmountsByYear.get(OVERRIDING_TYPE).get(i));
            detail.setBonus(typeAmountsByYear.get(BONUS_TYPE).get(i));
            detail.setReferensiAgent(typeAmountsByYear.get(REFERENSI_AGENT_TYPE).get(i));
            details.add(detail);
        }

        // Calculate proportions
        for (CommissionDetailDto.PolicyYearDetail detail : details) {
            if (total > 0.0) {
                Double proportion = (detail.getAmount() * 100.0) / total;
                // Round to 2 decimal places
                proportion = Math.round(proportion * 100.0) / 100.0;
                detail.setProportion(proportion);
            } else {
                detail.setProportion(0.0);
            }
        }

        response.setDetails(details);
        response.setTotal(total);

        return response;
    }

    /**
     * Consolidates policy years 7 and above into a single "Y-7+" entry
     */
    private void consolidateHighPolicyYears(List<CommissionDetailDto.PolicyYearDetail> details) {
        Long consolidatedAmount = 0l;
        List<CommissionDetailDto.PolicyYearDetail> toRemove = new ArrayList<>();

        for (CommissionDetailDto.PolicyYearDetail detail : details) {
            String year = detail.getYear();
            if (year.startsWith("Y-")) {
                try {
                    int yearNum = Integer.parseInt(year.substring(2));
                    if (yearNum >= 7) {
                        consolidatedAmount += detail.getAmount();
                        toRemove.add(detail);
                    }
                } catch (NumberFormatException e) {
                    // Skip if not a valid number
                }
            }
        }

        // Remove the individual entries for years 7+
        details.removeAll(toRemove);

        // Add the consolidated entry if there are any high policy years
        if (consolidatedAmount.compareTo(0l) > 0) {
            CommissionDetailDto.PolicyYearDetail consolidated = new CommissionDetailDto.PolicyYearDetail();
            consolidated.setYear("Y-7+");
            consolidated.setAmount(consolidatedAmount);
            details.add(consolidated);
        }
    }

    private String getCurrentDate() {
        LocalDate now = LocalDate.now();
        return now.getDayOfMonth() + " " + getMonthName(now.getMonthValue()) + " " + now.getYear();
    }

    private String getMonthName(int month) {
        return Month.of(month).getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
    }

    /**
     * Formats the period date based on P1/P2 logic
     * P1: 01-15 [Month] [Year]
     * P2: 16-[Last day of month] [Month] [Year]
     */
    private String formatPeriodDate(String periode, Integer month, Integer year) {
        if (periode == null || periode.isEmpty()) {
            return "";
        }

        String monthName = getIndonesianMonthName(month);

        if ("P1".equals(periode)) {
            return String.format("01-15 %s %d", monthName, year);
        } else if ("P2".equals(periode)) {
            LocalDate lastDayOfMonth = LocalDate.of(year, month, 1).withDayOfMonth(
                    LocalDate.of(year, month, 1).lengthOfMonth()
            );
            return String.format("16-%02d %s %d", lastDayOfMonth.getDayOfMonth(), monthName, year);
        }

        return "";
    }

    /**
     * Gets Indonesian month name in MMM format
     */
    private String getIndonesianMonthName(int month) {
        return switch (month) {
            case 1 -> "Jan";
            case 2 -> "Feb";
            case 3 -> "Mar";
            case 4 -> "Apr";
            case 5 -> "Mei";
            case 6 -> "Jun";
            case 7 -> "Jul";
            case 8 -> "Agu";
            case 9 -> "Sep";
            case 10 -> "Okt";
            case 11 -> "Nov";
            case 12 -> "Des";
            default -> "";
        };
    }

    /**
     * Gets the commission type mappings for display names
     */
    private Map<String, String> getCommissionTypeNames() {
        Map<String, String> typeNames = new LinkedHashMap<>();
        typeNames.put(COMMISSION_TYPE, "Komisi Dasar");
        typeNames.put(OVERRIDING_TYPE, "Override");
        typeNames.put(BONUS_TYPE, "Bonus Generasi");
        typeNames.put(REFERENSI_AGENT_TYPE, "Bonus Referensi Agen");
        return typeNames;
    }

    /**
     * Processes policy year data and returns total amount and amounts by year
     */
    private PolicyYearData processPolicyYearData(String agentCode, Integer year, Integer month) {
        List<Object[]> results = commissionCompensationRepository.findAmountByAgentAndPolicyYear(agentCode, year, month);
        Double total = 0.0;
        Map<Integer, Long> amountsByYear = new HashMap<>();

        // Initialize all years from 1 to 7+ with 0.0
        for (int i = 1; i <= 7; i++) {
            amountsByYear.put(i, 0L);
        }

        // Fill in the actual data from results
        for (Object[] result : results) {
            Integer policyYear = (Integer) result[0];
            Long amount = ((Number) result[1]).longValue();

            if (policyYear >= 7) {
                // Add to the Y-7+ bucket
                amountsByYear.compute(7, (k, currentAmount) -> currentAmount + amount);
            } else {
                // Add to the specific year bucket
                amountsByYear.put(policyYear, amount);
            }

            total += amount;
        }

        return new PolicyYearData(total, amountsByYear);
    }

    /**
     * Processes commission type data by policy year
     */
    private Map<String, Map<Integer, Long>> processCommissionTypeData(String agentCode, Integer year, Integer month) {
        Map<String, Map<Integer, Long>> typeAmountsByYear = new HashMap<>();

        for (String type : COMMISSION_TYPES) {
            List<Object[]> typeResults = commissionCompensationRepository.findAmountByAgentAndPolicyYearAndType(agentCode, year, month, type);
            Map<Integer, Long> typeAmounts = new HashMap<>();

            // Initialize all years from 1 to 7+ with 0.0 for this type
            for (int i = 1; i <= 7; i++) {
                typeAmounts.put(i, 0L);
            }

            // Fill in the data for this type
            for (Object[] result : typeResults) {
                Integer policyYear = (Integer) result[0];
                Long amount = ((Number) result[1]).longValue();

                if (policyYear >= 7) {
                    // Add to the Y-7+ bucket
                    typeAmounts.compute(7, (k, currentAmount) -> currentAmount + amount);
                } else {
                    // Add to the specific year bucket
                    typeAmounts.put(policyYear, amount);
                }
            }

            typeAmountsByYear.put(type, typeAmounts);
        }

        return typeAmountsByYear;
    }

    /**
     * Helper class to hold policy year data
     */
    private static class PolicyYearData {
        private final Double total;
        private final Map<Integer, Long> amountsByYear;

        public PolicyYearData(Double total, Map<Integer, Long> amountsByYear) {
            this.total = total;
            this.amountsByYear = amountsByYear;
        }

        public Double getTotal() {
            return total;
        }

        public Map<Integer, Long> getAmountsByYear() {
            return amountsByYear;
        }
    }
}
