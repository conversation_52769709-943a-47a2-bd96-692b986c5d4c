package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.ValidasiPerG1;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ValidasiPerG1Repository extends BaseRepository<ValidasiPerG1, Long> {

    // Find by agent code
    ValidasiPerG1 findByAgentCode(String agentCode);

    // Find by branch code
    List<ValidasiPerG1> findByBranchCode(String branchCode);

    // Find by BDM code
    List<ValidasiPerG1> findByBdmCode(String bdmCode);

    // Find by HOS code
    List<ValidasiPerG1> findByHosCode(String hosCode);

    @Modifying
    @Transactional
    @Query("DELETE FROM ValidasiPerG1 v WHERE v.year = :year")
    void deleteByYear(@Param("year") Integer year);

    long countByYear(Integer year);

}
