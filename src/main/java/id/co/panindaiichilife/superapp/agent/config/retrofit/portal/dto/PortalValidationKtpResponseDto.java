package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * Response DTO for KTP validation with QuantumX integration
 */
@Data
public class PortalValidationKtpResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("code")
    @JsonProperty("code")
    private String code;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("extra")
    @JsonProperty("extra")
    private Object extra;

    @SerializedName("transactionId")
    @JsonProperty("transactionId")
    private String transactionId;

    @SerializedName("pricingStrategy")
    @JsonProperty("pricingStrategy")
    private String pricingStrategy;

    @SerializedName("data")
    @JsonProperty("data")
    private KtpData data;

    @Data
    public static class KtpData {
        @SerializedName("idNumber")
        @JsonProperty("idNumber")
        private String idNumber;

        @SerializedName("name")
        @JsonProperty("name")
        private String name;

        @SerializedName("birthDate")
        @JsonProperty("birthDate")
        private String birthDate;

        @SerializedName("facePercent")
        @JsonProperty("facePercent")
        private String facePercent;

        @SerializedName("faceResult")
        @JsonProperty("faceResult")
        private String faceResult;
    }
}
