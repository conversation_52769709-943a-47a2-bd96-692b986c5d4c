package id.co.panindaiichilife.superapp.agent;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepositoryImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@ComponentScan(basePackages = {"id.co.panindaiichilife.superapp.agent"})
@EnableJpaRepositories(
        basePackages = {"id.co.panindaiichilife.superapp.agent"},
        repositoryBaseClass = BaseRepositoryImpl.class
)
@EnableAspectJAutoProxy
public class SuperAppAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(SuperAppAgentApplication.class, args);
    }

}
