package id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates;

public final class RejoinNotificationTemplates {
    public static final class Inbox {
        public static final String TARGET_REJOIN_REQUEST_REJECTED = "Pengajuan Anda untuk bergabung kembali ditolak " +
                "oleh %s dengan alasan %s.";
        public static final String REQUESTER_REJOIN_REQUEST_APPROVED = "Pengajuan rejoin untuk agen %s dengan kode " +
                "agen %s telah disetujui.";
        public static final String REQUESTER_REJOIN_REQUEST_REJECTED = "Pengajuan rejoin untuk agen %s telah ditolak " +
                "oleh %s dengan alasan %s.";
        public static final String REQUESTER_REJOIN_REQUEST_RETURNED = "Pengajuan rejoin untuk agen %s telah " +
                "dikembalikan dengan alasan %s, harap memperbaiki data pengajuan dan submit kembali.";
    }

    public static final class Email {
        public static final class Keys {
            public static final String NEW_SUBMISSION_TO_CAS_REVIEWER =
                    "mail.rejoin.submission.to.cas_reviewer.template.id";
            public static final String TO_REJOINED_AGENT_APPROVED_BY_CAS =
                    "mail.rejoin.approved_by_cas.to.target.template.id";
            public static final String TO_REJOINED_AGENT_REJECTED_BY_CAS =
                    "mail.rejoin.rejected_by_cas.to.target.template.id";
            public static final String TO_BDM_ABDD_BDD_APPROVED_BY_CAS =
                    "mail.rejoin.approved_by_cas.to.bdm_abdd_bdd.template.id";
            public static final String TO_BDM_ABDD_BDD_REJECTED_BY_CAS =
                    "mail.rejoin.rejected_by_cas.to.bdm_abdd_bdd.template.id";
        }
    }
}
