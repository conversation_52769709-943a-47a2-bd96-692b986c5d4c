package id.co.panindaiichilife.superapp.agent.model;


import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "trx_approval_headers")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@SQLDelete(sql = "UPDATE trx_approval_headers SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class TrxApprovalHeader extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "trx_approval_headers_id_seq")
    @SequenceGenerator(name = "trx_approval_headers_id_seq", sequenceName = "trx_approval_headers_id_seq", allocationSize = 1)
    private Long id;

    @Column(name = "request_id", nullable = false, length = 36)
    private String requestId = UUID.randomUUID().toString();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "request_by_id")
    private User requestBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "branch")
    private Branch branch;

    @Audited
    @Enumerated(EnumType.STRING)
    private TrxType trxType;

    @Audited
    private Long trxId;

    @Audited
    @Enumerated(EnumType.STRING)
    private ApprovalStatus approvalStatus;

    @Column(nullable = false)
    private Integer currentLevel = 1;

    @Column(nullable = false)
    private Integer maxLevel = 1;

    @Audited
    @Column(columnDefinition = "text")
    private String detailApproval;

    @Column(columnDefinition = "TEXT")
    private String remarks;

    @Audited
    @Column(nullable = false)
    private String approverRole;

    @Audited
    private String lastApproverRole;

    @Audited
    private Integer lastLevel;

    @Audited
    @Enumerated(EnumType.STRING)
    private Channel channel;

    @Audited
    private Boolean isPublic = Boolean.FALSE;

    @OneToMany(mappedBy = "approvalHeader", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("createdAt")
    private Set<TrxApprovalDetail> approvalDetails = new HashSet<>();

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;
}
