package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * DTO for bank account validation with QuantumX integration
 */
@Data
public class PortalValidationBankAccountDto {
    @SerializedName("trxNum")
    @JsonProperty("trxNum")
    private String trxNum;

    @SerializedName("bankCode")
    @JsonProperty("bankCode")
    private String bankCode;

    @SerializedName("bankAccount")
    @JsonProperty("bankAccount")
    private String bankAccount;

    @SerializedName("caller")
    @JsonProperty("caller")
    private String caller = "QuantumX-UAT";
}
