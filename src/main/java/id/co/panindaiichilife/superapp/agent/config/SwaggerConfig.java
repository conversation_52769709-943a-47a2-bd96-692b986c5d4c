package id.co.panindaiichilife.superapp.agent.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.*;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {
    @Value("${swagger.title}")
    private String title;

    @Value("${swagger.description}")
    private String description;

    @Value("${swagger.version}")
    private String version;

    @Bean
    public OpenAPI customOpenAPI() {
        final String securitySchemeName = "bearerAuth"; // Name of the security scheme

        return new OpenAPI()
                .info(new Info()
                        .title(title)
                        .description(description)
                        .version(version))
                .addSecurityItem(new SecurityRequirement().addList(securitySchemeName)) // Apply security globally
                .components(new Components()
                        .addSchemas("Filter", new ObjectSchema()
                                .addProperty("q", new StringSchema().description("Query string"))
                                .addProperty("cdString", new ArraySchema().items(new StringSchema()).description("CD strings"))
                                .addProperty("filterQuery", new StringSchema().description("Filter query")))
                        .addSchemas("Pageable", new ObjectSchema()
                                .addProperty("page", new IntegerSchema().description("Page number"))
                                .addProperty("size", new IntegerSchema().description("Page size"))
                                .addProperty("sort", new ArraySchema().items(new StringSchema()).description("Sorting criteria")))
                        .addSecuritySchemes(securitySchemeName,
                                new SecurityScheme()
                                        .name(securitySchemeName)
                                        .type(SecurityScheme.Type.HTTP) // Use HTTP type
                                        .scheme("bearer") // Scheme is "bearer"
                                        .bearerFormat("JWT") // Optional: Specify the bearer format (e.g., JWT)
                        )
                );
    }

    @Bean
    public GroupedOpenApi defaultApi() {
        return GroupedOpenApi.builder()
                .group("default")
                .packagesToScan("id.co.panindaiichilife.superapp") // Specify your main package
                .pathsToMatch("/api/**") // Only include paths under /api
                .build();
    }

    @Bean
    public GroupedOpenApi superAppAgentCmsApi() {
        return GroupedOpenApi.builder()
                .group("superapp-agent-cms-api")
                .packagesToScan("id.co.panindaisichlife.superapp.agent.api")
                .pathsToMatch("/api/cms/**") // Only include paths with /api/cms
                .build();
    }

    @Bean
    public GroupedOpenApi superAppAgentApi() {
        return GroupedOpenApi.builder()
                .group("superapp-agent-api")
                .packagesToScan("id.co.panindaisichlife.superapp.agent.api")
                .pathsToMatch("/api/**")
                .pathsToExclude("/api/cms/**") // Exclude any paths with /api/cms
                .build();
    }

    @Bean
    public OpenApiCustomizer addFilterAndPageableParameters() {
        return openApi -> {
            openApi.getPaths().forEach((path, pathItem) -> {
                pathItem.readOperations().forEach(operation -> {
                    // Add filter parameter
                    operation.addParametersItem(new Parameter()
                            .name("filter")
                            .in("query")
                            .description("Filter criteria")
                            .schema(new Schema<>().$ref("#/components/schemas/Filter")));

                    // Add pageable parameter
                    operation.addParametersItem(new Parameter()
                            .name("pageable")
                            .in("query")
                            .description("Pagination and sorting criteria")
                            .schema(new Schema<>().$ref("#/components/schemas/Pageable")));
                });
            });
        };
    }
}