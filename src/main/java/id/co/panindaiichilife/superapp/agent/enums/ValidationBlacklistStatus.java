package id.co.panindaiichilife.superapp.agent.enums;

/**
 * Enum representing the validation status of agents in the PDL system.
 */
@SuppressWarnings("java:S115") // Suppress naming convention rule - enum values depend on external system
public enum ValidationBlacklistStatus {
    /**
     * Agent is still registered as an active agent in PDL
     */
    Active,

    /**
     * Agent was previously registered in PDL but has been terminated
     */
    InActive,

    /**
     * Agent was previously registered in PDL but has been terminated and is recorded in the Compass blacklist
     */
    BlackList,

    /**
     * Agent was previously registered in PDL but has been terminated, and based on validation criteria,
     * can only rejoin PDL through the rejoin/reactivate process
     */
    Rejoin,

    /**
     * Agent has never been registered in PDL and is not on the Compass blacklist
     */
    Clear
}