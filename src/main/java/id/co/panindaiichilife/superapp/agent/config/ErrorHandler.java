package id.co.panindaiichilife.superapp.agent.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@ControllerAdvice
@RequiredArgsConstructor
public class ErrorHandler extends ResponseEntityExceptionHandler {

    private final MessageSource messageSource;

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {

        Map<String, Object> validationMap = getValidationMap(ex);

        return new ResponseEntity<>(
                new ErrorResponse(
                        ex.getClass().getSimpleName(),
                        "Form contains one or more invalid fields",
                        validationMap
                ),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(AccessDeniedException.class)
    protected ResponseEntity<Object> handleAccessDeniedException(AccessDeniedException ex) {
        return new ResponseEntity<>(
                new ErrorResponse(
                        ex.getClass().getSimpleName(),
                        "Access denied"
                ),
                HttpStatus.FORBIDDEN
        );
    }

    @ExceptionHandler(IllegalArgumentException.class)
    protected ResponseEntity<Object> handleIllegalArgumentException(IllegalArgumentException ex) {
        return new ResponseEntity<>(
                new ErrorResponse(
                        ex.getClass().getSimpleName(),
                        ex.getMessage()
                ),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(Exception.class)
    protected ResponseEntity<Object> handleException(Exception ex) {
        ResponseStatus rs = ex.getClass().getAnnotation(ResponseStatus.class);
        HttpStatus status = rs != null ? rs.value() : HttpStatus.INTERNAL_SERVER_ERROR;

        return new ResponseEntity<>(
                new ErrorResponse(
                        ex.getClass().getSimpleName(),
                        ex.getMessage()
                ),
                status
        );
    }

    private Map<String, Object> getValidationMap(MethodArgumentNotValidException ex) {
        BindingResult binding = ex.getBindingResult();
        Map<String, Object> validationMap = new HashMap<>();

        for (FieldError fe : binding.getFieldErrors()) {
            Map<String, Object> map = new HashMap<>();
            map.put("code", fe.getCode());
            map.put("message", messageSource.getMessage(fe, Locale.getDefault()));

            String fieldName = getFieldName(binding, fe);
            validationMap.put(fieldName, map);
        }

        return validationMap;
    }

    private String getFieldName(BindingResult binding, FieldError fe) {
        try {
            Field field = binding.getTarget().getClass().getDeclaredField(fe.getField());
            JsonProperty column = field.getAnnotation(JsonProperty.class);
            return column != null && StringUtils.isNotBlank(column.value())
                    ? column.value()
                    : field.getName();
        } catch (ReflectiveOperationException ex) {
            return fe.getField().toLowerCase();
        }
    }

    @Data
    @JsonPropertyOrder({"error", "errorDescription", "validation"})
    @JsonInclude(Include.NON_NULL)
    public static class ErrorResponse {

        private String error;

        @JsonProperty(value = "error_description")
        private String errorDescription;

        private Map<String, Object> validation;

        public ErrorResponse(String error, String errorDescription) {
            this(error, errorDescription, null);
        }

        public ErrorResponse(String error, String errorDescription, Map<String, Object> validation) {
            this.error = error;
            this.errorDescription = errorDescription;
            this.validation = validation;
        }
    }
}