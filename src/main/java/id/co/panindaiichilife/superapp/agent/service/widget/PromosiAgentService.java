package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.PromosiAgentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.widget.PromosiLeaderDto;
import id.co.panindaiichilife.superapp.agent.api.filter.PromosiAgentFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.PromosiLeaderFilter;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.PromosiAgent;
import id.co.panindaiichilife.superapp.agent.model.PromosiLeader;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.PromosiAgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.PromosiLeaderRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.AgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PromosiAgentService {
    private static final String STATUS_TERCAPAI = "Tercapai";
    private static final String STATUS_BELUM_TERCAPAI = "Belum Tercapai";
    private static final String LICENSE_STATUS_AAJI_EXPIRED = "Kadaluarsa";
    private static final String LICENSE_STATUS_AAJI_UNLICENSED = "Belum Berlisensi";
    private static final String LICENSE_STATUS_ACTIVE = "Aktif";

    private final UserRepository userRepository;
    private final AgentRepository agentRepository;
    private final PromosiAgentRepository promosiAgentRepository;
    private final PromosiLeaderRepository promosiLeaderRepository;
    private final AgentService agentService;

    public List<PromosiAgentDto> getPromosiAgent(String username, PromosiAgentFilter filter) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        if (StringUtils.isBlank(filter.getAgentCode())) {
            filter.setAgentCode(agent.getAgentCode());
        }

        // Trim -D suffix from agentCode if present
        filter.setAgentCode(AgentCodeUtil.trimDSuffix(filter.getAgentCode()));

        return promosiAgentRepository.findAll(filter)
                .stream()
                .map(entity -> mapPromosiAgentDto(entity, agent))
                .collect(Collectors.toList());
    }

    public List<PromosiLeaderDto> getPromosiLeader(String username, PromosiLeaderFilter filter) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        if (StringUtils.isBlank(filter.getAgentCode())) {
            filter.setAgentCode(agent.getAgentCode());
        }

        return promosiLeaderRepository.findAll(filter)
                .stream()
                .map(entity -> mapPromosiLeaderDto(entity, agent))
                .collect(Collectors.toList());
    }

    private PromosiAgentDto mapPromosiAgentDto(PromosiAgent entity, Agent agent) {
        PromosiAgentDto dto = new PromosiAgentDto();
        BeanUtils.copyProperties(entity, dto);

        // Map Agent Count data
        Integer agentCountTarget = entity.getAgentCountTarget();
        Integer agentCountAktual = entity.getAgentCount();
        int agentCountKurang = Math.max(0, agentCountTarget - agentCountAktual);
        dto.setAgentCount(new PromosiAgentDto.AgentCountData(agentCountTarget, agentCountAktual, agentCountKurang));

        // Map New Agent Count data
        Integer newAgentCountTarget = entity.getNewAgentCountTarget();
        Integer newAgentCountAktual = entity.getNewAgentCount();
        int newAgentCountKurang = Math.max(0, newAgentCountTarget - newAgentCountAktual);
        dto.setNewAgentCount(new PromosiAgentDto.NewAgentCountData(newAgentCountTarget, newAgentCountAktual, newAgentCountKurang));

        // Map NetApe data
        double netApeTargetMax = entity.getApeTargetMax();
        double netApeTargetMin = entity.getApeTargetMin();
        double netApeAktual = entity.getNetApe();
        double netApeKurang = Math.max(0, netApeTargetMin - netApeAktual);
        dto.setNetApe(new PromosiAgentDto.NetApeData(netApeTargetMin, netApeTargetMax, netApeAktual, netApeKurang));

        // Map NetApe Group data
        double netApeGroupTarget = entity.getRecruitApeTarget();
        double netApeGroupAktual = entity.getRecruitApe();
        double netApeGroupKurang = Math.max(0, netApeGroupTarget - netApeGroupAktual);
        dto.setNetApeGroup(new PromosiAgentDto.NetApeGroupData(netApeGroupTarget, netApeGroupAktual, netApeGroupKurang));


        // Map Persistensi data
        double persistensiTarget = entity.getP13Target();
        double persistensiAktual = entity.getP13();
        double persistensiKurang = persistensiAktual >= persistensiTarget ? 0 : (persistensiTarget - persistensiAktual);
        dto.setPersistensi(new PromosiAgentDto.PersistensiData(
                round(persistensiTarget),
                round(persistensiAktual),
                round(persistensiKurang)));

        // Training data
        dto.setPelatihan(new PromosiAgentDto.TrainingData(
                entity.getTrainingCount(), entity.getTrainingCountTarget()));

        // Set license status
        setLicenseStatus(dto, agent);

        // Determine status and deficiencies
        boolean targetsMet = persistensiKurang == 0 && newAgentCountKurang == 0 &&
                agentCountKurang == 0 && netApeKurang == 0 &&
                entity.getTrainingCount() >= entity.getTrainingCountTarget() &&
                isLicenseActive(dto);

        dto.setStatus(targetsMet ? STATUS_TERCAPAI : STATUS_BELUM_TERCAPAI);
        dto.setKekurangan(determineAgentDeficiencies(
                agentCountKurang, newAgentCountKurang, netApeGroupKurang, netApeKurang,
                persistensiKurang, entity.getTrainingCount(), entity.getTrainingCountTarget(),
                dto.getStatusLisensiAAJI(), dto.getStatusLisensiAASI()));

        return dto;
    }

    private PromosiLeaderDto mapPromosiLeaderDto(PromosiLeader entity, Agent agent) {
        PromosiLeaderDto dto = new PromosiLeaderDto();
        BeanUtils.copyProperties(entity, dto);

        // Map Agent Count data
        Integer agentCountTarget = entity.getAgentCountTarget();
        Integer agentCountAktual = entity.getAgentCount();
        int agentCountKurang = Math.max(0, agentCountTarget - agentCountAktual);
        dto.setAgentCount(new PromosiLeaderDto.AgentCountData(agentCountTarget, agentCountAktual, agentCountKurang));

        // Map New Agent Count data
        Integer newAgentCountTarget = entity.getNewAgentCountTarget();
        Integer newAgentCountAktual = entity.getNewAgentCount();
        int newAgentCountKurang = Math.max(0, newAgentCountTarget - newAgentCountAktual);
        dto.setNewAgentCount(new PromosiLeaderDto.NewAgentCountData(newAgentCountTarget, newAgentCountAktual, newAgentCountKurang));

        // Map Leader Count data
        Integer leaderCountTarget = entity.getLeaderCountTarget();
        Integer leaderCountAktual = entity.getLeaderCount();
        int leaderCountKurang = Math.max(0, leaderCountTarget - leaderCountAktual);
        dto.setLeaderCount(new PromosiLeaderDto.LeaderCountData(leaderCountTarget, leaderCountAktual, leaderCountKurang));

        // Map NetApe data
        double netApeTargetMax = entity.getApeTargetMax();
        double netApeTargetMin = entity.getApeTargetMin();
        double netApeAktual = entity.getNetApe();
        double netApeKurang = Math.max(0, netApeTargetMin - netApeAktual);
        dto.setNetApe(new PromosiLeaderDto.NetApeData(netApeTargetMin, netApeTargetMax, netApeAktual, netApeKurang));

        // Map NetApe Group data
        double netApeGroupTarget = entity.getGroupApeTarget();
        double netApeGroupAktual = entity.getGroupApe();
        double netApeGroupKurang = Math.max(0, netApeGroupTarget - netApeGroupAktual);
        dto.setNetApeGroup(new PromosiLeaderDto.NetApeGroupData(netApeGroupTarget, netApeGroupAktual, netApeGroupKurang));


        // Map Persistensi data
        double persistensiTarget = entity.getP13Target();
        double persistensiAktual = entity.getP13();
        double persistensiKurang = persistensiAktual >= persistensiTarget ? 0 : (persistensiTarget - persistensiAktual);
        dto.setPersistensi(new PromosiLeaderDto.PersistensiData(
                round(persistensiTarget),
                round(persistensiAktual),
                round(persistensiKurang)));

        // Training data
        dto.setPelatihan(new PromosiLeaderDto.TrainingData(
                entity.getTrainingCount(), entity.getTrainingCountTarget()));

        // Set license status
        setLicenseLeaderStatus(dto, agent);

        // Determine status and deficiencies
        boolean targetsMet = persistensiKurang == 0 && newAgentCountKurang == 0 &&
                agentCountKurang == 0 && netApeKurang == 0 &&
                entity.getTrainingCount() >= entity.getTrainingCountTarget() &&
                isLicenseLeaderActive(dto);

        dto.setStatus(targetsMet ? STATUS_TERCAPAI : STATUS_BELUM_TERCAPAI);
        dto.setKekurangan(determineLeaderDeficiencies(
                agentCountKurang, newAgentCountKurang, leaderCountKurang, netApeGroupKurang, netApeKurang,
                persistensiKurang, entity.getTrainingCount(), entity.getTrainingCountTarget(),
                dto.getStatusLisensiAAJI(), dto.getStatusLisensiAASI()));

        return dto;
    }

    private void setLicenseStatus(PromosiAgentDto dto, Agent agent) {
        String licenseStatusAAJI = agentService.determineLicenseStatusAAJI(agent);
        String licenseStatusAASI = agentService.determineLicenseStatusAASI(agent);
        dto.setStatusLisensiAAJI(licenseStatusAAJI);
        dto.setStatusLisensiAASI(licenseStatusAASI);
    }

    private void setLicenseLeaderStatus(PromosiLeaderDto dto, Agent agent) {
        String licenseStatusAAJI = agentService.determineLicenseStatusAAJI(agent);
        String licenseStatusAASI = agentService.determineLicenseStatusAASI(agent);
        dto.setStatusLisensiAAJI(licenseStatusAAJI);
        dto.setStatusLisensiAASI(licenseStatusAASI);
    }

    private boolean isLicenseActive(PromosiAgentDto dto) {
        return !(LICENSE_STATUS_AAJI_EXPIRED.equals(dto.getStatusLisensiAAJI()) ||
                LICENSE_STATUS_AAJI_UNLICENSED.equals(dto.getStatusLisensiAAJI())) &&
                LICENSE_STATUS_ACTIVE.equals(dto.getStatusLisensiAASI());
    }

    private boolean isLicenseLeaderActive(PromosiLeaderDto dto) {
        return !(LICENSE_STATUS_AAJI_EXPIRED.equals(dto.getStatusLisensiAAJI()) ||
                LICENSE_STATUS_AAJI_UNLICENSED.equals(dto.getStatusLisensiAAJI())) &&
                LICENSE_STATUS_ACTIVE.equals(dto.getStatusLisensiAASI());
    }

    private double round(double value) {
        return Math.round(value * 100) / 100.0;
    }

    private List<String> determineAgentDeficiencies(long agentCountKurang, long newAgentCountKurang,
                                                    double netApeGroupKurang, double netApeKurang, double persistensiKurang,
                                                    long trainingCount, long trainingTarget, String licenseStatusAAJI, String licenseStatusAASI) {
        List<String> kekurangan = new ArrayList<>();

        if (agentCountKurang > 0) kekurangan.add("BP belum memenuhi validasi");
        if (newAgentCountKurang > 0) kekurangan.add("Kurang BP Baru Aktif");
        if (netApeGroupKurang > 0) kekurangan.add("Kurang Net Ape Group");
        if (netApeKurang > 0) kekurangan.add("Kurang Net Ape");
        if (persistensiKurang > 0) kekurangan.add("Kurang Persistensi-13");
        if (trainingCount < trainingTarget) kekurangan.add("Kurang pelatihan wajib");
        if (!isLicenseActive(licenseStatusAAJI, licenseStatusAASI)) {
            if (LICENSE_STATUS_AAJI_EXPIRED.equals(licenseStatusAAJI) ||
                    LICENSE_STATUS_AAJI_UNLICENSED.equals(licenseStatusAAJI)) {
                kekurangan.add("Lisensi AAJI tidak aktif");
            }
            if (!LICENSE_STATUS_ACTIVE.equals(licenseStatusAASI)) {
                kekurangan.add("Lisensi AASI tidak aktif");
            }
        }
        return kekurangan;
    }

    private List<String> determineLeaderDeficiencies(long agentCountKurang, long newAgentCountKurang, long leaderCountKurang,
                                                     double netApeGroupKurang, double netApeKurang, double persistensiKurang,
                                                     long trainingCount, long trainingTarget, String licenseStatusAAJI, String licenseStatusAASI) {
        List<String> kekurangan = new ArrayList<>();

        if (agentCountKurang > 0) kekurangan.add("BP belum memenuhi validasi");
        if (newAgentCountKurang > 0) kekurangan.add("Kurang BP Baru Aktif");
        if (leaderCountKurang > 0) kekurangan.add("BM belum memenuhi validasi");
        if (netApeGroupKurang > 0) kekurangan.add("Kurang Net Ape Group");
        if (netApeKurang > 0) kekurangan.add("Kurang Net Ape");
        if (persistensiKurang > 0) kekurangan.add("Kurang Persistensi-13");
        if (trainingCount < trainingTarget) kekurangan.add("Kurang pelatihan wajib");
        if (!isLicenseActive(licenseStatusAAJI, licenseStatusAASI)) {
            if (LICENSE_STATUS_AAJI_EXPIRED.equals(licenseStatusAAJI) ||
                    LICENSE_STATUS_AAJI_UNLICENSED.equals(licenseStatusAAJI)) {
                kekurangan.add("Lisensi AAJI tidak aktif");
            }
            if (!LICENSE_STATUS_ACTIVE.equals(licenseStatusAASI)) {
                kekurangan.add("Lisensi AASI tidak aktif");
            }
        }
        return kekurangan;
    }

    private boolean isLicenseActive(String licenseStatusAAJI, String licenseStatusAASI) {
        return !(LICENSE_STATUS_AAJI_EXPIRED.equals(licenseStatusAAJI) ||
                LICENSE_STATUS_AAJI_UNLICENSED.equals(licenseStatusAAJI)) &&
                LICENSE_STATUS_ACTIVE.equals(licenseStatusAASI);
    }
}