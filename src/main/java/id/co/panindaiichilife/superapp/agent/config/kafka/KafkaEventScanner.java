package id.co.panindaiichilife.superapp.agent.config.kafka;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Utility class that scans the classpath for classes annotated with @KafkaEvent
 * and builds the type mappings string for Kafka serialization/deserialization.
 */
@Slf4j
public class KafkaEventScanner {

    private static final String DEFAULT_EVENT_PACKAGE = "id.co.panindaiichilife.superapp.agent.model.event";
    
    /**
     * Scans the specified package for classes annotated with @KafkaEvent
     * and builds the type mappings string for Kafka serialization/deserialization.
     *
     * @param basePackage The base package to scan for event classes
     * @return The type mappings string in the format required by Kafka
     */
    public static String scanForTypeMappings(String basePackage) {
        if (basePackage == null || basePackage.isEmpty()) {
            basePackage = DEFAULT_EVENT_PACKAGE;
        }
        
        log.info("Scanning for Kafka event classes in package: {}", basePackage);
        
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        scanner.addIncludeFilter(new AnnotationTypeFilter(KafkaEvent.class));
        
        Map<String, String> typeMappings = new HashMap<>();
        
        for (BeanDefinition beanDefinition : scanner.findCandidateComponents(basePackage)) {
            try {
                String className = beanDefinition.getBeanClassName();
                Class<?> clazz = Class.forName(className);
                KafkaEvent annotation = clazz.getAnnotation(KafkaEvent.class);
                
                String alias = annotation.value();
                if (!StringUtils.hasText(alias)) {
                    alias = clazz.getSimpleName();
                }
                
                typeMappings.put(alias, className);
                log.info("Found Kafka event class: {} with alias: {}", className, alias);
                
            } catch (ClassNotFoundException e) {
                log.error("Error loading class: {}", beanDefinition.getBeanClassName(), e);
            }
        }
        
        // Build the type mappings string in the format: "alias:fully.qualified.ClassName,alias2:fully.qualified.ClassName2"
        String mappings = typeMappings.entrySet().stream()
                .map(entry -> entry.getKey() + ":" + entry.getValue())
                .collect(Collectors.joining(","));
        
        log.info("Generated Kafka type mappings: {}", mappings);
        return mappings;
    }
}
