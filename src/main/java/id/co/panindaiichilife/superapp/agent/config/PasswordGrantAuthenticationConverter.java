package id.co.panindaiichilife.superapp.agent.config;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

@Slf4j
public class PasswordGrantAuthenticationConverter implements AuthenticationConverter {

    @Override
    public Authentication convert(HttpServletRequest request) {
        // Only process requests with grant_type=password
        String grantType = request.getParameter("grant_type");
        if (!"password".equals(grantType)) {
            return null;
        }

        // Retrieve username and password from request
        String username = request.getParameter("username");
        String password = request.getParameter("password");

        // Retrieve remember me parameter from request
        String rememberMeParam = request.getParameter("rememberMe");
        Boolean rememberMe = "true".equalsIgnoreCase(rememberMeParam);

        // Return custom authentication token with remember me information
        return new RememberMeUsernamePasswordAuthenticationToken(username, password, rememberMe);
    }
}
