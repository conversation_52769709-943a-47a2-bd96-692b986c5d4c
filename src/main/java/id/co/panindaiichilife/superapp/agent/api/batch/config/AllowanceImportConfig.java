package id.co.panindaiichilife.superapp.agent.api.batch.config;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.AllowanceImportDto;
import id.co.panindaiichilife.superapp.agent.api.batch.exception.ReaderInitializationException;
import id.co.panindaiichilife.superapp.agent.api.batch.processor.AllowanceImportProcessor;
import id.co.panindaiichilife.superapp.agent.api.batch.validation.AllowanceImportDtoValidator;
import id.co.panindaiichilife.superapp.agent.core.data.batch.*;
import id.co.panindaiichilife.superapp.agent.model.Allowance;
import id.co.panindaiichilife.superapp.agent.repository.AllowanceRepository;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobScope;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.job.flow.FlowExecutionStatus;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.skip.SkipPolicy;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.transaction.PlatformTransactionManager;

import java.io.File;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class AllowanceImportConfig {

    public static final String NAME = "allowanceImport";

    public static final String INITIAL_FOLDER = "csv/initial/";

    public static final String PROGRESS_FOLDER = "csv/progress/";

    public static final String SUCCESS_FOLDER = "csv/success/";

    public static final String ERROR_FOLDER = "csv/error/";

    private final JobRepository jobRepository;

    private final PlatformTransactionManager transactionManager;

    private final EntityManagerFactory entityManagerFactory;

    private final AllowanceImportDtoValidator allowanceImportDtoValidator;

    private final AllowanceRepository allowanceRepository;

    @Bean(NAME + "Job")
    public Job allowanceImportJob() {
        return new JobBuilder(NAME + "Job", jobRepository)
                .incrementer(new RunIdIncrementer())
                .start(moveFileProgressStep()) // Move file from initial to progress
                .next(downloadAndDecryptStep())
                .on(FlowExecutionStatus.COMPLETED.getName()) // If downloadAndDecryptStep completes successfully
                .to(cleanupDatabaseStep())
                .next(importStep()) // Proceed to importStep
                .next(moveFileSuccessStep()) // Move file to success folder if import completes successfully
                .next(cleanupImportFolderStep())
                .from(downloadAndDecryptStep())
                .on(FlowExecutionStatus.FAILED.getName()) // If downloadAndDecryptStep fails
                .to(moveFileErrorStep()) // Move file to error folder
                .from(importStep())
                .on(FlowExecutionStatus.FAILED.getName()) // If importStep fails
                .to(moveFileErrorStep()) // Move file to error folder
                .next(cleanupImportFolderStep())
                .end() // End the job
                .listener(getProgressListener(null)) // Add job listener
                .build();
    }


    @Bean(NAME + "DownloadAndDecryptStep")
    public Step downloadAndDecryptStep() {
        return new StepBuilder(NAME + "DownloadAndDecryptStep", jobRepository)
                .tasklet(downloadAndDecryptTasklet(null), transactionManager)
                .build();
    }

    @Bean(NAME + "MoveFileProgressStep")
    public Step moveFileProgressStep() {
        return new StepBuilder(NAME + "MoveFileProgressStep", jobRepository)
                .tasklet(moveFileProgressTasklet(null), transactionManager)
                .build();
    }

    @Bean(NAME + "MoveFileProgressTasklet")
    @StepScope
    public Tasklet moveFileProgressTasklet(@Value("#{jobParameters['key']}") String key) {
        return new MoveFileTasklet(INITIAL_FOLDER, PROGRESS_FOLDER, key);
    }

    @Bean(NAME + "MoveFileSuccessStep")
    public Step moveFileSuccessStep() {
        return new StepBuilder(NAME + "MoveFileSuccessStep", jobRepository)
                .tasklet(moveFileSuccessTasklet(null), transactionManager)
                .build();
    }

    @Bean(NAME + "CleanupStep")
    public Step cleanupDatabaseStep() {
        return new StepBuilder(NAME + "CleanupStep", jobRepository)
                .tasklet((contribution, chunkContext) -> {
                    try {
                        // Get current year and month
                        LocalDate currentDate = LocalDate.now();
                        Integer currentYear = currentDate.getYear();
                        Integer currentMonth = currentDate.getMonthValue();

                        // Log before deletion
                        long recordCount = allowanceRepository.countByYearAndMonth(currentYear, currentMonth);
                        log.info("Deleting {} records for current year {} and month {}",
                                recordCount, currentYear, currentMonth);

                        // Delete records for current year and month
                        allowanceRepository.deleteByYearAndMonth(currentYear, currentMonth);

                        log.info("Cleanup completed for current year {} and month {}", currentYear, currentMonth);
                        return RepeatStatus.FINISHED;
                    } catch (Exception e) {
                        log.error("Error during database cleanup for current year and month", e);
                        contribution.setExitStatus(ExitStatus.FAILED);
                        return RepeatStatus.FINISHED;
                    }
                }, transactionManager)
                .build();
    }

    @Bean(NAME + "MoveFileSuccessTasklet")
    @StepScope
    public Tasklet moveFileSuccessTasklet(@Value("#{jobParameters['key']}") String key) {
        return new MoveFileTasklet(PROGRESS_FOLDER, SUCCESS_FOLDER, key);
    }

    @Bean(NAME + "MoveFileErrorStep")
    public Step moveFileErrorStep() {
        return new StepBuilder(NAME + "MoveFileErrorStep", jobRepository)
                .tasklet(moveFileErrorTasklet(null), transactionManager)
                .build();
    }

    @Bean(NAME + "MoveFileErrorTasklet")
    @StepScope
    public Tasklet moveFileErrorTasklet(@Value("#{jobParameters['key']}") String key) {
        return new MoveFileTasklet(PROGRESS_FOLDER, ERROR_FOLDER, key);
    }

    @Bean(NAME + "DownloadAndDecryptTasklet")
    @StepScope
    public Tasklet downloadAndDecryptTasklet(@Value("#{jobParameters['key']}") String key) {
        return new DownloadAndDecryptTasklet(PROGRESS_FOLDER, key);
    }

    @Bean(NAME + "ValidationStep")
    public Step validationStep() {
        return new StepBuilder(NAME + "ValidationStep", jobRepository)
                .<AllowanceImportDto, BatchError>chunk(100, transactionManager)
                .reader(allowanceImportReader(null))
                .processor(getValidator())
                .writer(batchErrorWriter(null))
                .build();
    }

    @Bean(NAME + "ImportStep")
    public Step importStep() {
        return new StepBuilder(NAME + "ImportStep", jobRepository)
                .<AllowanceImportDto, Allowance>chunk(100, transactionManager)
                .reader(allowanceImportReader(null))
                .processor(allowanceImportProcessor())
                .writer(allowanceImportWriter())
                .faultTolerant()
                .skipPolicy(skipPolicy())
                .listener(errorHandlingListener())
                .build();
    }

    @Bean(NAME + "SkipPolicy")
    public SkipPolicy skipPolicy() {
        return (throwable, skipCount) -> {
            // Log the error
            log.error("Error occurred during processing: {}", throwable.getMessage());

            // Skip records with data errors
            return throwable instanceof DataIntegrityViolationException ||
                    throwable.getCause() instanceof org.hibernate.exception.DataException;
        };
    }

    @Bean(NAME + "ErrorHandlingListener")
    @StepScope
    public ErrorHandlingListener errorHandlingListener() {
        return new ErrorHandlingListener(batchErrorWriter(null));
    }

    @Bean(NAME + "ImportReader")
    @StepScope
    public ItemReader<AllowanceImportDto> allowanceImportReader(@Value("#{jobExecutionContext['resourceUri']}") String resourceUri) {
        log.info("Creating CSV reader with resourceUri: {}", resourceUri);

        if (resourceUri == null) {
            throw new IllegalArgumentException("Resource URI is null. The download step might have failed to set it in the job context.");
        }

        // Create the reader with quoted field support
        CsvItemReader<AllowanceImportDto> reader = new CsvItemReader<>(AllowanceImportDto.class, resourceUri);

        // Force initialization (this will happen again in the step but we're doing it for validation)
        try {
            ExecutionContext executionContext = new ExecutionContext();
            reader.open(executionContext);
            // Read a line to ensure it works, then reset
            reader.close();
        } catch (Exception e) {
            log.error("Failed to test initialize reader: {}", e.getMessage(), e);
            throw new ReaderInitializationException("Failed to initialize reader: " + e.getMessage(), e);
        }

        return reader;
    }

    @Bean(NAME + "Validator")
    @JobScope
    public BatchItemValidator<AllowanceImportDto> getValidator() {
        return new BatchItemValidator<>();
    }

    @Bean(NAME + "CleanupImportFolderStep")
    public Step cleanupImportFolderStep() {
        return new StepBuilder("cleanupImportFolderStep", jobRepository)
                .tasklet((contribution, chunkContext) -> {
                    // Get resourceUri from the job execution context
                    String resourceUri = (String) chunkContext.getStepContext()
                            .getJobExecutionContext()
                            .get("resourceUri");

                    if (resourceUri == null || resourceUri.isEmpty()) {
                        log.warn("No resourceUri found in job execution context, skipping cleanup");
                        return RepeatStatus.FINISHED;
                    }

                    log.info("Cleaning up file: {}", resourceUri);

                    try {
                        // Convert the resourceUri to a file path
                        String filePath = resourceUri.startsWith("file:") ?
                            new URI(resourceUri).getPath() : resourceUri;

                        // Delete the file using Files.delete() for better error messages
                        Path pathToDelete = Paths.get(filePath);
                        if (Files.exists(pathToDelete)) {
                            Files.delete(pathToDelete);
                            log.info("Successfully deleted file: {}", filePath);
                        } else {
                            log.info("File not found (may have been already removed): {}", filePath);
                        }
                    } catch (Exception e) {
                        log.error("Error deleting file: {}", resourceUri, e);
                    }

                    return RepeatStatus.FINISHED;
                }, transactionManager)
                .build();
    }

    @Bean(NAME + "BatchErrorWriter")
    @StepScope
    public BatchErrorWriter batchErrorWriter(
            @Value("#{jobExecutionContext['resourceUri']}") String resourceUri) {
        return new BatchErrorWriter(resourceUri, "_errors.csv");
    }

    @Bean(NAME + "ImportProcessor")
    public ItemProcessor<AllowanceImportDto, Allowance> allowanceImportProcessor() {
        return new AllowanceImportProcessor(allowanceRepository);
    }

    @Bean(NAME + "ImportWriter")
    public ItemWriter<Allowance> allowanceImportWriter() {
        return new DbItemWriter<>(entityManagerFactory);
    }

    @Bean(NAME + "ProgressListener")
    @JobScope
    public ProgressListener getProgressListener(
            @Value("#{jobExecutionContext['resourceUri']}") String resourceUri) {
        return new ProgressListener(new ExcelItemCounter(resourceUri));
    }
}