package id.co.panindaiichilife.superapp.agent.service.kafka;

import com.sendgrid.helpers.mail.Mail;
import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.core.service.MailService;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.model.event.EditProfileEvent;
import id.co.panindaiichilife.superapp.agent.repository.TrxApprovalHeaderRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.AgentService;
import id.co.panindaiichilife.superapp.agent.service.FirebaseService;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import id.co.panindaiichilife.superapp.agent.service.RedisService;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class EditProfileEventHandler {

    private final MailService mailService;

    private final GlobalConfigService globalConfigService;

    private final UserRepository userRepository;

    private final FirebaseService firebaseService;

    private final AgentService agentService;

    private final TrxApprovalHeaderRepository trxApprovalHeaderRepository;

    private final RedisService redisService;

    /**
     * Handles edit profile events based on their approval status
     *
     * @param event The edit profile event to handle
     */
    public void handleEditProfileEvent(EditProfileEvent event) {
        log.info("Handling edit profile event for ID: {}", event.getEditProfileId());

        try {
            TrxApprovalHeader header = trxApprovalHeaderRepository.findById(event.getApprovalHeaderId()).orElse(null);

            if (header == null) {
                log.error("Could not find approval header with ID: {} for Kafka event", event.getApprovalHeaderId());
                return;
            }

            agentService.handleTrxEditProfile(header.getTrxId(), event.getApprovalStatus(), header.getDetailApproval());

            // First check if we need to notify the next approvers
            if ((event.getApprovalStatus() == ApprovalStatus.BARU || event.getApprovalStatus() == ApprovalStatus.MENUNGGU_PERSETUJUAN) &&
                    event.getNextApproverUserIds() != null && !event.getNextApproverUserIds().isEmpty()) {
                // Send notification to the next approvers
                log.info("Sending notification to {} next approvers for edit profile ID: {}",
                        event.getNextApproverUserIds().size(), event.getEditProfileId());
                sendNextApproverNotifications(event);
            }

            // Process the event based on the transaction status
            switch (event.getTrxStatus()) {
                case COMPLETE:
                    // Handle approved edit profile
                    log.info("Processing approved edit profile for ID: {}", event.getEditProfileId());

                    // Send approval email to agent
                    sendApprovalEmail(event);
                    break;

                case REJECTED:
                    // Handle rejected edit profile
                    log.info("Processing rejected edit profile for ID: {}", event.getEditProfileId());

                    // Send rejection email
                    sendRejectionEmail(event);
                    break;

                case DIKEMBALIKAN:
                    // Handle returned edit profile
                    log.info("Processing returned edit profile for ID: {}", event.getEditProfileId());

                    // Send returned notification
                    sendReturnedNotification(event);
                    break;

                case CANCELLED:
                    // Handle cancelled edit profile
                    log.info("Processing cancelled edit profile for ID: {}", event.getEditProfileId());
                    break;

                default:
                    log.info("No specific action needed for status: {}", event.getTrxStatus());
                    break;
            }
        } catch (Exception e) {
            log.error("Error handling edit profile event: {}", e.getMessage(), e);
        }
    }

    /**
     * Sends an approval email to the agent
     *
     * @param event The edit profile event
     */
    private void sendApprovalEmail(EditProfileEvent event) throws MessagingException {
        if (event.getAgentEmail() != null && !event.getAgentEmail().isEmpty()) {
            // Get the template ID from global config or use a default
            String templateId = globalConfigService.getGlobalConfig(
                    "mail.edit.profile.approved.template.id",
                    "d-edit-profile-approved-template");

            // Prepare the data for the template
            Map<String, Object> data = new HashMap<>();
            data.put("name", event.getAgentName());
            data.put("editProfileId", event.getEditProfileId());
            data.put("timestamp", event.getTimestamp().toString());

            // Add changed fields to the template data
            if (event.getChangedFields() != null && !event.getChangedFields().isEmpty()) {
                data.put("changedFields", event.getChangedFields());
            }

            // Create and send the email
            Mail email = mailService.createEmail(templateId, data, event.getAgentEmail());
            mailService.sendEmail(email);

            log.info("Approval email sent to agent: {}", event.getAgentEmail());
        }

        // Send Firebase notification if the user has a device token
        User requester = userRepository.findByUsername(event.getAgentCode()).orElse(null);
        if (requester == null) {
            log.warn("Could not find agent with ID: {} or email is missing", event.getAgentCode());
            return;
        }

        NotificationDto notification = new NotificationDto();
        notification.setTitle(event.getNotificationTitle());
        notification.setBody(event.getNotificationBody());
        notification.setData(Map.of(
                "approvalId", event.getApprovalHeaderId().toString(),
                "trxId", event.getEditProfileId().toString(),
                "trxType", event.getTrxType().name(),
                "status", event.getApprovalStatus().name()
        ));
        notification.setInboxType(InboxType.NOTIFICATION);

        firebaseService.sendNotification(List.of(requester), notification);
    }

    /**
     * Sends a rejection email to the agent
     *
     * @param event The edit profile event
     */
    private void sendRejectionEmail(EditProfileEvent event) throws MessagingException {
        if (event.getAgentEmail() != null && !event.getAgentEmail().isEmpty()) {
            // Get the template ID from global config or use a default
            String templateId = globalConfigService.getGlobalConfig(
                    "mail.edit.profile.rejected.template.id",
                    "d-edit-profile-rejected-template");

            // Prepare the data for the template
            Map<String, Object> data = new HashMap<>();
            data.put("name", event.getAgentName());
            data.put("editProfileId", event.getEditProfileId());
            data.put("timestamp", event.getTimestamp().toString());

            // Add changed fields to the template data
            if (event.getChangedFields() != null && !event.getChangedFields().isEmpty()) {
                data.put("changedFields", event.getChangedFields());
            }

            // Create and send the email
            Mail email = mailService.createEmail(templateId, data, event.getAgentEmail());
            mailService.sendEmail(email);

            log.info("Rejection email sent to agent: {}", event.getAgentEmail());
        }

        // Send Firebase notification if the user has a device token
        User requester = userRepository.findByUsername(event.getAgentCode()).orElse(null);
        if (requester == null) {
            log.warn("Could not find agent with ID: {} or email is missing", event.getAgentCode());
            return;
        }

        NotificationDto notification = new NotificationDto();
        notification.setTitle(event.getNotificationTitle());
        notification.setBody(event.getNotificationBody());
        notification.setData(Map.of(
                "approvalId", event.getApprovalHeaderId().toString(),
                "trxId", event.getEditProfileId().toString(),
                "trxType", event.getTrxType().name(),
                "status", event.getApprovalStatus().name()
        ));
        notification.setInboxType(InboxType.NOTIFICATION);

        firebaseService.sendNotification(List.of(requester), notification);
    }

    /**
     * Sends a returned notification to the agent
     *
     * @param event The edit profile event
     */
    private void sendReturnedNotification(EditProfileEvent event) throws MessagingException {
        if (event.getAgentEmail() != null && !event.getAgentEmail().isEmpty()) {
            // Get the template ID from global config or use a default
            String templateId = globalConfigService.getGlobalConfig(
                    "mail.edit.profile.returned.template.id",
                    "d-edit-profile-returned-template");

            // Prepare the data for the template
            Map<String, Object> data = new HashMap<>();
            data.put("name", event.getAgentName());
            data.put("editProfileId", event.getEditProfileId());
            data.put("timestamp", event.getTimestamp().toString());

            // Add changed fields to the template data
            if (event.getChangedFields() != null && !event.getChangedFields().isEmpty()) {
                data.put("changedFields", event.getChangedFields());
            }

            // Create and send the email
            Mail email = mailService.createEmail(templateId, data, event.getAgentEmail());
            mailService.sendEmail(email);
            log.info("Returned notification email sent to agent: {}", event.getAgentEmail());
        }

        // Send Firebase notification if the user has a device token
        User requester = userRepository.findByUsername(event.getAgentCode()).orElse(null);
        if (requester == null) {
            log.warn("Could not find agent with ID: {} or email is missing", event.getAgentCode());
            return;
        }

        NotificationDto notification = new NotificationDto();
        notification.setTitle(event.getNotificationTitle());
        notification.setBody(event.getNotificationBody());
        notification.setData(Map.of(
                "approvalId", event.getApprovalHeaderId().toString(),
                "trxId", event.getEditProfileId().toString(),
                "trxType", event.getTrxType().name(),
                "status", event.getApprovalStatus().name()
        ));
        notification.setInboxType(InboxType.NOTIFICATION);

        firebaseService.sendNotification(List.of(requester), notification);
    }

    /**
     * Sends notifications to the next approvers in the approval chain
     *
     * @param event The edit profile event
     */
    private void sendNextApproverNotifications(EditProfileEvent event) throws MessagingException {
        // Get the list of next approver user IDs
        List<String> approverUserIds = event.getNextApproverUserIds();

        if (approverUserIds != null && !approverUserIds.isEmpty()) {
            // Get the template ID from global config or use a default
            String templateId = globalConfigService.getGlobalConfig(
                    "mail.edit.profile.approval.request.template.id",
                    "d-edit-profile-approval-request-template");

            // For each approver user ID
            for (String userId : approverUserIds) {
                // Find the user by username
                User approver = userRepository.findByUsername(userId).orElse(null);

                if (approver != null) {
                    if (approver.getEmail() != null && !approver.getEmail().isEmpty()) {
                        // Prepare the data for the template
                        Map<String, Object> data = new HashMap<>();
                        data.put("approverName", approver.getName() != null ? approver.getName() : "Approver");
                        data.put("agentName", event.getAgentName());
                        data.put("agentCode", event.getAgentCode());
                        data.put("editProfileId", event.getEditProfileId());
                        data.put("approvalHeaderId", event.getApprovalHeaderId());
                        data.put("requestByName", event.getRequestByName());
                        data.put("timestamp", event.getTimestamp().toString());

                        // Add changed fields to the template data
                        if (event.getChangedFields() != null && !event.getChangedFields().isEmpty()) {
                            data.put("changedFields", event.getChangedFields().entrySet().stream()
                                    .map(entry -> entry.getKey() + ": " + entry.getValue())
                                    .collect(Collectors.joining(", ")));
                        }

                        // Create and send the email
                        Mail email = mailService.createEmail(templateId, data, approver.getEmail());
                        mailService.sendEmail(email);
                    }

                    NotificationDto notification = new NotificationDto();
                    notification.setTitle(event.getNotificationTitle());
                    notification.setBody(event.getNotificationBody());
                    notification.setData(Map.of(
                            "approvalId", event.getApprovalHeaderId().toString(),
                            "trxId", event.getEditProfileId().toString(),
                            "trxType", event.getTrxType().name(),
                            "status", event.getApprovalStatus().name()
                    ));
                    notification.setInboxType(InboxType.NOTIFICATION);

                    firebaseService.sendNotification(List.of(approver), notification);

                    log.info("Approval request notification sent to approver: {} ({})", approver.getUsername(), approver.getEmail());
                } else {
                    log.warn("Could not find approver with ID: {} or email is missing", userId);
                }
            }
        } else {
            log.warn("No next approver user IDs found for edit profile ID: {}", event.getEditProfileId());
        }
    }
}
