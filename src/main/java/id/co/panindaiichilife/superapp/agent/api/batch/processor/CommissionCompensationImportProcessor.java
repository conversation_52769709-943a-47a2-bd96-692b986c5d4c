package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.CommissionCompensationImportDto;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.CommissionCompensation;
import id.co.panindaiichilife.superapp.agent.repository.CommissionCompensationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class CommissionCompensationImportProcessor implements ItemProcessor<CommissionCompensationImportDto, CommissionCompensation> {

    private final CommissionCompensationRepository commissionCompensationRepository;

    @Override
    public CommissionCompensation process(CommissionCompensationImportDto item) {
        return findOrCreateCommissionCompensation(item);
    }

    private CommissionCompensation findOrCreateCommissionCompensation(CommissionCompensationImportDto item) {
        CommissionCompensation commissionCompensation = new CommissionCompensation();

        // Copy properties
        BeanUtils.copyProperties(item, commissionCompensation);
        commissionCompensation.setDistributionCode(DistributionCode.valueOf(item.getDistributionCode()));

        return commissionCompensation;
    }
}