package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.model.Access;
import id.co.panindaiichilife.superapp.agent.model.Role;
import id.co.panindaiichilife.superapp.agent.model.User;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Root;

import java.util.List;

public class AccessRepositoryImpl implements AccessRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public boolean isDefined(String domain, String action) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> query = cb.createQuery(Long.class);
        Root<Access> root = query.from(Access.class);

        query.where(
                cb.equal(root.get("domain"), domain),
                cb.equal(root.get("action"), action)
        ).select(cb.count(root));

        return entityManager.createQuery(query).getSingleResult() > 0l;
    }

    @Override
    public boolean hasAccess(String username, String domain, String action) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> query = cb.createQuery(Long.class);

        Root<User> root = query.from(User.class);
        Join<User, Role> roles = root.join("roles");
        Join<Role, Access> accesses = roles.join("accesses");

        query.where(
                cb.equal(root.get("username"), username),
                cb.equal(accesses.get("domain"), domain),
                cb.equal(accesses.get("action"), action)
        ).select(cb.count(root));

        return entityManager.createQuery(query).getSingleResult() > 0l;
    }

    @Override
    public List<Access> listByUsername(String username) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Access> query = cb.createQuery(Access.class);

        Root<User> root = query.from(User.class);
        Join<User, Role> roles = root.join("roles");
        Join<Role, Access> accesses = roles.join("accesses");

        query.where(cb.equal(root.get("username"), username))
                .orderBy(cb.asc(accesses.get("domain")), cb.asc(accesses.get("action")))
                .select(accesses);

        return entityManager.createQuery(query).getResultList();
    }
}
