package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.UserDto;
import id.co.panindaiichilife.superapp.agent.api.filter.UserFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.UserForm;
import id.co.panindaiichilife.superapp.agent.api.validation.UserValidator;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController("cmsUserController")
@RequestMapping("/api/cms/user")
@Tag(name = "User - CMS", description = "API CMS User")
@Slf4j
@RequiredArgsConstructor
public class UserCmsController {

    private final UserService userService;

    private final UserValidator userValidator;

    @Operation(summary = "List users")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('user.cms.User', 'view')")
    public Page<UserDto> index(@ParameterObject @ModelAttribute("filter") UserFilter filter,
                               @ParameterObject @PageableDefault(sort = "id") Pageable pageable) {
        return userService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific user")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('user.cms.User', 'view')")
    public UserDto view(@PathVariable long id) {
        return userService.findOne(id);
    }

    @Operation(summary = "Get list timezone")
    @GetMapping(value = "timezone")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('user.cms.User', 'view')")
    public List<String> getTimezone() {
        return userService.getTimezone();
    }

    @Operation(summary = "Add new users")
    @PostMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('user.cms.User', 'add')")
    public UserDto insert(@Valid @RequestBody UserForm userForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        userValidator.validate(userForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        return userService.add(userForm);
    }

    @Operation(summary = "Modify existing user")
    @PutMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('user.cms.User', 'edit')")
    public UserDto edit(@PathVariable long id,
                        @Valid @RequestBody UserForm userForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        userValidator.validate(userForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        return userService.update(id, userForm);
    }

    @Operation(summary = "Delete existing user")
    @DeleteMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('user.cms.User', 'delete')")
    public void delete(@PathVariable long id) {
        userService.delete(id);
    }
}
