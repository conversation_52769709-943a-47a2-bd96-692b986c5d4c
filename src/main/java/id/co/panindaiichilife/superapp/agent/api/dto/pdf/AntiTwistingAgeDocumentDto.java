package id.co.panindaiichilife.superapp.agent.api.dto.pdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * DTO for ANTI-TWISTING-AGE (Anti Twisting Agen) document generation parameters.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AntiTwistingAgeDocumentDto {

    private String agentName;

    private String placeOfBirth;

    private String dateOfBirth;

    private String address;

    private String ktpNumber;

    private String location;

    private String signature;

    private Map<String, Object> additionalVariables;

    /**
     * Convert DTO to variables map for template processing.
     *
     * @return Map of variables for template injection
     */
    public Map<String, Object> toVariablesMap() {
        Map<String, Object> variables = new HashMap<>();

        addBasicVariables(variables);
        PdfDocumentUtils.addStandardDateTimeVariables(variables);
        PdfDocumentUtils.addAdditionalVariables(variables, additionalVariables);

        return variables;
    }

    /**
     * Add basic document variables to the map.
     */
    private void addBasicVariables(Map<String, Object> variables) {
        variables.put("agentName", PdfDocumentUtils.getStringOrEmpty(agentName));
        variables.put("placeOfBirth", PdfDocumentUtils.getStringOrEmpty(placeOfBirth));
        variables.put("location", PdfDocumentUtils.getStringOrEmpty(location));
        variables.put("dateOfBirth", PdfDocumentUtils.getStringOrEmpty(dateOfBirth));
        variables.put("address", PdfDocumentUtils.getStringOrEmpty(address));
        variables.put("ktpNumber", PdfDocumentUtils.getStringOrEmpty(ktpNumber));
        variables.put("signature", PdfDocumentUtils.getStringOrEmpty(signature));
    }
}
