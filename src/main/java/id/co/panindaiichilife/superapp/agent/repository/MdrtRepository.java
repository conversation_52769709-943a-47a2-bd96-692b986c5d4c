package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.Mdrt;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface MdrtRepository extends BaseRepository<Mdrt, Long> {

    // Find by agent code
    Mdrt findByAgentCode(String agentCode);

    // Find by leader code
    List<Mdrt> findByLeaderCode(String leaderCode);

    // Find by branch code
    List<Mdrt> findByBranchCode(String branchCode);

    // Find by year and month
    List<Mdrt> findByYearAndMonth(Integer year, Integer month);

    // Find by year and month
    Optional<Mdrt> findByAgentCodeAndYearAndMonth(String agentCode, Integer year, Integer month);

    // Find by BDM code
    List<Mdrt> findByBdmCode(String bdmCode);

    // Find by HOS code
    List<Mdrt> findByHosCode(String hosCode);

    // Find MDRT qualified agents (MDRT point >= MDRT target)
    @Query("SELECT m FROM Mdrt m WHERE m.mdrtPoint >= m.mdrtTarget")
    List<Mdrt> findMdrtQualifiedAgents();

    // Find COT qualified agents (MDRT point >= COT target)
    @Query("SELECT m FROM Mdrt m WHERE m.mdrtPoint >= m.cotTarget")
    List<Mdrt> findCotQualifiedAgents();

    // Find TOT qualified agents (MDRT point >= TOT target)
    @Query("SELECT m FROM Mdrt m WHERE m.mdrtPoint >= m.totTarget")
    List<Mdrt> findTotQualifiedAgents();

    // Find by achievement percentage range
    @Query("SELECT m FROM Mdrt m WHERE m.mdrtTarget > 0 AND (m.mdrtPoint / m.mdrtTarget * 100) BETWEEN :minPercentage AND :maxPercentage")
    List<Mdrt> findByMdrtAchievementBetween(@Param("minPercentage") Double minPercentage, @Param("maxPercentage") Double maxPercentage);

    // Find top performers by MDRT point
    @Query("SELECT m FROM Mdrt m ORDER BY m.mdrtPoint DESC")
    List<Mdrt> findTopPerformersByMdrtPoint();

    // Find agents close to qualifying (percentage of achievement)
    @Query("SELECT m FROM Mdrt m WHERE m.mdrtTarget > 0 AND m.mdrtPoint < m.mdrtTarget AND (m.mdrtPoint / m.mdrtTarget * 100) >= :threshold")
    List<Mdrt> findAgentsCloseToQualifying(@Param("threshold") Double threshold);

    // Sum MDRT points by branch
    @Query("SELECT SUM(m.mdrtPoint) FROM Mdrt m WHERE m.branchCode = :branchCode")
    Double sumMdrtPointsByBranch(@Param("branchCode") String branchCode);

    // Count qualified agents by branch for each category
    @Query("SELECT COUNT(m) FROM Mdrt m WHERE m.branchCode = :branchCode AND m.mdrtPoint >= m.mdrtTarget")
    Long countMdrtQualifiedByBranch(@Param("branchCode") String branchCode);

    @Query("SELECT COUNT(m) FROM Mdrt m WHERE m.branchCode = :branchCode AND m.mdrtPoint >= m.cotTarget")
    Long countCotQualifiedByBranch(@Param("branchCode") String branchCode);

    @Query("SELECT COUNT(m) FROM Mdrt m WHERE m.branchCode = :branchCode AND m.mdrtPoint >= m.totTarget")
    Long countTotQualifiedByBranch(@Param("branchCode") String branchCode);

    // Calculate average achievement percentage by branch
    @Query("SELECT AVG(m.mdrtPoint / m.mdrtTarget * 100) FROM Mdrt m WHERE m.branchCode = :branchCode AND m.mdrtTarget > 0")
    Double calculateAverageAchievementByBranch(@Param("branchCode") String branchCode);

    // Find highest point for the year
    @Query("SELECT MAX(m.mdrtPoint) FROM Mdrt m WHERE m.year = :year")
    Double findHighestPointForYear(@Param("year") Integer year);

    // Find by point range
    List<Mdrt> findByMdrtPointBetween(Double minPoint, Double maxPoint);

    @Modifying
    @Transactional
    @Query("DELETE FROM Mdrt m WHERE m.year = :year AND m.month = :month")
    void deleteByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

    long countByYearAndMonth(Integer year, Integer month);
}
