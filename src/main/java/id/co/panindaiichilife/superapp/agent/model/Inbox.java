package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;

import java.time.Instant;

@Entity
@Table(name = "inboxes")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
public class Inbox extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "inboxes_id_seq")
    @SequenceGenerator(name = "inboxes_id_seq", sequenceName = "inboxes_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Audited
    @Enumerated(EnumType.STRING)
    private TrxType trxType;

    @Audited
    @Enumerated(EnumType.STRING)
    private InboxType inboxType;

    @Audited
    private Long trxId;

    @Audited
    private Long approvalId;

    @Audited
    private String title;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String body;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String footer;

    @Audited
    private Boolean isArchived = Boolean.FALSE;

    @Audited
    private Boolean isRead = Boolean.FALSE;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;
}
