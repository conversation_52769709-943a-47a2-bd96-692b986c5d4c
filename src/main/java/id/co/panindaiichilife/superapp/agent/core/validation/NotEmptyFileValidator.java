package id.co.panindaiichilife.superapp.agent.core.validation;

import id.co.panindaiichilife.superapp.agent.core.validation.constraints.NotEmptyFile;
import org.springframework.web.multipart.MultipartFile;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class NotEmptyFileValidator implements
        ConstraintValidator<NotEmptyFile, MultipartFile> {

    @Override
    public void initialize(NotEmptyFile notEmptyFile) {
        // No initialization required for this validator
    }

    @Override
    public boolean isValid(MultipartFile file, ConstraintValidatorContext cvc) {
        return file != null && !file.isEmpty();
    }
}
