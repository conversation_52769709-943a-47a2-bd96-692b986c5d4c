package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "agent_productive")
@Data
@ToString(of = {"id", "agentCode"})
public class AgentProductive {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "agent_productive_id_seq")
    @SequenceGenerator(name = "agent_productive_id_seq", sequenceName = "agent_productive_id_seq", allocationSize = 1)
    private Long id;

    @Enumerated(EnumType.STRING)
    private DistributionCode distributionCode;

    private String agentCode;

    private String leaderCode;

    private String mainBranch;

    private String branchCode;

    private String bdmCode;

    private String bdmName;

    private String abddCode;

    private String abddName;

    private String bddCode;

    private String bddName;

    private String hosCode;

    private String hosName;

    private Integer year;

    private String type;

    private Double apeTarget;

    private Double netApe;

    private Integer caseTarget;

    private Integer netCase;

    private Double p13Target;

    private Double p13;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
