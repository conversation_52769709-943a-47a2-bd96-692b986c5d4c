package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.enums.UserType;
import id.co.panindaiichilife.superapp.agent.model.*;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxApprovalDetailRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRecruitmentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.kafka.KafkaEventService;
import id.co.panindaiichilife.superapp.agent.util.ApprovalRoleUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Service for handling notifications in the approval system.
 * This centralizes all notification-related logic.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NotificationService {

    private final KafkaEventService kafkaEventService;

    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    private final TrxApprovalDetailRepository trxApprovalDetailRepository;

    private final TrxRecruitmentRepository trxRecruitmentRepository;

    /**
     * Sends a notification for an approval header
     *
     * @param header The approval header
     */
    public void sendNotification(TrxApprovalHeader header) {
        try {
            // Create notification data map
            Map<String, String> data = Map.of(
                    "approvalId", header.getId().toString(),
                    "trxId", header.getTrxId().toString(),
                    "trxType", header.getTrxType().name(),
                    "status", header.getApprovalStatus().name()
            );

            // Determine recipients based on approval status and role
            List<User> recipients = determineNotificationRecipients(header);
            if (recipients.isEmpty()) {
                log.warn("No recipients found for notification: header ID {}, type {}, status {}",
                        header.getId(), header.getTrxType(), header.getApprovalStatus());
                return;
            }

            // Create notification payload with appropriate title and body
            NotificationDto payload = createNotificationPayload(header, data);

            // Send Kafka event for the status change
            kafkaEventService.sendApprovalEvent(header, header.getApprovalStatus(), recipients, payload)
                    .whenComplete((result, ex) -> {
                        if (ex != null) {
                            log.error("Failed to send Kafka event for header ID: {}, type: {}, trxId: {}, status: {}",
                                    header.getId(), header.getTrxType(), header.getTrxId(), header.getApprovalStatus(), ex);
                        } else {
                            log.info("Successfully sent notification for header ID: {}, type: {}, to {} recipients",
                                    header.getId(), header.getTrxType(), recipients.size());
                        }
                    });
        } catch (Exception e) {
            log.error("Error sending notification for header ID: {}, type: {}", header.getId(), header.getTrxType(), e);
        }
    }

    /**
     * Determines the recipients for a notification based on approval header
     *
     * @param header The approval header
     * @return List of users who should receive the notification
     */
    private List<User> determineNotificationRecipients(TrxApprovalHeader header) {
        // For approved, rejected, or pending status, notify the requester
        if (header.getApprovalStatus() == ApprovalStatus.DISETUJUI ||
                header.getApprovalStatus() == ApprovalStatus.DITOLAK ||
                header.getApprovalStatus() == ApprovalStatus.TERTUNDA ||
                header.getApprovalStatus() == ApprovalStatus.DIBATALKAN) {
            return List.of(header.getRequestBy());
        }

        // For new or waiting for approval status, notify the approver
        if (header.getApprovalStatus() == ApprovalStatus.BARU ||
                header.getApprovalStatus() == ApprovalStatus.MENUNGGU_PERSETUJUAN) {
            return findApprovers(header);
        }

        return Collections.emptyList();
    }

    /**
     * Finds the appropriate approvers based on the approval header configuration.
     * Supports multiple approver roles separated by commas.
     *
     * @param header The approval header
     * @return List of users who should approve the request
     */
    private List<User> findApprovers(TrxApprovalHeader header) {
        if (header.getApproverRole() == null) {
            log.warn("No approver role set for header ID: {}", header.getId());
            return Collections.emptyList();
        }

        // Check if this is an upline approval
        if (header.getApproverRole().startsWith("UPLINE:")) {
            return findUplineApprover(header.getApproverRole());
        }
        // Check if this is a recruiter approval
        else if (header.getApproverRole().startsWith("RECRUITER:")) {
            return findRecruiterApprover(header.getApproverRole());
        }
        // Standard role-based notification
        else {
            return findRoleBasedApprovers(header.getApproverRole(), header);
        }
    }

    /**
     * Finds an upline approver based on the approver role string
     *
     * @param approverRole The approver role string (format: "UPLINE:agentCode")
     * @return List containing the upline user, or empty if not found
     */
    private List<User> findUplineApprover(String approverRole) {
        // Extract the upline agent code from the approverRole field
        String uplineAgentCode = approverRole.substring("UPLINE:".length());

        // Find the upline agent and their user
        Agent uplineAgent = agentRepository.findTopByAgentCode(uplineAgentCode).orElse(null);
        if (uplineAgent != null && uplineAgent.getUser() != null) {
            return List.of(uplineAgent.getUser());
        } else {
            log.warn("Could not find upline agent for notification: {}", uplineAgentCode);
            return Collections.emptyList();
        }
    }

    /**
     * Finds a recruiter approver based on the approver role string
     *
     * @param approverRole The approver role string (format: "RECRUITER:username")
     * @return List containing the recruiter user, or empty if not found
     */
    private List<User> findRecruiterApprover(String approverRole) {
        // Extract the recruiter username from the approverRole field
        String recruiterUsername = approverRole.substring("RECRUITER:".length());

        // Find the recruiter user
        User recruiter = userRepository.findByUsername(recruiterUsername).orElse(null);
        if (recruiter != null) {
            return List.of(recruiter);
        } else {
            log.warn("Could not find recruiter for notification: {}", recruiterUsername);
            return Collections.emptyList();
        }
    }

    /**
     * Finds role-based approvers with branch filtering for STAFF users.
     * Supports both single roles and comma-separated multiple roles.
     *
     * @param approverRole The role code(s) - can be single role or comma-separated multiple roles
     * @param header The approval header containing branch information
     * @return List of users with any of the specified roles, filtered by branch for STAFF users
     */
    private List<User> findRoleBasedApprovers(String approverRole, TrxApprovalHeader header) {
        // Parse multiple roles from the approver role configuration
        Set<String> approverRoles = ApprovalRoleUtil.parseRoles(approverRole);

        if (approverRoles.isEmpty()) {
            log.warn("No valid roles found in approver role configuration: {}", approverRole);
            return Collections.emptyList();
        }

        // Find users with any of the specified roles
        List<User> roleUsers = userRepository.findUsersByRoleCodes(approverRoles);
        if (roleUsers.isEmpty()) {
            log.warn("No users found with roles: {}", approverRoles);
            return Collections.emptyList();
        }

        log.info("Found {} users with roles: {}", roleUsers.size(), approverRoles);

        // Filter by branch for STAFF users only
        if (header.getBranch() != null) {
            String approvalBranchCode = header.getBranch().getBranchCode();
            List<User> filteredUsers = roleUsers.stream()
                    .filter(user -> {
                        // Only apply branch filtering for STAFF users
                        if (UserType.STAFF.equals(user.getUserType())) {
                            // Check if user has access to the approval's branch
                            return user.getBranches() != null &&
                                   user.getBranches().stream()
                                           .anyMatch(branch -> approvalBranchCode.equals(branch.getBranchCode()));
                        } else {
                            // Non-STAFF users can see all approvals (no branch filtering)
                            return true;
                        }
                    })
                    .toList();

            if (filteredUsers.isEmpty()) {
                log.warn("No users found with roles {} and access to branch {}", approverRoles, approvalBranchCode);
            } else {
                log.info("Found {} users with roles {} for branch {}", filteredUsers.size(), approverRoles, approvalBranchCode);
            }

            return filteredUsers;
        }

        return roleUsers;
    }

    /**
     * Creates a notification payload with appropriate title and body based on transaction type and status
     *
     * @param header The approval header
     * @param data   Additional data to include in the notification
     * @return The notification payload
     */
    private NotificationDto createNotificationPayload(TrxApprovalHeader header, Map<String, String> data) {
        NotificationDto payload = new NotificationDto();
        payload.setData(data);
        payload.setInboxType(header.getTrxType() == TrxType.EDIT_PROFILE ? InboxType.NOTIFICATION : InboxType.INBOX);

        String title = "";
        String body = "";

        // Get the last approval detail if available
        TrxApprovalDetail lastDetail = null;
        if (header.getId() != null) {
            List<TrxApprovalDetail> details = trxApprovalDetailRepository.findByApprovalHeaderOrderByCreatedAtDesc(header);
            if (!details.isEmpty()) {
                lastDetail = details.get(0);
            }
        }

        // Get approver name if available
        String approverName = "";
        if (lastDetail != null && lastDetail.getActionBy() != null) {
            approverName = lastDetail.getActionBy().getName();
        }

        // Determine title and body based on transaction type and approval status
        switch (header.getApprovalStatus()) {
            case BARU:
            case MENUNGGU_PERSETUJUAN:
                if (header.getApprovalStatus() == ApprovalStatus.BARU) {
                    title = getNewRequestTitle(header.getTrxType());
                    body = "Agen " + header.getRequestBy().getName() + " " + getNewRequestBody(header.getTrxType());
                } else {
                    title = getApprovalRequestTitle(header.getTrxType());

                    // Check if this is a specific level of approval and has a previous approver
                    if (header.getCurrentLevel() > 1 && !approverName.isEmpty()) {
                        // Get candidate name for recruitment transactions
                        String candidateName = getCandidateName(header);
                        String nameToUse = !candidateName.isEmpty() ? candidateName : header.getRequestBy().getName();

                        // Format: "Pengajuan Agen baru atas nama XXX telah disetujui oleh YYY dan menunggu persetujuan Anda."
                        body = "Pengajuan " + getApprovalRequestBody(header.getTrxType()) +
                                " atas nama " + nameToUse +
                                " telah disetujui oleh " + approverName +
                                " dan menunggu persetujuan Anda";
                    } else if (header.getCurrentLevel() > 1) {
                        body = "Terdapat pengajuan " + getApprovalRequestBody(header.getTrxType()) +
                                " yang membutuhkan persetujuan Anda (Level " + header.getCurrentLevel() + ")";
                    } else {
                        body = "Terdapat pengajuan " + getApprovalRequestBody(header.getTrxType()) +
                                " yang membutuhkan persetujuan Anda";
                    }
                }
                break;

            case TERTUNDA:
                title = getStatusTitle(header.getTrxType());
                if (approverName.isEmpty()) {
                    body = "Pengajuan " + getStatusBody(header.getTrxType()) + " Anda dikembalikan untuk perbaikan";
                } else {
                    body = "Pengajuan " + getStatusBody(header.getTrxType()) + " Anda dikembalikan oleh " + approverName + " untuk perbaikan";
                }

                // Add remarks if available
                if (lastDetail != null && lastDetail.getRemarks() != null && !lastDetail.getRemarks().isEmpty()) {
                    body += ". Catatan: " + lastDetail.getRemarks();
                }
                break;

            case DITOLAK:
                title = getStatusTitle(header.getTrxType());
                if (approverName.isEmpty()) {
                    body = "Pengajuan " + getStatusBody(header.getTrxType()) + " Anda ditolak";
                } else {
                    body = "Pengajuan " + getStatusBody(header.getTrxType()) + " Anda ditolak oleh " + approverName;
                }

                // Add remarks if available
                if (lastDetail != null && lastDetail.getRemarks() != null && !lastDetail.getRemarks().isEmpty()) {
                    body += ". Alasan: " + lastDetail.getRemarks();
                }
                break;

            case DISETUJUI:
                title = getStatusTitle(header.getTrxType());
                if (approverName.isEmpty()) {
                    body = "Pengajuan " + getStatusBody(header.getTrxType()) + " Anda telah disetujui";
                } else {
                    // For final approval
                    if (header.getCurrentLevel() != null && header.getCurrentLevel().equals(header.getMaxLevel())) {
                        body = "Pengajuan " + getStatusBody(header.getTrxType()) + " Anda telah disetujui sepenuhnya";
                    } else {
                        body = "Pengajuan " + getStatusBody(header.getTrxType()) + " Anda telah disetujui oleh " + approverName;
                    }
                }
                break;

            case DIBATALKAN:
                title = "Pembatalan " + getStatusTitle(header.getTrxType());
                body = "Pengajuan " + getStatusBody(header.getTrxType()) + " Anda telah dibatalkan";
                break;

            default:
                // No notification for other statuses
                break;
        }

        payload.setTitle(title);
        payload.setBody(body);

        return payload;
    }

    /**
     * Gets the title for a new request notification
     */
    private String getNewRequestTitle(TrxType trxType) {
        switch (trxType) {
            case EDIT_PROFILE:
                return "Pengajuan Perubahan Data Profil Baru";
            case RECRUITMENT_BP:
                return "Pengajuan Rekrutmen BP Baru";
            case RECRUITMENT_BM:
                return "Pengajuan Rekrutmen BM Baru";
            case RECRUITMENT_BD:
                return "Pengajuan Rekrutmen BD Baru";
            case DEMOSI_BM:
                return "Pengajuan Demosi BM Baru";
            case DEMOSI_BD:
                return "Pengajuan Demosi BD Baru";
            case TERMINASI_BP:
                return "Pengajuan Terminasi BP Baru";
            case TERMINASI_BM:
                return "Pengajuan Terminasi BM Baru";
            case TERMINASI_BD:
                return "Pengajuan Terminasi BD Baru";
            case REJOIN_BP:
                return "Pengajuan Rejoin BP Baru";
            case REJOIN_BM:
                return "Pengajuan Rejoin BM Baru";
            case BOP:
                return "Pengajuan BOP Baru";
            default:
                return "Pengajuan Baru";
        }
    }

    /**
     * Gets the body text for a new request notification
     */
    private String getNewRequestBody(TrxType trxType) {
        switch (trxType) {
            case EDIT_PROFILE:
                return "mengajukan perubahan data profil";
            case RECRUITMENT_BP:
                return "mengajukan rekrutmen BP";
            case RECRUITMENT_BM:
                return "mengajukan rekrutmen BM";
            case RECRUITMENT_BD:
                return "mengajukan rekrutmen BD";
            case DEMOSI_BM:
                return "mengajukan demosi BM";
            case DEMOSI_BD:
                return "mengajukan demosi BD";
            case TERMINASI_BP:
                return "mengajukan terminasi BP";
            case TERMINASI_BM:
                return "mengajukan terminasi BM";
            case TERMINASI_BD:
                return "mengajukan terminasi BD";
            case REJOIN_BP:
                return "mengajukan rejoin BP";
            case REJOIN_BM:
                return "mengajukan rejoin BM";
            case BOP:
                return "mengajukan BOP";
            default:
                return "mengajukan persetujuan";
        }
    }

    /**
     * Gets the title for an approval request notification
     */
    private String getApprovalRequestTitle(TrxType trxType) {
        switch (trxType) {
            case EDIT_PROFILE:
                return "Persetujuan Perubahan Data Profil";
            case RECRUITMENT_BP:
                return "Persetujuan Rekrutmen BP";
            case RECRUITMENT_BM:
                return "Persetujuan Rekrutmen BM";
            case RECRUITMENT_BD:
                return "Persetujuan Rekrutmen BD";
            case DEMOSI_BM:
                return "Persetujuan Demosi BM";
            case DEMOSI_BD:
                return "Persetujuan Demosi BD";
            case TERMINASI_BP:
                return "Persetujuan Terminasi BP";
            case TERMINASI_BM:
                return "Persetujuan Terminasi BM";
            case TERMINASI_BD:
                return "Persetujuan Terminasi BD";
            case REJOIN_BP:
                return "Persetujuan Rejoin BP";
            case REJOIN_BM:
                return "Persetujuan Rejoin BM";
            case BOP:
                return "Persetujuan BOP";
            default:
                return "Persetujuan Diperlukan";
        }
    }

    /**
     * Gets the body text for an approval request notification
     */
    private String getApprovalRequestBody(TrxType trxType) {
        switch (trxType) {
            case EDIT_PROFILE:
                return "perubahan data profil";
            case RECRUITMENT_BP:
                return "rekrutmen BP";
            case RECRUITMENT_BM:
                return "rekrutmen BM";
            case RECRUITMENT_BD:
                return "rekrutmen BD";
            case DEMOSI_BM:
                return "demosi BM";
            case DEMOSI_BD:
                return "demosi BD";
            case TERMINASI_BP:
                return "terminasi BP";
            case TERMINASI_BM:
                return "terminasi BM";
            case TERMINASI_BD:
                return "terminasi BD";
            case REJOIN_BP:
                return "rejoin BP";
            case REJOIN_BM:
                return "rejoin BM";
            case BOP:
                return "BOP";
            default:
                return "";
        }
    }

    /**
     * Gets the title for a status update notification
     */
    private String getStatusTitle(TrxType trxType) {
        switch (trxType) {
            case EDIT_PROFILE:
                return "Status Perubahan Data Profil";
            case RECRUITMENT_BP:
                return "Status Rekrutmen BP";
            case RECRUITMENT_BM:
                return "Status Rekrutmen BM";
            case RECRUITMENT_BD:
                return "Status Rekrutmen BD";
            case DEMOSI_BM:
                return "Status Demosi BM";
            case DEMOSI_BD:
                return "Status Demosi BD";
            case TERMINASI_BP:
                return "Status Terminasi BP";
            case TERMINASI_BM:
                return "Status Terminasi BM";
            case TERMINASI_BD:
                return "Status Terminasi BD";
            case REJOIN_BP:
                return "Status Rejoin BP";
            case REJOIN_BM:
                return "Status Rejoin BM";
            case BOP:
                return "Status BOP";
            default:
                return "Status Pengajuan";
        }
    }

    /**
     * Gets the body text for a status update notification
     */
    private String getStatusBody(TrxType trxType) {
        switch (trxType) {
            case EDIT_PROFILE:
                return "perubahan data profil";
            case RECRUITMENT_BP:
                return "rekrutmen BP";
            case RECRUITMENT_BM:
                return "rekrutmen BM";
            case RECRUITMENT_BD:
                return "rekrutmen BD";
            case DEMOSI_BM:
                return "demosi BM";
            case DEMOSI_BD:
                return "demosi BD";
            case TERMINASI_BP:
                return "terminasi BP";
            case TERMINASI_BM:
                return "terminasi BM";
            case TERMINASI_BD:
                return "terminasi BD";
            case REJOIN_BP:
                return "rejoin BP";
            case REJOIN_BM:
                return "rejoin BM";
            case BOP:
                return "BOP";
            default:
                return "";
        }
    }

    /**
     * Gets the candidate name from a recruitment transaction
     *
     * @param header The approval header for a recruitment transaction
     * @return The candidate's full name or empty string if not found
     */
    private String getCandidateName(TrxApprovalHeader header) {
        // Only applicable for recruitment transaction types
        if (header.getTrxType() != TrxType.RECRUITMENT_BP &&
                header.getTrxType() != TrxType.RECRUITMENT_BM &&
                header.getTrxType() != TrxType.RECRUITMENT_BD) {
            return "";
        }

        // Find the recruitment transaction
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(header.getTrxId()).orElse(null);
        if (recruitment != null && recruitment.getFullName() != null) {
            return recruitment.getFullName();
        }

        return "";
    }
}
