package id.co.panindaiichilife.superapp.agent.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import id.co.panindaiichilife.superapp.agent.api.dto.TrxEditProfileDto;
import id.co.panindaiichilife.superapp.agent.api.dto.batch.JobCreationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.AgentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.SimpleAgentV2Dto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.UserDto;
import id.co.panindaiichilife.superapp.agent.api.filter.AgentFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.TrxEditProfileFilter;
import id.co.panindaiichilife.superapp.agent.api.form.ProfileAgentForm;
import id.co.panindaiichilife.superapp.agent.api.form.cms.AgentForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalEditProfileDto;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.core.service.BatchJobService;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.*;
import id.co.panindaiichilife.superapp.agent.model.*;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.RoleRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxEditProfileRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class AgentService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AgentRepository agentRepository;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final ApprovalService approvalService;
    private final TrxEditProfileRepository trxEditProfileRepository;
    private final BatchJobService batchJobService;
    private final AmazonS3Service amazonS3Service;
    @Qualifier("agentImportJob")
    private final Job agentImportJob;
    private final PortalProvider portalProvider;
    private final RedisService redisService;

    public Page<AgentDto> findAll(Pageable pageable, AgentFilter filter) {
        Page<Agent> agents = agentRepository.findAll(filter, pageable);
        return BaseDto.of(AgentDto.class, agents, pageable);
    }

    public AgentDto findOne(Long id) {
        Agent data = agentRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(AgentDto.class, data);
    }

    @Transactional
    public AgentDto add(AgentForm agentForm) {
        Channel channel = agentForm.getDistributionCode() == DistributionCode.A ? Channel.AGE : Channel.BAN;
        Role role = roleRepository.findByCode("ROLE_" + channel + "_" + agentForm.getLevel());
        User.Status status = agentForm.getStatus() == AgentStatus.A ? User.Status.Active : User.Status.Inactive;

        User user = new User();
        user.setIsAgent(Boolean.TRUE);
        user.setChannel(channel);
        user.setUsername(agentForm.getAgentCode());
        user.setEmail(agentForm.getEmail());
        user.setPhone(agentForm.getPhoneNumber());
        user.setName(agentForm.getAgentName());
        user.setStatus(status);
        user.setTimezone("Asia/Jakarta");
        Set<Role> roles = new HashSet<>();
        roles.add(role);
        user.setRoles(roles);
        userRepository.save(user);

        Agent data = new Agent();
        BeanUtils.copyProperties(agentForm, data);
        data.setUser(user);
        agentRepository.save(data);
        return BaseDto.of(AgentDto.class, data);
    }


    @Transactional
    public AgentDto update(Long id, AgentForm agentForm) {
        Agent agent = agentRepository.findById(id).orElseThrow(NotFoundException::new);
        BeanUtils.copyProperties(agentForm, agent);
        agentRepository.save(agent);

        return BaseDto.of(AgentDto.class, agent);
    }

    public void delete(Long id) {
        agentRepository.deleteById(id);
    }

    @CacheableWithTTL(cacheName = "agentCache", key = "#username", ttl = 900, db = 7)
    public AgentDto getProfile(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        return BaseDto.of(AgentDto.class, agent);
    }

    @CacheableWithTTL(cacheName = "simpleAgentCache", key = "#username", ttl = 900, db = 7)
    public SimpleAgentV2Dto getSimpleProfile(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        return BaseDto.of(SimpleAgentV2Dto.class, agent);
    }


    @CacheableWithTTL(cacheName = "agentCache", key = "#username", ttl = 900, db = 7)
    public Object getProfileV2(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElse(null);
        if (null != agent) {
            return BaseDto.of(AgentDto.class, agent);
        } else {
            return BaseDto.of(UserDto.class, user);
        }
    }


    public Page<TrxEditProfileDto> findAllEditProfile(Pageable pageable, TrxEditProfileFilter filter) {
        Page<TrxEditProfile> data = trxEditProfileRepository.findAll(filter, pageable);
        return BaseDto.of(TrxEditProfileDto.class, data, pageable);
    }

    @Transactional
    public void requestEditProfile(String username, ProfileAgentForm profileAgentForm) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // Extract only changed fields and their old/new values
        ProfileChangeData changeData = extractChangedFields(profileAgentForm, agent);

        //Validate Existing field edit on approval
        checkForOverlappingFields(agent, changeData.getNewDataJson());

        // Create edit profile transaction
        TrxEditProfile editProfile = new TrxEditProfile();
        editProfile.setAgent(agent);
        editProfile.setChannel(agent.getChannel());
        editProfile.setData(changeData.getNewDataJson()); // Only changed fields with new values
        editProfile.setOldData(changeData.getOldDataJson()); // Only changed fields with old values
        trxEditProfileRepository.save(editProfile);

        // Request Approval
        TrxApprovalHeader approvalHeader = approvalService.requestApproval(TrxType.EDIT_PROFILE, editProfile.getId(), user, profileAgentForm.getRemark(), agent.getChannel(), Boolean.FALSE);
        editProfile.setApprovalHeader(approvalHeader);
        editProfile.setApprovalStatus(approvalHeader.getApprovalStatus());
        trxEditProfileRepository.save(editProfile);
    }

    @Transactional
    public void reviseEditProfile(String username, Long id, ProfileAgentForm profileAgentForm) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // Revise edit profile transaction
        TrxEditProfile editProfile = trxEditProfileRepository.findById(id).orElseThrow(() -> new NotFoundException("Transaksi ubah profile " + id + " tidak ditemukan"));

        // Validate ownership
        validateAgentOwnership(agent, editProfile);
        if (!editProfile.getApprovalStatus().equals(ApprovalStatus.TERTUNDA)) {
            throw new BadRequestException("Transaksi hanya dengan status Tertunda yang boleh di revisi");
        }

        // Extract only changed fields and their old/new values for revision
        ProfileChangeData changeData = extractChangedFields(profileAgentForm, agent);

        editProfile.setData(changeData.getNewDataJson()); // Only changed fields with new values
        editProfile.setOldData(changeData.getOldDataJson()); // Only changed fields with old values
        trxEditProfileRepository.save(editProfile);

        // Resend Approval
        TrxApprovalHeader approvalHeader = approvalService.resendApproval(TrxType.EDIT_PROFILE, editProfile.getId());
        editProfile.setApprovalStatus(approvalHeader.getApprovalStatus());
        trxEditProfileRepository.save(editProfile);
    }

    @Transactional
    public void cancelEditProfile(String username, Long id) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // Revise edit profile transaction
        TrxEditProfile editProfile = trxEditProfileRepository.findById(id).orElseThrow(() -> new NotFoundException("Transaksi ubah profile " + id + " tidak ditemukan"));

        //Validate ownership
        validateAgentOwnership(agent, editProfile);

        editProfile.setApprovalStatus(ApprovalStatus.DIBATALKAN);
        trxEditProfileRepository.save(editProfile);

        // Cancel Approval
        approvalService.cancelApproval(TrxType.EDIT_PROFILE, editProfile.getId());
    }


    @Transactional
    public void handleTrxEditProfile(Long id, ApprovalStatus approvalStatus, String approvalDetail) {
        TrxEditProfile trxEditProfile = trxEditProfileRepository.findById(id).orElseThrow(() -> new NotFoundException("Transaksi ubah profile " + id + " tidak ditemukan"));
        trxEditProfile.setApprovalStatus(approvalStatus);
        trxEditProfile.setDetailApproval(approvalDetail);
        trxEditProfileRepository.save(trxEditProfile);

        if (approvalStatus.equals(ApprovalStatus.DISETUJUI)) {
            Agent agent = trxEditProfile.getAgent();
            ProfileAgentForm profileAgentForm = new Gson().fromJson(trxEditProfile.getData(), ProfileAgentForm.class);
            copyNonNullPropertiesFromProfileForm(profileAgentForm, agent);
            agentRepository.save(agent);

            User user = agent.getUser();
            updateUserFromProfileAgentForm(user, profileAgentForm);
            userRepository.save(user);

            // Clear agent cache when profile is approved
            redisService.clearCache("userCache", user.getUsername(), 7);
            redisService.clearCache("agentCache", user.getUsername(), 7);
            redisService.clearCache("userAclCache", user.getUsername(), 7);

            // Send updated profile data to portal provider
            sendProfileDataToPortal(agent, profileAgentForm);
        }
    }

    /**
     * Sends the updated agent profile data to the portal provider
     *
     * @param agent            The agent entity with updated information
     * @param profileAgentForm The form containing the updated profile data
     */
    private void sendProfileDataToPortal(Agent agent, ProfileAgentForm profileAgentForm) {
        try {
            PortalEditProfileDto portalEditProfileDto = mapToPortalEditProfileDto(agent, profileAgentForm);

            // Call the portal provider to update the agent info
            portalProvider.updateAgentInfo(agent.getAgentCode(), portalEditProfileDto).execute();

            log.info("Successfully sent updated profile data to portal for agent: {}", agent.getAgentCode());
        } catch (Exception e) {
            log.error("Failed to send updated profile data to portal: {}", e.getMessage(), e);
            // We don't throw the exception here to avoid rolling back the transaction
            // The local update is still successful even if the portal update fails
        }
    }

    /**
     * Maps the agent entity and profile form to the DTO required by the portal API
     *
     * @param agent            The agent entity
     * @param profileAgentForm The form containing the profile data
     * @return A populated PortalEditProfileDto
     */
    private PortalEditProfileDto mapToPortalEditProfileDto(Agent agent, ProfileAgentForm profileAgentForm) {
        PortalEditProfileDto portalEditProfileDto = new PortalEditProfileDto();
        // Map ProfileAgentForm fields to PortalEditProfileDto fields
        portalEditProfileDto.setName(profileAgentForm.getAgentName());
        portalEditProfileDto.setEmail(profileAgentForm.getEmail());
        portalEditProfileDto.setPhone(profileAgentForm.getPhoneNumber());
        portalEditProfileDto.setBankName(profileAgentForm.getBank());
        portalEditProfileDto.setBankAccountNo(profileAgentForm.getBankAccountNumber());
        portalEditProfileDto.setMaritalStatus(profileAgentForm.getMaritalStatus());

        // Handle address - split if needed or use the full address
        String fullAddress = profileAgentForm.getAddress();
        if (fullAddress != null && !fullAddress.isEmpty()) {
            String[] addressParts = fullAddress.split("\\n", 3);
            portalEditProfileDto.setAddress1(addressParts.length > 0 ? addressParts[0] : fullAddress);
            portalEditProfileDto.setAddress2(addressParts.length > 1 ? addressParts[1] : "");
            portalEditProfileDto.setAddress3(addressParts.length > 2 ? addressParts[2] : "");
        }

        return portalEditProfileDto;
    }

    public List<JobCreationDto> runImportAgent() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "Agent_");

        fileNames.forEach((fileName) -> {
            log.info("fileName : {}", fileName);
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(agentImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    private void updateUserFromProfileAgentForm(User user, ProfileAgentForm profileAgentForm) {
        if (profileAgentForm.getAgentName() != null) {
            user.setName(profileAgentForm.getAgentName());
        }
        if (profileAgentForm.getEmail() != null) {
            user.setEmail(profileAgentForm.getEmail());
        }
        if (profileAgentForm.getPhoneNumber() != null) {
            user.setPhone(profileAgentForm.getPhoneNumber());
        }
        if (profileAgentForm.getPhoto() != null) {
            user.setPicture(profileAgentForm.getPhoto());
        }
    }

    /**
     * Copies non-null properties from ProfileAgentForm to Agent entity
     * This prevents null values from overwriting existing data
     *
     * @param profileAgentForm The source form with updated data
     * @param agent            The target agent entity to update
     */
    private void copyNonNullPropertiesFromProfileForm(ProfileAgentForm profileAgentForm, Agent agent) {
        if (profileAgentForm.getAgentName() != null) {
            agent.setAgentName(profileAgentForm.getAgentName());
        }
        if (profileAgentForm.getEmail() != null) {
            agent.setEmail(profileAgentForm.getEmail());
        }
        if (profileAgentForm.getPhoneNumber() != null) {
            agent.setPhoneNumber(profileAgentForm.getPhoneNumber());
        }
        if (profileAgentForm.getBankAccountNumber() != null) {
            agent.setBankAccountNumber(profileAgentForm.getBankAccountNumber());
        }
        if (profileAgentForm.getBank() != null) {
            agent.setBank(profileAgentForm.getBank());
        }
        if (profileAgentForm.getBankAttachment() != null) {
            agent.setBankAttachment(profileAgentForm.getBankAttachment());
        }
        if (profileAgentForm.getKtpAttachment() != null) {
            agent.setKtpAttachment(profileAgentForm.getKtpAttachment());
        }
        if (profileAgentForm.getKkAttachment() != null) {
            agent.setKkAttachment(profileAgentForm.getKkAttachment());
        }
        if (profileAgentForm.getAddress() != null) {
            agent.setAddress(profileAgentForm.getAddress());
        }
        if (profileAgentForm.getMaritalStatus() != null) {
            agent.setMaritalStatus(profileAgentForm.getMaritalStatus());
        }
        if (profileAgentForm.getPhoto() != null) {
            agent.setPhoto(profileAgentForm.getPhoto());
        }
        if (profileAgentForm.getEducation() != null) {
            agent.setEducation(profileAgentForm.getEducation());
        }
        // Note: remark field is not copied to Agent entity as it's only used for approval purposes
    }

    public void validateAgentOwnership(Agent agent, TrxEditProfile editProfile) {
        if (!agent.getId().equals(editProfile.getAgent().getId())) {
            throw new BadRequestException("Transaksi ubah profil ini bukan milik Anda.");
        }
    }


    /**
     * Checks if the new edit request overlaps with fields that are already under review or approval.
     *
     * @param agent       The agent submitting the edit request.
     * @param jsonPayload The JSON payload containing the fields to be edited.
     * @throws BadRequestException If there is an overlap with fields under review or approval.
     */
    public void checkForOverlappingFields(Agent agent, String jsonPayload) {
        // Parse the JSON payload to get the fields being edited
        Set<String> newFields = getFieldsFromJson(jsonPayload);

        // Get existing edit requests for the agent that are under review or approval
        List<TrxEditProfile> existingRequests = trxEditProfileRepository.findByAgentAndApprovalStatusIn(
                agent, Arrays.asList(ApprovalStatus.BARU, ApprovalStatus.TERTUNDA, ApprovalStatus.MENUNGGU_PERSETUJUAN));

        // Check for overlapping fields
        for (TrxEditProfile request : existingRequests) {
            Set<String> existingFields = getFieldsFromJson(request.getData());
            existingFields.retainAll(newFields); // Get intersection of fields

            if (!existingFields.isEmpty()) {
                throw new BadRequestException("Tidak dapat mengajukan perubahan untuk field yang sedang dalam proses approval: " + existingFields);
            }
        }
    }

    /**
     * Extracts field names from the JSON payload.
     *
     * @param jsonPayload The JSON payload to parse.
     * @return A set of field names.
     * @throws Exception If there is an error parsing the JSON.
     */
    private Set<String> getFieldsFromJson(String jsonPayload) {
        try {
            Map<String, Object> payloadMap = objectMapper.readValue(jsonPayload, new TypeReference<Map<String, Object>>() {
            });
            return payloadMap.keySet();
        } catch (Exception e) {
            throw new InternalServerErrorException("Tidak dapat mengajukan perubahan karena kesalahan server");
        }
    }

    public String determineLicenseStatusAAJI(Agent agent) {
        if (agent == null) {
            return "Belum Berlisensi";
        }

        // Check if agent has AAJI license
        if (agent.getLicenseNumberAAJI() == null || agent.getLicenseNumberAAJI().trim().isEmpty()) {
            return "Belum Berlisensi";
        }

        // Check if AAJI license is expired
        if (agent.getLicenseExpiredDateAAJI() != null &&
                agent.getLicenseExpiredDateAAJI().isBefore(LocalDate.now())) {
            return "Kadaluarsa";
        }

        return "Aktif";
    }

    public String determineLicenseStatusAASI(Agent agent) {
        if (agent == null) {
            return "Belum Berlisensi";
        }

        // Check if agent has AASI license
        if (agent.getLicenseNumberAASI() == null || agent.getLicenseNumberAASI().trim().isEmpty()) {
            return "Belum Berlisensi";
        }

        // Check if AASI license is expired
        if (agent.getLicenseExpiredDateAASI() != null &&
                agent.getLicenseExpiredDateAASI().isBefore(LocalDate.now())) {
            return "Kadaluarsa";
        }

        return "Aktif";
    }

    /**
     * Manually clears the agent cache using the RedisService
     * This method works reliably even when called from Kafka consumers or internal method calls
     * where Spring AOP might not be properly applied
     *
     * @param username The username of the agent whose cache should be cleared
     */
    public void manualClearAgentCache(String username) {
        redisService.clearAgentCache(username);
    }

    /**
     * Extracts only the changed fields from ProfileAgentForm and creates old/new data JSON
     * This method compares the form data with current agent data and only captures fields that are being changed
     *
     * @param profileAgentForm The form with new values
     * @param agent            The current agent entity
     * @return ProfileChangeData containing old and new values for changed fields only
     */
    private ProfileChangeData extractChangedFields(ProfileAgentForm profileAgentForm, Agent agent) {
        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();

        // Check each field for changes (only include non-null form values)
        if (profileAgentForm.getAgentName() != null && !profileAgentForm.getAgentName().equals(agent.getAgentName())) {
            oldValues.put("agentName", agent.getAgentName());
            newValues.put("agentName", profileAgentForm.getAgentName());
        }

        if (profileAgentForm.getEmail() != null && !profileAgentForm.getEmail().equals(agent.getEmail())) {
            oldValues.put("email", agent.getEmail());
            newValues.put("email", profileAgentForm.getEmail());
        }

        if (profileAgentForm.getPhoneNumber() != null && !profileAgentForm.getPhoneNumber().equals(agent.getPhoneNumber())) {
            oldValues.put("phoneNumber", agent.getPhoneNumber());
            newValues.put("phoneNumber", profileAgentForm.getPhoneNumber());
        }

        if (profileAgentForm.getBankAccountNumber() != null && !profileAgentForm.getBankAccountNumber().equals(agent.getBankAccountNumber())) {
            oldValues.put("bankAccountNumber", agent.getBankAccountNumber());
            newValues.put("bankAccountNumber", profileAgentForm.getBankAccountNumber());
        }

        if (profileAgentForm.getBank() != null && !profileAgentForm.getBank().equals(agent.getBank())) {
            oldValues.put("bank", agent.getBank());
            newValues.put("bank", profileAgentForm.getBank());
        }

        if (profileAgentForm.getBankAttachment() != null && !profileAgentForm.getBankAttachment().equals(agent.getBankAttachment())) {
            oldValues.put("bankAttachment", agent.getBankAttachment());
            newValues.put("bankAttachment", profileAgentForm.getBankAttachment());
        }

        if (profileAgentForm.getKtpAttachment() != null && !profileAgentForm.getKtpAttachment().equals(agent.getKtpAttachment())) {
            oldValues.put("ktpAttachment", agent.getKtpAttachment());
            newValues.put("ktpAttachment", profileAgentForm.getKtpAttachment());
        }

        if (profileAgentForm.getKkAttachment() != null && !profileAgentForm.getKkAttachment().equals(agent.getKkAttachment())) {
            oldValues.put("kkAttachment", agent.getKkAttachment());
            newValues.put("kkAttachment", profileAgentForm.getKkAttachment());
        }

        if (profileAgentForm.getAddress() != null && !profileAgentForm.getAddress().equals(agent.getAddress())) {
            oldValues.put("address", agent.getAddress());
            newValues.put("address", profileAgentForm.getAddress());
        }

        if (profileAgentForm.getMaritalStatus() != null && !profileAgentForm.getMaritalStatus().equals(agent.getMaritalStatus())) {
            oldValues.put("maritalStatus", agent.getMaritalStatus());
            newValues.put("maritalStatus", profileAgentForm.getMaritalStatus());
        }

        if (profileAgentForm.getPhoto() != null && !profileAgentForm.getPhoto().equals(agent.getPhoto())) {
            oldValues.put("photo", agent.getPhoto());
            newValues.put("photo", profileAgentForm.getPhoto());
        }

        if (profileAgentForm.getEducation() != null && !profileAgentForm.getEducation().equals(agent.getEducation())) {
            oldValues.put("education", agent.getEducation());
            newValues.put("education", profileAgentForm.getEducation());
        }

        // Always include remark in new values if provided (for approval purposes)
        if (profileAgentForm.getRemark() != null) {
            newValues.put("remark", profileAgentForm.getRemark());
        }

        return new ProfileChangeData(
                new Gson().toJson(oldValues),
                new Gson().toJson(newValues)
        );
    }

    /**
     * Inner class to hold old and new data for profile changes
     */
    private static class ProfileChangeData {
        private final String oldDataJson;
        private final String newDataJson;

        public ProfileChangeData(String oldDataJson, String newDataJson) {
            this.oldDataJson = oldDataJson;
            this.newDataJson = newDataJson;
        }

        public String getOldDataJson() {
            return oldDataJson;
        }

        public String getNewDataJson() {
            return newDataJson;
        }
    }
}
