package id.co.panindaiichilife.superapp.agent.core.data.repository;

import id.co.panindaiichilife.superapp.agent.core.util.ExpressionUtils;
import jakarta.persistence.EntityGraph;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.metamodel.SingularAttribute;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

public class BaseRepositoryImpl<E, I extends Serializable>
        extends SimpleJpaRepository<E, I> implements BaseRepository<E, I> {

    protected EntityManager entityManager;

    public BaseRepositoryImpl(JpaEntityInformation<E, ?> entityInformation,
                              EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.entityManager = entityManager;
    }

    /**
     * @deprecated Replaced by findById
     */
    @Override
    @Deprecated(since = "1.0", forRemoval = true)
    public E findOne(I id) {
        Optional<E> result = super.findById(id);
        return result.orElse(null);
    }

    @Override
    public Optional<E> findByIdThenInitialize(I id, String... attributes) {
        return findByIdThenInitialize(id, graph -> {
            graph.addAttributeNodes(attributes);
        });
    }

    @Override
    public Optional<E> findByIdThenInitialize(I id, Consumer<EntityGraph<E>> consumer) {
        EntityGraph<E> graph = this.entityManager.createEntityGraph(getDomainClass());
        consumer.accept(graph);

        Map<String, Object> hints = Collections.singletonMap("javax.persistence.loadgraph", graph);

        try {
            return Optional.of(this.entityManager.find(getDomainClass(), id, hints));
        } catch (NoResultException ex) {
            return Optional.empty();
        }
    }

    @Override
    public E findUnique(String field, Object value) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<E> query = cb.createQuery(getDomainClass());

        Root<E> root = query.from(getDomainClass());

        query.where(
                cb.equal(root.get(field), value)
        ).select(root);

        try {
            return entityManager
                    .createQuery(query)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (NoResultException ex) {
            return null;
        }
    }

    @Override
    public boolean exists(String field, Object value) {
        return exists(field, value, null);
    }

    @Override
    @SuppressWarnings("unchecked")
    public boolean exists(String field, Object value, I excludeId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> query = cb.createQuery(Long.class);

        Root<E> root = query.from(getDomainClass());
        SingularAttribute idAttr = ExpressionUtils.getIdAttribute(entityManager, getDomainClass());

        query.where(
                cb.equal(root.get(field), value),
                excludeId == null ? cb.conjunction() : cb.notEqual(root.get(idAttr), excludeId)
        ).select(cb.count(root));

        return entityManager
                .createQuery(query)
                .getSingleResult() > 0l;
    }

    @Override
    public void refresh(E entity) {
        entityManager.refresh(entity);
    }

    /**
     * @deprecated Replaced by deleteById
     */
    @Override
    @Deprecated(since = "1.0", forRemoval = true)
    @Transactional
    public void delete(I id) {
        super.deleteById(id);
    }
}
