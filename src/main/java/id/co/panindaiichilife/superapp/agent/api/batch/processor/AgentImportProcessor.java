package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import com.google.gson.Gson;
import id.co.panindaiichilife.superapp.agent.api.batch.dto.AgentImportDto;
import id.co.panindaiichilife.superapp.agent.enums.AgentStatus;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.enums.UserType;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.Branch;
import id.co.panindaiichilife.superapp.agent.model.Role;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.BranchRepository;
import id.co.panindaiichilife.superapp.agent.repository.RoleRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;

import java.util.HashSet;
import java.util.Set;

@Slf4j
@RequiredArgsConstructor
public class AgentImportProcessor implements ItemProcessor<AgentImportDto, Agent> {

    private static final String ROLE_PREFIX = "ROLE_";

    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final AgentRepository agentRepository;
    private final BranchRepository branchRepository;

    @Override
    public Agent process(AgentImportDto item) {
        try {
            Channel channel = determineChannel(item.getDistributionCode());
            Role role = findRoleByNameAndLevel(item.getRoleName(), channel, item.getLevel());
            User.Status status = determineUserStatus(item.getStatus());

            Set<Branch> branches = collectBranches(item);
            Agent leader = findLeader(item.getLeaderCode());

            User user = createOrUpdateUser(item, channel, status, branches, role);
            return createOrUpdateAgent(item, user, channel, leader);
        } catch (Exception e) {
            log.debug("Agent : {}", new Gson().toJson(item));
        }
        return null;
    }

    /**
     * Determines the channel based on distribution code
     */
    private Channel determineChannel(String distributionCode) {
        return DistributionCode.valueOf(distributionCode).equals(DistributionCode.A) ? Channel.AGE : Channel.BAN;
    }

    /**
     * Determines user status based on agent status
     */
    private User.Status determineUserStatus(String status) {
        return AgentStatus.valueOf(status).equals(AgentStatus.A) ? User.Status.Active : User.Status.Inactive;
    }

    /**
     * Finds leader agent by leader code
     */
    private Agent findLeader(String leaderCode) {
        if (leaderCode != null) {
            return agentRepository.findTopByAgentCode(leaderCode).orElse(null);
        }
        return null;
    }

    /**
     * Collects all branches for the agent
     */
    private Set<Branch> collectBranches(AgentImportDto item) {
        Set<Branch> branches = new HashSet<>();

        addBranchIfExists(branches, item.getBranchCode());
        addBranchIfExists(branches, item.getMainBranchCode());
        addSubBranches(branches, item.getSubBranchCode());

        return branches;
    }

    /**
     * Adds a branch to the set if it exists
     */
    private void addBranchIfExists(Set<Branch> branches, String branchCode) {
        if (branchCode != null) {
            branchRepository.findByBranchCode(branchCode)
                    .ifPresent(branches::add);
        }
    }

    /**
     * Adds sub-branches based on sub-branch code string
     */
    private void addSubBranches(Set<Branch> branches, String subBranchCode) {
        if (subBranchCode == null) {
            return;
        }

        if (subBranchCode.contains(":")) {
            processFormattedSubBranches(branches, subBranchCode);
        } else {
            addBranchIfExists(branches, subBranchCode);
        }
    }

    /**
     * Processes formatted sub-branches (format: MAIN: SUB1, SUB2, SUB3...)
     */
    private void processFormattedSubBranches(Set<Branch> branches, String subBranchCode) {
        String[] parts = subBranchCode.split(":");
        if (parts.length > 1 && !parts[1].trim().isEmpty()) {
            String[] subBranchCodes = parts[1].trim().split(",");
            for (String subCode : subBranchCodes) {
                addBranchIfExists(branches, subCode.trim());
            }
        }
    }

    /**
     * Creates or updates user entity
     */
    private User createOrUpdateUser(AgentImportDto item, Channel channel, User.Status status,
                                    Set<Branch> branches, Role role) {
        User user = userRepository.findByUsername(item.getAgentCode()).orElse(new User());

        user.setBranches(branches);
        user.setIsAgent(Boolean.TRUE);
        user.setUserType(UserType.AGENT);
        user.setChannel(channel);
        user.setUsername(item.getAgentCode());
        user.setName(item.getAgentName());
        user.setStatus(status);
        user.setEmail(item.getEmail());
        user.setTimezone("Asia/Jakarta");

        Set<Role> roles = new HashSet<>();
        roles.add(role);
        user.setRoles(roles);

        return userRepository.save(user);
    }

    /**
     * Creates or updates agent entity
     */
    private Agent createOrUpdateAgent(AgentImportDto item, User user, Channel channel, Agent leader) {
        Agent agent = agentRepository.findTopByAgentCode(item.getAgentCode()).orElse(new Agent());

        if (agent.getUser() == null) {
            agent.setUser(user);
        }

        BeanUtils.copyProperties(item, agent);
        agent.setChannel(channel);
        agent.setLeader(leader);
        agent.setMBranchCode(item.getMainBranchCode());
        agent.setMBranchName(item.getMainBranchName());
        agent.setSBranchCode(item.getSubBranchCode());
        agent.setSBranchName(item.getSubBranchName());
        agent.setStatus(AgentStatus.valueOf(item.getStatus()));
        agent.setDistributionCode(DistributionCode.valueOf(item.getDistributionCode()));
        agent.setDob(item.getDob() != null ? item.getDob().toLocalDate() : null);
        agent.setLicenseExpiredDateAAJI(item.getLicenseExpiredDateAAJI() != null ?
                item.getLicenseExpiredDateAAJI().toLocalDate() : null);
        agent.setLicenseExpiredDateAASI(item.getLicenseExpiredDateAASI() != null ?
                item.getLicenseExpiredDateAASI().toLocalDate() : null);

        return agent;
    }

    /**
     * Finds the appropriate role based on role name, channel, and level
     *
     * @param roleName The role name from import data (Agent, Secretary, Admin)
     * @param channel  The channel (AGE or BAN)
     * @param level    The level from import data
     * @return The Role entity or null if not found
     */
    private Role findRoleByNameAndLevel(String roleName, Channel channel, String level) {
        if (roleName == null || roleName.trim().isEmpty()) {
            log.warn("Role name is null or empty, falling back to level-based role finding");
            return roleRepository.findByCode(ROLE_PREFIX + channel + "_" + level);
        }

        String roleCode;
        String normalizedRoleName = roleName.trim();

        if ("Agent".equalsIgnoreCase(normalizedRoleName)) {
            roleCode = ROLE_PREFIX + channel + "_" + level;
        } else {
            String upperRoleName = normalizedRoleName.toUpperCase();
            roleCode = ROLE_PREFIX + channel + "_" + upperRoleName + "_" + level;
        }

        log.debug("Looking for role with code: {}", roleCode);
        Role role = roleRepository.findByCode(roleCode);

        if (role == null) {
            log.warn("Role not found with code: {}. RoleName: {}, Channel: {}, Level: {}",
                    roleCode, roleName, channel, level);
        }

        return role;
    }
}