package id.co.panindaiichilife.superapp.agent.config;

import id.co.panindaiichilife.superapp.agent.core.security.EncryptionSuperAppService;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

@Slf4j
@RequiredArgsConstructor
public class CustomAuthenticationProvider implements AuthenticationProvider {

    @Qualifier("portalAuthenticationProvider")
    private final AuthenticationProvider portalAuthenticationProvider;
    @Qualifier("dbAuthenticationProvider")
    private final AuthenticationProvider dbAuthenticationProvider;
    private final UserService userService;
    private final EncryptionSuperAppService encryptionService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        // Get username
        String username = encryptionService.decrypt(authentication.getName());

        // Check if user is admin based on your business logic
        boolean isPortalAuthentication = userService.checkIsAgent(username) || userService.checkIsUserStaff(username);

        if (isPortalAuthentication) {
            return portalAuthenticationProvider.authenticate(authentication);
        } else {
            return dbAuthenticationProvider.authenticate(authentication);
        }
    }

    @Override
    public boolean supports(Class<?> authenticationClass) {
        // Support authentication types supported by either provider
        return portalAuthenticationProvider.supports(authenticationClass) ||
                dbAuthenticationProvider.supports(authenticationClass);
    }
}
