package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.batch.JobCreationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.BankDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BankFilter;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionService;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.AgentService;
import id.co.panindaiichilife.superapp.agent.service.BankService;
import id.co.panindaiichilife.superapp.agent.service.BranchService;
import id.co.panindaiichilife.superapp.agent.service.FirebaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController("publicController")
@RequestMapping("/api/public")
@Tag(name = "Public API - Testing Only", description = "API Public")
@Slf4j
@RequiredArgsConstructor
public class PublicController {

    private final EncryptionService encryptionService;
    private final AgentService agentService;
    private final BranchService branchService;
    private final BankService bankService;
    private final FirebaseService firebaseService;
    private final UserRepository userRepository;

    @Operation(summary = "Get Encryption Test Data")
    @GetMapping(value = "encrypt/{username}")
    public ResponseEntity<String> encryptTest(@PathVariable String username) {
        return ResponseEntity.ok(encryptionService.encrypt(username, "SALTTest"));
    }

    @Operation(summary = "Get Encryption Test Data")
    @GetMapping(value = "decrypt/{key}")
    public ResponseEntity<String> decryptTest(@PathVariable String key) {

        return ResponseEntity.ok(encryptionService.decrypt(key));
    }

    @Operation(summary = "Health Check API")
    @GetMapping(value = "health-check")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("API Service is running");
    }

    @Operation(summary = "Test - Run Job Import Agent")
    @GetMapping(value = "sync-agent")
    public List<JobCreationDto> syncAgent() {
        return agentService.runImportAgent();
    }

    @Operation(summary = "Test - Run Sync Branch")
    @GetMapping(value = "sync-branch")
    public void syncBranch() {
        branchService.syncBranch();
    }

    @Operation(summary = "Get Public Bank")
    @GetMapping(value = "bank")
    public Page<BankDto> getBank(@ParameterObject @ModelAttribute("filter") BankFilter filter,
                                 @ParameterObject @PageableDefault(sort = "bankName", direction = Sort.Direction.ASC) Pageable pageable) {

        return bankService.findAll(pageable, filter);
    }

    @Operation(summary = "Test - Push Notification")
    @PostMapping(value = "push-notification/{username}")
    public void pushNotification(@PathVariable String username, @RequestBody NotificationDto notificationDto) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        firebaseService.sendNotification(Collections.singletonList(user), notificationDto);
    }

}
