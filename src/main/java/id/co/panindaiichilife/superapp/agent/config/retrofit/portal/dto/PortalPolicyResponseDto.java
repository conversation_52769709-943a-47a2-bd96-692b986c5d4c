package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalPolicyResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("totalData")
    @JsonProperty("totalData")
    private int totalData;

    @SerializedName("data")
    @JsonProperty("data")
    private List<PolicyDataDto> data;

    @Data
    public static class PolicyDataDto {

        @SerializedName("policyNumber")
        @JsonProperty("policyNumber")
        private String policyNumber;

        @SerializedName("policyHolderName")
        @JsonProperty("policyHolderName")
        private String policyHolderName;

        @SerializedName("insuredName")
        @JsonProperty("insuredName")
        private String insuredName;

        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("agentName")
        @JsonProperty("agentName")
        private String agentName;

        @SerializedName("product")
        @JsonProperty("product")
        private String product;

        @SerializedName("policyStatus")
        @JsonProperty("policyStatus")
        private String policyStatus;

        @SerializedName("riskCommencementDate")
        @JsonProperty("riskCommencementDate")
        private String riskCommencementDate;

        @SerializedName("paymentFrequency")
        @JsonProperty("paymentFrequency")
        private String paymentFrequency;

        @SerializedName("dueDate")
        @JsonProperty("dueDate")
        private String dueDate;

        @SerializedName("basicPremium")
        @JsonProperty("basicPremium")
        private double basicPremium;

        @SerializedName("rtu")
        @JsonProperty("rtu")
        private double rtu;

        @SerializedName("currency")
        @JsonProperty("currency")
        private String currency;

        @SerializedName("twisting")
        @JsonProperty("twisting")
        private String twisting;

        @SerializedName("nlgStatus")
        @JsonProperty("nlgStatus")
        private String nlgStatus;

        @SerializedName("lastPaidDate")
        @JsonProperty("lastPaidDate")
        private String lastPaidDate;

        @SerializedName("lapseDate")
        @JsonProperty("lapseDate")
        private String lapseDate;
    }
}
