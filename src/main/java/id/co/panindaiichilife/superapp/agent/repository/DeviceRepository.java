package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.Device;
import id.co.panindaiichilife.superapp.agent.model.User;

import java.util.List;
import java.util.Optional;

public interface DeviceRepository extends BaseRepository<Device, Long> {
    List<Device> findByUserAndStatus(User user, Device.Status status);

    Optional<Device> findByDeviceIdAndUser(String deviceId, User user);

    Optional<Device> findTopByDeviceIdAndUser(String deviceId, User user);

    Boolean existsByDeviceIdAndStatus(String deviceId, Device.Status status);
}
