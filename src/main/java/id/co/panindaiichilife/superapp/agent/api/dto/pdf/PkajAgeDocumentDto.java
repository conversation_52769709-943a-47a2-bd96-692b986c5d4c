package id.co.panindaiichilife.superapp.agent.api.dto.pdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * DTO for PKAJ-AGE (<PERSON><PERSON><PERSON><PERSON>nsi <PERSON>) document generation parameters.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PkajAgeDocumentDto {

    private String pkajNumber;

    private String address;

    private String candidateName;

    private String candidateBirthOfPlace;

    private String candidateDomicileCity;

    private String candidateKtp;

    private String candidateSignature;

    private String candidateParaf;

    private String casSignature;

    private String casParaf;

    private String casName;

    private String casRole;


    /**
     * Additional custom variables for the template.
     */
    private Map<String, Object> additionalVariables;



    /**
     * Convert DTO to variables map for template processing.
     *
     * @return Map of variables for template injection
     */
    public Map<String, Object> toVariablesMap() {
        Map<String, Object> variables = new HashMap<>();

        addDocumentVariables(variables);
        addCandidateVariables(variables);
        addSignatureVariables(variables);
        PdfDocumentUtils.addStandardDateTimeVariables(variables);
        PdfDocumentUtils.addAdditionalVariables(variables, additionalVariables);

        return variables;
    }

    /**
     * Add document-specific variables to the map.
     */
    private void addDocumentVariables(Map<String, Object> variables) {
        variables.put("pkajNumber", PdfDocumentUtils.getStringOrEmpty(pkajNumber));
        variables.put("address", PdfDocumentUtils.getStringOrEmpty(address));
    }

    /**
     * Add candidate information variables to the map.
     */
    private void addCandidateVariables(Map<String, Object> variables) {
        variables.put("candidateName", PdfDocumentUtils.getStringOrEmpty(candidateName));
        variables.put("candidateBirthOfPlace", PdfDocumentUtils.getStringOrEmpty(candidateBirthOfPlace));
        variables.put("candidateDomicileCity", PdfDocumentUtils.getStringOrEmpty(candidateDomicileCity));
        variables.put("candidateKtp", PdfDocumentUtils.getStringOrEmpty(candidateKtp));
    }

    /**
     * Add signature information variables to the map.
     */
    private void addSignatureVariables(Map<String, Object> variables) {
        variables.put("candidateSignature", PdfDocumentUtils.getStringOrEmpty(candidateSignature));
        variables.put("candidateParaf", PdfDocumentUtils.getStringOrEmpty(candidateParaf));
        variables.put("casSignature", PdfDocumentUtils.getStringOrEmpty(casSignature));
        variables.put("casParaf", PdfDocumentUtils.getStringOrEmpty(casParaf));
        variables.put("casName", PdfDocumentUtils.getStringOrEmpty(casName));
        variables.put("casRole", PdfDocumentUtils.getStringOrEmpty(casRole));
    }
}
