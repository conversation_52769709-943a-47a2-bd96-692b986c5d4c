package id.co.panindaiichilife.superapp.agent.service.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxPolicyTransferDto;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxPolicyTransferForm;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ApprovalForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalGetPolicyServicingResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.enums.*;
import id.co.panindaiichilife.superapp.agent.model.*;
import id.co.panindaiichilife.superapp.agent.repository.*;
import id.co.panindaiichilife.superapp.agent.service.ApprovalService;
import id.co.panindaiichilife.superapp.agent.service.FirebaseService;
import id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.TerminationNotificationTemplates;
import kotlin.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import retrofit2.Call;
import retrofit2.Response;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrxPolicyTransferService {
    private final TrxPolicyTransferRepository trxPolicyTransferRepository;
    private final TrxTerminationRepository trxTerminationRepository;
    private final UserRepository userRepository;
    private final AgentRepository agentRepository;
    private final TrxTerminationService trxTerminationService;
    private final ApprovalService approvalService;
    private final FirebaseService firebaseService;
    private final BranchRepository branchRepository;
    private final PortalProvider portalProvider;

    private final Set<AgentStatus> ALLOWED_AGENT_STATUSES_FOR_POLICY_TRANSFER = Set.of(
            AgentStatus.S, AgentStatus.A);

    private Long getPolicyServicingByAgentCode(String agentCode) {
        Long count = 0L;
        try {
            Call<PortalGetPolicyServicingResponseDto> call = portalProvider.getPolicyServicingByAgentCode(agentCode);
            Response<PortalGetPolicyServicingResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalGetPolicyServicingResponseDto body = response.body();
                if (body.getStatusCode().equals("200")) {
                    log.info("Retrieve policy servicing by agent {} succeed", agentCode);
                    count = body.getTotalRecords();
                } else {
                    log.warn("Failed to retrieve policy servicing by agent");
                }
            } else {
                log.error("Failed to retrieve policy servicing by agent");
            }
        } catch (Exception e) {
            log.error("Failed to retrieve policy servicing by agent due to: {}", e.getMessage());
        }

        return count;
    }

    private TrxPolicyTransferDto enrichData(TrxPolicyTransfer trxPolicyTransfer) {
        Agent target = trxPolicyTransfer.getSource().getAgent();

        Branch branch = branchRepository.findByBranchCode(target.getBranchCode())
                .orElseThrow(() -> new NotFoundException(String.format("Branch %s not found", target.getBranchCode())));

        TrxPolicyTransferDto dto = TrxPolicyTransferDto.of(TrxPolicyTransferDto.class, trxPolicyTransfer);
        dto.setBranchCode(branch.getBranchCode());
        dto.setBranchName(branch.getBranchName());

        Long totalPolicies = getPolicyServicingByAgentCode(target.getAgentCode());
        dto.setTotalPolicies(totalPolicies);

        trxTerminationRepository.findTopByTrxPolicyTransferId(trxPolicyTransfer.getId())
                .ifPresent(termination -> dto.setTrxTerminationId(termination.getId()));

        return dto;
    }

    public TrxPolicyTransferDto getPolicyTransferByTerminationId(Long terminationId) {
        TrxTermination termination = trxTerminationRepository.findById(terminationId)
                .orElseThrow(NotFoundException::new);
        if (termination.getTrxPolicyTransfer() == null) {
            throw new NotFoundException("No policy transfer found for termination %d".formatted(terminationId));
        }

        return enrichData(termination.getTrxPolicyTransfer());
    }

    public Page<TrxPolicyTransferDto> getPolicyTransferByTarget(String agentCode, String searchQuery, Pageable pageable) {
        Page<TrxPolicyTransfer> queryResult = trxPolicyTransferRepository
                .findPolicyTransferByTargetAgentCode(agentCode, searchQuery == null ? "" : searchQuery, pageable);

        return new PageImpl<>(
                queryResult.getContent()
                        .stream()
                        .map(this::enrichData).toList(),
                pageable,
                queryResult.getTotalElements());
    }

    @Transactional
    public TrxPolicyTransferDto createPolicyTransfer(String username, TrxPolicyTransferForm form) {
        Long terminationId = form.getTrxTerminationId();

        log.info("Start initiating policy transfer for termination {}", terminationId);

        User currentUser = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        String authUserAgentCode = currentUser.getAgent().getAgentCode(); // assume current user is the leader

        TrxTermination termination = trxTerminationRepository.findById(form.getTrxTerminationId())
                .orElseThrow(() -> new NotFoundException("No termination request found for %d".formatted(form.getTrxTerminationId())));

        Agent targetAgent = agentRepository.findTopByAgentCode(form.getTargetAgentCode()).orElseThrow(NotFoundException::new);
        User source = termination.getTarget(); // source of policy transfer is a target of the termination request

        // validate that the creator of this policy transfer is the leader of the target of termination
        Agent agentLeader = termination.getTarget().getAgent().getLeader();

        if (!isAuthUserLeader(authUserAgentCode, agentLeader)) {
            throw new BadRequestException("Invalid operation access for current user.");
        }

        verifyAgentHierarchyValidity(targetAgent, agentLeader, authUserAgentCode);

        // target must not source
        if (targetAgent.getAgentCode().equals(source.getAgent().getAgentCode())) {
            throw new BadRequestException("Target agent should not be same as source agent.");
        }

        // validate target's status should be A/S
        if (!ALLOWED_AGENT_STATUSES_FOR_POLICY_TRANSFER.contains(targetAgent.getStatus())) {
            throw new BadRequestException("Status agent invalid.");
        }

        // persist policy transfer updates
        TrxPolicyTransfer policyTransfer = new TrxPolicyTransfer();
        policyTransfer.setAssignedBy(currentUser);
        policyTransfer.setTarget(targetAgent.getUser());
        policyTransfer.setSource(source);
        policyTransfer.setStatus(PolicyTransferStatus.PENDING);

        // if target agent is a leader, directly set status to approved
        if (isLeaderSelfAssigningPolicyTransfer(targetAgent, agentLeader)) {
            policyTransfer.setStatus(PolicyTransferStatus.APPROVED);
        }

        policyTransfer = trxPolicyTransferRepository.save(policyTransfer);

        trxTerminationService.setPolicyTransfer(policyTransfer, termination);

        log.info("Policy transfer created, requesting approval from {} for termination {}",
                targetAgent.getUser().getId(), form.getTrxTerminationId());

        // if policy transfer already approved and was assigned to leader, while termination was not submitted by
        // leader, then invoke auto approve for direct upline
        if (isPolicyTransferApproved(policyTransfer) && isAbleToAutoApproveDirectUpline(termination)) {
            invokeAutoApproveDirectLeader(termination, agentLeader);
        } else {
            // send notification to the target of policy transfer if not auto approved
            sendPolicyAssignmentNotificationToTarget(targetAgent, source.getAgent(), termination,
                    termination.getTrxType());
        }

        TrxPolicyTransferDto response = TrxPolicyTransferDto.of(TrxPolicyTransferDto.class, policyTransfer);
        response.setTrxTerminationId(terminationId);
        return response;
    }

    private void invokeAutoApproveDirectLeader(Long terminationId, Agent agentLeader) {
        TrxTermination termination = trxTerminationRepository.findById(terminationId)
                .orElseThrow(() -> new NotFoundException(String.format("No termination request found with id %d",
                        terminationId)));
        invokeAutoApproveDirectLeader(termination, agentLeader);
    }

    private void invokeAutoApproveDirectLeader(TrxTermination termination, Agent agentLeader) {
        log.info("Directly mark current level of termination as approved");

        TrxApprovalHeader approvalHeader = termination.getApprovalHeader();

        ApprovalForm form = new ApprovalForm(approvalHeader.getId(), ApprovalStatus.DISETUJUI);
        form.setRemarks("auto approved");

        approvalService.processApproval(form, agentLeader.getUser());
    }

    private TrxTermination retrieveTerminationRequest(Long terminationId) {
        return trxTerminationRepository.findById(terminationId)
                .orElseThrow(NotFoundException::new);
    }

    private void verifyAgentHierarchyValidity(Agent target, Agent agentLeader, String authUserAgentCode) {
        // validate that new target MUST BE the leader themselves or the members
        if (!isLeaderSelfAssigningPolicyTransfer(target, agentLeader) &&
                (target.getLeader() != null &&
                        !target.getLeader().getAgentCode().equals(authUserAgentCode))) {
            throw new BadRequestException("Invalid agent hierarchy.");
        }
    }

    @Transactional
    public TrxPolicyTransferDto updateTarget(String username, Long terminationId, String targetAgentCode) {
        log.info("Start policy transfer target reassignment for termination {}", terminationId);

        TrxTermination termination = retrieveTerminationRequest(terminationId);

        User currentUser = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        String authUserAgentCode = currentUser.getAgent().getAgentCode(); // get current user's agent code

        TrxPolicyTransfer policyTransfer = retrieveTerminationRequest(terminationId).getTrxPolicyTransfer();
        Agent newTargetAgent = agentRepository.findTopByAgentCode(targetAgentCode)
                .orElseThrow(() ->
                        new NotFoundException(String.format("No agent was found with code %s", targetAgentCode)));
        String sourceAgentCode = policyTransfer.getSource().getAgent().getAgentCode();
        Agent agentLeader = termination.getTarget().getAgent().getLeader();

        // validate that current user is allowed for this operation (leader only)
        if (!isAuthUserLeader(authUserAgentCode, agentLeader)) {
            throw new BadRequestException("Invalid operation access for current user.");
        }

        // target must not source
        if (targetAgentCode.equals(sourceAgentCode)) {
            throw new BadRequestException("Target agent should not be same as source agent.");
        }

        // validate target's status should be A/S
        if (!ALLOWED_AGENT_STATUSES_FOR_POLICY_TRANSFER.contains(newTargetAgent.getStatus())) {
            throw new BadRequestException("Status agent invalid.");
        }

        verifyAgentHierarchyValidity(newTargetAgent, agentLeader, authUserAgentCode);

        log.info("Reassign policy transfer to {}", targetAgentCode);

        // persist policy transfer updates
        policyTransfer.setTarget(newTargetAgent.getUser());

        // if policy transfer status rejected, set back to pending
        if (policyTransfer.getStatus().equals(PolicyTransferStatus.REJECTED)) {
            policyTransfer.setStatus(PolicyTransferStatus.PENDING);
        }

        // if target agent is a leader, directly set status to approved
        if (isLeaderSelfAssigningPolicyTransfer(newTargetAgent, agentLeader)) {
            policyTransfer.setStatus(PolicyTransferStatus.APPROVED);
        }

        // persist to db
        policyTransfer = trxPolicyTransferRepository.save(policyTransfer);

        log.info("Policy transfer for termination {} updated", terminationId);

        if (isPolicyTransferApproved(policyTransfer) && isAbleToAutoApproveDirectUpline(termination)) {
            invokeAutoApproveDirectLeader(terminationId, policyTransfer.getSource().getAgent().getLeader());
        } else {
            // if not assigned to leader, send notification to the target for policy transfer approval
            sendPolicyAssignmentNotificationToTarget(newTargetAgent, policyTransfer.getSource().getAgent(),
                    termination, termination.getTrxType());
        }

        return TrxPolicyTransferDto.of(TrxPolicyTransferDto.class, policyTransfer);
    }

    @Transactional
    public void approve(String username, Long terminationId) {
        User currentUser = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        TrxTermination termination = retrieveTerminationRequest(terminationId);
        TrxPolicyTransfer policyTransfer = termination.getTrxPolicyTransfer();

        log.info("Approve policy transfer initiated by {} for policy transfer of termination {}",
                currentUser.getId(), terminationId);

        // if policy transfer status is not pending, something must be wrong
        if (!policyTransfer.getStatus().equals(PolicyTransferStatus.PENDING)) {
            throw new BadRequestException("Invalid policy transfer status.");
        }

        if (!currentUser.getId().equals(policyTransfer.getTarget().getId())) {
            throw new BadRequestException("Invalid approver.");
        }

        policyTransfer.setStatus(PolicyTransferStatus.APPROVED);
        trxPolicyTransferRepository.save(policyTransfer);

        log.info("Policy transfer for termination {} approved", terminationId);

        // if termination not requested by leader, meaning it was requested by target/agent themselves
        // so need to invoke the auto approve and skip leader
        if (isAbleToAutoApproveDirectUpline(termination)) {
            // assignedBy has to be the leader
            invokeAutoApproveDirectLeader(termination, policyTransfer.getAssignedBy().getAgent());
        }
    }

    @Transactional
    public void reject(String username, Long terminationId) {
        User currentUser = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        TrxTermination termination = retrieveTerminationRequest(terminationId);
        TrxPolicyTransfer policyTransfer = termination.getTrxPolicyTransfer();

        log.info("Reject policy transfer initiated by {} for policy transfer of termination {}",
                currentUser.getId(), policyTransfer.getId());

        // if policy transfer status is not pending, something must be wrong
        if (!policyTransfer.getStatus().equals(PolicyTransferStatus.PENDING)) {
            throw new BadRequestException("Invalid policy transfer status.");
        }

        if (!currentUser.getId().equals(policyTransfer.getTarget().getId())) {
            throw new BadRequestException("Invalid approver.");
        }

        policyTransfer.setStatus(PolicyTransferStatus.REJECTED);
        trxPolicyTransferRepository.save(policyTransfer);

        trxTerminationService.updateLastAction(termination);

        log.info("Policy transfer for termination {} rejected", terminationId);

        composeApprovalResultNotificationForLeader(policyTransfer, termination).forEach((p) ->
                firebaseService.sendNotification(List.of(p.getFirst()), p.getSecond()));
    }

    private void sendPolicyAssignmentNotificationToTarget(
            Agent target, Agent source, TrxTermination termination, TrxType type) {
        User targetUser = target.getUser();
        String targetEmail = targetUser.getEmail();

        Long numberOfPolicies = getPolicyServicingByAgentCode(source.getAgentCode());

        // prepare payload for inbox notification
        String bodyContent = String.format(
                TerminationNotificationTemplates.Inbox.POLICY_TRANSFER_TARGET_ASSIGNED,
                numberOfPolicies,
                source.getAgentName(),
                source.getAgentCode());

        NotificationDto notification = new NotificationDto();
        notification.setData(Map.of(
                "approvalId", termination.getApprovalHeader().getId().toString(),
                "trxId", termination.getId().toString(),
                "trxType", type.name()));
        notification.setTitle("Pelimpahan Polis");
        notification.setBody(bodyContent);
        notification.setInboxType(InboxType.INBOX);

        // send inbox notification
        firebaseService.sendNotification(List.of(target.getUser()), notification);

        log.info("Policy transfer approval notification sent to: {} ({})", targetUser.getUsername(), targetEmail);
    }

    private boolean isTerminationSubmittedByLeader(TrxTermination termination) {
        String leaderAgentCode = termination.getTarget().getAgent().getLeaderCode();
        String requesterAgentCode = termination.getRequester().getAgent().getAgentCode();
        return leaderAgentCode.equals(requesterAgentCode);
    }

    private boolean isAbleToAutoApproveDirectUpline(TrxTermination termination) {
        Agent requesterLeader = termination.getRequester().getAgent().getLeader();

        // means termination was not submitted by leader and leader is the next approver hence allowed to be skipped
        return !isTerminationSubmittedByLeader(termination) && requesterLeader != null;
    }

    private boolean isLeaderSelfAssigningPolicyTransfer(Agent target, Agent leader) {
        return leader != null && target.getAgentCode().equals(leader.getAgentCode());
    }

    private boolean isAuthUserLeader(String authUserAgentCode, Agent leader) {
        return leader != null && leader.getAgentCode().equals(authUserAgentCode);
    }

    private boolean isPolicyTransferApproved(TrxPolicyTransfer policyTransfer) {
        return policyTransfer.getStatus() == PolicyTransferStatus.APPROVED;
    }

    private List<Pair<User, NotificationDto>> composeApprovalResultNotificationForLeader(
            TrxPolicyTransfer policyTransfer, TrxTermination termination) {
        String sourceUsername = policyTransfer.getSource().getName();
        String sourceAgentCode = policyTransfer.getSource().getAgent().getAgentCode();
        String targetUsername = policyTransfer.getTarget().getName();
        String targetAgentCode = policyTransfer.getTarget().getAgent().getAgentCode();

        User leader = policyTransfer.getSource().getAgent().getLeader().getUser();

        NotificationDto notification = NotificationDto.builder()
                .inboxType(InboxType.INBOX)
                .data(Map.of(
                        "approvalId", termination.getApprovalHeader().getId().toString(),
                        "trxId", termination.getId().toString(),
                        "trxType", termination.getTrxType().name()))
                .build();

        switch (policyTransfer.getStatus()) {
            case APPROVED:
                if (isTerminationSubmittedByLeader(termination)) {
                    notification.setTitle("Pengalihan polis diterima");
                    notification.setBody(String.format(
                            TerminationNotificationTemplates.Inbox.REQUESTER_POLICY_TRANSFER_APPROVED,
                            sourceUsername,
                            sourceAgentCode,
                            targetUsername,
                            targetAgentCode));
                    return List.of(new Pair<>(leader, notification));
                }
                break;
            case REJECTED:
                if (isTerminationSubmittedByLeader(termination)) {
                    notification.setTitle("Pengalihan polis ditolak");
                    notification.setBody(String.format(
                            TerminationNotificationTemplates.Inbox.REQUESTER_POLICY_TRANSFER_REJECTED,
                            sourceUsername,
                            sourceAgentCode,
                            targetUsername,
                            targetAgentCode));
                    notification.setInboxType(InboxType.INBOX);
                    return List.of(new Pair<>(leader, notification));
                }
                break;

        }


        return List.of();
    }
}
