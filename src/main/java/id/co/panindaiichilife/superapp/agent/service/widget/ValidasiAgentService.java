package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.ValidasiAgentDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ValidasiPerG1Filter;
import id.co.panindaiichilife.superapp.agent.api.filter.ValidasiPerHirarkiFilter;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.model.ValidasiPerG1;
import id.co.panindaiichilife.superapp.agent.model.ValidasiPerHirarki;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.repository.ValidasiPerG1Repository;
import id.co.panindaiichilife.superapp.agent.repository.ValidasiPerHirarkiRepository;
import id.co.panindaiichilife.superapp.agent.service.AgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ValidasiAgentService {
    private static final String STATUS_TERCAPAI = "Tercapai";
    private static final String STATUS_BELUM_TERCAPAI = "Belum Tercapai";
    private static final String LICENSE_STATUS_AAJI_EXPIRED = "Kadaluarsa";
    private static final String LICENSE_STATUS_AAJI_UNLICENSED = "Belum Berlisensi";
    private static final String LICENSE_STATUS_ACTIVE = "Aktif";


    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    private final ValidasiPerHirarkiRepository validasiPerHirarkiRepository;

    private final ValidasiPerG1Repository validasiPerG1Repository;

    private final AgentService agentService;

    public List<ValidasiAgentDto> getValidasiHirarki(String username, ValidasiPerHirarkiFilter filter) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        if (StringUtils.isBlank(filter.getAgentCode())) {
            filter.setAgentCode(agent.getAgentCode());
        }

        // Trim -D suffix from agentCode if present
        filter.setAgentCode(AgentCodeUtil.trimDSuffix(filter.getAgentCode()));

        return validasiPerHirarkiRepository.findAll(filter)
                .stream()
                .map(entity -> mapHirarkiToDTO(entity, agent))
                .collect(Collectors.toList());
    }

    public List<ValidasiAgentDto> getValidasiG1(String username, ValidasiPerG1Filter filter) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        if (StringUtils.isBlank(filter.getAgentCode())) {
            filter.setAgentCode(agent.getAgentCode());
        }

        // Trim -D suffix from agentCode if present
        filter.setAgentCode(AgentCodeUtil.trimDSuffix(filter.getAgentCode()));

        return validasiPerG1Repository.findAll(filter)
                .stream()
                .map(entity -> mapG1ToDTO(entity, agent))
                .collect(Collectors.toList());
    }

    private ValidasiAgentDto mapHirarkiToDTO(ValidasiPerHirarki entity, Agent agent) {
        ValidasiAgentDto dto = new ValidasiAgentDto();
        BeanUtils.copyProperties(entity, dto);

        if (dto.getValidasiPerHirarki() == null) {
            dto.setValidasiPerHirarki(new ValidasiAgentDto.ValidasiPerHirarkiData());
        }

        // Map NET Polis data
        Long netPolisTarget = entity.getCaseTarget();
        Long netPolisAktual = entity.getNetCase();
        long netPolisKurang = Math.max(0, (netPolisTarget != null ? netPolisTarget : 0L) - (netPolisAktual != null ? netPolisAktual : 0L));
        dto.getValidasiPerHirarki().setNetPolis(new ValidasiAgentDto.NetPolisData(netPolisTarget, netPolisAktual, netPolisKurang));

        // Map Agent Count data
        Long agentCountTarget = entity.getAgentCountTarget();
        Long agentCountAktual = entity.getAgentCount();
        long agentCountKurang = Math.max(0, (agentCountTarget != null ? agentCountTarget : 0L) - (agentCountAktual != null ? agentCountAktual : 0L));
        dto.getValidasiPerHirarki().setAgentCount(new ValidasiAgentDto.AgentCountData(agentCountTarget, agentCountAktual, agentCountKurang));

        // Map Leader Count data
        Long leaderCountTarget = entity.getLeaderCountTarget();
        Long leaderCountAktual = entity.getLeaderCount();
        long leaderCountKurang = Math.max(0, (leaderCountTarget != null ? leaderCountTarget : 0L) - (leaderCountAktual != null ? leaderCountAktual : 0L));
        dto.getValidasiPerHirarki().setLeaderCount(new ValidasiAgentDto.LeaderCountData(leaderCountTarget, leaderCountAktual, leaderCountKurang));

        // Map NetApe data
        double netApeTarget = entity.getApeTarget() != null ? entity.getApeTarget() : 0.0;
        double netApeAktual = entity.getNetApe() != null ? entity.getNetApe() : 0.0;
        double netApeKurang = Math.max(0, netApeTarget - netApeAktual);
        dto.getValidasiPerHirarki().setNetApe(new ValidasiAgentDto.NetApeData(netApeTarget, netApeAktual, netApeKurang));

        // Map Persistensi data
        double persistensiTarget = entity.getP13Target() != null ? entity.getP13Target() : 0.0;
        double persistensiAktual = entity.getP13() != null ? entity.getP13() : 0.0;
        double persistensiKurang = persistensiAktual >= persistensiTarget ? 0 : (persistensiTarget - persistensiAktual);
        dto.getValidasiPerHirarki().setPersistensi(new ValidasiAgentDto.PersistensiData(
                round(persistensiTarget),
                round(persistensiAktual),
                round(persistensiKurang)));

        // Training data
        dto.getValidasiPerHirarki().setPelatihan(new ValidasiAgentDto.TrainingData(
                entity.getTrainingCount(), entity.getTrainingTarget()));

        // Set license status
        setLicenseStatus(dto, agent);

        // Determine status and deficiencies
        boolean targetsMet = netPolisKurang == 0 && persistensiKurang == 0 && leaderCountKurang == 0 &&
                agentCountKurang == 0 && netApeKurang == 0 &&
                (entity.getTrainingCount() != null ? entity.getTrainingCount() : 0L) >= (entity.getTrainingTarget() != null ? entity.getTrainingTarget() : 0L) &&
                isLicenseActive(dto);

        dto.getValidasiPerHirarki().setStatus(targetsMet ? STATUS_TERCAPAI : STATUS_BELUM_TERCAPAI);
        dto.setKekurangan(determineDeficiencies(
                netPolisKurang, agentCountKurang, leaderCountKurang, netApeKurang,
                persistensiKurang, entity.getTrainingCount() != null ? entity.getTrainingCount() : 0L, entity.getTrainingTarget() != null ? entity.getTrainingTarget() : 0L,
                dto.getStatusLisensiAAJI(), dto.getStatusLisensiAASI()));

        return dto;
    }

    private ValidasiAgentDto mapG1ToDTO(ValidasiPerG1 entity, Agent agent) {
        ValidasiAgentDto dto = new ValidasiAgentDto();
        BeanUtils.copyProperties(entity, dto);

        if (dto.getValidasiPerG1() == null) {
            dto.setValidasiPerG1(new ValidasiAgentDto.ValidasiPerG1Data());
        }

        // Map G1 Count data
        Long g1CountTarget = entity.getG1CountTarget();
        Long g1CountAktual = entity.getG1Count();
        long g1CountKurang = Math.max(0, (g1CountTarget != null ? g1CountTarget : 0L) - (g1CountAktual != null ? g1CountAktual : 0L));
        dto.getValidasiPerG1().setG1Count(new ValidasiAgentDto.G1CountData(g1CountTarget, g1CountAktual, g1CountKurang));

        // Map G1 NetApe data
        double netApeG1Target = entity.getG1ApeTarget() != null ? entity.getG1ApeTarget() : 0.0;
        double netApeG1Aktual = entity.getG1NetApe() != null ? entity.getG1NetApe() : 0.0;
        double netApeG1Kurang = Math.max(0, netApeG1Target - netApeG1Aktual);
        dto.getValidasiPerG1().setNetApeG1(new ValidasiAgentDto.NetApeG1Data(netApeG1Target, netApeG1Aktual, netApeG1Kurang));

        // Map NetApe data
        double netApeTarget = entity.getApeTarget() != null ? entity.getApeTarget() : 0.0;
        double netApeAktual = entity.getNetApe() != null ? entity.getNetApe() : 0.0;
        double netApeKurang = Math.max(0, netApeTarget - netApeAktual);
        dto.getValidasiPerG1().setNetApe(new ValidasiAgentDto.NetApeData(netApeTarget, netApeAktual, netApeKurang));

        // Map GNetApe data
        double gNetApeTarget = entity.getGNetApeTarget() != null ? entity.getGNetApeTarget() : 0.0;
        double gNetApeAktual = entity.getGNetApe() != null ? entity.getGNetApe() : 0.0;
        double gNetApeKurang = Math.max(0, gNetApeTarget - gNetApeAktual);
        dto.getValidasiPerG1().setGNetApe(new ValidasiAgentDto.GNetApeData(gNetApeTarget, gNetApeAktual, gNetApeKurang));

        // Map Persistensi data
        double persistensiTarget = entity.getP13Target() != null ? entity.getP13Target() : 0.0;
        double persistensiAktual = entity.getP13() != null ? entity.getP13() : 0.0;
        double persistensiKurang = persistensiAktual >= persistensiTarget ? 0 : (persistensiTarget - persistensiAktual);
        dto.getValidasiPerG1().setPersistensi(new ValidasiAgentDto.PersistensiData(
                round(persistensiTarget),
                round(persistensiAktual),
                round(persistensiKurang)));

        // Training data
        dto.getValidasiPerG1().setPelatihan(new ValidasiAgentDto.TrainingData(
                entity.getTrainingCount(), entity.getTrainingTarget()));

        // Set license status
        setLicenseStatus(dto, agent);

        // Determine status and deficiencies
        boolean targetsMet = g1CountKurang == 0 && gNetApeKurang == 0 && netApeG1Kurang == 0 &&
                netApeKurang == 0 && persistensiKurang == 0 &&
                (entity.getTrainingCount() != null ? entity.getTrainingCount() : 0L) >= (entity.getTrainingTarget() != null ? entity.getTrainingTarget() : 0L) &&
                isLicenseActive(dto);

        dto.getValidasiPerG1().setStatus(targetsMet ? STATUS_TERCAPAI : STATUS_BELUM_TERCAPAI);
        dto.setKekurangan(determineG1Deficiencies(
                g1CountKurang, netApeG1Kurang, gNetApeKurang, netApeKurang,
                persistensiKurang, entity.getTrainingCount() != null ? entity.getTrainingCount() : 0L, entity.getTrainingTarget() != null ? entity.getTrainingTarget() : 0L,
                dto.getStatusLisensiAAJI(), dto.getStatusLisensiAASI()));

        return dto;
    }

    private void setLicenseStatus(ValidasiAgentDto dto, Agent agent) {
        String licenseStatusAAJI = agentService.determineLicenseStatusAAJI(agent);
        String licenseStatusAASI = agentService.determineLicenseStatusAASI(agent);
        dto.setStatusLisensiAAJI(licenseStatusAAJI);
        dto.setStatusLisensiAASI(licenseStatusAASI);
    }

    private boolean isLicenseActive(ValidasiAgentDto dto) {
        return !(LICENSE_STATUS_AAJI_EXPIRED.equals(dto.getStatusLisensiAAJI()) ||
                LICENSE_STATUS_AAJI_UNLICENSED.equals(dto.getStatusLisensiAAJI())) &&
                LICENSE_STATUS_ACTIVE.equals(dto.getStatusLisensiAASI());
    }

    private double round(double value) {
        return Math.round(value * 100) / 100.0;
    }

    private List<String> determineDeficiencies(long netPolisKurang, long agentCountKurang,
                                               long leaderCountKurang, double netApeKurang, double persistensiKurang,
                                               long trainingCount, long trainingTarget, String licenseStatusAAJI, String licenseStatusAASI) {
        List<String> kekurangan = new ArrayList<>();

        if (netPolisKurang > 0) kekurangan.add("Kurang Net Polis");
        if (agentCountKurang > 0) kekurangan.add("Kurang BP");
        if (leaderCountKurang > 0) kekurangan.add("Kurang BM");
        if (netApeKurang > 0) kekurangan.add("Kurang NET APE");
        if (persistensiKurang > 0) kekurangan.add("Kurang Persistensi-13");
        if (trainingCount < trainingTarget) kekurangan.add("Kurang pelatihan wajib");
        if (!isLicenseActive(licenseStatusAAJI, licenseStatusAASI)) {
            if (LICENSE_STATUS_AAJI_EXPIRED.equals(licenseStatusAAJI) ||
                    LICENSE_STATUS_AAJI_UNLICENSED.equals(licenseStatusAAJI)) {
                kekurangan.add("Lisensi AAJI tidak aktif");
            }
            if (!LICENSE_STATUS_ACTIVE.equals(licenseStatusAASI)) {
                kekurangan.add("Lisensi AASI tidak aktif");
            }
        }
        return kekurangan;
    }

    private List<String> determineG1Deficiencies(long g1CountKurang, double netApeG1Kurang,
                                                 double gNetApeKurang, double netApeKurang, double persistensiKurang,
                                                 long trainingCount, long trainingTarget, String licenseStatusAAJI, String licenseStatusAASI) {
        List<String> kekurangan = new ArrayList<>();

        if (g1CountKurang > 0) kekurangan.add("Kurang G1");
        if (netApeG1Kurang > 0) kekurangan.add("Kurang G1 Net APE");
        if (gNetApeKurang > 0) kekurangan.add("Kurang G Net APE");
        if (netApeKurang > 0) kekurangan.add("Kurang Net APE");
        if (persistensiKurang > 0) kekurangan.add("Kurang Persistensi-13");
        if (trainingCount < trainingTarget) kekurangan.add("Kurang pelatihan wajib");
        if (!isLicenseActive(licenseStatusAAJI, licenseStatusAASI)) {
            if (LICENSE_STATUS_AAJI_EXPIRED.equals(licenseStatusAAJI) ||
                    LICENSE_STATUS_AAJI_UNLICENSED.equals(licenseStatusAAJI)) {
                kekurangan.add("Lisensi AAJI tidak aktif");
            }
            if (!LICENSE_STATUS_ACTIVE.equals(licenseStatusAASI)) {
                kekurangan.add("Lisensi AASI tidak aktif");
            }
        }
        return kekurangan;
    }

    private boolean isLicenseActive(String licenseStatusAAJI, String licenseStatusAASI) {
        return !(LICENSE_STATUS_AAJI_EXPIRED.equals(licenseStatusAAJI) ||
                LICENSE_STATUS_AAJI_UNLICENSED.equals(licenseStatusAAJI)) &&
                LICENSE_STATUS_ACTIVE.equals(licenseStatusAASI);
    }
}