package id.co.panindaiichilife.superapp.agent.api.dto.agency;

import id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TrxRejoinEligibleCandidateDto {
    private String agentCode;
    private String agentName;
    private String previousLevel;
    private String previousLeaderCode;
    private String previousLeaderName;
    private String branchCode;
    private String branchName;
    private String branchCity;
    private String branchAddress;
    private String agentPicture;

    public static TrxRejoinEligibleCandidateDto of(AgentWithBranchInfo agent) {
        return TrxRejoinEligibleCandidateDto.builder()
                .agentCode(agent.getAgentCode())
                .agentName(agent.getAgentName())
                .previousLevel(agent.getLevel())
                .previousLeaderCode(agent.getLeaderCode())
                .previousLeaderName(agent.getLeaderName())
                .branchCode(agent.getBranchCode())
                .branchName(agent.getBranchName())
                .branchCity(agent.getBranchCity())
                .branchAddress(agent.getBranchAddress())
                .agentPicture(agent.getPicture())
                .build();
    }
}
