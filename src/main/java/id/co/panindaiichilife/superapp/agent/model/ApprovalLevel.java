package id.co.panindaiichilife.superapp.agent.model;


import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;

@Entity
@Table(name = "approval_levels")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@SQLDelete(sql = "UPDATE approval_levels SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class ApprovalLevel extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "approval_levels_id_seq")
    @SequenceGenerator(name = "approval_levels_id_seq", sequenceName = "approval_levels_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private TrxType trxType;

    @Audited
    @Column(nullable = false)
    private Integer levelNumber;

    @Audited
    @Column
    private String requesterRole;

    @Audited
    @Column(nullable = false)
    private String approverRole;

    @Audited
    private String branchCode;

    @Audited
    @Enumerated(EnumType.STRING)
    private Channel channel;

    @Column(nullable = false)
    private Integer minApprovers = 1;

    @Column(nullable = false)
    private Boolean isActive = true;

    @Column
    private Boolean isDirectUpline = Boolean.FALSE;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;
}
