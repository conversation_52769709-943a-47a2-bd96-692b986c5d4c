package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.AccessCheckDto;
import id.co.panindaiichilife.superapp.agent.api.dto.ChannelCheckDto;
import id.co.panindaiichilife.superapp.agent.api.dto.ForgotPasswordDto;
import id.co.panindaiichilife.superapp.agent.api.dto.LoginDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccountDto;
import id.co.panindaiichilife.superapp.agent.api.form.AccessCheckForm;
import id.co.panindaiichilife.superapp.agent.api.form.ChannelCheckForm;
import id.co.panindaiichilife.superapp.agent.api.form.LoginForm;
import id.co.panindaiichilife.superapp.agent.api.form.ResetPasswordForm;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ForgetPasswordForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalForgotPasswordDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalForgotPasswordResponseDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalRequestChangePasswordDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalRequestChangePasswordResponseDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.superapp.SuperAppProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.superapp.dto.TokenResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.http.UnauthorizedException;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionService;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionSuperAppService;
import id.co.panindaiichilife.superapp.agent.core.security.ForgetPasswordService;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AccessRepository;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.time.Instant;
import java.util.Arrays;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class AuthService {

    private final SuperAppProvider superAppProvider;

    private final AccessRepository accessRepository;

    private final ForgetPasswordService forgetPasswordService;

    private final UserRepository userRepository;

    private final PasswordEncoder passwordEncoder;

    private final PortalProvider portalProvider;

    private final AgentRepository agentRepository;

    private final EncryptionService encryptionService;

    private final UserService userService;

    private final DeviceService deviceService;

    private final EncryptionSuperAppService encryptionSuperAppService;

    public static String maskEmail(String email) {
        if (email == null || !email.contains("@")) return email;

        String[] parts = email.split("@");
        if (parts.length != 2) return email;

        String username = parts[0];
        String domain = parts[1];

        // Mask username
        String maskedUsername = username.length() > 1
                ? username.charAt(0) + "*".repeat(username.length() - 2) + username.charAt(username.length() - 1)
                : username;

        // Mask domain (everything before last dot)
        String[] domainParts = domain.split("\\.");
        if (domainParts.length < 2) return maskedUsername + "@" + domain;

        String domainName = String.join(".", Arrays.copyOf(domainParts, domainParts.length - 1));
        String maskedDomain = domainName.length() > 0
                ? domainName.charAt(0) + "*".repeat(domainName.length() - 1)
                : domainName;

        return maskedUsername + "@" + maskedDomain + "." + domainParts[domainParts.length - 1];
    }

    public LoginDto login(LoginForm request) {
        LoginDto loginDto = new LoginDto();

        Call<TokenResponseDto> call = superAppProvider.getToken(
                request.getUsername(),
                request.getPassword(),
                "password",
                "read+write",
                request.getRememberMe()
        );

        try {
            Response<TokenResponseDto> response = call.execute();
            if (response.isSuccessful()) {
                BeanUtils.copyProperties(Objects.requireNonNull(response.body()), loginDto);

                // Activate device after successful login if deviceId is provided
                if (request.getDeviceId() != null && !request.getDeviceId().trim().isEmpty()) {
                    activateDevice(request.getUsername(), request.getDeviceId().trim());
                }

                return loginDto;
            }
            throw new UnauthorizedException("Invalid credentials login");
        } catch (IOException e) {
            throw new InternalServerErrorException("Error occurred while processing request");
        }
    }



    /**
     * Activate device after successful login
     */
    private void activateDevice(String username, String deviceId) {
        try {
            username = encryptionSuperAppService.decrypt(username);
            deviceService.activateDeviceOnLogin(username, deviceId);
        } catch (Exception e) {
            log.warn("Failed to activate device {} for user {}: {}", deviceId, username, e.getMessage());
            // Don't throw exception here to avoid breaking login flow
        }
    }

    public AccessCheckDto checkAccess(AccessCheckForm accessCheckForm) {
        AccessCheckDto accessCheckDto = new AccessCheckDto();
        boolean hasAccess = accessRepository.hasAccess(accessCheckForm.getUsername(), accessCheckForm.getDomain(), accessCheckForm.getPermission());
        accessCheckDto.setHasAccess(hasAccess);
        return accessCheckDto;
    }

    public ChannelCheckDto checkChannel(ChannelCheckForm accessCheckForm) {
        AccountDto user = userService.findByUsername(accessCheckForm.getUsername());
        ChannelCheckDto channelCheckDto = new ChannelCheckDto();
        channelCheckDto.setChannel(user.getChannel());
        return channelCheckDto;
    }

    public ForgotPasswordDto forgotPassword(ForgetPasswordForm forgetPasswordForm) {
        String rawUsername = encryptionService.decrypt(forgetPasswordForm.getUsername());
        String rawEmail = encryptionService.decrypt(forgetPasswordForm.getEmail());
        User user = userRepository.findByUsername(rawUsername).orElseThrow(NotFoundException::new);
        if (user == null) {
            throw new BadRequestException("User not found");
        }

        if (userService.checkIsUserStaff(user)) {
            throw new BadRequestException("User staff cannot forgot password in this system. Please contact your IT Admin");
        }

        if (!StringUtils.equals(rawEmail, user.getEmail())) {
            throw new BadRequestException("Kode agen atau alamat email tidak sesuai");
        }

        forgetPasswordService.requestResetPassword(user);

        ForgotPasswordDto forgotPasswordDto = new ForgotPasswordDto();
        forgotPasswordDto.setEmail(maskEmail(user.getEmail()));
        return forgotPasswordDto;
    }

    @Transactional
    public void resetPassword(String token, ResetPasswordForm resetPasswordForm) {
        // Get user from token
        User user = forgetPasswordService.readUserFromToken(token);
        if (user == null) {
            throw new BadRequestException("Invalid user");
        }

        // Decrypt password
        String rawPassword = encryptionService.decrypt(resetPasswordForm.getPassword());

        // Handle password reset based on user type
        if (user.getIsAgent()) {
            resetAgentPassword(user, rawPassword);
        } else {
            resetRegularUserPassword(user, rawPassword);
        }
    }

    /**
     * Reset password for regular users
     */
    private void resetRegularUserPassword(User user, String rawPassword) {
        user.setPassword(passwordEncoder.encode(rawPassword));
        user.setLastChangePassword(Instant.now());
        userRepository.save(user);
    }

    /**
     * Reset password for agent users through portal service
     */
    private void resetAgentPassword(User user, String rawPassword) {
        try {
            Agent agent = agentRepository.findTopByUser(user)
                    .orElseThrow(NotFoundException::new);

            // First call: Request change password authorization
            String authCode = requestChangePasswordAuth(agent.getAgentCode());
            String encryptedAgentCode = encryptionService.encrypt(agent.getAgentCode(), "");
            // Second call: Perform password change with auth code
            boolean success = performPasswordChange(encryptedAgentCode, authCode, rawPassword);

            if (success) {
                user.setLastChangePassword(Instant.now());
                userRepository.save(user);
            }
        } catch (Exception e) {
            throw new BadRequestException("Invalid user to reset password");
        }
    }

    /**
     * Request authorization code for password change
     */
    private String requestChangePasswordAuth(String agentCode) throws IOException {
        PortalRequestChangePasswordDto request = new PortalRequestChangePasswordDto();
        request.setAgentCode(agentCode);

        Response<PortalRequestChangePasswordResponseDto> response =
                portalProvider.requestChangePassword(request).execute();

        if (!response.isSuccessful() ||
                !StringUtils.equals(Objects.requireNonNull(response.body()).getStatusCode(), "200")) {
            throw new BadRequestException("Failed to get authorization for password change");
        }

        return response.body().getAuthCode();
    }

    /**
     * Perform the actual password change using authorization code
     */
    private boolean performPasswordChange(String agentCode, String authCode, String rawPassword) {
        try {
            PortalForgotPasswordDto request = new PortalForgotPasswordDto();
            request.setAgentCode(agentCode);
            request.setAuthCode(authCode);
            request.setNewPassword(encryptionService.encrypt(rawPassword, ""));

            Response<PortalForgotPasswordResponseDto> response =
                    portalProvider.forgotPassword(request).execute();

            if (response.isSuccessful() && StringUtils.equals(Objects.requireNonNull(response.body()).getStatusCode(), "200")) {
                return true;
            } else {
                throw new BadRequestException(Objects.requireNonNull(response.body()).getMessage());
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * Logout user by clearing firebase token from device(s)
     *
     * @param username The username of the user to logout
     * @param deviceId Optional device ID. If provided, logout only that device. If null, logout from all devices.
     */
    @Transactional
    public void logout(String username, String deviceId) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        if (deviceId != null && !deviceId.trim().isEmpty()) {
            // Logout from specific device
            deviceService.logoutFromDevice(username, deviceId.trim());
            log.info("User {} logged out from device: {}", username, deviceId);
        }
    }

}
