package id.co.panindaiichilife.superapp.agent.api.validation;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxPolicyTransferForm;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.model.TrxPolicyTransfer;
import id.co.panindaiichilife.superapp.agent.model.TrxTermination;
import id.co.panindaiichilife.superapp.agent.repository.TrxTerminationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

@Component
@Slf4j
@RequiredArgsConstructor
public class TrxPolicyTransferValidator implements Validator {
    private final TrxTerminationRepository trxTerminationRepository;

    @Override
    public boolean supports(Class<?> clazz) {
        return TrxPolicyTransferForm.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        TrxPolicyTransferForm form = (TrxPolicyTransferForm) target;

        if (form.getTrxTerminationId() == null) {
            errors.rejectValue("trxTerminationId", "trxTerminationId.required", "trxTerminationId is required");
        }

        if (form.getTargetAgentCode() == null || form.getTargetAgentCode().trim().isEmpty()) {
            errors.rejectValue("targetAgentCode", "targetAgentCode.required", "targetAgentCode is required");
        }

        TrxTermination trxTermination = trxTerminationRepository.findById(form.getTrxTerminationId()).orElseThrow(() ->
                new NotFoundException("Termination request was not found with id: %d".formatted(form.getTrxTerminationId())));

        TrxPolicyTransfer trxPolicyTransfer = trxTermination.getTrxPolicyTransfer();
        if (trxPolicyTransfer != null && !trxPolicyTransfer.isDeleted()) {
            throw new BadRequestException("There is an active Policy Transfer request for termination %d".formatted(form.getTrxTerminationId()));
        }
    }
}
