package id.co.panindaiichilife.superapp.agent.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@RequiredArgsConstructor
public class DeviceStatusWebConfig implements WebMvcConfigurer {

    private final DeviceStatusInterceptor deviceStatusInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(deviceStatusInterceptor)
                .addPathPatterns("/api/**");
    }
}