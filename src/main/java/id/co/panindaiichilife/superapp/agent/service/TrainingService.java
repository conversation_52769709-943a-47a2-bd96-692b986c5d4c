package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.TrainingPassDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.TrainingDto;
import id.co.panindaiichilife.superapp.agent.api.filter.TrainingFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.TrainingForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalTrainingPassResponseDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalTrainingResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheEvictWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.Training;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrainingRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TrainingService {

    private final TrainingRepository trainingRepository;
    private final PortalProvider portalProvider;
    private final UserRepository userRepository;
    private final AgentRepository agentRepository;

    public Page<TrainingDto> findAll(Pageable pageable, TrainingFilter filter) {
        Page<Training> trainings = trainingRepository.findAll(filter, pageable);
        return BaseDto.of(TrainingDto.class, trainings, pageable);
    }

    public TrainingDto findOne(Long id) {
        Training data = trainingRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(TrainingDto.class, data);
    }

    public TrainingDto update(Long id, TrainingForm trainingForm) {
        Training data = trainingRepository.findById(id).orElseThrow(NotFoundException::new);
        BeanUtils.copyProperties(trainingForm, data);
        trainingRepository.save(data);

        return BaseDto.of(TrainingDto.class, data);
    }

    @CacheEvictWithTTL(cacheName = "trainingCityCache", db = 7)
    public void delete(Long id) {
        trainingRepository.deleteById(id);
    }

    public void syncTraining() {
        //Sync Training Agent
        Call<PortalTrainingResponseDto> callTrainingAgent = portalProvider.getTrainingList(DistributionCode.A.name());

        try {
            Response<PortalTrainingResponseDto> response = callTrainingAgent.execute();
            if (response.isSuccessful()) {
                PortalTrainingResponseDto portalTrainingResponseDto = response.body();
                List<PortalTrainingResponseDto.TrainingDto> trainingAgent = portalTrainingResponseDto.getTrainingList();
                for (PortalTrainingResponseDto.TrainingDto training : trainingAgent) {
                    Training trainingData = trainingRepository.findByCode(training.getCode()).orElse(null);
                    if (null == trainingData) {
                        trainingData = new Training();
                        trainingData.setIsMandatory(Boolean.TRUE);
                    }
                    BeanUtils.copyProperties(training, trainingData);
                    trainingData.setCode(training.getCode());
                    trainingData.setName(training.getName());
                    trainingData.setDescription(training.getDesc());
                    trainingData.setChannel(Channel.AGE);
                    trainingRepository.save(trainingData);
                }
            }
        } catch (IOException e) {
            throw new InternalServerErrorException("Error occurred while sync training agent AGE");
        }

        //Sync Training BAN
        Call<PortalTrainingResponseDto> callTrainingBan = portalProvider.getTrainingList(DistributionCode.L.name());

        try {
            Response<PortalTrainingResponseDto> response = callTrainingBan.execute();
            if (response.isSuccessful()) {
                PortalTrainingResponseDto portalTrainingResponseDto = response.body();
                List<PortalTrainingResponseDto.TrainingDto> trainingBans = portalTrainingResponseDto.getTrainingList();
                for (PortalTrainingResponseDto.TrainingDto training : trainingBans) {
                    Training trainingData = trainingRepository.findByCode(training.getCode()).orElse(null);
                    if (null == trainingData) {
                        trainingData = new Training();
                        trainingData.setIsMandatory(Boolean.TRUE);
                    }
                    BeanUtils.copyProperties(training, trainingData);
                    trainingData.setCode(training.getCode());
                    trainingData.setName(training.getName());
                    trainingData.setDescription(training.getDesc());
                    trainingData.setChannel(Channel.BAN);
                    trainingRepository.save(trainingData);
                }
            }
        } catch (IOException e) {
            throw new InternalServerErrorException("Error occurred while sync training agent BAN");
        }
    }

    public List<TrainingPassDto> getMyTraining(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // Get all trainings for the user's channel
        List<Training> trainings = trainingRepository.findByChannel(user.getChannel());
        List<TrainingPassDto> trainingPassDtos = new ArrayList<>();

        if (!trainings.isEmpty()) {
            // Prepare training code list
            String trainingCodeList = trainings.stream()
                    .map(Training::getCode)
                    .collect(Collectors.joining(","));

            // Call the portal API to get training pass results
            String agentCode = agent.getAgentCode();

            // Trim -D suffix from agentCode if present
            agentCode = AgentCodeUtil.trimDSuffix(agentCode);

            String distributionCode = agent.getDistributionCode().name();

            // Call portal API using Retrofit
            Call<PortalTrainingPassResponseDto> call = portalProvider.getTrainingPass(
                    agentCode,
                    distributionCode,
                    trainingCodeList
            );

            try {
                Response<PortalTrainingPassResponseDto> response = call.execute();
                if (response.isSuccessful()) {
                    PortalTrainingPassResponseDto responseDto = response.body();
                    if (responseDto != null && responseDto.getTrainingResults() != null) {
                        // Create a map of training objects by code for quick lookup
                        Map<String, PortalTrainingPassResponseDto.TrainingResultDto> trainingMap = responseDto.getTrainingResults().stream()
                                .collect(Collectors.toMap(PortalTrainingPassResponseDto.TrainingResultDto::getCode, training -> training));

                        // Process the results and update existing training objects
                        for (Training training : trainings) {
                            // Find the matching training in our map
                            PortalTrainingPassResponseDto.TrainingResultDto trainingPassDto = trainingMap.get(training.getCode());
                            TrainingPassDto dto = new TrainingPassDto();
                            BeanUtils.copyProperties(training, dto);
                            if (trainingPassDto != null) {
                                dto.setIsPassed(null != trainingPassDto.getPassDate() ? Boolean.TRUE : Boolean.FALSE);
                                dto.setPassDate(trainingPassDto.getPassDate());
                            } else {
                                dto.setIsPassed(Boolean.FALSE);
                                dto.setPassDate(null);
                            }

                            trainingPassDtos.add(dto);
                        }
                    }
                }
            } catch (IOException e) {
                throw new InternalServerErrorException("Error occurred while retrieving training pass results");
            }
        }

        return trainingPassDtos;
    }
}
