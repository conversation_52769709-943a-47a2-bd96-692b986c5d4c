package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface AgentRepository extends BaseRepository<Agent, Long> {

    Optional<Agent> findTopByUser(User user);

    Optional<Agent> findTopByAgentCode(String agentCode);

    List<Agent> findByLeaderCode(String leaderCode);

    @Query("""
        SELECT DISTINCT new id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo(
            a.agentCode,
            a.agentName,
            b.branchCode,
            b.branchName,
            b.city,
            b.address,
            a.level,
            a.leaderCode,
            a.leader.agentName,
            a.photo,
            a.status
        )
        FROM Agent a
            JOIN Branch b ON a.branchCode = b.branchCode
            JOIN a.user u
            LEFT JOIN TrxTermination t ON t.target = u
        WHERE
            a.status = AgentStatus.T
            AND a.leaderCode    = :leaderCode
            AND (t IS NULL OR t.effectiveDate >= :cutoff)
            AND (:searchText IS NULL
                OR (
                    LOWER(a.agentName)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    OR LOWER(a.agentCode)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    )
            )
    """)
    Page<AgentWithBranchInfo> findAllEligibleAgentsForRejoinBasedOnLeader(
            @Param("leaderCode") String leaderCode,
            @Param("cutoff") Instant cutoff,
            @Param("searchText") String searchText,
            Pageable pageable);

    @Query("""
        SELECT DISTINCT new id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo(
            a.agentCode,
            a.agentName,
            b.branchCode,
            b.branchName,
            b.city,
            b.address,
            a.level,
            a.leaderCode,
            a.leader.agentName,
            a.photo,
            a.status
        )
        FROM Agent a
            JOIN Branch b ON a.branchCode = b.branchCode
            JOIN a.user u
            LEFT JOIN TrxTermination t ON t.target = u
        WHERE
            a.status = AgentStatus.T 
            AND a.branchCode  = :branchCode
            AND (t IS NULL OR t.effectiveDate >= :cutoff)
            AND (:searchText IS NULL
                OR (
                    LOWER(a.agentName)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    OR LOWER(a.agentCode)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    )
            )
    """)
    Page<AgentWithBranchInfo> findAllEligibleAgentsForRejoinBasedOnBranch(
            @Param("branchCode") String branchCode,
            @Param("cutoff") Instant cutoff,
            @Param("searchText") String searchText,
            Pageable pageable);

    @Query("""
        SELECT DISTINCT new id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo(
            a.agentCode,
            a.agentName,
            b.branchCode,
            b.branchName,
            b.city,
            b.address,
            a.level,
            a.leaderCode,
            a.leader.agentName,
            a.photo,
            a.status
        )
        FROM Agent a JOIN Branch b ON a.branchCode = b.branchCode
        WHERE
            a.status in (AgentStatus.A, AgentStatus.S)
            AND a.leaderCode    = :leaderCode
            AND (:searchText IS NULL
                OR (
                    LOWER(a.agentName)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    OR LOWER(a.agentCode)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    )
            )
            AND NOT EXISTS (
                SELECT 1 FROM TrxTermination t
                WHERE
                    t.target = a.user
                    AND t.status NOT IN (
                        TrxStatus.DRAFT,
                        TrxStatus.IN_PROGRESS,
                        TrxStatus.DIKEMBALIKAN
                    )
            )
    """)
    Page<AgentWithBranchInfo> findAllEligibleAgentsForTerminationBasedOnLeader(
            @Param("leaderCode") String leaderCode,
            @Param("searchText") String searchText,
            Pageable pageable);

    @Query("""
        SELECT DISTINCT new id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo(
            a.agentCode,
            a.agentName,
            b.branchCode,
            b.branchName,
            b.city,
            b.address,
            a.level,
            a.leaderCode,
            a.leader.agentName,
            a.photo,
            a.status
        )
        FROM
            Agent a JOIN Branch b ON a.branchCode = b.branchCode
        WHERE
            a.status in (AgentStatus.A, AgentStatus.S)
            AND a.branchCode    = :branchCode
            AND (:searchText IS NULL
                OR (
                    LOWER(a.agentName)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    OR LOWER(a.agentCode)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    )
            )
            AND NOT EXISTS (
                SELECT 1 FROM TrxTermination t
                WHERE
                    t.target = a.user
                    AND t.status NOT IN (
                        TrxStatus.DRAFT,
                        TrxStatus.IN_PROGRESS,
                        TrxStatus.DIKEMBALIKAN
                    )
            )
    """)
    Page<AgentWithBranchInfo> findAllEligibleAgentsForTerminationBasedOnBranch(
            @Param("branchCode") String branchCode,
            @Param("searchText") String searchText,
            Pageable pageable);
}
