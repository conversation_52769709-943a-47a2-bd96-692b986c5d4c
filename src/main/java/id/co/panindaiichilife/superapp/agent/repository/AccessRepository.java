package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.model.Access;
import id.co.panindaiichilife.superapp.agent.model.Role;
import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;

import java.util.List;

public interface AccessRepository extends BaseRepository<Access, Long>, AccessRepositoryCustom {

    List<Access> findByRolesOrderByDomainAscPrivilegeAsc(Role roles);
}
