package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.MdrtImportDto;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.Mdrt;
import id.co.panindaiichilife.superapp.agent.repository.MdrtRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class MdrtImportProcessor implements ItemProcessor<MdrtImportDto, Mdrt> {

    private final MdrtRepository mdrtRepository;

    @Override
    public Mdrt process(MdrtImportDto item) {
        return findOrCreateMdrt(item);
    }

    private Mdrt findOrCreateMdrt(MdrtImportDto item) {
        Mdrt mdrt = mdrtRepository.findByAgentCodeAndYearAndMonth(item.getAgentCode(), item.getYear(), item.getMonth())
                .orElse(new Mdrt());

        // Copy properties
        BeanUtils.copyProperties(item, mdrt);
        mdrt.setDistributionCode(DistributionCode.valueOf(item.getDistributionCode()));

        return mdrt;
    }
}