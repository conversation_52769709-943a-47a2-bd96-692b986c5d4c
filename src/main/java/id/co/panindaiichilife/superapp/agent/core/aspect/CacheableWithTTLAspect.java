package id.co.panindaiichilife.superapp.agent.core.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class CacheableWithTTLAspect {

    // Redis connection timeout in seconds
    private static final int REDIS_TIMEOUT_SECONDS = 1;

    private final RedisTemplate<String, Object> redisTemplate;

    private final RedisConnectionFactory redisConnectionFactory;

    private final ObjectMapper objectMapper;

    @Around("@annotation(cacheableWithTTL)")
    public Object handleCacheableWithTTL(ProceedingJoinPoint joinPoint, CacheableWithTTL cacheableWithTTL) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // Generate cache key
        String cacheKey = generateCacheKey(method, joinPoint.getArgs(), cacheableWithTTL);

        long ttl = cacheableWithTTL.ttl();
        int dbIndex = cacheableWithTTL.db();
        Class<?> returnType = method.getReturnType();

        Object cachedValue = null;
        boolean redisAvailable = true;

        try {
            // Switch database dynamically with timeout
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);

            // Try to get the value from cache
            String cachedValueJson = (String) dbSpecificTemplate.opsForValue().get(cacheKey);
            if (cachedValueJson != null) {
                log.info("Fetching from cache (DB {}): {}", dbIndex, cacheKey);
                try {
                    cachedValue = objectMapper.readValue(cachedValueJson, returnType);
                } catch (Exception e) {
                    log.warn("Failed to deserialize cached value for key: {}. Error: {}", cacheKey, e.getMessage());
                }
            }
        } catch (RedisConnectionFailureException e) {
            redisAvailable = false;
            log.warn("Redis connection failure. Bypassing cache for key: {}. Error: {}", cacheKey, e.getMessage());
        } catch (Exception e) {
            log.warn("Error accessing Redis cache. Bypassing cache for key: {}. Error: {}", cacheKey, e.getMessage());
            redisAvailable = false;
        }

        // If we have a valid cached value, return it
        if (cachedValue != null) {
            return cachedValue;
        }

        // Otherwise proceed with the method execution
        Object result = joinPoint.proceed();

        // Only try to cache the result if Redis is available
        if (result != null && redisAvailable) {
            try {
                RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);
                String resultJson = objectMapper.writeValueAsString(result);
                dbSpecificTemplate.opsForValue().set(cacheKey, resultJson, ttl, TimeUnit.SECONDS);
                log.info("Caching key: {} in DB {} with TTL: {} seconds", cacheKey, dbIndex, ttl);
            } catch (Exception e) {
                log.warn("Failed to cache result for key: {}. Error: {}", cacheKey, e.getMessage());
            }
        }

        return result;
    }

    /**
     * Generate a unique cache key based on method, arguments, and annotation
     */
    private String generateCacheKey(Method method, Object[] args, CacheableWithTTL cacheableWithTTL) {
        // If a specific key is provided, parse it
        if (!cacheableWithTTL.key().isEmpty()) {
            return cacheableWithTTL.cacheName() + "::" + parseKey(cacheableWithTTL.key(), method, args);
        }

        return cacheableWithTTL.cacheName().isEmpty()
                ? "default"
                : cacheableWithTTL.cacheName();
    }

    private RedisTemplate<String, Object> createDatabaseSpecificTemplate(int dbIndex) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();

        try {
            LettuceConnectionFactory connectionFactory =
                    (LettuceConnectionFactory) createDatabaseSpecificConnectionFactory(dbIndex);
            connectionFactory.start(); // Initialize the connection factory

            template.setConnectionFactory(connectionFactory);
            template.setKeySerializer(redisTemplate.getKeySerializer());
            template.setValueSerializer(redisTemplate.getValueSerializer());
            template.setHashKeySerializer(redisTemplate.getHashKeySerializer());
            template.setHashValueSerializer(redisTemplate.getHashValueSerializer());
            template.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Failed to create database-specific Redis template for DB: {}. Error: {}", dbIndex, e.getMessage());
            throw e;
        }

        return template;
    }

    private RedisConnectionFactory createDatabaseSpecificConnectionFactory(int dbIndex) {
        RedisConnectionFactory connectionFactory = redisConnectionFactory;
        if (connectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) connectionFactory;

            RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration(
                    lettuceFactory.getStandaloneConfiguration().getHostName(),
                    lettuceFactory.getStandaloneConfiguration().getPort()
            );

            // Set any authentication if required
            if (lettuceFactory.getStandaloneConfiguration().getPassword().isPresent()) {
                configuration.setPassword(lettuceFactory.getStandaloneConfiguration().getPassword());
            }

            configuration.setDatabase(dbIndex);

            // Create Lettuce client configuration with timeouts
            LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                    .commandTimeout(Duration.ofSeconds(REDIS_TIMEOUT_SECONDS))
                    .shutdownTimeout(Duration.ofSeconds(REDIS_TIMEOUT_SECONDS))
                    .build();

            // Create connection factory with timeout configuration
            LettuceConnectionFactory newFactory = new LettuceConnectionFactory(configuration, clientConfig);
            newFactory.afterPropertiesSet();
            return newFactory;
        }
        return connectionFactory;
    }

    private String parseKey(String key, Method method, Object[] args) {
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();

        String[] parameterNames = new DefaultParameterNameDiscoverer().getParameterNames(method);

        if (parameterNames != null) {
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
        }

        Expression expression = parser.parseExpression(key);
        return expression.getValue(context, String.class);
    }
}