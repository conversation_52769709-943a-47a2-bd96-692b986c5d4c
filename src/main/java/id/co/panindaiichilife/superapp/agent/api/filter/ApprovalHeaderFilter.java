package id.co.panindaiichilife.superapp.agent.api.filter;

import id.co.panindaiichilife.superapp.agent.core.data.filter.FieldFilter;
import id.co.panindaiichilife.superapp.agent.core.data.filter.FilterParam;
import id.co.panindaiichilife.superapp.agent.core.view.FilterMode;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class ApprovalHeaderFilter extends FieldFilter<TrxApprovalHeader> {

    @FilterParam
    private TrxType trxType;

    @Parameter(hidden = true)
    @FilterParam(value = "requestBy.username", modes = FilterMode.FK)
    private String requestBy;

    @Parameter(hidden = true)
    @FilterParam(value = "branch.id", modes = FilterMode.FK)
    private Long branch;
}
