package id.co.panindaiichilife.superapp.agent.service.agency.validator;

import com.google.gson.Gson;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationHirarkiResponseDto;
import id.co.panindaiichilife.superapp.agent.enums.ValidationHirarkiStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;

/**
 * Validator for checking hierarchy validation
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HirarkiValidator extends AbstractRecruitmentValidator {

    private final PortalProvider portalProvider;

    @Override
    public boolean canValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        return StringUtils.isNotBlank(entity.getRecruiterCode()) && entity.getPositionLevel() != null;
    }

    @Override
    protected TrxRecruitment doValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        PortalValidationHirarkiResponseDto response = validateHirarki(entity.getRecruiterCode(), entity.getPositionLevel().name());

        if (response != null) {
            // Store the response as JSON
            entity.setResultValidationHirarki(new Gson().toJson(response.getUplineDTO()));
            entity.setValidationHirarkiStatus(ValidationHirarkiStatus.PASS);
            log.info("Hierarchy validation passed for recruiter code: {}", entity.getRecruiterCode());
        } else {
            entity.setValidationHirarkiStatus(ValidationHirarkiStatus.POSTPONED);
            log.warn("Hierarchy validation skipped or failed for recruiter code: {}", entity.getRecruiterCode());
        }

        return entity;
    }

    @Override
    public String getValidatorName() {
        return "Hierarchy Validator";
    }

    /**
     * Validates hierarchy for a recruitment candidate
     *
     * @param recruiterCode  The agent code of the recruiter
     * @param recruiteeLevel The position level of the recruitee
     * @return The PortalValidationHirarkiResponseDto object containing the upline information
     */
    private PortalValidationHirarkiResponseDto validateHirarki(String recruiterCode, String recruiteeLevel) {
        try {
            if (StringUtils.isBlank(recruiterCode) || StringUtils.isBlank(recruiteeLevel)) {
                log.warn("Hierarchy validation skipped - missing required data");
                return null;
            }

            // Call the portal API to validate hierarchy
            Call<PortalValidationHirarkiResponseDto> call = portalProvider.getAgentUpline(recruiterCode, recruiteeLevel);
            Response<PortalValidationHirarkiResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalValidationHirarkiResponseDto hirarkiResponse = response.body();

                // Check if the API call was successful
                if ("200".equals(hirarkiResponse.getStatusCode())) {
                    log.info("Successfully retrieved upline information for recruiter code: {}", recruiterCode);
                    return hirarkiResponse;
                } else {
                    log.error("Error in hierarchy validation: {} - {}", hirarkiResponse.getStatusCode(), hirarkiResponse.getMessage());
                    return null;
                }
            } else {
                log.error("Failed to validate hierarchy: {}", response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                return null;
            }
        } catch (Exception e) {
            log.error("Error validating hierarchy", e);
            return null;
        }
    }
}
