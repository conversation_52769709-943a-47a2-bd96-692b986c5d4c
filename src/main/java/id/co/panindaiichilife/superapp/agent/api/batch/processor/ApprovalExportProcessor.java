package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.ApprovalExportDto;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.model.*;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxEditProfileRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRecruitmentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxTerminationRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRejoinApplicationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;

import java.time.Instant;
import java.util.Comparator;
import java.util.Optional;
import java.util.Set;

@Slf4j
@RequiredArgsConstructor
public class ApprovalExportProcessor implements ItemProcessor<TrxApprovalHeader, ApprovalExportDto> {

    private final TrxEditProfileRepository trxEditProfileRepository;
    private final TrxRecruitmentRepository trxRecruitmentRepository;
    private final TrxTerminationRepository trxTerminationRepository;
    private final TrxRejoinApplicationRepository trxRejoinApplicationRepository;
    private final AgentRepository agentRepository;

    @Override
    public ApprovalExportDto process(TrxApprovalHeader data) {
        ApprovalExportDto dto = new ApprovalExportDto();

        // Set common fields
        dto.setChannel(data.getChannel());
        dto.setApprovalStatus(data.getApprovalStatus());
        dto.setCreatedAt(data.getCreatedAt());
        dto.setTrxType(data.getTrxType().name());

        // Set processed by information
        dto.setProcessedBy(getProcessedByInfo(data));

        // Set approval date (when approved by CAS)
        dto.setApprovedAt(getApprovalDate(data));

        // Set remarks for pending/rejection reasons
        dto.setRemarks(getRemarks(data));

        // Process based on transaction type
        switch (data.getTrxType()) {
            case EDIT_PROFILE:
                if (!processEditProfile(data, dto)) {
                    return null; // Skip this record
                }
                break;
            case RECRUITMENT_BP:
            case RECRUITMENT_BM:
            case RECRUITMENT_BD:
                if (!processRecruitment(data, dto)) {
                    return null; // Skip this record
                }
                break;
            case TERMINASI_BP:
            case TERMINASI_BM:
            case TERMINASI_BD:
                if (!processTerminasi(data, dto)) {
                    return null; // Skip this record
                }
                break;
            case REJOIN_BP:
            case REJOIN_BM:
                if (!processRejoin(data, dto)) {
                    return null; // Skip this record
                }
                break;
            default:
                log.warn("Unsupported transaction type for export: {}, skipping record", data.getTrxType());
                return null; // Skip this record
        }

        return dto;
    }

    private boolean processEditProfile(TrxApprovalHeader data, ApprovalExportDto dto) {
        Optional<TrxEditProfile> trxEditProfileOpt = trxEditProfileRepository.findByApprovalHeader(data);
        if (trxEditProfileOpt.isEmpty()) {
            log.warn("Edit profile not found for approval header: {}, skipping record", data.getId());
            return false;
        }

        TrxEditProfile trxEditProfile = trxEditProfileOpt.get();
        Agent agent = trxEditProfile.getAgent();
        if (agent == null) {
            log.warn("Agent not found for edit profile: {}, skipping record", trxEditProfile.getId());
            return false;
        }

        dto.setAgentCode(agent.getAgentCode());
        dto.setAgentName(agent.getAgentName());
        dto.setAgentLevel(agent.getLevel());
        dto.setBranchCode(agent.getBranchCode());
        dto.setData(trxEditProfile.getData());

        // For edit profile, recruiter and leader info are not applicable
        dto.setRecruiterInfo("");
        dto.setLeaderInfo("");

        return true;
    }

    private boolean processRecruitment(TrxApprovalHeader data, ApprovalExportDto dto) {
        Optional<TrxRecruitment> recruitmentOpt = trxRecruitmentRepository.findByApprovalHeader(data);
        if (recruitmentOpt.isEmpty()) {
            log.warn("Recruitment not found for approval header: {}, skipping record", data.getId());
            return false;
        }

        TrxRecruitment recruitment = recruitmentOpt.get();

        // Set candidate information
        dto.setAgentCode(recruitment.getAgentCode() != null ? recruitment.getAgentCode() : "");
        dto.setAgentName(recruitment.getFullName());
        dto.setAgentLevel(recruitment.getPositionLevel() != null ? recruitment.getPositionLevel().name() : "");
        dto.setBranchCode(recruitment.getBranch() != null ? recruitment.getBranch().getBranchCode() : "");

        // Set recruiter information
        dto.setRecruiterInfo(getRecruiterInfo(recruitment));

        // Set leader information
        dto.setLeaderInfo(getLeaderInfo(recruitment));

        // For recruitment, we don't show "Perubahan Data" - set empty string
        dto.setData("");

        return true;
    }

    private boolean processTerminasi(TrxApprovalHeader data, ApprovalExportDto dto) {
        Optional<TrxTermination> terminationOpt = trxTerminationRepository.findById(data.getTrxId());
        if (terminationOpt.isEmpty()) {
            log.warn("Termination not found for approval header: {}, skipping record", data.getId());
            return false;
        }

        TrxTermination termination = terminationOpt.get();
        User targetUser = termination.getTarget();
        if (targetUser == null) {
            log.warn("Target user not found for termination: {}, skipping record", termination.getId());
            return false;
        }

        Agent targetAgent = targetUser.getAgent();
        if (targetAgent == null) {
            log.warn("Target agent not found for termination: {}, skipping record", termination.getId());
            return false;
        }

        // Set agent information (the agent being terminated)
        dto.setAgentCode(targetAgent.getAgentCode());
        dto.setAgentName(targetAgent.getAgentName());
        dto.setAgentLevel(targetAgent.getLevel());
        dto.setBranchCode(targetAgent.getBranchCode());

        // Set requester information as recruiter info
        dto.setRecruiterInfo(getRequesterInfo(termination.getRequester()));

        // Set leader information
        dto.setLeaderInfo(getAgentLeaderInfo(targetAgent));

        // For termination, show reason as data
        dto.setData(termination.getReason() != null ? termination.getReason() : "");

        return true;
    }

    private boolean processRejoin(TrxApprovalHeader data, ApprovalExportDto dto) {
        Optional<TrxRejoinApplication> rejoinOpt = trxRejoinApplicationRepository.findById(data.getTrxId());
        if (rejoinOpt.isEmpty()) {
            log.warn("Rejoin application not found for approval header: {}, skipping record", data.getId());
            return false;
        }

        TrxRejoinApplication rejoin = rejoinOpt.get();
        Agent agent = rejoin.getAgent();
        if (agent == null) {
            log.warn("Agent not found for rejoin application: {}, skipping record", rejoin.getId());
            return false;
        }

        // Set agent information (the agent rejoining)
        dto.setAgentCode(agent.getAgentCode());
        dto.setAgentName(agent.getAgentName());
        dto.setAgentLevel(rejoin.getProposedLevel() != null ? rejoin.getProposedLevel() : agent.getLevel());
        dto.setBranchCode(rejoin.getBranch() != null ? rejoin.getBranch().getBranchCode() : agent.getBranchCode());

        // Set submitter information as recruiter info
        dto.setRecruiterInfo(getRequesterInfo(rejoin.getSubmittedBy()));

        // Set proposed leader information
        dto.setLeaderInfo(getProposedLeaderInfo(rejoin.getProposedLeaderCode()));

        // For rejoin, show remarks as data
        dto.setData(rejoin.getRemarks() != null ? rejoin.getRemarks() : "");

        return true;
    }

    private String getRecruiterInfo(TrxRecruitment recruitment) {
        if (recruitment.getRecruiter() != null) {
            User recruiterUser = recruitment.getRecruiter();
            Agent recruiterAgent = agentRepository.findTopByUser(recruiterUser).orElse(null);

            if (recruiterAgent != null) {
                return String.format("%s - %s", recruiterAgent.getAgentCode(), recruiterAgent.getAgentName());
            } else {
                return String.format("%s - %s", recruitment.getRecruiterCode(), recruiterUser.getName());
            }
        } else if (recruitment.getRecruiterCode() != null) {
            // Try to find agent by recruiter code
            Agent recruiterAgent = agentRepository.findTopByAgentCode(recruitment.getRecruiterCode()).orElse(null);
            if (recruiterAgent != null) {
                return String.format("%s - %s", recruiterAgent.getAgentCode(), recruiterAgent.getAgentName());
            } else {
                return recruitment.getRecruiterCode();
            }
        }
        return "";
    }

    private String getLeaderInfo(TrxRecruitment recruitment) {
        if (recruitment.getLeaderCode() != null && !recruitment.getLeaderCode().isEmpty()) {
            Agent leaderAgent = agentRepository.findTopByAgentCode(recruitment.getLeaderCode()).orElse(null);
            if (leaderAgent != null) {
                return String.format("%s - %s", leaderAgent.getAgentCode(), leaderAgent.getAgentName());
            } else {
                return recruitment.getLeaderCode();
            }
        }
        return "";
    }

    private String getProcessedByInfo(TrxApprovalHeader approvalHeader) {
        Set<TrxApprovalDetail> approvalDetails = approvalHeader.getApprovalDetails();
        if (approvalDetails != null && !approvalDetails.isEmpty()) {
            // Get the last approval detail (most recent action)
            Optional<TrxApprovalDetail> lastApproval = approvalDetails.stream()
                    .max(Comparator.comparing(TrxApprovalDetail::getCreatedAt));

            if (lastApproval.isPresent() && lastApproval.get().getActionBy() != null) {
                User actionBy = lastApproval.get().getActionBy();
                return actionBy.getName();
            }
        }
        return "";
    }

    private Instant getApprovalDate(TrxApprovalHeader approvalHeader) {
        // Return approval date only if status is DISETUJUI (approved)
        if (approvalHeader.getApprovalStatus() == ApprovalStatus.DISETUJUI) {
            Set<TrxApprovalDetail> approvalDetails = approvalHeader.getApprovalDetails();
            if (approvalDetails != null && !approvalDetails.isEmpty()) {
                // Find the approval detail where status changed to DISETUJUI
                Optional<TrxApprovalDetail> approvalDetail = approvalDetails.stream()
                        .filter(detail -> detail.getApprovalStatus() == ApprovalStatus.DISETUJUI)
                        .max(Comparator.comparing(TrxApprovalDetail::getCreatedAt));

                if (approvalDetail.isPresent()) {
                    return approvalDetail.get().getCreatedAt();
                }
            }
        }
        return null;
    }

    private String getRemarks(TrxApprovalHeader approvalHeader) {
        // Get remarks for pending or rejection reasons
        if (approvalHeader.getApprovalStatus() == ApprovalStatus.TERTUNDA ||
                approvalHeader.getApprovalStatus() == ApprovalStatus.DITOLAK) {

            Set<TrxApprovalDetail> approvalDetails = approvalHeader.getApprovalDetails();
            if (approvalDetails != null && !approvalDetails.isEmpty()) {
                // Get the most recent approval detail with remarks
                Optional<TrxApprovalDetail> lastApprovalWithRemarks = approvalDetails.stream()
                        .filter(detail -> detail.getRemarks() != null && !detail.getRemarks().trim().isEmpty())
                        .max(Comparator.comparing(TrxApprovalDetail::getCreatedAt));

                if (lastApprovalWithRemarks.isPresent()) {
                    return lastApprovalWithRemarks.get().getRemarks();
                }
            }

            // Fallback to approval header remarks
            if (approvalHeader.getRemarks() != null && !approvalHeader.getRemarks().trim().isEmpty()) {
                return approvalHeader.getRemarks();
            }
        }
        return "";
    }

    private String getRequesterInfo(User requester) {
        if (requester == null) {
            return "";
        }

        Agent requesterAgent = agentRepository.findTopByUser(requester).orElse(null);
        if (requesterAgent != null) {
            return String.format("%s - %s", requesterAgent.getAgentCode(), requesterAgent.getAgentName());
        } else {
            return requester.getName();
        }
    }

    private String getAgentLeaderInfo(Agent agent) {
        if (agent.getLeader() != null) {
            return String.format("%s - %s", agent.getLeader().getAgentCode(), agent.getLeader().getAgentName());
        }
        return "";
    }

    private String getProposedLeaderInfo(String proposedLeaderCode) {
        if (proposedLeaderCode != null && !proposedLeaderCode.isEmpty()) {
            Agent leaderAgent = agentRepository.findTopByAgentCode(proposedLeaderCode).orElse(null);
            if (leaderAgent != null) {
                return String.format("%s - %s", leaderAgent.getAgentCode(), leaderAgent.getAgentName());
            } else {
                return proposedLeaderCode;
            }
        }
        return "";
    }


}
