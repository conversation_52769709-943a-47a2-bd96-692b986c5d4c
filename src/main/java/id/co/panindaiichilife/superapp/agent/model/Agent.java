package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.AgentStatus;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;
import java.time.LocalDate;

@Entity
@Table(name = "agents", uniqueConstraints = {
        @UniqueConstraint(name = "uk_agents_agent_code", columnNames = "agentCode")
})
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id", "agentCode"})
@SQLDelete(sql = "UPDATE agents SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class Agent extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "agents_id_seq")
    @SequenceGenerator(name = "agents_id_seq", sequenceName = "agents_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    @OneToOne(targetEntity = User.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Audited
    @ManyToOne(targetEntity = Agent.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "leader_id")
    private Agent leader;

    @Audited
    private String agentCode;

    @Audited
    private String agentName;

    @Audited
    @Enumerated(EnumType.STRING)
    private DistributionCode distributionCode;

    @Audited
    private String roleName;

    @Audited
    private String level;

    @Audited
    private String positionLevel;

    @Audited
    private String leaderCode;

    @Audited
    private String regionCode;

    @Audited
    private String regionName;

    @Audited
    private String subRegionCode;

    @Audited
    private String subRegionName;

    @Audited
    private String areaCode;

    @Audited
    private String areaName;

    @Audited
    private String branchCode;

    @Audited
    private String branchName;

    @Audited
    private String groupCode;

    @Audited
    private String groupName;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String mBranchCode;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String mBranchName;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String sBranchCode;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String sBranchName;

    @Audited
    private String licenseNumberAAJI;

    @Audited
    private LocalDate licenseExpiredDateAAJI;

    @Audited
    private String licenseNumberAASI;

    @Audited
    private LocalDate licenseExpiredDateAASI;

    @Audited
    private String leaderG2G;

    @Audited
    private String recruiterCode;

    @Audited
    private LocalDate dob;

    @Audited
    private String gender;

    @Audited
    private String education;

    @Audited
    @Enumerated(EnumType.STRING)
    private AgentStatus status;

    @Audited
    @Enumerated(EnumType.STRING)
    private Channel channel;

    @Audited
    private String email;

    @Audited
    private String phoneNumber;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String address;

    @Audited
    private String bankAccountNumber;

    @Audited
    private String bank;

    @Audited
    private String maritalStatus;

    @Audited
    private String bankAttachment;

    @Audited
    private String ktpAttachment;

    @Audited
    private String kkAttachment;

    @Audited
    private String photo;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
