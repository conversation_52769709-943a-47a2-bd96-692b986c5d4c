package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalValidationAmlDto {
    @SerializedName("name")
    @JsonProperty("name")
    private String name;

    @SerializedName("user")
    @JsonProperty("user")
    private String user;
}
