package id.co.panindaiichilife.superapp.agent.service;

import com.google.gson.Gson;
import id.co.panindaiichilife.superapp.agent.api.dto.TrxEditProfileDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRecruitmentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRejoinApplicationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxTerminationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalHeaderDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalLevelDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ApprovalFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.ApprovalHeaderFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.HistoryApprovalFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ApprovalForm;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.*;
import id.co.panindaiichilife.superapp.agent.model.*;
import id.co.panindaiichilife.superapp.agent.repository.*;
import id.co.panindaiichilife.superapp.agent.util.ApprovalRoleUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ApprovalService {
    private final UserRepository userRepository;
    private final TrxApprovalHeaderRepository trxApprovalHeaderRepository;
    private final TrxApprovalDetailRepository trxApprovalDetailRepository;
    private final ApprovalLevelRepository approvalLevelRepository;
    private final TrxEditProfileRepository trxEditProfileRepository;
    private final AgentRepository agentRepository;
    private final TrxRecruitmentRepository trxRecruitmentRepository;
    private final NotificationService notificationService;
    private final TrxTerminationRepository trxTerminationRepository;
    private final TrxRejoinApplicationRepository trxRejoinApplicationRepository;
    private final BranchRepository branchRepository;
    private final ApplicationContext applicationContext;

    public ApprovalHeaderDto findOne(Long id) {
        TrxApprovalHeader data = trxApprovalHeaderRepository.findById(id).orElseThrow(NotFoundException::new);

        ApprovalHeaderDto dto = BaseDto.of(ApprovalHeaderDto.class, data);
        // Set detail data based on transaction type and ID
        if (data.getTrxId() != null && data.getTrxType() != null) {
            dto.setDetailData(getDetailDataByType(data.getTrxType(), data.getTrxId()));
        }
        return dto;
    }

    public TrxApprovalHeader getTrxApprovalHeaderById(Long id) {
        return trxApprovalHeaderRepository.findById(id).orElseThrow(() ->
                new NotFoundException("Approval header not found with ID: " + id));
    }

    public Page<ApprovalHeaderDto> getHistoryApproval(HistoryApprovalFilter filter, Pageable pageable) {
        // Get approval details with filter
        Page<TrxApprovalDetail> approvalDetails = trxApprovalDetailRepository.findAll(filter, pageable);

        // Convert to ApprovalHeaderDto with detailData
        return approvalDetails.map(detail -> {
            TrxApprovalHeader header = detail.getApprovalHeader();
            ApprovalHeaderDto dto = BaseDto.of(ApprovalHeaderDto.class, header);
            dto.setApprovalStatus(detail.getApprovalStatus());
            // Set detail data based on transaction type and ID
            if (header.getTrxId() != null && header.getTrxType() != null) {
                dto.setDetailData(getDetailDataByType(header.getTrxType(), header.getTrxId()));
            }

            return dto;
        });
    }

    /**
     * Checks if the approval role matches any of the requester's roles
     * This is used to skip approval levels for non-public recruitment transactions when the approver role
     * is the same as the requester's role to avoid self-approval scenarios.
     * Supports both single roles and comma-separated multiple roles.
     *
     * @param approverRole The role required for approval (can be comma-separated)
     * @param requester    The user who requested the approval
     * @return true if the approval role matches any of the requester's roles
     */
    private boolean approvalRoleMatchesRequesterRole(String approverRole, User requester) {
        if (approverRole == null || requester == null) {
            return false;
        }

        Set<String> requesterRoles = requester.getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());

        return ApprovalRoleUtil.hasMatchingRole(approverRole, requesterRoles);
    }

    /**
     * Sets up the approver role for a given approval level
     *
     * @param approvalHeader The approval header to update
     * @param level          The approval level configuration
     * @param levelNumber    The level number being set up
     * @param user           The user who will be the requester (used for finding upline)
     * @return true if setup was successful, false if no valid approver could be found
     */
    private boolean setupApproverRole(TrxApprovalHeader approvalHeader, ApprovalLevel level, int levelNumber, User user) {
        return setupApproverRole(approvalHeader, level, levelNumber, user, false);
    }

    /**
     * Sets up the approver role for a given approval level
     *
     * @param approvalHeader         The approval header to update
     * @param level                  The approval level configuration
     * @param levelNumber            The level number being set up
     * @param user                   The user who will be the requester (used for finding upline)
     * @param isNonPublicRecruitment Whether this is a non-public recruitment transaction
     * @return true if setup was successful, false if no valid approver could be found
     */
    private boolean setupApproverRole(TrxApprovalHeader approvalHeader, ApprovalLevel level, int levelNumber, User user, boolean isNonPublicRecruitment) {
        // For non-public recruitment transactions, check if approval role matches requester role
        // Only apply this check when the user is the original requester (level 1), not for subsequent levels
        if (isNonPublicRecruitment && levelNumber == 1 && approvalRoleMatchesRequesterRole(level.getApproverRole(), user)) {
            log.info("Skipping approval level {} for non-public recruitment: approval role {} matches requester role",
                    levelNumber, level.getApproverRole());
            return false;
        }

        // If this level requires direct upline approval
        if (Boolean.TRUE.equals(level.getIsDirectUpline())) {
            // Find the upline agent for the user
            Agent uplineAgent = findUplineAgentForUser(user);

            if (uplineAgent != null && uplineAgent.getUser() != null) {
                // Set a special marker in the approverRole field to indicate this is for upline approval
                // Format: UPLINE:{uplineAgentCode}
                approvalHeader.setApproverRole("UPLINE:" + uplineAgent.getAgentCode());
                log.info("Setting direct upline approver for level {}: {}", levelNumber, uplineAgent.getAgentCode());
                return true;
            } else {
                log.warn("Upline not found for user {} at level {}", user.getUsername(), levelNumber);
                return false;
            }
        } else {
            // Standard role-based approval
            approvalHeader.setApproverRole(level.getApproverRole());

            // Parse multiple roles from the approver role configuration
            Set<String> approverRoles = ApprovalRoleUtil.parseRoles(level.getApproverRole());

            // Check if there are any users with any of these roles
            List<User> usersWithRole = userRepository.findUsersByRoleCodes(approverRoles);
            if (usersWithRole == null || usersWithRole.isEmpty()) {
                log.warn("No users found with roles {} for level {}", approverRoles, levelNumber);
                return false;
            }

            log.info("Found {} users with roles {} for level {}", usersWithRole.size(), approverRoles, levelNumber);
            return true;
        }
    }

    /**
     * Attempts to skip to the next approval level when the current level cannot be processed
     * (e.g., when upline not found or no users with the required role)
     *
     * @param approvalHeader  The current approval header
     * @param currentLevel    The current level configuration
     * @param nextLevelNumber The next level number to try
     * @param trxType         The transaction type
     * @param channel         The channel
     * @param requesterRoles  The requester roles
     * @param reason          The reason for skipping (for logging)
     * @return true if successfully skipped to next level, false otherwise
     */
    private boolean trySkipToNextLevel(TrxApprovalHeader approvalHeader, ApprovalLevel currentLevel, int nextLevelNumber,
                                       TrxType trxType, Channel channel, Set<String> requesterRoles, String reason) {
        return trySkipToNextLevel(approvalHeader, currentLevel, nextLevelNumber, trxType, channel, requesterRoles, reason, false);
    }

    /**
     * Attempts to skip to the next approval level when the current level cannot be processed
     * (e.g., when upline not found or no users with the required role)
     *
     * @param approvalHeader         The current approval header
     * @param currentLevel           The current level configuration
     * @param nextLevelNumber        The next level number to try
     * @param trxType                The transaction type
     * @param channel                The channel
     * @param requesterRoles         The requester roles
     * @param reason                 The reason for skipping (for logging)
     * @param isNonPublicRecruitment Whether this is a non-public recruitment transaction
     * @return true if successfully skipped to next level, false otherwise
     */
    private boolean trySkipToNextLevel(TrxApprovalHeader approvalHeader, ApprovalLevel currentLevel, int nextLevelNumber,
                                       TrxType trxType, Channel channel, Set<String> requesterRoles, String reason, boolean isNonPublicRecruitment) {
        if (nextLevelNumber > approvalHeader.getMaxLevel()) {
            log.warn("Cannot skip to next level: already at max level {}", approvalHeader.getMaxLevel());
            return false;
        }

        try {
            ApprovalLevel nextLevel = approvalLevelRepository.findApplicableLevel(
                            trxType, nextLevelNumber, channel, requesterRoles, null, null)
                    .orElse(null);

            if (nextLevel == null) {
                log.warn("Cannot skip to next level: no level configuration found for level {}", nextLevelNumber);
                return false;
            }

            // Skip to the next level
            log.info("Skipping to level {} since {}", nextLevelNumber, reason);
            approvalHeader.setCurrentLevel(nextLevelNumber);

            // Try to set up the approver role for this level
            boolean setupSuccess = setupApproverRole(approvalHeader, nextLevel, nextLevelNumber, approvalHeader.getRequestBy(), isNonPublicRecruitment);

            // If setup failed, try to skip to the next level recursively
            if (!setupSuccess) {
                log.warn("Could not set up approver for level {}, trying next level", nextLevelNumber);
                return trySkipToNextLevel(approvalHeader, nextLevel, nextLevelNumber + 1, trxType, channel, requesterRoles,
                        "no valid approver found for level " + nextLevelNumber, isNonPublicRecruitment);
            }

            return true;
        } catch (Exception e) {
            log.error("Error finding next level: {}", e.getMessage());
            return false;
        }
    }

    @Transactional
    public TrxApprovalHeader requestApproval(TrxType trxType, Long trxId, User user, String remark, Channel channel, Boolean isPublic) {
        // Check if approval header already exists for this transaction
        Optional<TrxApprovalHeader> existingApproval = trxApprovalHeaderRepository.findTopByTrxTypeAndTrxId(trxType, trxId);

        if (existingApproval.isPresent()) {
            TrxApprovalHeader existing = existingApproval.get();
            ApprovalStatus currentStatus = existing.getApprovalStatus();

            // If approval is in pending state, return existing approval header
            if (currentStatus == ApprovalStatus.BARU ||
                    currentStatus == ApprovalStatus.MENUNGGU_PERSETUJUAN ||
                    currentStatus == ApprovalStatus.TERTUNDA) {
                log.info("Approval already exists for transaction {} with ID {} in status {}, returning existing approval",
                        trxType, trxId, currentStatus);
                return existing;
            }

            // If approval is in final state (DISETUJUI, DITOLAK, DIBATALKAN),
            // we can proceed to create a new approval header
            log.info("Found existing approval for transaction {} with ID {} in final status {}, creating new approval",
                    trxType, trxId, currentStatus);
        }

        Set<String> roles = user.getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());

        Integer maxLevel = approvalLevelRepository.findMaxLevelForTransactionTypeAndRequesterRole(trxType, roles);
        if (maxLevel == null) {
            throw new IllegalArgumentException("No approval levels defined for transaction type: " + trxType);
        }

        ApprovalLevel firstLevel = approvalLevelRepository.findApplicableLevel(
                        trxType, 1, channel, roles, null, null)
                .orElseThrow(() -> new IllegalStateException("No approval level configuration found"));

        // Create approval header
        TrxApprovalHeader approvalHeader = new TrxApprovalHeader();
        approvalHeader.setRequestBy(user);
        approvalHeader.setTrxType(trxType);
        approvalHeader.setApprovalStatus(ApprovalStatus.BARU);
        approvalHeader.setTrxId(trxId);
        approvalHeader.setCurrentLevel(1);
        approvalHeader.setMaxLevel(maxLevel);
        approvalHeader.setRemarks(remark);
        approvalHeader.setChannel(channel);
        approvalHeader.setIsPublic(isPublic);

        // Set branch based on transaction or requester
        Branch branch = determineBranchForApproval(trxType, trxId, user);
        approvalHeader.setBranch(branch);

        // For public recruitment, the recruiter needs to verify first
        if (Boolean.TRUE.equals(isPublic) && trxType.name().startsWith("RECRUITMENT_")) {
            // For public recruitment, set the recruiter as the approver
            // Format: RECRUITER:{username}
            approvalHeader.setApproverRole("RECRUITER:" + user.getUsername());
            log.info("Setting recruiter as approver for public recruitment: {}", user.getUsername());
        } else {
            // Determine if this is a non-public recruitment transaction for role checking
            boolean isNonPublicRecruitment = trxType.name().startsWith("RECRUITMENT_") && !Boolean.TRUE.equals(isPublic);

            // Try to set up the approver role for the first level
            boolean setupSuccess = setupApproverRole(approvalHeader, firstLevel, 1, user, isNonPublicRecruitment);

            // If setup failed, try to skip to the next level
            if (!setupSuccess && maxLevel > 1) {
                log.warn("Could not set up approver for level 1, trying to skip to next level");
                boolean skipped = trySkipToNextLevel(approvalHeader, firstLevel, 2, trxType, channel, roles,
                        "no valid approver found for level 1", isNonPublicRecruitment);

                if (skipped) {
                    // Save the header and send notification before returning
                    trxApprovalHeaderRepository.save(approvalHeader);
                    sendNotification(approvalHeader);
                    return approvalHeader;
                }

                // If we couldn't skip to any level, use the first level's role as a fallback
                // This is a last resort and might not work if there are no users with this role
                log.warn("Could not skip to any level, falling back to first level role: {}", firstLevel.getApproverRole());
                approvalHeader.setApproverRole(firstLevel.getApproverRole());
            }
        }

        trxApprovalHeaderRepository.save(approvalHeader);

        // send push notification
        sendNotification(approvalHeader);

        return approvalHeader;
    }

    @Transactional
    public TrxApprovalHeader resendApproval(TrxType trxType, Long trxId) {
        // Find the approval header
        TrxApprovalHeader approvalHeader = trxApprovalHeaderRepository.findTopByTrxTypeAndTrxId(trxType, trxId)
                .orElseThrow(() -> new NotFoundException("Approval for transaction " + trxType.name() + " " + trxId + " not found"));

        // Reset to first level
        approvalHeader.setCurrentLevel(1);
        approvalHeader.setApprovalStatus(ApprovalStatus.MENUNGGU_PERSETUJUAN);

        // Clear previous approval state
        approvalHeader.setLastApproverRole(null);
        approvalHeader.setLastLevel(null);

        // Get the requester's roles
        Set<String> roles = approvalHeader.getRequestBy().getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());

        // Find the first level configuration
        ApprovalLevel firstLevel = approvalLevelRepository.findApplicableLevel(
                        trxType, 1, approvalHeader.getChannel(), roles, null, null)
                .orElseThrow(() -> new IllegalStateException("No approval level configuration found"));

        if (Boolean.TRUE.equals(firstLevel.getIsDirectUpline())) {
            // If first level requires direct upline approval, find the upline agent
            Agent uplineAgent = findUplineAgentForUser(approvalHeader.getRequestBy());

            if (uplineAgent != null && uplineAgent.getUser() != null) {
                // Set upline approver
                approvalHeader.setApproverRole("UPLINE:" + uplineAgent.getAgentCode());
                log.info("Resetting to direct upline approver: {}", uplineAgent.getAgentCode());
            } else {
                // Fall back to role-based approval if upline not found
                log.warn("Direct upline approval required but no upline found for user: {}", approvalHeader.getRequestBy().getUsername());
                approvalHeader.setApproverRole(firstLevel.getApproverRole());

                // Parse multiple roles from the approver role configuration
                Set<String> approverRoles = ApprovalRoleUtil.parseRoles(firstLevel.getApproverRole());

                // Try to skip to next level if no users with these roles
                List<User> usersWithRole = userRepository.findUsersByRoleCodes(approverRoles);
                if ((usersWithRole == null || usersWithRole.isEmpty()) && approvalHeader.getMaxLevel() > 1) {
                    trySkipToNextLevel(approvalHeader, firstLevel, 2, trxType, approvalHeader.getChannel(), roles,
                            "no users found with roles " + approverRoles);
                }
            }
        } else {
            // Determine if this is a non-public recruitment transaction for role checking
            boolean isNonPublicRecruitment = trxType.name().startsWith("RECRUITMENT_") &&
                    !Boolean.TRUE.equals(approvalHeader.getIsPublic());

            // Standard role-based approval
            approvalHeader.setApproverRole(firstLevel.getApproverRole());

            // Parse multiple roles from the approver role configuration
            Set<String> approverRoles = ApprovalRoleUtil.parseRoles(firstLevel.getApproverRole());

            // Try to skip to next level if no users with these roles or if role matches requester for non-public recruitment
            List<User> usersWithRole = userRepository.findUsersByRoleCodes(approverRoles);
            boolean shouldSkip = (usersWithRole == null || usersWithRole.isEmpty()) ||
                    (isNonPublicRecruitment && approvalRoleMatchesRequesterRole(firstLevel.getApproverRole(), approvalHeader.getRequestBy()));

            if (shouldSkip && approvalHeader.getMaxLevel() > 1) {
                String reason = isNonPublicRecruitment && approvalRoleMatchesRequesterRole(firstLevel.getApproverRole(), approvalHeader.getRequestBy()) ?
                        "approval role matches requester role for non-public recruitment" :
                        "no users found with roles " + approverRoles;
                trySkipToNextLevel(approvalHeader, firstLevel, 2, trxType, approvalHeader.getChannel(), roles, reason, isNonPublicRecruitment);
            }
        }

        // Save the updated header
        trxApprovalHeaderRepository.save(approvalHeader);

        // Send push notification
        sendNotification(approvalHeader);
        return approvalHeader;
    }

    @Transactional
    public void cancelApproval(TrxType trxType, Long trxId) {
        TrxApprovalHeader approvalHeader = trxApprovalHeaderRepository.findTopByTrxTypeAndTrxId(trxType, trxId).orElseThrow(() -> new NotFoundException("Approval for transaction " + trxType.name() + " " + trxId + " not found"));
        approvalHeader.setApprovalStatus(ApprovalStatus.DIBATALKAN);
        trxApprovalHeaderRepository.save(approvalHeader);
    }

    @Transactional
    public void cancelApproval(TrxType trxType, Long trxId, String remarks) {
        TrxApprovalHeader approvalHeader = trxApprovalHeaderRepository.findTopByTrxTypeAndTrxId(trxType, trxId).orElse(null);

        if (approvalHeader == null) {
            log.warn("Approval not found for {} with id {}, skipped...", trxType, trxId);
            return;
        }

        approvalHeader.setApprovalStatus(ApprovalStatus.DIBATALKAN);
        approvalHeader.setRemarks(remarks);
        trxApprovalHeaderRepository.save(approvalHeader);
    }

    @Transactional
    public ApprovalHeaderDto processApproval(ApprovalForm request, User user) {
        // Get the approval header
        TrxApprovalHeader header = trxApprovalHeaderRepository.findById(request.getApprovalHeaderId())
                .orElseThrow(() -> new IllegalArgumentException("Approval request not found with ID: " + request.getApprovalHeaderId()));

        if (null != request.getApprovalDetails()) {
            header.setDetailApproval(new Gson().toJson(request.getApprovalDetails()));
        }

        // Validate user can approve
        validateUserCanApprove(user, header);

        // Check Approval Status
        ApprovalStatus approvalStatus = request.getAction();
        if (request.getApprovalDetails() != null) {
            approvalStatus = this.checkApprovalStatus(request);
        }

        // Create approval detail
        TrxApprovalDetail detail = new TrxApprovalDetail();
        detail.setApprovalHeader(header);
        detail.setActionBy(user);
        detail.setRemarks(request.getRemarks());
        detail.setApprovalStatus(approvalStatus);
        detail.setLevelNumber(header.getCurrentLevel());
        detail.setDetailApproval(header.getDetailApproval());

        trxApprovalDetailRepository.save(detail);

        // Update approval status based on action
        header = updateApprovalStatus(header, approvalStatus, user);

        // Return updated header
        return BaseDto.of(ApprovalHeaderDto.class, header);
    }

    @Transactional
    public ApprovalHeaderDto processApproval(ApprovalForm request, String username) {
        // Get the requesting user
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found: " + username));
        return processApproval(request, user);
    }

    private void validateCanApproveTermination(TrxApprovalHeader header) {
        final List<TrxType> terminationTypes = List.of(TrxType.TERMINASI_BP, TrxType.TERMINASI_BM,
                TrxType.TERMINASI_BD);
        if (!terminationTypes.contains(header.getTrxType())) {
            // skip non termination approvals
            return;
        }

        TrxTermination termination = trxTerminationRepository.findById(header.getTrxId()).orElseThrow(() ->
                new NotFoundException("No termination was found with ID: " + header.getTrxId()));

        // check if policy transfer is not finalized yet, can't proceed with approval/rejection
        if (termination.getTrxPolicyTransfer() == null ||
                termination.getTrxPolicyTransfer().getStatus() != PolicyTransferStatus.APPROVED) {
            throw new BadRequestException("Cannot proceed approval, policy transfer still pending");
        }
    }

    private void validateUserCanApprove(User user, TrxApprovalHeader header) {
        Set<ApprovalStatus> allowedStatuses = EnumSet.of(ApprovalStatus.BARU, ApprovalStatus.MENUNGGU_PERSETUJUAN);
        if (!allowedStatuses.contains(header.getApprovalStatus())) {
            throw new IllegalStateException("Cannot process approval for request with status: " + header.getApprovalStatus());
        }

        // validate specific for termination
        validateCanApproveTermination(header);

        // Check if this is an upline approval request
        String approverRole = header.getApproverRole();
        if (approverRole != null && approverRole.startsWith("UPLINE:")) {
            // Extract the upline agent code from the approverRole field
            String uplineAgentCode = approverRole.substring("UPLINE:".length());

            // Get the agent associated with the current user
            Agent userAgent = agentRepository.findTopByUser(user).orElse(null);

            // Check if the current user is the specified upline agent
            if (userAgent != null && uplineAgentCode.equals(userAgent.getAgentCode())) {
                log.info("User {} validated as direct upline approver", user.getUsername());
                return; // User is the specified upline agent, approval is allowed
            }

            throw new IllegalStateException("User is not the required upline approver for this request");
        }

        // Check if this is a recruiter approval for public recruitment
        if (approverRole != null && approverRole.startsWith("RECRUITER:")) {
            // Extract the recruiter username from the approverRole field
            String recruiterUsername = approverRole.substring("RECRUITER:".length());

            // Check if the current user is the specified recruiter
            if (recruiterUsername.equals(user.getUsername())) {
                log.info("User {} validated as recruiter for public recruitment", user.getUsername());
                return; // User is the specified recruiter, approval is allowed
            }

            throw new IllegalStateException("User is not the required recruiter for this public recruitment");
        }

        // Standard role-based approval validation
        // Extract roles and branch codes
        Set<String> requesterRoles = header.getRequestBy().getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());
        Set<String> roles = user.getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());
        Set<String> branchCodes = user.getBranches().stream()
                .map(Branch::getBranchCode)
                .collect(Collectors.toSet());

        // Check if user has the correct role for this level
        approvalLevelRepository.findApplicableLevel(
                        header.getTrxType(),
                        header.getCurrentLevel(),
                        header.getChannel(),
                        requesterRoles,
                        roles,
                        branchCodes)
                .orElseThrow(() -> new IllegalStateException("User does not have permission to approve this request"));
    }

    private TrxApprovalHeader updateApprovalStatus(TrxApprovalHeader header, ApprovalStatus action, User approvalActor) {
        // Extract roles and branch codes
        Set<String> requesterRoles = header.getRequestBy().getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());
        Set<String> roles = approvalActor.getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());
        Set<String> branchCodes = approvalActor.getBranches().stream()
                .map(Branch::getBranchCode)
                .collect(Collectors.toSet());

        switch (action) {
            case DISETUJUI:
                // Check if enough approvals at current level
                int approvalCount = trxApprovalDetailRepository.countByApprovalHeaderAndLevelNumberAndApprovalStatus(
                        header, header.getCurrentLevel(), ApprovalStatus.DISETUJUI);

                // Check if this is an upline approval or recruiter approval for public recruitment
                boolean isUplineApproval = header.getApproverRole() != null && header.getApproverRole().startsWith("UPLINE:");
                boolean isRecruiterApproval = header.getApproverRole() != null && header.getApproverRole().startsWith("RECRUITER:");
                ApprovalLevel level;

                if (isUplineApproval || isRecruiterApproval) {
                    // For upline approvals or recruiter approvals, we need to get the level configuration to determine next steps
                    level = approvalLevelRepository.findApplicableLevel(
                                    header.getTrxType(), header.getCurrentLevel(), header.getChannel(), requesterRoles, null, null)
                            .orElseThrow(() -> new IllegalStateException("No approval level configuration found"));

                    // For upline or recruiter approvals, we consider it approved with just one approval
                    approvalCount = 1;
                } else {
                    // Standard role-based approval
                    level = approvalLevelRepository.findApplicableLevel(
                                    header.getTrxType(), header.getCurrentLevel(), header.getChannel(), requesterRoles, roles, branchCodes)
                            .orElseThrow(() -> new IllegalStateException("No approval level configuration found"));
                }

                if (approvalCount >= level.getMinApprovers()) {
                    // If this is the final level, mark as fully approved
                    if (header.getCurrentLevel().equals(header.getMaxLevel())) {
                        // Handle synchronous portal system update for recruitment transactions BEFORE updating approval status
                        // This ensures that if portal registration fails, the approval status won't be updated to DISETUJUI
                        handlePortalSystemUpdate(header);

                        // Only update approval status to DISETUJUI if portal system update succeeded
                        header.setApprovalStatus(ApprovalStatus.DISETUJUI);
                    } else {
                        // Move to next level
                        int nextLevelNumber = header.getCurrentLevel() + 1;
                        ApprovalLevel nextLevel = approvalLevelRepository.findApplicableLevel(
                                        header.getTrxType(), nextLevelNumber, header.getChannel(), requesterRoles, null, null)
                                .orElseThrow(() -> new IllegalStateException("No approval level configuration found"));

                        // Store the current approver role and level before moving to the next level
                        header.setLastApproverRole(header.getApproverRole());
                        header.setLastLevel(header.getCurrentLevel());
                        header.setCurrentLevel(nextLevelNumber);
                        header.setApprovalStatus(ApprovalStatus.MENUNGGU_PERSETUJUAN);

                        // Determine if this is a non-public recruitment transaction for role checking
                        boolean isNonPublicRecruitment = header.getTrxType().name().startsWith("RECRUITMENT_") &&
                                !Boolean.TRUE.equals(header.getIsPublic());

                        // Try to set up the approver role for the next level
                        boolean setupSuccess = setupApproverRole(header, nextLevel, nextLevelNumber, approvalActor, isNonPublicRecruitment);

                        // If setup failed, try to skip to the next level
                        if (!setupSuccess && nextLevelNumber < header.getMaxLevel()) {
                            log.warn("Could not set up approver for level {}, trying to skip to next level", nextLevelNumber);
                            boolean skipped = trySkipToNextLevel(header, nextLevel, nextLevelNumber + 1,
                                    header.getTrxType(), header.getChannel(), requesterRoles,
                                    "no valid approver found for level " + nextLevelNumber, isNonPublicRecruitment);

                            if (skipped) {
                                // Save the header and send notification before returning
                                header = trxApprovalHeaderRepository.save(header);
                                sendNotification(header);
                                return header;
                            }

                            // If we couldn't skip to any level, use the next level's role as a fallback
                            // This is a last resort and might not work if there are no users with this role
                            log.warn("Could not skip to any level, falling back to level {} role: {}",
                                    nextLevelNumber, nextLevel.getApproverRole());
                            header.setApproverRole(nextLevel.getApproverRole());
                        }
                    }
                }
                break;

            case MENUNGGU_PERSETUJUAN:
                header.setApprovalStatus(ApprovalStatus.MENUNGGU_PERSETUJUAN);
                break;

            case TERTUNDA:
                header.setApprovalStatus(ApprovalStatus.TERTUNDA);
                header.setCurrentLevel(1);
                break;

            case DITOLAK:
                header.setApprovalStatus(ApprovalStatus.DITOLAK);
                break;

            case DIBATALKAN:
                header.setApprovalStatus(ApprovalStatus.DIBATALKAN);
                break;
        }

        header = trxApprovalHeaderRepository.save(header);

        // send push notification
        sendNotification(header);

        return header;
    }

    public Page<ApprovalHeaderDto> findAll(Pageable pageable, ApprovalHeaderFilter filter) {
        Page<TrxApprovalHeader> approvalHeaders = trxApprovalHeaderRepository.findAll(filter, pageable);

        Page<ApprovalHeaderDto> dtoPage = BaseDto.of(ApprovalHeaderDto.class, approvalHeaders, pageable);

        // Set detailData for each approval in the list
        dtoPage.getContent().forEach(dto -> {
            if (dto.getTrxId() != null && dto.getTrxType() != null) {
                dto.setDetailData(getDetailDataByType(dto.getTrxType(), dto.getTrxId()));
            }
        });
        return dtoPage;
    }

    public Page<ApprovalHeaderDto> getPendingApprovalsForUser(String username, ApprovalFilter filter, Pageable pageable, boolean includeLastApproved) {
        // Get the user
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found: " + username));

        // Extract roles and branch codes
        Set<String> roles = user.getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());

        Set<String> branchCodes = user.getBranches().stream()
                .map(Branch::getBranchCode)
                .collect(Collectors.toSet());

        // Get approval levels for this user based on role and branch
        List<ApprovalLevel> eligibleLevels = approvalLevelRepository.findByApproverRoleInAndBranchCodeIn(
                roles, branchCodes);

        // Get level numbers this user can approve
        List<Integer> eligibleLevelNumbers = eligibleLevels.stream()
                .map(ApprovalLevel::getLevelNumber)
                .toList();

        // Create and configure the filter
        filter.setTrxType(filter.getTrxType());
        filter.setApprovalStatusList(Arrays.asList(ApprovalStatus.MENUNGGU_PERSETUJUAN, ApprovalStatus.BARU));

        // Set current approver criteria
        filter.setEligibleLevelNumbers(eligibleLevelNumbers);
        filter.setApproverRoles(roles);

        // Check if the user is an agent
        Agent userAgent = agentRepository.findTopByUser(user).orElse(null);
        if (userAgent != null) {
            // Add a custom predicate to the filter to include approvals where this user is the upline approver
            // This will be handled in the repository layer by checking for approverRole starting with "UPLINE:{agentCode}"
            filter.setUplineAgentCode(userAgent.getAgentCode());
        }

        // Include approvals where this user is the recruiter for public recruitment
        // This will be handled in the repository layer by checking for approverRole starting with "RECRUITER:{username}"
        filter.setRecruiterUsername(user.getUsername());

        // Add branch filtering for STAFF users only
        if (UserType.STAFF.equals(user.getUserType()) && !branchCodes.isEmpty()) {
            filter.setBranchCodes(branchCodes);
            log.info("Applied branch filtering for STAFF user {} with branches: {}", username, branchCodes);
        }

        // Set last approver criteria if requested
        if (includeLastApproved) {
            filter.setIncludeLastApprover(true);
            filter.setLastLevelNumbers(eligibleLevelNumbers);
            filter.setLastApproverRoles(roles);
        }

        // Apply the filter and get results
        Page<TrxApprovalHeader> headers = trxApprovalHeaderRepository.findAll(filter, pageable);
        Page<ApprovalHeaderDto> dtoPage = BaseDto.of(ApprovalHeaderDto.class, headers, pageable);

        // Set detailData for each approval in the list
        dtoPage.getContent().forEach(dto -> {
            if (dto.getTrxId() != null && dto.getTrxType() != null) {
                dto.setDetailData(getDetailDataByType(dto.getTrxType(), dto.getTrxId()));
            }
        });

        return dtoPage;
    }

    public List<ApprovalLevelDto> getApprovalLevelsByTrxType(TrxType trxType) {
        List<ApprovalLevel> levels = approvalLevelRepository.findByTrxTypeOrderByLevelNumber(trxType);
        return BaseDto.of(ApprovalLevelDto.class, levels);
    }

    public ApprovalStatus checkApprovalStatus(ApprovalForm request) {
        ApprovalStatus approvalStatus = request.getAction();

        // Check if approvalDetails is not null and not empty
        if (request.getApprovalDetails() != null && !request.getApprovalDetails().isEmpty()) {
            boolean allApproved = request.getApprovalDetails().stream()
                    .allMatch(approvalDetail -> approvalDetail.getAction().equals(ApprovalStatus.DISETUJUI));

            if (allApproved) {
                approvalStatus = ApprovalStatus.DISETUJUI;
            } else {
                approvalStatus = ApprovalStatus.TERTUNDA;
            }
        }
        return approvalStatus;
    }

    private Object getDetailDataByType(TrxType trxType, Long trxId) {
        switch (trxType) {
            case EDIT_PROFILE:
                return BaseDto.of(TrxEditProfileDto.class, trxEditProfileRepository.findById(trxId).orElse(null));
            case RECRUITMENT_BP:
            case RECRUITMENT_BM:
            case RECRUITMENT_BD:
                return BaseDto.of(TrxRecruitmentDto.class, trxRecruitmentRepository.findById(trxId).orElse(null));
            case TERMINASI_BP:
            case TERMINASI_BM:
            case TERMINASI_BD:
                return BaseDto.of(TrxTerminationDto.class, trxTerminationRepository.findById(trxId).orElse(null));
            case REJOIN_BP:
            case REJOIN_BM:
                return BaseDto.of(TrxRejoinApplicationDto.class, trxRejoinApplicationRepository
                        .findById(trxId).orElse(null));
            default:
                return null;
        }
    }

    /**
     * Find the upline agent (leader) for a given agent
     *
     * @param agent The agent to find the upline for
     * @return The upline agent or null if not found
     */
    private Agent findUplineAgent(Agent agent) {
        if (agent == null) {
            return null;
        }

        // If the agent has a leader code, find the leader
        if (agent.getLeaderCode() != null && !agent.getLeaderCode().isEmpty()) {
            return agentRepository.findTopByAgentCode(agent.getLeaderCode()).orElse(null);
        }

        // If the agent has a leader relationship, use that
        if (agent.getLeader() != null) {
            return agent.getLeader();
        }

        return null;
    }

    /**
     * Find the upline agent (leader) for a given user
     *
     * @param user The user to find the upline for
     * @return The upline agent or null if not found
     */
    private Agent findUplineAgentForUser(User user) {
        if (user == null) {
            return null;
        }

        // Find the agent associated with this user
        Agent agent = agentRepository.findTopByUser(user).orElse(null);
        if (agent == null) {
            return null;
        }

        return findUplineAgent(agent);
    }

    /**
     * Get the user associated with an agent
     *
     * @param agent The agent to get the user for
     * @return The user or null if not found
     */
    private User getUserForAgent(Agent agent) {
        if (agent == null) {
            return null;
        }

        return agent.getUser();
    }

    private void sendNotification(TrxApprovalHeader header) {
        // Delegate to the NotificationService
        notificationService.sendNotification(header);
    }

    /**
     * Determine the branch for approval based on transaction type and data
     * If branch is not available in transaction, get from requester
     *
     * @param trxType   Transaction type
     * @param trxId     Transaction ID
     * @param requester The user requesting approval
     * @return Branch for the approval or null if not found
     */
    private Branch determineBranchForApproval(TrxType trxType, Long trxId, User requester) {
        Branch branch = null;

        try {
            // Try to get branch from transaction first
            switch (trxType) {
                case RECRUITMENT_BP:
                case RECRUITMENT_BM:
                case RECRUITMENT_BD:
                    TrxRecruitment recruitment = trxRecruitmentRepository.findById(trxId).orElse(null);
                    if (recruitment != null) {
                        branch = recruitment.getBranch();
                    }
                    break;

                case REJOIN_BP:
                case REJOIN_BM:
                    TrxRejoinApplication rejoin = trxRejoinApplicationRepository.findById(trxId).orElse(null);
                    if (rejoin != null) {
                        branch = rejoin.getBranch();
                    }
                    break;

                case EDIT_PROFILE:
                    TrxEditProfile editProfile = trxEditProfileRepository.findById(trxId).orElse(null);
                    if (editProfile != null && editProfile.getAgent() != null) {
                        // Get branch from agent's branch code
                        String branchCode = editProfile.getAgent().getBranchCode();
                        if (branchCode != null) {
                            branch = branchRepository.findByBranchCode(branchCode).orElse(null);
                        }
                    }
                    break;

                case TERMINASI_BP:
                case TERMINASI_BM:
                case TERMINASI_BD:
                    TrxTermination termination = trxTerminationRepository.findById(trxId).orElse(null);
                    if (termination != null && termination.getTarget() != null) {
                        Agent targetAgent = agentRepository.findTopByUser(termination.getTarget()).orElse(null);
                        if (targetAgent != null && targetAgent.getBranchCode() != null) {
                            branch = branchRepository.findByBranchCode(targetAgent.getBranchCode()).orElse(null);
                        }
                    }
                    break;

                default:
                    // For other transaction types, branch will be null and we'll fall back to requester's branch
                    break;
            }
        } catch (Exception e) {
            log.warn("Error getting branch from transaction {}: {}", trxType, e.getMessage());
        }

        // If branch not found in transaction, get from requester's first branch
        if (branch == null && requester.getBranches() != null && !requester.getBranches().isEmpty()) {
            branch = requester.getBranches().iterator().next();
            log.info("Using requester's branch {} for approval", branch.getBranchCode());
        }

        return branch;
    }

    /**
     * Handles portal system update for approved recruitment transactions
     * Throws exception if portal registration fails to prevent approval status update
     *
     * @param header The approval header
     * @throws RuntimeException if portal system update fails
     */
    private void handlePortalSystemUpdate(TrxApprovalHeader header) {
        // Only process recruitment transactions
        if (!isRecruitmentTransaction(header.getTrxType())) {
            return;
        }

        log.info("Processing synchronous portal system update for recruitment ID: {}", header.getTrxId());

        // Delegate to TrxRecruitmentService to handle portal system update
        // This avoids circular dependency and keeps the logic in the appropriate service
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(header.getTrxId()).orElse(null);
        if (recruitment == null) {
            String errorMessage = "Could not find recruitment with ID: " + header.getTrxId();
            log.error(errorMessage);
            throw new BadRequestException(errorMessage);
        }

        log.info("Delegating portal system update to TrxRecruitmentService for recruitment ID: {}", header.getTrxId());

        // Use ApplicationContext to get TrxRecruitmentService to avoid circular dependency
        Object trxRecruitmentService = applicationContext.getBean("trxRecruitmentService");

        try {
            // Use reflection to call the method to avoid compile-time circular dependency
            trxRecruitmentService.getClass()
                    .getMethod("handlePortalSystemUpdate", Long.class)
                    .invoke(trxRecruitmentService, header.getTrxId());
            log.info("Successfully delegated portal system update for recruitment ID: {}", header.getTrxId());
        } catch (Exception reflectionException) {
            // Extract the actual exception from reflection wrapper
            Throwable cause = reflectionException.getCause();
            if (cause instanceof BadRequestException) {
                // Re-throw the original runtime exception from TrxRecruitmentService
                throw (BadRequestException) cause;
            } else if (cause != null) {
                // Wrap other exceptions
                String errorMessage = "Portal system update failed for recruitment ID: " + header.getTrxId() + ". " + cause.getMessage();
                log.error(errorMessage, cause);
                throw new BadRequestException(errorMessage, cause);
            } else {
                // Handle reflection-specific errors
                String errorMessage = "Error calling handlePortalSystemUpdate via reflection for recruitment ID: " + header.getTrxId() + ". " + reflectionException.getMessage();
                log.error(errorMessage, reflectionException);
                throw new BadRequestException(errorMessage, reflectionException);
            }
        }
    }

    /**
     * Checks if the transaction type is a recruitment transaction
     *
     * @param trxType The transaction type
     * @return true if it's a recruitment transaction
     */
    private boolean isRecruitmentTransaction(TrxType trxType) {
        return trxType == TrxType.RECRUITMENT_BP ||
                trxType == TrxType.RECRUITMENT_BM ||
                trxType == TrxType.RECRUITMENT_BD;
    }


}


