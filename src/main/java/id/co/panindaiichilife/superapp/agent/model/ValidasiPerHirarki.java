package id.co.panindaiichilife.superapp.agent.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "validasi_per_hirarki")
@Data
@ToString(of = {"id", "agentCode"})
public class ValidasiPerHirarki {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "validasi_per_hirarki_id_seq")
    @SequenceGenerator(name = "validasi_per_hirarki_id_seq", sequenceName = "validasi_per_hirarki_id_seq", allocationSize = 1)
    private Long id;

    private String agentCode;

    private String branchCode;

    private String mainBranchCode;

    private String bdmCode;

    private String bdmName;

    private String abddCode;

    private String abddName;

    private String bddCode;

    private String bddName;

    private String hosCode;

    private String hosName;

    private Integer year;

    private String type;

    private Double netApe;

    private Double apeTarget;

    private Long caseTarget;

    private Long netCase;

    private Double p13;

    private Double p13Target;

    private Long leaderCountTarget;

    private Long leaderCount;

    private Long agentCountTarget;

    private Long agentCount;

    private Long trainingTarget;

    private Long trainingCount;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
