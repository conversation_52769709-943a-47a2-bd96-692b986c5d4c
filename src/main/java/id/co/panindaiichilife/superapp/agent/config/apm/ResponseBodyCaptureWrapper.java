package id.co.panindaiichilife.superapp.agent.config.apm;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import lombok.Getter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

/**
 * HttpServletResponseWrapper that captures the response body for APM monitoring
 */
public class ResponseBodyCaptureWrapper extends HttpServletResponseWrapper {

    @Getter
    private final ByteArrayOutputStream capturedContent;
    private ServletOutputStream outputStream;
    private PrintWriter writer;
    private final int maxBodySize;

    public ResponseBodyCaptureWrapper(HttpServletResponse response, int maxBodySize) {
        super(response);
        this.capturedContent = new ByteArrayOutputStream();
        this.maxBodySize = maxBodySize;
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (writer != null) {
            throw new IllegalStateException("getWriter() has already been called on this response.");
        }

        if (outputStream == null) {
            outputStream = new ServletOutputStreamWrapper(getResponse().getOutputStream(), capturedContent, maxBodySize);
        }

        return outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if (outputStream != null) {
            throw new IllegalStateException("getOutputStream() has already been called on this response.");
        }

        if (writer == null) {
            ServletOutputStream sos = new ServletOutputStreamWrapper(getResponse().getOutputStream(), capturedContent, maxBodySize);
            writer = new PrintWriter(new OutputStreamWriter(sos, getCharacterEncoding()));
        }

        return writer;
    }

    @Override
    public void flushBuffer() throws IOException {
        if (writer != null) {
            writer.flush();
        } else if (outputStream != null) {
            outputStream.flush();
        }
        super.flushBuffer();
    }

    /**
     * Get the captured response body as string
     */
    public String getCapturedContentAsString() {
        try {
            flushBuffer();
            return capturedContent.toString(getCharacterEncoding());
        } catch (IOException e) {
            return capturedContent.toString();
        }
    }

    /**
     * Get the captured response body as byte array
     */
    public byte[] getCapturedContentAsBytes() {
        try {
            flushBuffer();
        } catch (IOException e) {
            // Log error but continue
        }
        return capturedContent.toByteArray();
    }

    /**
     * Check if content was truncated due to size limit
     */
    public boolean isContentTruncated() {
        return capturedContent.size() >= maxBodySize;
    }

    /**
     * Custom ServletOutputStream that writes to both the original response and captures content
     */
    private static class ServletOutputStreamWrapper extends ServletOutputStream {
        private final ServletOutputStream originalStream;
        private final ByteArrayOutputStream capturedContent;
        private final int maxBodySize;
        private int bytesWritten = 0;

        public ServletOutputStreamWrapper(ServletOutputStream originalStream, 
                                        ByteArrayOutputStream capturedContent, 
                                        int maxBodySize) {
            this.originalStream = originalStream;
            this.capturedContent = capturedContent;
            this.maxBodySize = maxBodySize;
        }

        @Override
        public void write(int b) throws IOException {
            originalStream.write(b);
            
            // Only capture if we haven't exceeded the limit
            if (bytesWritten < maxBodySize) {
                capturedContent.write(b);
                bytesWritten++;
            }
        }

        @Override
        public void write(byte[] b, int off, int len) throws IOException {
            originalStream.write(b, off, len);
            
            // Only capture if we haven't exceeded the limit
            if (bytesWritten < maxBodySize) {
                int remainingCapacity = maxBodySize - bytesWritten;
                int bytesToCapture = Math.min(len, remainingCapacity);
                capturedContent.write(b, off, bytesToCapture);
                bytesWritten += bytesToCapture;
            }
        }

        @Override
        public boolean isReady() {
            return originalStream.isReady();
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {
            originalStream.setWriteListener(writeListener);
        }

        @Override
        public void flush() throws IOException {
            originalStream.flush();
        }

        @Override
        public void close() throws IOException {
            originalStream.close();
        }
    }
}
