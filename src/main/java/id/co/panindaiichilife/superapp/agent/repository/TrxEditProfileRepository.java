package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import id.co.panindaiichilife.superapp.agent.model.TrxEditProfile;

import java.util.List;
import java.util.Optional;

public interface TrxEditProfileRepository extends BaseRepository<TrxEditProfile, Long> {

    List<TrxEditProfile> findByAgentAndApprovalStatusIn(Agent agent, List<ApprovalStatus> approvalStatusList);

    Optional<TrxEditProfile> findByApprovalHeader(TrxApprovalHeader approvalHeader);
}
