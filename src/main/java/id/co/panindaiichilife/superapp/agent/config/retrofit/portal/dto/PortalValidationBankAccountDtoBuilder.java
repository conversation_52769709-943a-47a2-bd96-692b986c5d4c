package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

/**
 * Builder class for PortalValidationBankAccountDto
 */
public class PortalValidationBankAccountDtoBuilder {
    private final PortalValidationBankAccountDto dto;
    
    public PortalValidationBankAccountDtoBuilder() {
        dto = new PortalValidationBankAccountDto();
    }
    
    public PortalValidationBankAccountDtoBuilder withTrxNum(String trxNum) {
        dto.setTrxNum(trxNum);
        return this;
    }
    
    public PortalValidationBankAccountDtoBuilder withBankCode(String bankCode) {
        dto.setBankCode(bankCode);
        return this;
    }
    
    public PortalValidationBankAccountDtoBuilder withBankAccount(String bankAccount) {
        dto.setBankAccount(bankAccount);
        return this;
    }
    
    public PortalValidationBankAccountDtoBuilder withCaller(String caller) {
        dto.setCaller(caller);
        return this;
    }
    
    public PortalValidationBankAccountDto build() {
        return dto;
    }
}
