package id.co.panindaiichilife.superapp.agent.core.security;

import id.co.panindaiichilife.superapp.agent.model.Role;
import id.co.panindaiichilife.superapp.agent.model.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.io.Serializable;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;

@Data
@Slf4j
public class UserDetailsExtraImpl implements UserDetailsExtra {

    private transient User user;

    private Collection<GrantedAuthority> authorities;

    public UserDetailsExtraImpl(User user) {
        this.user = user;

        authorities = new ArrayList<>();
        for (Role role : user.getRoles()) {
            if (!StringUtils.isBlank(role.getCode())) {
                authorities.add(new SimpleGrantedAuthority(role.getCode()));
            }
        }
    }

    @Override
    public Serializable getId() {
        return user.getId();
    }

    @Override
    public String getName() {
        return user.getName();
    }

    @Override
    public String getPicture() {
        return user.getPicture();
    }

    @Override
    public String getPassword() {
        return user.getPassword();
    }

    @Override
    public String getUsername() {
        return user.getUsername();
    }

    @Override
    public ZoneId getZoneId() {
        return user.getZoneId();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return user.getStatus() == null
                || User.Status.Active.equals(user.getStatus());
    }
}
