package id.co.panindaiichilife.superapp.agent.enums;

/**
 * Enum representing different types of PDF documents that can be generated.
 * Each document type corresponds to a template file in src/main/resources/templates/pdf/
 */
public enum DocumentType {

    /**
     * PKAJ-AGE document type
     * Template: src/main/resources/templates/pdf/PKAJ-AGE.html
     */
    PKAJ_AGE("PKAJ-AGE", "PKAJ-AGE.pdf"),

    /**
     * PMKAJ-AGE document type
     * Template: src/main/resources/templates/pdf/PMKAJ-AGE.html
     */
    PMKAJ_AGE("PMKAJ-AGE", "PMKAJ-AGE.pdf"),

    /**
     * KODE-ETIK-AGE document type
     * Template: src/main/resources/templates/pdf/KODE-ETIK-AGE.html
     */
    KODE_ETIK_AGE("KODE-ETIK-AGE", "KODE-ETIK-AGE.pdf"),

    /**
     * ANTI-TWISTING-AGE document type
     * Template: src/main/resources/templates/pdf/ANTI-TWISTING-AGE.html
     */
    ANTI_TWISTING_AGE("ANTI-TWISTING-AGE", "ANTI-TWISTING-AGE.pdf"),

    /**
     * APGEN-AGE document type
     * Template: src/main/resources/templates/pdf/AP-AGE.html
     */
    APGEN_AGE("APGEN-AGE", "APGEN-AGE.pdf"),

    /**
     * TERMINATION_LETTER document type
     * Template: src/main/resources/templates/pdf/TERMINATION-LETTER.html
     */
    TERMINATION_LETTER("TERMINATION-LETTER", "TERMINATION-LETTER.pdf"),

    /**
     * REJOIN_LETTER document type
     * Template: src/main/resources/templates/pdf/REJOIN-LETTER.html
     */
    REJOIN_LETTER("REJOIN-LETTER", "REJOIN-LETTER.pdf");

    private final String templateName;
    private final String defaultFileName;

    DocumentType(String templateName, String defaultFileName) {
        this.templateName = templateName;
        this.defaultFileName = defaultFileName;
    }

    /**
     * Find DocumentType by template name (case-insensitive).
     *
     * @param templateName The template name to search for
     * @return The matching DocumentType, or null if not found
     */
    public static DocumentType fromTemplateName(String templateName) {
        if (templateName == null) {
            return null;
        }

        for (DocumentType type : values()) {
            if (type.templateName.equalsIgnoreCase(templateName)) {
                return type;
            }
        }
        return null;
    }

    /**
     * Check if a template name is valid.
     *
     * @param templateName The template name to check
     * @return true if the template name is valid, false otherwise
     */
    public static boolean isValidTemplateName(String templateName) {
        return fromTemplateName(templateName) != null;
    }

    /**
     * Get all available template names.
     *
     * @return Array of all template names
     */
    public static String[] getAllTemplateNames() {
        DocumentType[] types = values();
        String[] names = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            names[i] = types[i].templateName;
        }
        return names;
    }

    /**
     * Get the template name for this document type.
     * This corresponds to the template file name without the .html extension.
     *
     * @return The template name
     */
    public String getTemplateName() {
        return templateName;
    }

    /**
     * Get the default file name for this document type.
     *
     * @return The default file name
     */
    public String getDefaultFileName() {
        return defaultFileName;
    }

    /**
     * Get the full template path for this document type.
     *
     * @return The full template path (e.g., "pdf/PKAJ-AGE")
     */
    public String getTemplatePath() {
        return "pdf/" + templateName;
    }
}
