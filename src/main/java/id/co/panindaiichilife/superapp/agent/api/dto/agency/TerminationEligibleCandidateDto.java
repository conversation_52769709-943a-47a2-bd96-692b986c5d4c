package id.co.panindaiichilife.superapp.agent.api.dto.agency;

import id.co.panindaiichilife.superapp.agent.enums.AgentStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class TerminationEligibleCandidateDto {
    private String agentName;
    private String agentCode;
    private String level;
    private String leaderCode;
    private String leaderName;
    private String branchCode;
    private String branchName;
    private String branchCity;
    private String branchAddress;
    private AgentStatus status;
    private String agentPicture;

    public static TerminationEligibleCandidateDto of(AgentWithBranchInfo agent) {
        return TerminationEligibleCandidateDto.builder()
                .agentCode(agent.getAgentCode())
                .agentName(agent.getAgentName())
                .level(agent.getLevel())
                .leaderCode(agent.getLeaderName())
                .leaderName(agent.getLeaderName())
                .branchCode(agent.getBranchCode())
                .branchName(agent.getBranchName())
                .branchCity(agent.getBranchCity())
                .status(agent.getStatus())
                .branchAddress(agent.getBranchAddress())
                .agentPicture(agent.getPicture())
                .build();
    }
}
