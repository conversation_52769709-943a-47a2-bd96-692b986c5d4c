package id.co.panindaiichilife.superapp.agent.api.batch.validation;

import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

@Component
public class PromosiLeaderImportDtoValidator implements Validator {

    @Override
    public boolean supports(Class<?> clazz) {
        return PromosiLeaderImportDtoValidator.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        // This method is intentionally empty as validation is handled by annotation-based validation
        // Custom validation logic can be added here in the future if needed
    }
}