package id.co.panindaiichilife.superapp.agent.service.kafka;

import id.co.panindaiichilife.superapp.agent.config.kafka.KafkaConfig;
import id.co.panindaiichilife.superapp.agent.model.event.AgentReactivationEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class AgentReactivationConsumerService {
    private final AgentReactivationEventHandler eventHandler;

    @KafkaListener(
            topics = KafkaConfig.REJOIN_TOPIC,
            groupId = KafkaConfig.REJOIN_GROUP,
            containerFactory = "rejoinKafkaListenerContainerFactory")
    public void consumeEvent(AgentReactivationEvent event) {
        log.info("Received agent reactivation event: {}", event);

        try {
            eventHandler.handleEvent(event);
        } catch (Exception e) {
            log.error("Error processing agent reactivation event: {}", e.getMessage(), e);
        }
    }
}
