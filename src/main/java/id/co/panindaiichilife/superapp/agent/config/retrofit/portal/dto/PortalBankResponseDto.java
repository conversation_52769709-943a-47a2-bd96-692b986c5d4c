package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalBankResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("keyValue")
    @JsonProperty("keyValue")
    private List<KeyValueDto> keyValue;

    @Data
    public static class KeyValueDto {

        @SerializedName("key")
        @JsonProperty("key")
        private String key;

        @SerializedName("value")
        @JsonProperty("value")
        private String value;

        @SerializedName("value2")
        @JsonProperty("value2")
        private String value2;

        @SerializedName("value3")
        @JsonProperty("value3")
        private String value3;
    }
}
