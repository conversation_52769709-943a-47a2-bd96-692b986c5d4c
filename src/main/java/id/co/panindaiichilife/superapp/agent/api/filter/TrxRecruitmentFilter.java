package id.co.panindaiichilife.superapp.agent.api.filter;

import id.co.panindaiichilife.superapp.agent.core.data.filter.FieldFilter;
import id.co.panindaiichilife.superapp.agent.core.data.filter.FilterParam;
import id.co.panindaiichilife.superapp.agent.core.view.FilterMode;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAajiStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAasiStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class TrxRecruitmentFilter extends FieldFilter<TrxRecruitment> {

    @FilterParam
    private ApprovalStatus approvalStatus;

    @FilterParam
    private TrxStatus trxStatus;

    @FilterParam
    private ValidationLicenseAajiStatus validationLicenseAajiStatus;

    @FilterParam
    private ValidationLicenseAasiStatus validationLicenseAasiStatus;

    @Parameter(hidden = true)
    @FilterParam(value = "recruiter.id", modes = FilterMode.FK)
    private Long recruiter;
}
