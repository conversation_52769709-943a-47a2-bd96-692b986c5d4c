package id.co.panindaiichilife.superapp.agent.api.validation;

import id.co.panindaiichilife.superapp.agent.api.form.cms.UserForm;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

@Component
@RequiredArgsConstructor
public class UserValidator implements Validator {

    private static final String PASSWORD_PATTERN = "^(?=.*[A-Za-z])(?=.*\\d)(?=.*[@$!%*#?&])[A-Za-z\\d@$!%*#?&]{8,}$";

    private final EncryptionService encryptionService;

    @Override
    public boolean supports(Class<?> clazz) {
        return UserForm.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        UserForm form = (UserForm) target;

        if (StringUtils.isNotBlank(form.getPassword())) {
            if (!errors.hasFieldErrors("password")) {
                String decryptedPassword = encryptionService.decrypt(form.getPassword());

                // Validate password criteria (min length, letters, numbers, special chars)
                if (!decryptedPassword.matches(PASSWORD_PATTERN)) {
                    errors.rejectValue("password", "password.invalid",
                            "Password baru harus minimal 8 character, mengandung huruf, angka dan special character");
                }
            }
        }
    }
}
