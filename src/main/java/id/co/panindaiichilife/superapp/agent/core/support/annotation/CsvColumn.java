package id.co.panindaiichilife.superapp.agent.core.support.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, FIELD})
@Retention(RUNTIME)
public @interface CsvColumn {

  boolean preserveValue() default false;

  PreserveStrategy preserveStrategy() default PreserveStrategy.TAB;

  enum PreserveStrategy {
    FORMULA, //prepend "=" in front of value
    TAB //append "\t" to value
  }
}
