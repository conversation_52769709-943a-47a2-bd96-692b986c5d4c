package id.co.panindaiichilife.superapp.agent.api.form.agency;

import id.co.panindaiichilife.superapp.agent.enums.ValidationAdministrationAgentStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationAmlStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationBankAccountStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationBlacklistStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationHirarkiStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationKtpStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAajiStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAasiStatus;
import lombok.Data;

@Data
public class TrxRejoinValidationForm {
    private ValidationBlacklistStatus validationBlacklistStatus;
    private ValidationKtpStatus validationKtpStatus;
    private ValidationBankAccountStatus validationBankAccountStatus;
    private ValidationHirarkiStatus validationHirarkiStatus;
    private ValidationAmlStatus validationAmlStatus;
    private ValidationAdministrationAgentStatus validationAdministrationAgentStatus;
    private ValidationLicenseAajiStatus validationLicenseAajiStatus;
    private ValidationLicenseAasiStatus validationLicenseAasiStatus;
}
