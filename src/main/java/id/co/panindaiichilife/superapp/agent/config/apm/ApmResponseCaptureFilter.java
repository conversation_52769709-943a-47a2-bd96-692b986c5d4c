package id.co.panindaiichilife.superapp.agent.config.apm;

import co.elastic.apm.api.ElasticApm;
import co.elastic.apm.api.Span;
import co.elastic.apm.api.Transaction;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Filter to capture HTTP response bodies and send them to Elastic APM
 */
@Slf4j
@RequiredArgsConstructor
public class ApmResponseCaptureFilter implements Filter {

    private final ApmResponseCaptureProperties properties;
    private final ObjectMapper objectMapper;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (!(request instanceof HttpServletRequest) || !(response instanceof HttpServletResponse)) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // Check if this request should be captured
        if (!properties.shouldCapture(httpRequest.getRequestURI())) {
            chain.doFilter(request, response);
            return;
        }

        // Wrap the response to capture the body
        ResponseBodyCaptureWrapper responseWrapper = new ResponseBodyCaptureWrapper(
                httpResponse, properties.getMaxBodySize());

        long startTime = System.currentTimeMillis();

        try {
            // Continue with the filter chain
            chain.doFilter(request, responseWrapper);
        } finally {
            // Capture and send response data to APM
            captureResponseToApm(httpRequest, responseWrapper, startTime);
        }
    }

    /**
     * Capture response data and send to Elastic APM
     */
    private void captureResponseToApm(HttpServletRequest request,
                                    ResponseBodyCaptureWrapper responseWrapper,
                                    long startTime) {
        try {
            Transaction transaction = ElasticApm.currentTransaction();
            if (transaction == null) {
                log.debug("No active APM transaction found for request: {}", request.getRequestURI());
                return;
            }

            // Create a span for response capture with dynamic name
            String spanName = String.format("response-capture %s %s",
                                           request.getMethod(),
                                           request.getRequestURI());
            Span span = transaction.startSpan("http", "response-capture", "response-body");
            span.setName(spanName);

            try {
                long duration = System.currentTimeMillis() - startTime;

                // Add basic request/response metadata
                span.setLabel("http.request.method", request.getMethod());
                span.setLabel("http.request.url", request.getRequestURL().toString());
                span.setLabel("http.response.status_code", responseWrapper.getStatus());
                span.setLabel("http.response.duration_ms", duration);
                span.setLabel("http.response.content_type", responseWrapper.getContentType());

                // Capture response headers if enabled
                if (properties.isCaptureHeaders()) {
                    Map<String, String> headers = captureResponseHeaders(responseWrapper);
                    if (!headers.isEmpty()) {
                        span.setLabel("http.response.headers", objectMapper.writeValueAsString(headers));
                    }
                }

                // Capture response body
                String responseBody = captureResponseBody(responseWrapper);
                if (StringUtils.hasText(responseBody)) {
                    span.setLabel("http.response.body", responseBody);
                    span.setLabel("http.response.body_size", responseWrapper.getCapturedContentAsBytes().length);

                    if (responseWrapper.isContentTruncated()) {
                        span.setLabel("http.response.body_truncated", true);
                        span.setLabel("http.response.body_max_size", properties.getMaxBodySize());
                    }
                }

                // Add request context
                addRequestContext(span, request);

                log.debug("Captured response for {} {} - Status: {}, Size: {} bytes",
                         request.getMethod(), request.getRequestURI(),
                         responseWrapper.getStatus(), responseWrapper.getCapturedContentAsBytes().length);

            } finally {
                span.end();
            }

        } catch (Exception e) {
            log.error("Error capturing response to APM for request: {}", request.getRequestURI(), e);
        }
    }

    /**
     * Capture response headers (excluding sensitive ones)
     */
    private Map<String, String> captureResponseHeaders(ResponseBodyCaptureWrapper responseWrapper) {
        Map<String, String> headers = new HashMap<>();

        for (String headerName : responseWrapper.getHeaderNames()) {
            if (properties.shouldCaptureHeader(headerName)) {
                String headerValue = responseWrapper.getHeader(headerName);
                if (headerValue != null) {
                    headers.put(headerName.toLowerCase(), headerValue);
                }
            }
        }

        return headers;
    }

    /**
     * Capture response body with appropriate handling for different content types
     */
    private String captureResponseBody(ResponseBodyCaptureWrapper responseWrapper) {
        try {
            String contentType = responseWrapper.getContentType();

            // Only capture text-based content types
            if (contentType != null && isTextBasedContentType(contentType)) {
                return responseWrapper.getCapturedContentAsString();
            } else {
                // For binary content, just log the size
                byte[] content = responseWrapper.getCapturedContentAsBytes();
                return String.format("[Binary content: %d bytes, type: %s]",
                                   content.length, contentType);
            }
        } catch (Exception e) {
            log.warn("Error capturing response body", e);
            return "[Error capturing response body: " + e.getMessage() + "]";
        }
    }

    /**
     * Check if content type is text-based and should be captured as string
     */
    private boolean isTextBasedContentType(String contentType) {
        if (contentType == null) {
            return false;
        }

        String lowerContentType = contentType.toLowerCase();
        return lowerContentType.startsWith(MediaType.APPLICATION_JSON_VALUE) ||
               lowerContentType.startsWith(MediaType.APPLICATION_XML_VALUE) ||
               lowerContentType.startsWith(MediaType.TEXT_PLAIN_VALUE) ||
               lowerContentType.startsWith(MediaType.TEXT_HTML_VALUE) ||
               lowerContentType.startsWith(MediaType.TEXT_XML_VALUE) ||
               lowerContentType.startsWith("application/xml") ||
               lowerContentType.startsWith("text/");
    }

    /**
     * Add additional request context to the span
     */
    private void addRequestContext(Span span, HttpServletRequest request) {
        // Add user agent
        String userAgent = request.getHeader("User-Agent");
        if (StringUtils.hasText(userAgent)) {
            span.setLabel("http.request.user_agent", userAgent);
        }

        // Add remote address
        String remoteAddr = getClientIpAddress(request);
        if (StringUtils.hasText(remoteAddr)) {
            span.setLabel("http.request.remote_addr", remoteAddr);
        }

        // Add query string
        String queryString = request.getQueryString();
        if (StringUtils.hasText(queryString)) {
            span.setLabel("http.request.query_string", queryString);
        }
    }

    /**
     * Get client IP address considering proxy headers
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
