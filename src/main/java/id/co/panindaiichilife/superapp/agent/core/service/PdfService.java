package id.co.panindaiichilife.superapp.agent.core.service;

import com.itextpdf.text.DocumentException;
import id.co.panindaiichilife.superapp.agent.enums.DocumentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.xhtmlrenderer.pdf.ITextOutputDevice;
import org.xhtmlrenderer.pdf.ITextRenderer;
import org.xhtmlrenderer.pdf.ITextUserAgent;

import java.io.*;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class PdfService {

    private final SpringTemplateEngine templateEngine;

    @Value("${media.dir}")
    private String mediaDir;

    private String resolvePdfTemplate(String template, Map<String, Object> data) {
        Context context = new Context();
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }

        String processedTemplate = templateEngine.process(template, context);

        // Clean up HTML to make it XHTML compliant for Flying Saucer
        return processedTemplate;
    }

    /**
     * Create a PDF object using a given tempate populated with data objects.
     *
     * @param template thymeleaf template file
     * @param data     map of data objects HttpServletResponse
     */
    public ITextRenderer createPdf(String template, Map<String, Object> data) throws IOException {
        String content = resolvePdfTemplate(template, data);

        ITextRenderer renderer = new ITextRenderer();

        CustomUserAgentCallback uac = new CustomUserAgentCallback(renderer.getOutputDevice(), mediaDir);
        uac.setSharedContext(renderer.getSharedContext());

        renderer.getSharedContext().setUserAgentCallback(uac);

        // Note: Page size is controlled via CSS @page rule in the HTML template
        // See the template's CSS for page size configuration

        renderer.setDocumentFromString(content, "");
        return renderer;
    }

    /**
     * Create a PDF object using a given tempate populated with data objects, and automatically output
     * the resulting document to a specified output stream.
     *
     * @param template thymeleaf template file
     * @param data     map of data objects
     * @param out      output stream, could be FileOutputStream or output stream obtained from
     *                 HttpServletResponse
     */
    public ITextRenderer createPdf(String template, Map<String, Object> data, OutputStream out)
            throws IOException, DocumentException, com.lowagie.text.DocumentException {
        ITextRenderer renderer = createPdf(template, data);
        renderer.layout();
        renderer.createPDF(out);
        return renderer;
    }

    /**
     * Generic method to generate PDF for any document type with variable injection.
     *
     * @param documentType The document type (e.g., "PKAJ-AGE", "CONTRACT", "INVOICE")
     * @param variables    Map containing the variables to inject into the template
     * @param out          Output stream for the generated PDF
     * @return ITextRenderer object
     * @throws IOException                        if template processing fails
     * @throws DocumentException                  if PDF generation fails
     * @throws com.lowagie.text.DocumentException if PDF creation fails
     */
    public ITextRenderer generateDocumentPdf(String documentType, Map<String, Object> variables, OutputStream out)
            throws IOException, DocumentException, com.lowagie.text.DocumentException {

        // Ensure variables map is not null
        Map<String, Object> data = variables != null ? new HashMap<>(variables) : new HashMap<>();
        // Build template path
        String templatePath = "pdf/" + documentType;

        return createPdf(templatePath, data, out);
    }

    /**
     * Generic method to generate PDF for any document type with variable injection (without output stream).
     *
     * @param documentType The document type (e.g., "PKAJ-AGE", "CONTRACT", "INVOICE")
     * @param variables    Map containing the variables to inject into the template
     * @return ITextRenderer object
     * @throws IOException if template processing fails
     */
    public ITextRenderer generateDocumentPdf(String documentType, Map<String, Object> variables)
            throws IOException {

        // Ensure variables map is not null
        Map<String, Object> data = variables != null ? new java.util.HashMap<>(variables) : new java.util.HashMap<>();

        // Build template path
        String templatePath = "pdf/" + documentType;

        return createPdf(templatePath, data);
    }

    /**
     * Generate PDF as byte array for any document type.
     *
     * @param documentType The document type (e.g., "PKAJ-AGE", "CONTRACT", "INVOICE")
     * @param variables    Map containing the variables to inject into the template
     * @return byte array of the generated PDF
     * @throws Exception if PDF generation fails
     */
    public byte[] generateDocumentPdfAsBytes(String documentType, Map<String, Object> variables) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            generateDocumentPdf(documentType, variables, outputStream);
            return outputStream.toByteArray();
        }
    }

    // Enhanced methods using DocumentType enum for type safety

    /**
     * Generate PDF for a specific document type using enum with variable injection.
     *
     * @param documentType The document type enum
     * @param variables    Map containing the variables to inject into the template
     * @param out          Output stream for the generated PDF
     * @return ITextRenderer object
     * @throws IOException                        if template processing fails
     * @throws DocumentException                  if PDF generation fails
     * @throws com.lowagie.text.DocumentException if PDF creation fails
     */
    public ITextRenderer generateDocumentPdf(DocumentType documentType, Map<String, Object> variables, OutputStream out)
            throws IOException, DocumentException, com.lowagie.text.DocumentException {

        return generateDocumentPdf(documentType.getTemplateName(), variables, out);
    }

    /**
     * Generate PDF for a specific document type using enum (without output stream).
     *
     * @param documentType The document type enum
     * @param variables    Map containing the variables to inject into the template
     * @return ITextRenderer object
     * @throws IOException if template processing fails
     */
    public ITextRenderer generateDocumentPdf(DocumentType documentType, Map<String, Object> variables)
            throws IOException {

        return generateDocumentPdf(documentType.getTemplateName(), variables);
    }

    /**
     * Generate PDF as byte array for a specific document type using enum.
     *
     * @param documentType The document type enum
     * @param variables    Map containing the variables to inject into the template
     * @return byte array of the generated PDF
     * @throws Exception if PDF generation fails
     */
    public byte[] generateDocumentPdfAsBytes(DocumentType documentType, Map<String, Object> variables) throws Exception {
        return generateDocumentPdfAsBytes(documentType.getTemplateName(), variables);
    }

    /**
     * Validate if a document type template exists.
     *
     * @param documentType The document type to validate
     * @return true if the template exists, false otherwise
     */
    public boolean isTemplateAvailable(String documentType) {
        try {
            // Check if the template file exists in the classpath
            String templatePath = "templates/pdf/" + documentType + ".html";
            ClassPathResource resource = new ClassPathResource(templatePath);
            boolean exists = resource.exists();

            if (!exists) {
                log.warn("Template not found for document type: {} at path: {}", documentType, templatePath);
            }

            return exists;
        } catch (Exception e) {
            log.warn("Error checking template availability for document type: {}", documentType, e);
            return false;
        }
    }

    /**
     * Validate if a document type template exists using enum.
     *
     * @param documentType The document type enum to validate
     * @return true if the template exists, false otherwise
     */
    public boolean isTemplateAvailable(DocumentType documentType) {
        return isTemplateAvailable(documentType.getTemplateName());
    }

    /**
     * Create PDF from raw HTML content without using Thymeleaf template engine.
     * This method is useful for testing uploaded HTML templates.
     *
     * @param htmlContent Raw HTML content as string
     * @return ITextRenderer object
     * @throws IOException if PDF generation fails
     */
    public ITextRenderer createPdfFromRawHtml(String htmlContent) throws IOException {
        ITextRenderer renderer = new ITextRenderer();

        CustomUserAgentCallback uac = new CustomUserAgentCallback(renderer.getOutputDevice(), mediaDir);
        uac.setSharedContext(renderer.getSharedContext());

        renderer.getSharedContext().setUserAgentCallback(uac);

        // Note: Page size is controlled via CSS @page rule in the HTML template
        // See the template's CSS for page size configuration

        renderer.setDocumentFromString(htmlContent, "");
        return renderer;
    }

    /**
     * Create PDF from raw HTML content and output to stream.
     *
     * @param htmlContent Raw HTML content as string
     * @param out         Output stream for the generated PDF
     * @return ITextRenderer object
     * @throws IOException                        if template processing fails
     * @throws DocumentException                  if PDF generation fails
     * @throws com.lowagie.text.DocumentException if PDF creation fails
     */
    public ITextRenderer createPdfFromRawHtml(String htmlContent, OutputStream out)
            throws IOException, DocumentException, com.lowagie.text.DocumentException {
        ITextRenderer renderer = createPdfFromRawHtml(htmlContent);
        renderer.layout();
        renderer.createPDF(out);
        return renderer;
    }

    /**
     * Generate PDF as byte array from raw HTML content.
     *
     * @param htmlContent Raw HTML content as string
     * @return byte array of the generated PDF
     * @throws Exception if PDF generation fails
     */
    public byte[] generatePdfFromRawHtmlAsBytes(String htmlContent) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            createPdfFromRawHtml(htmlContent, outputStream);
            return outputStream.toByteArray();
        }
    }

    /**
     * Create PDF from raw HTML content with variable substitution using simple string replacement.
     * This method performs basic variable substitution by replacing {{variableName}} placeholders.
     *
     * @param htmlContent Raw HTML content with {{variableName}} placeholders
     * @param variables   Map containing the variables to substitute
     * @return ITextRenderer object
     * @throws IOException if PDF generation fails
     */
    public ITextRenderer createPdfFromRawHtmlWithVariables(String htmlContent, Map<String, Object> variables) throws IOException {
        String processedHtml = htmlContent;

        if (variables != null) {
            for (Map.Entry<String, Object> entry : variables.entrySet()) {
                String placeholder = "{{" + entry.getKey() + "}}";
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                processedHtml = processedHtml.replace(placeholder, value);
            }
        }

        return createPdfFromRawHtml(processedHtml);
    }

    /**
     * Create PDF from raw HTML content with variable substitution and output to stream.
     *
     * @param htmlContent Raw HTML content with {{variableName}} placeholders
     * @param variables   Map containing the variables to substitute
     * @param out         Output stream for the generated PDF
     * @return ITextRenderer object
     * @throws IOException                        if template processing fails
     * @throws DocumentException                  if PDF generation fails
     * @throws com.lowagie.text.DocumentException if PDF creation fails
     */
    public ITextRenderer createPdfFromRawHtmlWithVariables(String htmlContent, Map<String, Object> variables, OutputStream out)
            throws IOException, DocumentException, com.lowagie.text.DocumentException {
        ITextRenderer renderer = createPdfFromRawHtmlWithVariables(htmlContent, variables);
        renderer.layout();
        renderer.createPDF(out);
        return renderer;
    }

    /**
     * Generate PDF as byte array from raw HTML content with variable substitution.
     *
     * @param htmlContent Raw HTML content with {{variableName}} placeholders
     * @param variables   Map containing the variables to substitute
     * @return byte array of the generated PDF
     * @throws Exception if PDF generation fails
     */
    public byte[] generatePdfFromRawHtmlWithVariablesAsBytes(String htmlContent, Map<String, Object> variables) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            createPdfFromRawHtmlWithVariables(htmlContent, variables, outputStream);
            return outputStream.toByteArray();
        }
    }

    private static class CustomUserAgentCallback extends ITextUserAgent {

        private static final String MEDIA_PREFIX = "media/";
        private String mediaDir;

        public CustomUserAgentCallback(ITextOutputDevice outputDevice, String mediaDir) {
            super(outputDevice);
            this.mediaDir = mediaDir;
        }

        @Override
        protected InputStream resolveAndOpenStream(String uri) {
            if (StringUtils.startsWith(uri, MEDIA_PREFIX)) {
                uri = uri.substring(MEDIA_PREFIX.length());
                try {
                    return new FileInputStream(mediaDir + "/" + uri);
                } catch (FileNotFoundException ex) {
                    log.warn("Unable to resolve resource file: {}", uri);
                    return null;
                }
            } else if (StringUtils.startsWith(uri, "http")) {
                try {
                    URL url = new URL(uri);
                    return url.openStream();
                } catch (IOException ex) {
                    log.warn("Unable to resolve resource url: {}", uri);
                    return null;
                }
            } else {
                return getClass().getClassLoader().getResourceAsStream("static/" + uri);
            }
        }
    }
}
