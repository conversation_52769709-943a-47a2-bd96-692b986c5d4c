package id.co.panindaiichilife.superapp.agent.api.dto.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalHeaderDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.BankDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.BranchDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.RequestByDto;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.*;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
public class TrxRecruitmentDto extends BaseDto<TrxRecruitment> {

    private Long id;

    private String uuid;

    private RequestByDto recruiter;

    private ApprovalHeaderDto approvalHeader;

    private ApprovalStatus approvalStatus;

    private TrxStatus trxStatus;

    private String recruiterCode;

    private String recruiterName;

    private String recruiterBranchCode;

    private String leaderCode;

    private String agentCode;

    private String ktpPhoto;

    private String selfiePhoto;

    private String passPhoto;

    private PositionLevel positionLevel;

    private BranchDto branch;

    private String nik;

    private String fullName;

    private String birthPlace;

    private LocalDate birthDate;

    private Gender gender;

    private String ktpProvince;

    private String ktpCity;

    private String ktpDistrict;

    private String ktpSubDistrict;

    private String ktpRt;

    private String ktpRw;

    private String ktpAddress;

    private Boolean isDomicileSameAsKtp;

    private String domicileProvince;

    private String domicileCity;

    private String domicileDistrict;

    private String domicileSubDistrict;

    private String domicileRt;

    private String domicileRw;

    private String domicileAddress;

    private String phoneNumber;

    private MaritalStatus maritalStatus;

    private String occupation;

    private String occupationCode;

    private String email;

    private String emergencyContactName;

    private String emergencyContactRelation;

    private String emergencyContactPhone;

    private String bankAccountName;

    private String bankAccountNumber;

    private BankDto bank;

    private String lastJob;

    private String last5YearJob;

    private String last2YearProduction;

    private String lastCompanyManPower;

    private String rewardInfo;

    private String pkajFile;

    private String pmkajFile;

    private String apgenFile;

    private String kodeEtikFile;

    private String antiTwistingFile;

    private String signature;

    private String paraf;

    private Boolean isEmailVerified = Boolean.FALSE;

    private LocalDateTime emailVerifiedDate;

    private ValidationBlacklistStatus validationBlacklistStatus;

    private ValidationKtpStatus validationKtpStatus;

    private String resulValidationKtp;

    private ValidationBankAccountStatus validationBankAccountStatus;

    private String resultValidationBankAccount;

    private ValidationHirarkiStatus validationHirarkiStatus;

    private String resultValidationHirarki;

    private ValidationAmlStatus validationAmlStatus;

    private ValidationAdministrationAgentStatus validationAdministrationAgentStatus;

    private ValidationLicenseAajiStatus validationLicenseAajiStatus;

    private ValidationLicenseAasiStatus validationLicenseAasiStatus;

    private String resultInterview;

    private String pmkajNumber;

    private String pkajNumber;

    private String notes;

    private LocalDate effectiveDate;

    private Instant createdAt;

    private Instant updatedAt;

    @Override
    public void copy(TrxRecruitment data) {
        super.copy(data);
        recruiter = BaseDto.of(RequestByDto.class, data.getRecruiter());
        approvalHeader = BaseDto.of(ApprovalHeaderDto.class, data.getApprovalHeader());
        bank = BaseDto.of(BankDto.class, data.getBank());
        branch = BaseDto.of(BranchDto.class, data.getBranch());
    }
}
