package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.SignatureDto;
import id.co.panindaiichilife.superapp.agent.api.filter.SignatureFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.SignatureForm;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.service.SignatureService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;

@RestController("cmsSignatureController")
@RequestMapping("/api/cms/signature")
@Tag(name = "Signature - CMS", description = "API CMS Signature")
@Slf4j
@RequiredArgsConstructor
public class SignatureCmsController {

    private final SignatureService signatureService;

    @Operation(summary = "List signatures")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Signature', 'view')")
    public Page<SignatureDto> index(@ParameterObject @ModelAttribute("filter") SignatureFilter filter,
                                    @ParameterObject @PageableDefault(sort = "createdAt") Pageable pageable) {
        return signatureService.findAll(pageable, filter);
    }

    @Operation(summary = "Get signature detail")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Signature', 'view')")
    public SignatureDto show(@PathVariable Long id) {
        return signatureService.findOne(id);
    }

    @Operation(summary = "Create new signature")
    @PostMapping("")
    @ResponseStatus(HttpStatus.CREATED)
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Signature', 'create')")
    public SignatureDto add(@Valid @RequestBody SignatureForm signatureForm) {
        return signatureService.add(signatureForm);
    }

    @Operation(summary = "Update signature")
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Signature', 'update')")
    public SignatureDto update(@PathVariable Long id, @Valid @RequestBody SignatureForm signatureForm) {
        return signatureService.update(id, signatureForm);
    }

    @Operation(summary = "Delete signature")
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Signature', 'delete')")
    public void delete(@PathVariable Long id) {
        signatureService.delete(id);
    }

    @Operation(summary = "Upload signature")
    @PostMapping(value = "upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    public FileinputResponse uploadPicture(Principal principal, @RequestParam("file") MultipartFile file) {
        return signatureService.upload(principal.getName(), file);
    }
}
