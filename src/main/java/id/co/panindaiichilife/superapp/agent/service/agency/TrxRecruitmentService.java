package id.co.panindaiichilife.superapp.agent.service.agency;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.ProductivityRecruitmentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRecruitmentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.AgentDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ProductivityRecruitmentFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.TrxRecruitmentFilter;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentInterviewForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentSignForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalRegistrationDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalRegistrationResponseDto;
import id.co.panindaiichilife.superapp.agent.core.data.association.ManyToOneUtils;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.enums.*;
import id.co.panindaiichilife.superapp.agent.model.*;
import id.co.panindaiichilife.superapp.agent.repository.*;
import id.co.panindaiichilife.superapp.agent.service.ApprovalService;
import id.co.panindaiichilife.superapp.agent.service.FirebaseService;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Call;
import retrofit2.Response;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TrxRecruitmentService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final AgentRepository agentRepository;
    private final UserRepository userRepository;
    private final ApprovalService approvalService;
    private final TrxRecruitmentRepository trxRecruitmentRepository;
    private final AmazonS3Service amazonS3Service;
    private final BankRepository bankRepository;
    private final BranchRepository branchRepository;
    private final RecruitmentValidationService validationService;
    private final FirebaseService firebaseService;
    private final ApprovalLevelRepository approvalLevelRepository;
    private final PortalProvider portalProvider;
    private final GlobalConfigService globalConfigService;
    private final RoleRepository roleRepository;

    public Page<TrxRecruitmentDto> findAll(Pageable pageable, TrxRecruitmentFilter filter) {
        Page<TrxRecruitment> data = trxRecruitmentRepository.findAll(filter, pageable);
        return BaseDto.of(TrxRecruitmentDto.class, data, pageable);
    }

    public TrxRecruitmentDto findOne(Long id) {
        TrxRecruitment data = trxRecruitmentRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(TrxRecruitmentDto.class, data);
    }

    public TrxRecruitmentDto findByUuid(String uuid) {
        TrxRecruitment data = trxRecruitmentRepository.findByUuid(uuid)
                .orElseThrow(() -> new NotFoundException("Recruitment not found with UUID: " + uuid));
        return BaseDto.of(TrxRecruitmentDto.class, data);
    }

    @Transactional
    public void sign(String uuid, TrxRecruitmentSignForm trxRecruitmentSignForm) {
        TrxRecruitment trxRecruitment = trxRecruitmentRepository.findByUuid(uuid)
                .orElseThrow(() -> new NotFoundException("Recruitment not found with UUID: " + uuid));
        trxRecruitment.setSignature(trxRecruitmentSignForm.getSignature());
        trxRecruitment.setParaf(trxRecruitmentSignForm.getParaf());
        trxRecruitmentRepository.save(trxRecruitment);
    }

    @Transactional
    public TrxRecruitment saveDraft(String username, TrxRecruitmentForm trxRecruitmentForm) {
        User user = userRepository.findByUsername(username).orElse(null);
        Boolean isPublic = Boolean.FALSE;
        if (user == null) {
            isPublic = Boolean.TRUE;
        }

        if (user == null || StringUtils.isNotBlank(trxRecruitmentForm.getRecruiterCode())) {
            user = userRepository.findByUsername(trxRecruitmentForm.getRecruiterCode()).orElseThrow(NotFoundException::new);
        }

        // Check for existing recruitment requests with the same recruiter code and NIK
        // that are not in EXPIRED or REJECTED status
        if (StringUtils.isNotBlank(trxRecruitmentForm.getRecruiterCode()) && StringUtils.isNotBlank(trxRecruitmentForm.getNik())) {
            List<TrxStatus> excludedStatuses = Arrays.asList(TrxStatus.EXPIRED, TrxStatus.REJECTED, TrxStatus.DRAFT);
            boolean existingRecruitment = trxRecruitmentRepository.existsByRecruiterCodeAndNikAndTrxStatusNotIn(
                    trxRecruitmentForm.getRecruiterCode(), trxRecruitmentForm.getNik(), excludedStatuses);

            if (existingRecruitment) {
                throw new BadRequestException("There is already an active recruitment request for this candidate by this recruiter. "
                        + "Please wait for the existing request to be  draft, expired or rejected.");
            }
        }

        TrxRecruitment trxRecruitment = null;
        if (StringUtils.isNotBlank(trxRecruitmentForm.getRecruiterCode()) && StringUtils.isNotBlank(trxRecruitmentForm.getNik())) {
            trxRecruitment = trxRecruitmentRepository.findByRecruiterCodeAndNik(trxRecruitmentForm.getRecruiterCode(), trxRecruitmentForm.getNik()).orElse(null);
        }
        boolean isNewRecruitment = (null == trxRecruitment);
        if (isNewRecruitment) {
            trxRecruitment = new TrxRecruitment();
            BeanUtils.copyProperties(trxRecruitmentForm, trxRecruitment);
            trxRecruitment.setRecruiter(user);
            trxRecruitment.setChannel(user.getChannel());
            // Handle nullable fields when converting to JSON
            trxRecruitment.setLast5YearJob(trxRecruitmentForm.getLast5YearJobData() != null ?
                    new Gson().toJson(trxRecruitmentForm.getLast5YearJobData()) : null);

            trxRecruitment.setLast2YearProduction(trxRecruitmentForm.getLast2YearProductionData() != null ?
                    new Gson().toJson(trxRecruitmentForm.getLast2YearProductionData()) : null);

            trxRecruitment.setLastCompanyManPower(trxRecruitmentForm.getLastCompanyManPowerData() != null ?
                    new Gson().toJson(trxRecruitmentForm.getLastCompanyManPowerData()) : null);

            trxRecruitment.setRewardInfo(trxRecruitmentForm.getRewardInfoData() != null ?
                    new Gson().toJson(trxRecruitmentForm.getRewardInfoData()) : null);
            trxRecruitment.setTrxStatus(TrxStatus.DRAFT);
            trxRecruitment.setIsPublic(isPublic);

            // Set leader code based on hierarchy recruitment rules
            setLeaderCodeBasedOnHierarchy(user, trxRecruitment);

            ManyToOneUtils.save(trxRecruitmentForm, trxRecruitment, "bank", bankRepository);
            ManyToOneUtils.save(trxRecruitmentForm, trxRecruitment, "branch", branchRepository);
            return trxRecruitmentRepository.save(trxRecruitment);
        } else {
            return trxRecruitment;
        }

    }

    @Transactional
    public void submit(String username, TrxRecruitmentForm trxRecruitmentForm) {
        User user = userRepository.findByUsername(username).orElse(null);

        Boolean isPublic = Boolean.FALSE;
        if (user == null) {
            isPublic = Boolean.TRUE;
        }

        if (user == null || StringUtils.isNotBlank(trxRecruitmentForm.getRecruiterCode())) {
            user = userRepository.findByUsername(trxRecruitmentForm.getRecruiterCode()).orElseThrow(NotFoundException::new);
        }

        // Try to find an existing recruitment by recruiterCode and nik
        TrxRecruitment trxRecruitment = null;
        if (StringUtils.isNotBlank(trxRecruitmentForm.getRecruiterCode()) && StringUtils.isNotBlank(trxRecruitmentForm.getNik())) {
            trxRecruitment = trxRecruitmentRepository.findByRecruiterCodeAndNik(trxRecruitmentForm.getRecruiterCode(), trxRecruitmentForm.getNik()).orElse(null);
        }
        boolean isNewRecruitment = (null == trxRecruitment);

        if (isNewRecruitment) {
            trxRecruitment = new TrxRecruitment();

            // For new recruitments, check for existing active requests
            if (StringUtils.isNotBlank(trxRecruitmentForm.getRecruiterCode()) && StringUtils.isNotBlank(trxRecruitmentForm.getNik())) {
                List<TrxStatus> excludedStatuses = Arrays.asList(TrxStatus.EXPIRED, TrxStatus.REJECTED, TrxStatus.DRAFT);
                boolean existingRecruitment = trxRecruitmentRepository.existsByRecruiterCodeAndNikAndTrxStatusNotIn(
                        trxRecruitmentForm.getRecruiterCode(), trxRecruitmentForm.getNik(), excludedStatuses);

                if (existingRecruitment) {
                    throw new BadRequestException("There is already an active recruitment request for this candidate by this recruiter. "
                            + "Please wait for the existing request to be  draft, expired or rejected.");
                }
            }
        } else {
            // For existing recruitments, check if NIK has changed and if there's a conflict
            if (StringUtils.isNotBlank(trxRecruitmentForm.getNik()) &&
                    !trxRecruitmentForm.getNik().equals(trxRecruitment.getNik()) &&
                    StringUtils.isNotBlank(trxRecruitmentForm.getRecruiterCode())) {

                List<TrxStatus> excludedStatuses = Arrays.asList(TrxStatus.EXPIRED, TrxStatus.REJECTED);
                boolean existingRecruitment = trxRecruitmentRepository.existsByRecruiterCodeAndNikAndTrxStatusNotIn(
                        trxRecruitmentForm.getRecruiterCode(), trxRecruitmentForm.getNik(), excludedStatuses);
                log.debug("existing recruitment : {}", existingRecruitment);
                if (existingRecruitment) {
                    throw new BadRequestException("There is already an active recruitment request for this candidate by this recruiter. "
                            + "Please wait for the existing request to be completed or rejected.");
                }
            }
        }

        BeanUtils.copyProperties(trxRecruitmentForm, trxRecruitment);
        // Keep status as DRAFT initially - will be updated after successful approval creation
        trxRecruitment.setTrxStatus(isNewRecruitment ? TrxStatus.DRAFT : trxRecruitment.getTrxStatus());
        trxRecruitment.setRecruiter(user);
        trxRecruitment.setChannel(user.getChannel());
        trxRecruitment.setIsPublic(isPublic);

        // Handle nullable fields when converting to JSON
        trxRecruitment.setLast5YearJob(trxRecruitmentForm.getLast5YearJobData() != null ?
                new Gson().toJson(trxRecruitmentForm.getLast5YearJobData()) : null);

        trxRecruitment.setLast2YearProduction(trxRecruitmentForm.getLast2YearProductionData() != null ?
                new Gson().toJson(trxRecruitmentForm.getLast2YearProductionData()) : null);

        trxRecruitment.setLastCompanyManPower(trxRecruitmentForm.getLastCompanyManPowerData() != null ?
                new Gson().toJson(trxRecruitmentForm.getLastCompanyManPowerData()) : null);

        trxRecruitment.setRewardInfo(trxRecruitmentForm.getRewardInfoData() != null ?
                new Gson().toJson(trxRecruitmentForm.getRewardInfoData()) : null);

        // Set leader code based on hierarchy recruitment rules
        setLeaderCodeBasedOnHierarchy(user, trxRecruitment);
        ManyToOneUtils.save(trxRecruitmentForm, trxRecruitment, "bank", bankRepository);
        ManyToOneUtils.save(trxRecruitmentForm, trxRecruitment, "branch", branchRepository);

        trxRecruitment = trxRecruitmentRepository.save(trxRecruitment);

        TrxType trxType = TrxType.valueOf("RECRUITMENT_" + trxRecruitmentForm.getPositionLevel().name());

        // Validate approval configuration before attempting to create approval
        validateApprovalConfiguration(trxType, user);

        try {
            // Request Approval - this might fail
            TrxApprovalHeader approvalHeader = approvalService.requestApproval(trxType, trxRecruitment.getId(), user, null, user.getChannel(), isPublic);

            // Only update status to IN_PROGRESS after successful approval creation
            trxRecruitment.setTrxStatus(TrxStatus.IN_PROGRESS);
            trxRecruitment.setApprovalHeader(approvalHeader);
            trxRecruitment.setApprovalStatus(approvalHeader.getApprovalStatus());
            trxRecruitment = trxRecruitmentRepository.save(trxRecruitment);

            log.info("Recruitment {} submitted successfully with approval header {}", trxRecruitment.getId(), approvalHeader.getId());
        } catch (Exception e) {
            // If approval creation fails, log the error and keep the recruitment in DRAFT status
            log.error("Failed to create approval for recruitment {}: {}", trxRecruitment.getId(), e.getMessage(), e);

            // Ensure the recruitment remains in DRAFT status
            trxRecruitment.setTrxStatus(TrxStatus.DRAFT);
            trxRecruitment.setApprovalHeader(null);
            trxRecruitment.setApprovalStatus(null);
            trxRecruitmentRepository.save(trxRecruitment);

            // Re-throw the exception to inform the caller about the failure
            throw new IllegalStateException("Failed to submit recruitment: " + e.getMessage(), e);
        }

        // Run all validations asynchronously using the validation service
        // Wrap in try-catch to prevent validation failures from rolling back the transaction
        try {
            log.info("Starting validation process for recruitment ID: {}", trxRecruitment.getId());
            TrxRecruitment validatedRecruitment = runValidations(trxRecruitmentForm, trxRecruitment);

            // Only update if validation was successful and returned a valid entity
            if (validatedRecruitment != null) {
                trxRecruitment = validatedRecruitment;
                trxRecruitment = trxRecruitmentRepository.save(trxRecruitment);
                log.info("Validations completed successfully for recruitment ID: {}", trxRecruitment.getId());
            } else {
                log.warn("Validation returned null entity for recruitment ID: {}, keeping original state", trxRecruitment.getId());
            }
        } catch (Exception e) {
            log.error("Validation failed for recruitment ID: {}, but continuing with submission: {}",
                    trxRecruitment.getId(), e.getMessage(), e);

            // Ensure we save the current state even if validation failed
            // This prevents any partial validation updates from being lost
            try {
                trxRecruitment = trxRecruitmentRepository.save(trxRecruitment);
                log.info("Saved current recruitment state after validation failure for ID: {}", trxRecruitment.getId());
            } catch (Exception saveException) {
                log.error("Failed to save recruitment after validation failure: {}", saveException.getMessage(), saveException);
            }

            // Don't re-throw the exception - allow the submission to continue
            // The recruitment will be saved without validation results
        }
    }

    @Transactional
    public void revise(String username, Long id, TrxRecruitmentForm trxRecruitmentForm) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        TrxRecruitment trxRecruitment = trxRecruitmentRepository.findById(id).orElseThrow(() -> new NotFoundException("Transaksi recruitment " + id + " tidak ditemukan"));

        // Validate ownership
        validateRecruitmentOwnership(user, trxRecruitment);
        if (!trxRecruitment.getApprovalStatus().equals(ApprovalStatus.TERTUNDA) || !trxRecruitment.getTrxStatus().equals(TrxStatus.DIKEMBALIKAN)) {
            throw new BadRequestException("Transaksi hanya dengan status Dikembalikan yang boleh di revisi");
        }

        // Check if NIK has changed and if there's a conflict
        if (StringUtils.isNotBlank(trxRecruitmentForm.getNik()) &&
                !trxRecruitmentForm.getNik().equals(trxRecruitment.getNik()) &&
                StringUtils.isNotBlank(trxRecruitment.getRecruiterCode())) {

            List<TrxStatus> excludedStatuses = Arrays.asList(TrxStatus.EXPIRED, TrxStatus.REJECTED, TrxStatus.DIKEMBALIKAN);
            boolean existingRecruitment = trxRecruitmentRepository.existsByRecruiterCodeAndNikAndTrxStatusNotIn(
                    trxRecruitment.getRecruiterCode(), trxRecruitmentForm.getNik(), excludedStatuses);

            if (existingRecruitment) {
                throw new BadRequestException("There is already an active recruitment request for this candidate by this recruiter. "
                        + "Please wait for the existing request to be completed or rejected.");
            }
        }

        // Update the recruitment data
        BeanUtils.copyProperties(trxRecruitmentForm, trxRecruitment);

        // Handle nullable fields when converting to JSON
        trxRecruitment.setLast5YearJob(trxRecruitmentForm.getLast5YearJobData() != null ?
                new Gson().toJson(trxRecruitmentForm.getLast5YearJobData()) : null);

        trxRecruitment.setLast2YearProduction(trxRecruitmentForm.getLast2YearProductionData() != null ?
                new Gson().toJson(trxRecruitmentForm.getLast2YearProductionData()) : null);

        trxRecruitment.setLastCompanyManPower(trxRecruitmentForm.getLastCompanyManPowerData() != null ?
                new Gson().toJson(trxRecruitmentForm.getLastCompanyManPowerData()) : null);

        trxRecruitment.setRewardInfo(trxRecruitmentForm.getRewardInfoData() != null ?
                new Gson().toJson(trxRecruitmentForm.getRewardInfoData()) : null);

        ManyToOneUtils.save(trxRecruitmentForm, trxRecruitment, "bank", bankRepository);
        ManyToOneUtils.save(trxRecruitmentForm, trxRecruitment, "branch", branchRepository);

        // Run all validations asynchronously using the validation service
        trxRecruitment = runValidations(trxRecruitmentForm, trxRecruitment);

        trxRecruitmentRepository.save(trxRecruitment);

        // Resend Approval
        TrxType trxType = TrxType.valueOf("RECRUITMENT_" + trxRecruitmentForm.getPositionLevel().name());

        TrxApprovalHeader approvalHeader = approvalService.resendApproval(trxType, trxRecruitment.getId());
        trxRecruitment.setApprovalStatus(approvalHeader.getApprovalStatus());
        trxRecruitmentRepository.save(trxRecruitment);
    }

    @Transactional
    public void cancel(String username, Long id) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        // Revise edit profile transaction
        TrxRecruitment trxRecruitment = trxRecruitmentRepository.findById(id).orElseThrow(() -> new NotFoundException("Transaksi ubah profile " + id + " tidak ditemukan"));

        //Validate ownership
        validateRecruitmentOwnership(user, trxRecruitment);

        trxRecruitment.setApprovalStatus(ApprovalStatus.DIBATALKAN);
        trxRecruitment.setTrxStatus(TrxStatus.CANCELLED);
        trxRecruitmentRepository.save(trxRecruitment);

        // Cancel Approval
        approvalService.cancelApproval(TrxType.EDIT_PROFILE, trxRecruitment.getId());
    }


    @Transactional
    public void handleTrxRecruitment(Long id, ApprovalStatus approvalStatus) {
        TrxRecruitment trxRecruitment = trxRecruitmentRepository.findById(id).orElseThrow(() -> new NotFoundException("Transaksi rekrutment " + id + " tidak ditemukan"));
        trxRecruitment.setApprovalStatus(approvalStatus);
        trxRecruitmentRepository.save(trxRecruitment);

        if (approvalStatus.equals(ApprovalStatus.DISETUJUI)) {
            // When recruitment is approved, the leader code should be used when creating the agent
            // This would typically happen in a separate process that creates the agent from the recruitment data
            log.info("Recruitment {} approved. Leader code: {}", id, trxRecruitment.getLeaderCode());

            // Additional logic for creating the agent would go here
            // For example:
            // createAgentFromRecruitment(trxRecruitment);
        }
    }

    /**
     * Updates the status of a recruitment transaction based on the approval status.
     * This method handles all the status transitions for recruitment transactions.
     * Kafka event production is now handled in ApprovalService.
     *
     * @param id             The ID of the recruitment transaction
     * @param approvalStatus The new approval status
     * @throws NotFoundException if the recruitment transaction is not found
     */
    @Transactional
    public TrxRecruitment updateRecruitmentStatus(Long id, ApprovalStatus approvalStatus) {
        log.info("Updating recruitment status for ID: {} to {}", id, approvalStatus);

        // Find the recruitment transaction
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(id)
                .orElse(null);

        if (recruitment == null) return null;

        // Store the previous status for comparison
        ApprovalStatus previousStatus = recruitment.getApprovalStatus();
        TrxStatus previousTrxStatus = recruitment.getTrxStatus();

        if (previousTrxStatus.equals(TrxStatus.DRAFT)) return recruitment;

        // Update the approval status
        recruitment.setApprovalStatus(approvalStatus);

        // Update the transaction status based on the approval status
        switch (approvalStatus) {
            case DISETUJUI:
                recruitment.setTrxStatus(TrxStatus.COMPLETE);
                log.info("Recruitment {} approved and marked as COMPLETE. Leader code: {}",
                        id, recruitment.getLeaderCode());
                // Additional logic for approved recruitments could be added here
                break;

            case DITOLAK:
                recruitment.setTrxStatus(TrxStatus.REJECTED);
                log.info("Recruitment {} rejected and marked as REJECTED", id);
                break;

            case TERTUNDA:
                recruitment.setTrxStatus(TrxStatus.DIKEMBALIKAN);
                log.info("Recruitment {} returned for revision and marked as DIKEMBALIKAN", id);
                break;

            case DIBATALKAN:
                recruitment.setTrxStatus(TrxStatus.CANCELLED);
                log.info("Recruitment {} cancelled and marked as CANCELLED", id);
                break;

            case MENUNGGU_PERSETUJUAN:
            case BARU:
                // Keep the current transaction status for these approval statuses
                log.info("Recruitment {} status updated to {} without changing transaction status",
                        id, approvalStatus);
                break;

            default:
                log.warn("Unhandled approval status: {} for recruitment {}", approvalStatus, id);
                break;
        }

        // Save the updated recruitment
        TrxRecruitment updatedRecruitment = trxRecruitmentRepository.save(recruitment);

        // Kafka event production is now handled in ApprovalService
        if (!approvalStatus.equals(previousStatus) || !updatedRecruitment.getTrxStatus().equals(previousTrxStatus)) {
            log.info("Status changed for recruitment ID: {} from {} to {}",
                    id, previousStatus, approvalStatus);
        } else {
            log.info("No status change detected for recruitment ID: {}", id);
        }
        return updatedRecruitment;
    }

    public void validateRecruitmentOwnership(User user, TrxRecruitment trxRecruitment) {
        if (!user.getId().equals(trxRecruitment.getRecruiter().getId())) {
            throw new BadRequestException("Transaksi ubah profil ini bukan milik Anda.");
        }
    }


    public FileinputResponse upload(String username, String folder, MultipartFile file) {
        User data = userRepository.findByUsername(username).orElse(null);
        String user = "public";
        if (null != data) {
            data.setUsername(data.getUsername());
        }

        String filePath = "/assets/recruitment/" + user + "/" + folder + "/" + UUID.randomUUID() + "."
                + FilenameUtils.getExtension(file.getOriginalFilename());
        if (amazonS3Service.store(file, filePath)) {
            return FileinputResponse.success(amazonS3Service.getUrl(filePath));
        } else {
            throw new BadRequestException("Upload failed");
        }
    }

    /**
     * Submit interview results for a recruitment
     *
     * @param username      The username of the user submitting the interview results
     * @param recruitmentId The ID of the recruitment to update
     * @param interviewForm The interview results form
     * @return The updated recruitment DTO
     */
    @Transactional
    public TrxRecruitmentDto submitInterviewResult(String username, Long recruitmentId, TrxRecruitmentInterviewForm interviewForm) {
        // Validate user
        User user = userRepository.findByUsername(username).orElseThrow(() ->
                new NotFoundException("User not found: " + username));

        // Find the recruitment
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(recruitmentId).orElseThrow(() ->
                new NotFoundException("Recruitment not found with ID: " + recruitmentId));

        // Convert interview form to JSON
        String interviewResultJson = new Gson().toJson(interviewForm);

        // Update the recruitment with interview results
        recruitment.setResultInterview(interviewResultJson);

        // Save the updated recruitment
        TrxRecruitment updatedRecruitment = trxRecruitmentRepository.save(recruitment);

        log.info("Interview results submitted for recruitment ID: {} by user: {}", recruitmentId, username);

        // Return the updated recruitment DTO
        return BaseDto.of(TrxRecruitmentDto.class, updatedRecruitment);
    }

    /**
     * Sets the leader code for a recruitment based on the hierarchy recruitment rules:
     * <p>
     * BP:
     * - Can only recruit BP
     * - Leader of the recruiter will become the leader of the candidate
     * <p>
     * BM (leader):
     * - Can recruit BP and BM
     * - If recruiting BP -> BM becomes the leader (downline)
     * - If recruiting BM -> Leader of the recruiter will become the leader of the candidate
     * <p>
     * BD (leader):
     * - Can recruit all levels (BP, BM, BD)
     * - If recruiting BP -> BD becomes the leader (downline)
     * - If recruiting BM -> BD becomes the leader (downline)
     * - If recruiting BD -> Leader of the recruiter will become the leader of the candidate
     * and the recruited BD becomes Generation 1 of the recruiting BD
     * <p>
     * BDM/ABDD/BDD/HOS/CAO:
     * - Can recruit all levels (BP, BM, BD)
     * - All recruits become direct company (leader code is the recruiter's username)
     *
     * @param recruiter      The user who is recruiting
     * @param trxRecruitment The recruitment transaction
     */
    private void setLeaderCodeBasedOnHierarchy(User recruiter, TrxRecruitment trxRecruitment) {
        // Check if the recruiter has a special role (BDM, ABDD, BDD, HOS, CAO)
        boolean hasSpecialRole = false;
        String specialRoleType = null;
        if (recruiter.getRoles() != null) {
            for (Role role : recruiter.getRoles()) {
                String roleCode = role.getCode();
                if (roleCode != null) {
                    if (roleCode.contains("BDM")) {
                        hasSpecialRole = true;
                        specialRoleType = "BDM";
                        break;
                    } else if (roleCode.contains("ABDD")) {
                        hasSpecialRole = true;
                        specialRoleType = "ABDD";
                        break;
                    } else if (roleCode.contains("BDD")) {
                        hasSpecialRole = true;
                        specialRoleType = "BDD";
                        break;
                    } else if (roleCode.contains("HOS")) {
                        hasSpecialRole = true;
                        specialRoleType = "HOS";
                        break;
                    } else if (roleCode.contains("CAO")) {
                        hasSpecialRole = true;
                        specialRoleType = "CAO";
                        break;
                    }
                }
            }
        }

        // Get the recruiter's agent information if not a special role
        Agent recruiterAgent = null;
        if (!hasSpecialRole) {
            recruiterAgent = agentRepository.findTopByUser(recruiter).orElse(null);
            if (recruiterAgent == null) {
                log.warn("Recruiter {} does not have an associated agent record", recruiter.getUsername());
                return;
            }
        }

        // Get the position level of the recruiter
        String recruiterPositionLevel;

        if (hasSpecialRole) {
            // For special roles, use the determined role type
            recruiterPositionLevel = specialRoleType;
        } else {
            // For regular agent roles, get from agent record or determine from roles
            recruiterPositionLevel = recruiterAgent.getPositionLevel();

            // If position level is not set in agent record, try to determine from roles
            if (StringUtils.isBlank(recruiterPositionLevel) && recruiter.getRoles() != null) {
                for (Role role : recruiter.getRoles()) {
                    if (role.getCode() != null && role.getCode().contains("_BP")) {
                        recruiterPositionLevel = "BP";
                        break;
                    } else if (role.getCode() != null && role.getCode().contains("_BM")) {
                        recruiterPositionLevel = "BM";
                        break;
                    } else if (role.getCode() != null && role.getCode().contains("_BD")) {
                        recruiterPositionLevel = "BD";
                        break;
                    }
                }
            }
        }

        // Get the candidate's position level
        String candidatePositionLevel = trxRecruitment.getPositionLevel() != null ?
                trxRecruitment.getPositionLevel().name() : null;

        if (StringUtils.isBlank(recruiterPositionLevel) || StringUtils.isBlank(candidatePositionLevel)) {
            log.warn("Cannot determine position levels for recruitment. Recruiter: {}, Candidate: {}",
                    recruiterPositionLevel, candidatePositionLevel);
            return;
        }

        // First validate that the recruiter can recruit this position level
        boolean isValidRecruitment = validateRecruitmentHierarchy(recruiterPositionLevel, candidatePositionLevel);
        if (!isValidRecruitment) {
            log.warn("Invalid recruitment hierarchy. Recruiter: {} ({}), Candidate: {}",
                    recruiter.getUsername(), recruiterPositionLevel, candidatePositionLevel);
            return;
        }

        // Then validate that there are no existing active recruitment requests for the same candidate
        String recruiterCode = recruiter.getUsername();
        String nik = trxRecruitment.getNik();
        boolean noExistingRecruitment = validateNoExistingRecruitment(recruiterCode, nik);
        if (!noExistingRecruitment) {
            log.warn("Existing active recruitment found. Recruiter: {}, NIK: {}", recruiterCode, nik);
            // We don't return here because this is just a warning, the actual validation is done in the controller
        }

        // Apply hierarchy recruitment rules
        String leaderCode = recruiter.getUsername();

        switch (recruiterPositionLevel) {
            case "BP":
                // BP can only recruit BP, leader of recruiter becomes leader of candidate
                if ("BP".equals(candidatePositionLevel)) {
                    leaderCode = recruiterAgent.getLeaderCode();
                }
                break;

            case "BM":
                // BM can recruit BP and BM
                if ("BP".equals(candidatePositionLevel)) {
                    // If recruiting BP -> BM becomes the leader
                    leaderCode = recruiterAgent.getAgentCode();
                } else if ("BM".equals(candidatePositionLevel)) {
                    // If recruiting BM -> Leader of the recruiter becomes the leader
                    leaderCode = recruiterAgent.getLeaderCode();
                }
                break;

            case "BD":
                // BD can recruit all levels
                if ("BP".equals(candidatePositionLevel) || "BM".equals(candidatePositionLevel)) {
                    // If recruiting BP or BM -> BD becomes the leader
                    leaderCode = recruiterAgent.getAgentCode();
                } else if ("BD".equals(candidatePositionLevel)) {
                    // If recruiting BD -> Leader of the recruiter becomes the leader
                    leaderCode = recruiterAgent.getLeaderCode() != null ? recruiterAgent.getLeaderCode() : recruiterAgent.getAgentCode();
                    // Note: The recruited BD becomes Generation 1 of the recruiting BD
                    // This would typically be handled in a separate process when creating the agent
                }
                break;

            case "BDM":
            case "ABDD":
            case "BDD":
            case "HOS":
            case "CAO":
                // Special roles - all recruits become direct company
                // Use the recruiter's username as the leader code
                leaderCode = recruiter.getUsername();
                log.info("Special role {} recruiting. Setting direct company leadership.", recruiterPositionLevel);
                break;

            default:
                log.warn("Unknown recruiter position level: {}", recruiterPositionLevel);
                break;
        }

        // Set the leader code in the recruitment transaction
        if (StringUtils.isNotBlank(leaderCode)) {
            log.info("Setting leader code {} for recruitment by {} ({})",
                    leaderCode, recruiter.getUsername(), recruiterPositionLevel);
            trxRecruitment.setLeaderCode(leaderCode);
        } else {
            log.warn("Could not determine leader code for recruitment. Recruiter: {} ({}), Candidate: {}",
                    recruiter.getUsername(), recruiterPositionLevel, candidatePositionLevel);
        }
    }

    /**
     * Validates that the recruiter can recruit the candidate based on hierarchy rules
     * <p>
     * BP:
     * - Can only recruit BP
     * <p>
     * BM (leader):
     * - Can recruit BP and BM
     * <p>
     * BD (leader):
     * - Can recruit all levels (BP, BM, BD)
     * <p>
     * BDM/ABDD/BDD/HOS/CAO:
     * - Can recruit all levels (BP, BM, BD)
     *
     * @param recruiterPositionLevel The position level of the recruiter
     * @param candidatePositionLevel The position level of the candidate
     * @return true if the recruitment is valid according to hierarchy rules, false otherwise
     */
    private boolean validateRecruitmentHierarchy(String recruiterPositionLevel, String candidatePositionLevel) {
        switch (recruiterPositionLevel) {
            case "BP":
                // BP can only recruit BP
                return "BP".equals(candidatePositionLevel);

            case "BM":
                // BM can recruit BP and BM
                return "BP".equals(candidatePositionLevel) || "BM".equals(candidatePositionLevel);

            case "BD":
            case "BDM":
            case "ABDD":
            case "BDD":
            case "HOS":
            case "CAO":
                // These roles can recruit all levels
                return true;

            default:
                return false;
        }
    }

    /**
     * Validates that there are no existing active recruitment requests for the same candidate by the same recruiter
     *
     * @param recruiterCode The code of the recruiter
     * @param nik           The NIK (National ID) of the candidate
     * @return true if no existing active recruitment requests are found, false otherwise
     */
    private boolean validateNoExistingRecruitment(String recruiterCode, String nik) {
        if (StringUtils.isBlank(recruiterCode) || StringUtils.isBlank(nik)) {
            return true; // Skip validation if either recruiter code or NIK is not provided
        }

        List<TrxStatus> excludedStatuses = Arrays.asList(TrxStatus.EXPIRED, TrxStatus.REJECTED);
        return !trxRecruitmentRepository.existsByRecruiterCodeAndNikAndTrxStatusNotIn(
                recruiterCode, nik, excludedStatuses);
    }

    /**
     * Find a recruitment by ID
     *
     * @param id The ID of the recruitment
     * @return The recruitment if found
     * @throws NotFoundException if no recruitment is found
     */
    @Transactional(readOnly = true)
    public TrxRecruitment findById(Long id) {
        return trxRecruitmentRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Transaksi rekrutment " + id + " tidak ditemukan"));
    }

    /**
     * Updates the agent code for a recruitment
     *
     * @param id        The ID of the recruitment
     * @param agentCode The agent code to set
     * @return The updated recruitment
     * @throws NotFoundException if no recruitment is found
     */
    @Transactional
    public TrxRecruitment updateAgentCode(Long id, String agentCode) {
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Transaksi rekrutment " + id + " tidak ditemukan"));

        // Set the agent code
        recruitment.setAgentCode(agentCode);

        // Save and return the updated recruitment
        return trxRecruitmentRepository.save(recruitment);
    }

    /**
     * Updates the document URLs for a recruitment
     *
     * @param id                  The ID of the recruitment
     * @param apgenFileUrl        The APGEN-AGE document URL (can be null)
     * @param pkajFileUrl         The PKAJ document URL
     * @param pmkajFileUrl        The PMKAJ document URL (can be null)
     * @param antiTwistingFileUrl The ANTI TWISTING document URL
     * @param kodeEtikFileUrl     The KODE ETIK document URL
     * @return The updated recruitment
     * @throws NotFoundException if no recruitment is found
     */
    @Transactional
    public TrxRecruitment updateDocumentUrls(Long id, String apgenFileUrl, String pkajFileUrl, String pmkajFileUrl,
                                             String antiTwistingFileUrl, String kodeEtikFileUrl, String pkajNumber, String pmkajNumber) {
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Transaksi rekrutment " + id + " tidak ditemukan"));

        // Update document URLs
        if (apgenFileUrl != null) {
            recruitment.setApgenFile(apgenFileUrl);
        }
        if (pkajFileUrl != null) {
            recruitment.setPkajFile(pkajFileUrl);
        }
        if (pmkajFileUrl != null) {
            recruitment.setPmkajFile(pmkajFileUrl);
        }
        if (antiTwistingFileUrl != null) {
            recruitment.setAntiTwistingFile(antiTwistingFileUrl);
        }
        if (kodeEtikFileUrl != null) {
            recruitment.setKodeEtikFile(kodeEtikFileUrl);
        }
        if (pkajNumber != null) {
            recruitment.setPkajNumber(pkajNumber);
        }
        if (pmkajNumber != null) {
            recruitment.setPmkajNumber(pmkajNumber);
        }

        recruitment.setEffectiveDate(LocalDate.now());

        // Save and return the updated recruitment
        return trxRecruitmentRepository.save(recruitment);
    }

    /**
     * Runs validations for a recruitment
     * This method has been refactored to use the validation service
     * which runs validations asynchronously
     * <p>
     * Enhanced with better error handling to prevent transaction rollbacks
     *
     * @param form   The recruitment form
     * @param entity The recruitment entity to validate
     * @return The updated entity with validation results
     */
    private TrxRecruitment runValidations(TrxRecruitmentForm form, TrxRecruitment entity) {
        if (entity == null || entity.getId() == null) {
            log.warn("Cannot run validations on null entity or entity without ID");
            return entity;
        }

        log.info("Starting validation process for recruitment ID: {}", entity.getId());

        // Store the original state in case we need to revert
        TrxStatus originalStatus = entity.getTrxStatus();
        ApprovalStatus originalApprovalStatus = entity.getApprovalStatus();

        try {
            TrxRecruitment validatedEntity = validationService.validateRecruitment(form, entity);

            if (validatedEntity != null && validatedEntity.getId() != null) {
                log.info("Validation completed successfully for recruitment ID: {}", validatedEntity.getId());
                return validatedEntity;
            } else {
                log.warn("Validation service returned invalid entity for recruitment ID: {}, returning original", entity.getId());
                return entity;
            }
        } catch (Exception e) {
            log.error("Validation service failed for recruitment ID: {}: {}", entity.getId(), e.getMessage(), e);

            // Ensure the entity maintains its original transaction state
            entity.setTrxStatus(originalStatus);
            if (originalApprovalStatus != null) {
                entity.setApprovalStatus(originalApprovalStatus);
            }

            // Return the original entity unchanged if validation fails
            // This prevents the validation failure from affecting the main submission process
            return entity;
        }
    }

    /**
     * Updates validation statuses for a recruitment
     *
     * @param username The username of the user updating the validation statuses
     * @param uuid     The UUID of the recruitment to update
     * @param form     The validation status form
     * @return The updated recruitment DTO
     */
    @Transactional
    public TrxRecruitmentDto updateValidationStatuses(String username, String uuid,
                                                      id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentValidationForm form) {
        // Validate user
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException("User not found: " + username));

        // Find the recruitment by UUID
        TrxRecruitment recruitment = trxRecruitmentRepository.findByUuid(uuid)
                .orElseThrow(() -> new NotFoundException("Recruitment not found with UUID: " + uuid));

        // Update validation statuses if provided in the form
        if (form.getValidationBlacklistStatus() != null) {
            recruitment.setValidationBlacklistStatus(form.getValidationBlacklistStatus());
        }

        if (form.getValidationKtpStatus() != null) {
            recruitment.setValidationKtpStatus(form.getValidationKtpStatus());
        }

        if (form.getValidationBankAccountStatus() != null) {
            recruitment.setValidationBankAccountStatus(form.getValidationBankAccountStatus());
        }

        if (form.getValidationHirarkiStatus() != null) {
            recruitment.setValidationHirarkiStatus(form.getValidationHirarkiStatus());
        }

        if (form.getValidationAmlStatus() != null) {
            recruitment.setValidationAmlStatus(form.getValidationAmlStatus());
        }

        if (form.getValidationAdministrationAgentStatus() != null) {
            recruitment.setValidationAdministrationAgentStatus(form.getValidationAdministrationAgentStatus());
        }

        if (form.getValidationLicenseAajiStatus() != null) {
            recruitment.setValidationLicenseAajiStatus(form.getValidationLicenseAajiStatus());
        }

        if (form.getValidationLicenseAasiStatus() != null) {
            recruitment.setValidationLicenseAasiStatus(form.getValidationLicenseAasiStatus());
        }

        // Save the updated recruitment
        TrxRecruitment updatedRecruitment = trxRecruitmentRepository.save(recruitment);

        log.info("Validation statuses updated for recruitment UUID: {} by user: {}", uuid, username);

        // Return the updated recruitment DTO
        return BaseDto.of(TrxRecruitmentDto.class, updatedRecruitment);
    }

    @Transactional
    public void expireStaleRecruitments() {
        // select Recruitments that has no update since 30 days
        Instant cutoff = Instant.now().minus(30L, ChronoUnit.DAYS);
        List<TrxRecruitment> recruitments = trxRecruitmentRepository
                .findByUpdatedAtBeforeAndTrxStatusNotInAndDeletedFalse(cutoff,
                        List.of(TrxStatus.COMPLETE, TrxStatus.EXPIRED, TrxStatus.CANCELLED, TrxStatus.REJECTED));

        Set<TrxRecruitment> notificationEntries = new HashSet<>();
        List<TrxRecruitment> updatedRecruitments = recruitments.stream().peek((t) -> {
            t.setTrxStatus(TrxStatus.EXPIRED);
            t.setApprovalStatus(ApprovalStatus.DIBATALKAN);
            t.setDeleted(true);
            notificationEntries.add(t);

            approvalService.cancelApproval(t.getApprovalHeader().getTrxType(), t.getId());
        }).toList();

        trxRecruitmentRepository.saveAll(updatedRecruitments);

        // send notification to eligible requester
        notificationEntries.forEach(this::sendExpiredRecruitmentInbox);
    }

    private void sendExpiredRecruitmentInbox(TrxRecruitment recruitment) {

        // TODO ZZZ would need to be replaced with proper value - find a way to retrieve next approver name/agent code (confirm to BA)
        String bodyContent = String.format("Pengajuan agen baru atas nama %s telah kadaluarsa", recruitment.getFullName());

        // Create notification data map
        Map<String, String> data = Map.of(
                "approvalId", recruitment.getApprovalHeader().getId().toString(),
                "trxId", recruitment.getId().toString(),
                "trxUuid", recruitment.getUuid().toString(),
                "trxType", recruitment.getApprovalHeader().getTrxType().name(),
                "status", recruitment.getApprovalStatus().name()
        );

        NotificationDto notification = new NotificationDto();
        notification.setTitle("Pengajuan agen baru anda telah kadaluarsa");
        notification.setBody(bodyContent);
        notification.setInboxType(InboxType.INBOX);
        notification.setData(data);

        firebaseService.sendNotification(List.of(recruitment.getRecruiter()), notification);
    }

    /**
     * Get productivity recruitment data for charts and analysis
     *
     * @param username The username of the requesting user
     * @param filter   Filter parameters for the productivity data
     * @return ProductivityRecruitmentDto containing summary, chart data, and breakdown
     */
    public ProductivityRecruitmentDto getProductivityRecruitmentData(String username, ProductivityRecruitmentFilter filter) {
        // Validate user
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException("User not found: " + username));

        // Set default values for filter
        if (filter.getYear() == null) {
            filter.setYear(LocalDate.now().getYear());
        }
        if (filter.getScope() == null) {
            filter.setScope(ProductivityScope.SELF);
        }
        if (filter.getGroupBy() == null) {
            filter.setGroupBy(ProductivityGroupBy.MONTHLY);
        }

        // Calculate date range based on chart requirements
        Instant startDate, endDate;
        if (filter.getStartDate() != null && filter.getEndDate() != null) {
            // Custom date range
            startDate = filter.getStartDate().atStartOfDay(ZoneId.systemDefault()).toInstant();
            endDate = filter.getEndDate().atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant();
        } else if (filter.getMonth() != null) {
            // Monthly mode: Get 5 months of data (current month + 4 previous months)
            LocalDate currentMonth = LocalDate.of(filter.getYear(), filter.getMonth(), 1);
            LocalDate startMonth = currentMonth.minusMonths(4);
            LocalDate endMonth = currentMonth.withDayOfMonth(currentMonth.lengthOfMonth());
            startDate = startMonth.atStartOfDay(ZoneId.systemDefault()).toInstant();
            endDate = endMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant();
        } else {
            // Yearly mode: Get 5 years of data (current year + 4 previous years)
            LocalDate currentYear = LocalDate.of(filter.getYear(), 1, 1);
            LocalDate startYear = currentYear.minusYears(4);
            LocalDate endYear = LocalDate.of(filter.getYear(), 12, 31);
            startDate = startYear.atStartOfDay(ZoneId.systemDefault()).toInstant();
            endDate = endYear.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant();
        }

        // Determine filtering approach based on user type and scope
        List<Long> recruiterIds = null;
        List<String> branchCodes = null;

        if (ProductivityScope.SELF.equals(filter.getScope())) {
            recruiterIds = List.of(user.getId());
        } else if (ProductivityScope.TEAM.equals(filter.getScope())) {
            if (UserType.STAFF.equals(user.getUserType())) {
                // For STAFF users, filter by branch codes (more efficient)
                branchCodes = getStaffUserBranchCodes(user, username);
                log.info("Using branch filtering for STAFF user {} with branches: {}", username, branchCodes);
            } else {
                // For regular users (agents), use recruiter IDs approach
                recruiterIds = getAgentTeamRecruiterIds(user, username);
                log.info("Using recruiter ID filtering for agent user {} with {} recruiters", username, recruiterIds.size());
            }
        }

        // Get approved recruitments using appropriate filtering method
        List<TrxRecruitment> allApprovedRecruitments;
        List<TrxRecruitment> rekrutBerkodeAgen;
        List<TrxRecruitment> rekrutBaruBerlisensi;

        if (branchCodes != null) {
            // Use branch-based filtering for STAFF users
            allApprovedRecruitments = trxRecruitmentRepository.findApprovedRecruitmentsForProductivityByBranch(
                    ApprovalStatus.DISETUJUI, startDate, endDate, branchCodes);
            log.info("Found {} total approved recruitments by branch", allApprovedRecruitments.size());

            rekrutBerkodeAgen = trxRecruitmentRepository.findRekrutBerkodeAgenByBranch(
                    ApprovalStatus.DISETUJUI, startDate, endDate, branchCodes,
                    ValidationLicenseAajiStatus.ACTIVE, ValidationLicenseAasiStatus.ACTIVE);
            log.info("Found {} rekrut berkode agen by branch", rekrutBerkodeAgen.size());

            rekrutBaruBerlisensi = trxRecruitmentRepository.findRekrutBaruBerlisensiBybranch(
                    ApprovalStatus.DISETUJUI, startDate, endDate, branchCodes,
                    ValidationLicenseAajiStatus.ACTIVE, ValidationLicenseAasiStatus.ACTIVE);
            log.info("Found {} rekrut baru berlisensi by branch", rekrutBaruBerlisensi.size());
        } else {
            // Use recruiter ID-based filtering for agents and self scope
            allApprovedRecruitments = trxRecruitmentRepository.findApprovedRecruitmentsForProductivity(
                    ApprovalStatus.DISETUJUI, startDate, endDate, recruiterIds);
            log.info("Found {} total approved recruitments by recruiter", allApprovedRecruitments.size());

            rekrutBerkodeAgen = trxRecruitmentRepository.findRekrutBerkodeAgen(
                    ApprovalStatus.DISETUJUI, startDate, endDate, recruiterIds,
                    ValidationLicenseAajiStatus.ACTIVE, ValidationLicenseAasiStatus.ACTIVE);
            log.info("Found {} rekrut berkode agen by recruiter", rekrutBerkodeAgen.size());

            rekrutBaruBerlisensi = trxRecruitmentRepository.findRekrutBaruBerlisensi(
                    ApprovalStatus.DISETUJUI, startDate, endDate, recruiterIds,
                    ValidationLicenseAajiStatus.ACTIVE, ValidationLicenseAasiStatus.ACTIVE);
            log.info("Found {} rekrut baru berlisensi by recruiter", rekrutBaruBerlisensi.size());
        }

        // Filter by recruitment type if specified
        if (filter.getRecruitmentType() != null) {
            if (ProductivityRecruitmentType.REKRUT_BERKODE_AGEN.equals(filter.getRecruitmentType())) {
                allApprovedRecruitments = rekrutBerkodeAgen;
            } else if (ProductivityRecruitmentType.REKRUT_BARU_BERLISENSI.equals(filter.getRecruitmentType())) {
                allApprovedRecruitments = rekrutBaruBerlisensi;
            }
        }

        // Build response
        return ProductivityRecruitmentDto.builder()
                .summary(buildProductivitySummary(rekrutBerkodeAgen, rekrutBaruBerlisensi, filter))
                .chartData(buildChartData(rekrutBerkodeAgen, rekrutBaruBerlisensi, filter))
                .build();
    }

    /**
     * Get branch codes for STAFF users for branch-based filtering
     */
    private List<String> getStaffUserBranchCodes(User user, String username) {
        // Get user's assigned branches
        if (user.getBranches() == null || user.getBranches().isEmpty()) {
            log.warn("STAFF user {} has no assigned branches, returning empty list", username);
            return List.of();
        }

        // Extract branch codes from user's branches
        List<String> branchCodes = user.getBranches().stream()
                .map(Branch::getBranchCode)
                .filter(Objects::nonNull)
                .toList();

        if (branchCodes.isEmpty()) {
            log.warn("STAFF user {} has branches but no valid branch codes, returning empty list", username);
            return List.of();
        }

        log.info("Found {} branch codes for STAFF user {}: {}", branchCodes.size(), username, branchCodes);
        return branchCodes;
    }

    /**
     * Get recruiter IDs for agent users using leader code approach
     */
    private List<Long> getAgentTeamRecruiterIds(User user, String username) {
        List<Long> recruiterIds = new ArrayList<>();

        // Add current user
        recruiterIds.add(user.getId());

        // Get current user's agent
        Agent currentAgent = agentRepository.findTopByUser(user)
                .orElseThrow(() -> new NotFoundException("Agent not found for user: " + username));

        // Find all team members using leaderCode
        List<Agent> teamMembers = agentRepository.findByLeaderCode(currentAgent.getAgentCode());
        log.info("Found {} team members for leader code: {}", teamMembers.size(), currentAgent.getAgentCode());

        // Add team members' user IDs
        for (Agent teamMember : teamMembers) {
            if (teamMember.getUser() != null) {
                recruiterIds.add(teamMember.getUser().getId());
            }
        }

        return recruiterIds;
    }

    /**
     * Build productivity summary data
     */
    private ProductivityRecruitmentDto.ProductivitySummaryDto buildProductivitySummary(
            List<TrxRecruitment> rekrutBerkodeAgen,
            List<TrxRecruitment> rekrutBaruBerlisensi,
            ProductivityRecruitmentFilter filter) {

        long totalRekrutBerkodeAgen = rekrutBerkodeAgen.size();
        long totalRekrutBaruBerlisensi = rekrutBaruBerlisensi.size();
        long totalRecruitments = totalRekrutBerkodeAgen + totalRekrutBaruBerlisensi;

        // Calculate percentages
        double percentageRekrutBerkodeAgen = totalRecruitments > 0 ?
                (double) totalRekrutBerkodeAgen / totalRecruitments * 100 : 0.0;
        double percentageRekrutBaruBerlisensi = totalRecruitments > 0 ?
                (double) totalRekrutBaruBerlisensi / totalRecruitments * 100 : 0.0;

        // Build period description
        String period;
        if (filter.getMonth() != null) {
            // For monthly mode: show range of 5 months
            LocalDate currentMonth = LocalDate.of(filter.getYear(), filter.getMonth(), 1);
            LocalDate startMonth = currentMonth.minusMonths(4);
            period = DateTimeFormatter.ofPattern("MMM yyyy").format(startMonth) +
                    " - " + DateTimeFormatter.ofPattern("MMM yyyy").format(currentMonth);
        } else {
            // For yearly mode: show range of 5 years
            int startYear = filter.getYear() - 4;
            period = startYear + " - " + filter.getYear();
        }

        return ProductivityRecruitmentDto.ProductivitySummaryDto.builder()
                .totalRecruitments(totalRecruitments)
                .rekrutBerkodeAgen(totalRekrutBerkodeAgen)
                .rekrutBaruBerlisensi(totalRekrutBaruBerlisensi)
                .period(period)
                .percentageRekrutBerkodeAgen(Math.round(percentageRekrutBerkodeAgen * 100.0) / 100.0)
                .percentageRekrutBaruBerlisensi(Math.round(percentageRekrutBaruBerlisensi * 100.0) / 100.0)
                .build();
    }

    /**
     * Build chart data grouped by time period
     */
    private List<ProductivityRecruitmentDto.ProductivityChartDataDto> buildChartData(
            List<TrxRecruitment> rekrutBerkodeAgen,
            List<TrxRecruitment> rekrutBaruBerlisensi,
            ProductivityRecruitmentFilter filter) {

        Map<LocalDate, ProductivityRecruitmentDto.ProductivityChartDataDto> chartDataMap = new LinkedHashMap<>();

        // Initialize all periods with zero data to ensure we always show 5 periods
        initializeChartPeriods(chartDataMap, filter);

        // Process rekrut berkode agen
        for (TrxRecruitment recruitment : rekrutBerkodeAgen) {
            LocalDate date = getGroupingDate(recruitment.getUpdatedAt(), filter.getGroupBy());
            // Add to chart data map even if not in initialized periods
            chartDataMap.computeIfAbsent(date, d -> ProductivityRecruitmentDto.ProductivityChartDataDto.builder()
                    .date(d)
                    .label(formatDateLabel(d, filter.getGroupBy()))
                    .rekrutBerkodeAgen(0L)
                    .rekrutBaruBerlisensi(0L)
                    .total(0L)
                    .build());

            chartDataMap.get(date).setRekrutBerkodeAgen(chartDataMap.get(date).getRekrutBerkodeAgen() + 1);
        }

        // Process rekrut baru berlisensi
        for (TrxRecruitment recruitment : rekrutBaruBerlisensi) {
            LocalDate date = getGroupingDate(recruitment.getUpdatedAt(), filter.getGroupBy());
            // Add to chart data map even if not in initialized periods
            chartDataMap.computeIfAbsent(date, d -> ProductivityRecruitmentDto.ProductivityChartDataDto.builder()
                    .date(d)
                    .label(formatDateLabel(d, filter.getGroupBy()))
                    .rekrutBerkodeAgen(0L)
                    .rekrutBaruBerlisensi(0L)
                    .total(0L)
                    .build());

            chartDataMap.get(date).setRekrutBaruBerlisensi(chartDataMap.get(date).getRekrutBaruBerlisensi() + 1);
        }

        // Calculate totals and return sorted list
        List<ProductivityRecruitmentDto.ProductivityChartDataDto> result = chartDataMap.values().stream()
                .peek(data -> data.setTotal(data.getRekrutBerkodeAgen() + data.getRekrutBaruBerlisensi()))
                .sorted(Comparator.comparing(ProductivityRecruitmentDto.ProductivityChartDataDto::getDate))
                .collect(Collectors.toList());

        // If we have more than 5 periods, take the most recent 5
        if (result.size() > 5) {
            result = result.subList(result.size() - 5, result.size());
        }

        log.info("Final chart data periods: {}", result.stream()
                .map(d -> d.getDate() + "=" + d.getTotal())
                .collect(Collectors.toList()));

        return result;
    }

    /**
     * Initialize chart periods to ensure we always show 5 periods
     */
    private void initializeChartPeriods(Map<LocalDate, ProductivityRecruitmentDto.ProductivityChartDataDto> chartDataMap,
                                        ProductivityRecruitmentFilter filter) {
        if (filter.getMonth() != null) {
            // Monthly mode: Initialize 5 months
            LocalDate currentMonth = LocalDate.of(filter.getYear(), filter.getMonth(), 1);
            for (int i = 4; i >= 0; i--) {
                LocalDate month = currentMonth.minusMonths(i);
                chartDataMap.put(month, ProductivityRecruitmentDto.ProductivityChartDataDto.builder()
                        .date(month)
                        .label(formatDateLabel(month, ProductivityGroupBy.MONTHLY))
                        .rekrutBerkodeAgen(0L)
                        .rekrutBaruBerlisensi(0L)
                        .total(0L)
                        .build());
            }
        } else {
            // Yearly mode: Initialize 5 years
            for (int i = 4; i >= 0; i--) {
                LocalDate year = LocalDate.of(filter.getYear() - i, 1, 1);
                chartDataMap.put(year, ProductivityRecruitmentDto.ProductivityChartDataDto.builder()
                        .date(year)
                        .label(formatDateLabel(year, ProductivityGroupBy.YEARLY))
                        .rekrutBerkodeAgen(0L)
                        .rekrutBaruBerlisensi(0L)
                        .total(0L)
                        .build());
            }
        }
    }


    /**
     * Get grouping date based on groupBy parameter
     */
    private LocalDate getGroupingDate(Instant instant, ProductivityGroupBy groupBy) {
        LocalDate date = instant.atZone(ZoneId.systemDefault()).toLocalDate();

        if (groupBy == null) {
            groupBy = ProductivityGroupBy.MONTHLY;
        }

        switch (groupBy) {
            case MONTHLY:
                return date.withDayOfMonth(1);
            case YEARLY:
                return date.withDayOfYear(1);
            default:
                return date.withDayOfMonth(1); // Default to monthly
        }
    }

    /**
     * Format date label based on groupBy parameter
     */
    private String formatDateLabel(LocalDate date, ProductivityGroupBy groupBy) {
        if (groupBy == null) {
            groupBy = ProductivityGroupBy.MONTHLY;
        }

        switch (groupBy) {
            case MONTHLY:
                return DateTimeFormatter.ofPattern("MMM yyyy").format(date);
            case YEARLY:
                return DateTimeFormatter.ofPattern("yyyy").format(date);
            default:
                return DateTimeFormatter.ofPattern("MMM yyyy").format(date); // Default to monthly
        }
    }

    /**
     * Validates that approval configuration exists for the given transaction type and user
     * This helps prevent approval creation failures by checking configuration beforehand
     *
     * @param trxType The transaction type
     * @param user    The user requesting approval
     * @throws IllegalStateException if approval configuration is missing or invalid
     */
    private void validateApprovalConfiguration(TrxType trxType, User user) {
        try {
            // Check if user has roles
            if (user.getRoles() == null || user.getRoles().isEmpty()) {
                throw new IllegalStateException("User " + user.getUsername() + " has no roles assigned");
            }

            Set<String> roles = user.getRoles().stream()
                    .map(Role::getCode)
                    .collect(Collectors.toSet());

            // Check if approval levels are defined for this transaction type and user roles
            Integer maxLevel = approvalLevelRepository.findMaxLevelForTransactionTypeAndRequesterRole(trxType, roles);
            if (maxLevel == null) {
                throw new IllegalStateException("No approval levels defined for transaction type: " + trxType + " and user roles: " + roles);
            }

            // Check if first level configuration exists
            ApprovalLevel firstLevel = approvalLevelRepository.findApplicableLevel(
                            trxType, 1, user.getChannel(), roles, null, null)
                    .orElse(null);

            if (firstLevel == null) {
                throw new IllegalStateException("No approval level configuration found for transaction type: " + trxType +
                        ", level: 1, channel: " + user.getChannel() + ", roles: " + roles);
            }

            log.debug("Approval configuration validated successfully for transaction type: {}, user: {}, max level: {}",
                    trxType, user.getUsername(), maxLevel);

        } catch (Exception e) {
            log.error("Approval configuration validation failed for transaction type: {}, user: {}: {}",
                    trxType, user.getUsername(), e.getMessage());
            throw new IllegalStateException("Invalid approval configuration: " + e.getMessage(), e);
        }
    }

    /**
     * Handles portal system update for approved recruitment transactions
     * This method is called synchronously when recruitment approval is completed
     *
     * @param recruitmentId The recruitment ID
     */
    @Transactional
    public void handlePortalSystemUpdate(Long recruitmentId) {
        log.info("Processing synchronous portal system update for recruitment ID: {}", recruitmentId);

        // Retrieve the full recruitment data
        TrxRecruitment recruitment = trxRecruitmentRepository.findById(recruitmentId).orElse(null);
        if (recruitment == null) {
            String errorMessage = "Could not find recruitment with ID: " + recruitmentId;
            log.error(errorMessage);
            throw new IllegalArgumentException(errorMessage);
        }

        // Create the registration DTO
        PortalRegistrationDto registrationDto = createPortalRegistrationDto(recruitment);

        try {
            // Call the portal API to register the agent
            log.info("Calling portal API to register agent for recruitment ID: {}", recruitmentId);
            Call<PortalRegistrationResponseDto> call = portalProvider.registerAgent(registrationDto);
            Response<PortalRegistrationResponseDto> response = call.execute();
            PortalRegistrationResponseDto responseBody = response.body();
            if (responseBody != null) {
                if (response.isSuccessful()) {
                    String agentCode = responseBody.getAgentReg() != null ? responseBody.getAgentReg().getAgentCode() : "Unknown";
                    log.info("Successfully registered agent in portal system. Recruitment ID: {}, Agent Code: {}",
                            recruitmentId, agentCode);

                    // Update the recruitment record with the agent code
                    if (agentCode != null && !"Unknown".equals(agentCode)) {
                        // Update the recruitment record with the agent code
                        updateAgentCode(recruitmentId, agentCode);
                        log.info("Updated recruitment record with agent code: {}", agentCode);

                        // Create agent record in the system
                        AgentDto agent = createAgentFromRecruitment(recruitment, agentCode);

                        // Send CAS Lisensi notification
                        if (agent != null) {
                            sendCasLisensiNotification(recruitment, agent);
                        }
                    } else {
                        String errorMessage = "Portal registration returned invalid agent code for recruitment ID: " + recruitmentId;
                        log.error(errorMessage);
                        throw new BadRequestException(errorMessage);
                    }
                } else {
                    // Portal registration failed - throw exception with portal error message and field errors
                    String errorMessage = buildPortalErrorMessage(recruitmentId, responseBody);
                    log.error(errorMessage);
                    throw new BadRequestException(errorMessage);
                }
            } else {
                PortalRegistrationResponseDto errorResponseBody = null;
                if (response.errorBody() != null) {
                    try {
                        String errorBodyString = response.errorBody().string();
                        Gson gson = new Gson();
                        errorResponseBody = gson.fromJson(errorBodyString, PortalRegistrationResponseDto.class);
                    } catch (Exception e) {
                        log.error("Failed to parse error body for recruitment ID: {}", recruitmentId, e);
                    }
                }

                String errorMessage;
                if (errorResponseBody != null) {
                    errorMessage = buildPortalErrorMessage(recruitmentId, errorResponseBody);
                } else {
                    errorMessage = String.format("Portal registration API call failed for recruitment ID: %d. HTTP Status: %d, Message: %s. No response body received.",
                            recruitmentId, response.code(), response.message());
                }
                log.error(errorMessage);
                throw new BadRequestException(errorMessage);
            }
        } catch (BadRequestException e) {
            // Re-throw runtime exceptions (our custom errors)
            throw e;
        } catch (Exception e) {
            // Wrap other exceptions with context
            String errorMessage = "Error updating portal system for recruitment ID: " + recruitmentId + ". " + e.getMessage();
            log.error(errorMessage, e);
            throw new InternalServerErrorException(errorMessage, e);
        }
    }

    /**
     * Creates a PortalRegistrationDto from a TrxRecruitment entity
     *
     * @param recruitment The recruitment entity
     * @return The portal registration DTO
     */
    private PortalRegistrationDto createPortalRegistrationDto(TrxRecruitment recruitment) {
        String user = globalConfigService.getGlobalConfig("compass.user", "UAT-QX");

        PortalRegistrationDto dto = new PortalRegistrationDto();

        // Map position level to agent level
        String agentLevel = "BP"; // Default value
        if (recruitment.getPositionLevel() != null) {
            switch (recruitment.getPositionLevel()) {
                case BP:
                    agentLevel = "BP";
                    break;
                case BM:
                    agentLevel = "BM";
                    break;
                case BD:
                    agentLevel = "BD";
                    break;
                default:
                    agentLevel = "BP";
                    break;
            }
        }

        // Basic information
        dto.setAgentLevel(agentLevel);
        dto.setAgentName(recruitment.getFullName());
        dto.setAgentStatus("S"); // Active status

        // Bank information
        Bank bank = recruitment.getBank();
        if (bank != null) {
            dto.setBankName(bank.getBankCode());
        }
        dto.setBankAccountName(recruitment.getBankAccountName());
        dto.setBankAccountNo(recruitment.getBankAccountNumber());
        dto.setBankBranch(""); // Default empty
        dto.setBankCity(""); // Default empty

        // Branch information
        Branch branch = recruitment.getBranch();
        if (branch != null) {
            dto.setBranchCode(branch.getBranchCode());
        } else {
            dto.setBranchCode(recruitment.getBranchCode());
        }

        // Personal information
        dto.setCitizenship("WNI"); // Default to WNI (Indonesian citizen)
        dto.setClientNum("");
        dto.setClientStatus("");
        dto.setClientType("");
        dto.setCreby(user); // Default value
        dto.setEducation(""); // Default value
        dto.setEmail(recruitment.getEmail());

        // Format date of birth
        if (recruitment.getBirthDate() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            dto.setDateOfBirth(recruitment.getBirthDate().format(formatter));
        }

        // Emergency contact information
        dto.setEmergencyContactName(recruitment.getEmergencyContactName());
        dto.setEmergencyContactRelationship(recruitment.getEmergencyContactRelation());

        // Format emergency contact phone number: convert 08xxx to 628xxx
        String emergencyPhone = recruitment.getEmergencyContactPhone();
        if (emergencyPhone != null && emergencyPhone.startsWith("08")) {
            emergencyPhone = "+62" + emergencyPhone.substring(1); // Replace 0 with 62
        }
        dto.setEmergencyContactHP1(emergencyPhone);
        dto.setEmergencyContactHP2("");
        dto.setEmergencyContactCity(""); // Default empty
        dto.setEmergencyContactAddress(""); // Default empty
        dto.setEmergencyContactAddress2(""); // Default empty
        dto.setEmergencyContactDoB(""); // Default empty
        dto.setEmergencyContactGender(""); // Default empty
        dto.setEmergencyContactPoB(""); // Default empty
        dto.setEmergencyContactZipCode(""); // Default empty

        // Work information
        dto.setFieldOfWork(""); // Default value
        dto.setFlagLicenseSyariah(false); // Default value
        dto.setGender(recruitment.getGender().name()); // Default value, should be mapped from actual data if available
        dto.setGrossIncome(""); // Default value
        dto.setIdExpired("3000-12-31"); // Default value
        dto.setIdType("KTP/E-KTP");
        dto.setIdNumber(recruitment.getNik());
        dto.setIncompleteDocumentDate("");
        dto.setIncompleteDocumentRemark("");
        dto.setJobType(""); // Default value

        // Join date (current date)
        LocalDate now = LocalDate.now();
        dto.setJoinDate(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        // Leader and recruiter information
        dto.setRecruiterCode(recruitment.getRecruiterCode());
        dto.setLeaderCode(recruitment.getLeaderCode() != null ? recruitment.getLeaderCode() : recruitment.getRecruiterCode());

        // License information
        dto.setLicenseCategory(""); // Default value
        dto.setLicenseExpirationDate(""); // Default value
        dto.setLicenseType(""); // Default value
        dto.setNoLicense("");

        // Additional personal information
        // Map marital status according to requirements
        String maritalStatus = "S"; // Default to Single
        if (recruitment.getMaritalStatus() != null) {
            switch (recruitment.getMaritalStatus()) {
                case KAWIN:
                    maritalStatus = "M"; // Married
                    break;
                case CERAI:
                case CERAI_MATI:
                    maritalStatus = "D"; // Divorced
                    break;
                case BELUM_KAWIN:
                    maritalStatus = "S"; // Single
                    break;
                default:
                    maritalStatus = "S"; // Default to Single
                    break;
            }
        }
        dto.setMaritalStatus(maritalStatus);

        // Format phone number: ensure +62 prefix for mobile phones
        String phoneNumber = formatPhoneNumber(recruitment.getPhoneNumber());
        dto.setMobilePhone("+" + phoneNumber);
        dto.setMobilePhone2("+" + phoneNumber); // Same as primary phone

        // Tax information
        dto.setNpwp(""); // Default value
        dto.setNpwpaddress(""); // Default value
        dto.setNpwpcity(""); // Default value
        dto.setNpwpname("");
        dto.setNpwpzipCode(""); // Default value

        // Occupation information
        dto.setOccupation(recruitment.getOccupationCode()); // Default value
        dto.setPlaceOfBirth(recruitment.getBirthPlace());
        dto.setPosition(""); // Default value
        dto.setPositionLevel(recruitment.getPositionLevel().name()); // Default value
        dto.setPtkpcode("-"); // Default value

        // Registration information
        dto.setRegistrationDate(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setReligion(""); // Default value
        dto.setSalesChannel(recruitment.getChannel() == Channel.AGE ? DistributionCode.A.name() : DistributionCode.L.name()); // Default value
        dto.setSmsInfo("Y"); // Default value
        dto.setSourceOfIncome(""); // Default value
        dto.setStatusName("noLicense"); // Default value

        // Address information
        List<PortalRegistrationDto.Address> addresses = new ArrayList<>();
        PortalRegistrationDto.Address domicileAddress = new PortalRegistrationDto.Address();
        domicileAddress.setAddressType("H"); // Home address
        domicileAddress.setActiveAddress("true"); // Active address

        // Set domicile address details
        domicileAddress.setAddress1(recruitment.getDomicileAddress() != null ? recruitment.getDomicileAddress() : ""); // Full address

        // Build RT/RW with null checks
        String domRtRw = "";
        if (recruitment.getDomicileRt() != null && recruitment.getDomicileRw() != null) {
            domRtRw = "RT " + recruitment.getDomicileRt() + " RW " + recruitment.getDomicileRw();
        } else if (recruitment.getDomicileRt() != null) {
            domRtRw = "RT " + recruitment.getDomicileRt();
        } else if (recruitment.getDomicileRw() != null) {
            domRtRw = "RW " + recruitment.getDomicileRw();
        }
        domicileAddress.setAddress2(domRtRw);

        domicileAddress.setAddress3(recruitment.getDomicileSubDistrict() != null ? recruitment.getDomicileSubDistrict() : ""); // Sub district
        domicileAddress.setAddress4(recruitment.getDomicileDistrict() != null ? recruitment.getDomicileDistrict() : ""); // District
        domicileAddress.setCityCode(recruitment.getDomicileCity() != null ? recruitment.getDomicileCity() : ""); // City
        domicileAddress.setRegion(recruitment.getDomicileProvince() != null ? recruitment.getDomicileProvince() : ""); // Province
        domicileAddress.setCountry("INDONESIA");

        // Set phone information (null values)
        domicileAddress.setPhone1("");
        domicileAddress.setPhone2("");
        domicileAddress.setPhone3("");
        domicileAddress.setFax1("");
        domicileAddress.setFax2("");
        domicileAddress.setFax3("");

        addresses.add(domicileAddress);

        dto.setAddresses(addresses);

        return dto;
    }

    /**
     * Creates an Agent record from the approved recruitment data
     *
     * @param recruitment The approved recruitment record
     * @param agentCode   The agent code received from the portal system
     */
    private AgentDto createAgentFromRecruitment(TrxRecruitment recruitment, String agentCode) {
        log.info("Creating agent record for recruitment ID: {} with agent code: {}", recruitment.getId(), agentCode);

        try {
            // Check if agent already exists with this code
            if (agentRepository.findTopByAgentCode(agentCode).isPresent()) {
                log.warn("Agent with code {} already exists. Skipping agent creation.", agentCode);
                return null;
            }

            // Create user for the agent
            User user = new User();
            user.setIsAgent(Boolean.TRUE);
            user.setChannel(recruitment.getChannel());
            user.setUsername(agentCode); // Use agent code as username
            user.setEmail(recruitment.getEmail());
            user.setPhone(recruitment.getPhoneNumber());
            user.setName(recruitment.getFullName());
            user.setStatus(User.Status.Active);
            user.setTimezone("Asia/Jakarta");
            user.setUserType(UserType.AGENT);
            user.setPicture(recruitment.getSelfiePhoto());

            // Determine role based on position level
            String roleCode = "ROLE_" + recruitment.getChannel().name() + "_";
            if (recruitment.getPositionLevel() != null) {
                roleCode += recruitment.getPositionLevel().name();
            } else {
                roleCode += "BP"; // Default to BP if position level is not set
            }

            Role role = roleRepository.findByCode(roleCode);
            if (role != null) {
                Set<Role> roles = new HashSet<>();
                roles.add(role);
                user.setRoles(roles);
            } else {
                log.warn("Role with code {} not found. Using default role.", roleCode);
                // Try to find a default role
                role = roleRepository.findByCode("ROLE_" + recruitment.getChannel().name() + "_BP");
                if (role != null) {
                    Set<Role> roles = new HashSet<>();
                    roles.add(role);
                    user.setRoles(roles);
                }
            }

            // Add branch to user if available
            if (recruitment.getBranch() != null) {
                // Fetch the branch from the repository to ensure it's in a managed state
                Branch managedBranch = branchRepository.findByBranchCode(recruitment.getBranch().getBranchCode())
                        .orElse(null);

                if (managedBranch != null) {
                    Set<Branch> branches = new HashSet<>();
                    branches.add(managedBranch);
                    user.setBranches(branches);
                } else {
                    log.warn("Branch with ID {} not found in repository. Skipping branch assignment for user.",
                            recruitment.getBranch().getId());
                }
            }

            // Create agent
            Agent agent = new Agent();
            agent.setUser(user);
            agent.setAgentCode(agentCode);
            agent.setAgentName(recruitment.getFullName());
            agent.setDistributionCode(DistributionCode.A); // Default to A (Agency)
            agent.setLevel(recruitment.getPositionLevel() != null ? recruitment.getPositionLevel().name() : "BP");
            agent.setPositionLevel(recruitment.getPositionLevel() != null ? recruitment.getPositionLevel().name() : "BP");
            agent.setLeaderCode(recruitment.getLeaderCode());
            agent.setRecruiterCode(recruitment.getRecruiterCode());
            agent.setStatus(AgentStatus.A); // Active status
            agent.setChannel(recruitment.getChannel());
            agent.setEmail(recruitment.getEmail());
            agent.setAreaCode(recruitment.getBranch().getAreaCode());
            agent.setAreaName(recruitment.getBranch().getAreaName());
            agent.setSubRegionCode(recruitment.getBranch().getSubRegionCode());
            agent.setSubRegionName(recruitment.getBranch().getSubRegionName());
            agent.setRegionCode(recruitment.getBranch().getRegionCode());
            agent.setRegionName(recruitment.getBranch().getRegionName());
            agent.setRoleName("Agent");
            agent.setGender(recruitment.getGender().name());
            if (recruitment.getLeaderCode() != null) {
                Agent leader = agentRepository.findTopByAgentCode(recruitment.getRecruiterCode()).orElse(null);
                agent.setLeader(leader);
            }
            // Format phone number: ensure +62 prefix for mobile phones
            String phoneNumber = formatPhoneNumber(recruitment.getPhoneNumber());
            agent.setPhoneNumber(phoneNumber);
            agent.setDob(recruitment.getBirthDate());

            // Map marital status according to requirements
            String maritalStatus = null;
            if (recruitment.getMaritalStatus() != null) {
                switch (recruitment.getMaritalStatus()) {
                    case KAWIN:
                        maritalStatus = "M"; // Married
                        break;
                    case CERAI:
                    case CERAI_MATI:
                        maritalStatus = "D"; // Divorced
                        break;
                    case BELUM_KAWIN:
                        maritalStatus = "S"; // Single
                        break;
                    default:
                        maritalStatus = "S"; // Default to Single
                        break;
                }
                agent.setMaritalStatus(maritalStatus);
            }

            // Set branch information if available
            if (recruitment.getBranch() != null) {
                agent.setBranchCode(recruitment.getBranch().getBranchCode());
                agent.setBranchName(recruitment.getBranch().getBranchName());
            } else if (recruitment.getBranchCode() != null) {
                agent.setBranchCode(recruitment.getBranchCode());
            }

            // Set bank information if available
            if (recruitment.getBank() != null) {
                agent.setBank(recruitment.getBank().getBankName());
            }
            agent.setBankAccountNumber(recruitment.getBankAccountNumber());

            // Set address information - use domicile address if available, otherwise use KTP address
            StringBuilder addressBuilder = new StringBuilder();

            // Determine which address to use as primary
            boolean useDomicileAddress = recruitment.getIsDomicileSameAsKtp() != null &&
                    !recruitment.getIsDomicileSameAsKtp() &&
                    recruitment.getDomicileAddress() != null;

            // Build the primary address string
            if (useDomicileAddress) {
                // Use domicile address
                if (recruitment.getDomicileAddress() != null) {
                    addressBuilder.append(recruitment.getDomicileAddress());
                }
                if (recruitment.getDomicileRt() != null && recruitment.getDomicileRw() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append("RT ").append(recruitment.getDomicileRt())
                            .append(" RW ").append(recruitment.getDomicileRw());
                }
                if (recruitment.getDomicileSubDistrict() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(recruitment.getDomicileSubDistrict());
                }
                if (recruitment.getDomicileDistrict() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(recruitment.getDomicileDistrict());
                }
                if (recruitment.getDomicileCity() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(recruitment.getDomicileCity());
                }
                if (recruitment.getDomicileProvince() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(recruitment.getDomicileProvince());
                }
            } else {
                // Use KTP address
                if (recruitment.getKtpAddress() != null) {
                    addressBuilder.append(recruitment.getKtpAddress());
                }
                if (recruitment.getKtpRt() != null && recruitment.getKtpRw() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append("RT ").append(recruitment.getKtpRt())
                            .append(" RW ").append(recruitment.getKtpRw());
                }
                if (recruitment.getKtpSubDistrict() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(recruitment.getKtpSubDistrict());
                }
                if (recruitment.getKtpDistrict() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(recruitment.getKtpDistrict());
                }
                if (recruitment.getKtpCity() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(recruitment.getKtpCity());
                }
                if (recruitment.getKtpProvince() != null) {
                    if (addressBuilder.length() > 0) addressBuilder.append(", ");
                    addressBuilder.append(recruitment.getKtpProvince());
                }
            }

            // Set the address in the agent record
            agent.setAddress(addressBuilder.toString());

            // Set photos if available
            agent.setKtpAttachment(recruitment.getKtpPhoto());
            agent.setPhoto(recruitment.getPassPhoto());

            // Save the user and agent
            agentRepository.save(agent);
            log.info("Successfully created agent with code: {} for recruitment ID: {}", agentCode, recruitment.getId());
            return AgentDto.of(AgentDto.class, agent);
        } catch (Exception e) {
            log.error("Error creating agent from recruitment ID: {}", recruitment.getId(), e);
        }
        return null;
    }

    /**
     * Formats phone number to ensure +62 prefix for Indonesian mobile phones
     *
     * @param phoneNumber The original phone number
     * @return Formatted phone number with 62 prefix (without +)
     */
    private String formatPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return phoneNumber;
        }

        // Remove any spaces, dashes, or other non-numeric characters except +
        String cleanNumber = phoneNumber.replaceAll("[^0-9+]", "");

        // Remove leading + if present
        if (cleanNumber.startsWith("+")) {
            cleanNumber = cleanNumber.substring(1);
        }

        // Handle different cases:
        // 1. Already starts with 628 -> keep as is
        if (cleanNumber.startsWith("628")) {
            return cleanNumber;
        }

        // 2. Starts with 08 -> convert to 628
        if (cleanNumber.startsWith("08")) {
            return "62" + cleanNumber.substring(1);
        }

        // 3. Starts with 62 but not 628 -> keep as is (might be other Indonesian numbers)
        if (cleanNumber.startsWith("62")) {
            return cleanNumber;
        }

        // 4. Starts with 8 (without leading 0) -> add 62
        if (cleanNumber.startsWith("8")) {
            return "62" + cleanNumber;
        }

        // 5. For any other number that doesn't start with 62, 08, or 8 -> force add 62
        // This handles cases like local numbers or other formats
        return "62" + cleanNumber;
    }

    /**
     * Sends notification to CAS Lisensi users when agent registration is completed
     *
     * @param recruitment The recruitment data
     * @param agent       The created agent DTO
     */
    private void sendCasLisensiNotification(TrxRecruitment recruitment, AgentDto agent) {
        try {
            // Determine CAS Lisensi role based on recruitment channel
            String casLisensiRole = "ROLE_CAS_LISENSI_REVIEW_" + recruitment.getChannel().name();

            // Find CAS Lisensi users with the appropriate role
            List<User> casLisensiUsers = userRepository.findUsersByRoleCode(casLisensiRole);
            if (casLisensiUsers.isEmpty()) {
                log.warn("No CAS Lisensi users found with role: {}", casLisensiRole);
                return;
            }

            // Create notification
            NotificationDto notification = new NotificationDto();
            notification.setTitle("CAS Lisensi - Agen Baru Terdaftar");
            notification.setBody(String.format(
                    "Agen %s telah mendapatkan kode agen %s, silahkan lakukan pengarahan ujian.",
                    recruitment.getFullName(),
                    agent.getAgentCode()
            ));
            notification.setData(Map.of(
                    "trxId", recruitment.getId().toString(),
                    "trxType", "RECRUITMENT",
                    "agentCode", agent.getAgentCode(),
                    "candidateName", recruitment.getFullName()
            ));
            notification.setInboxType(InboxType.NOTIFICATION);

            // Send notification to all CAS Lisensi users
            firebaseService.sendNotification(casLisensiUsers, notification);

            log.info("CAS Lisensi notification sent to {} users for agent: {} ({})",
                    casLisensiUsers.size(), agent.getAgentCode(), recruitment.getFullName());
        } catch (Exception e) {
            log.error("Error sending CAS Lisensi notification for recruitment ID: {}", recruitment.getId(), e);
        }
    }

    /**
     * Builds a detailed error message from portal API response including field validation errors
     * Formats the message in Indonesian with proper line breaks for field errors
     *
     * @param recruitmentId The recruitment ID
     * @param responseBody  The portal API response body
     * @return Formatted error message with field errors in Indonesian format
     */
    private String buildPortalErrorMessage(Long recruitmentId, PortalRegistrationResponseDto responseBody) {
        StringBuilder errorMessage = new StringBuilder();

        // Start with Indonesian error message
        errorMessage.append("Pengajuan gagal dilakukan dengan error :");

        // Add field validation errors if present
        if (responseBody != null && responseBody.getFieldsError() != null && !responseBody.getFieldsError().isEmpty()) {
            for (String fieldError : responseBody.getFieldsError()) {
                errorMessage.append("\n").append(fieldError);
            }
        } else {
            // If no field errors, show the general message
            String generalMessage = responseBody != null ? responseBody.getMessage() : "Unknown error";
            errorMessage.append("\n").append(generalMessage);
        }

        // Add closing message
        errorMessage.append("\nMohon perbaiki data dan/atau klik simpan kembali");

        return errorMessage.toString();
    }
}
