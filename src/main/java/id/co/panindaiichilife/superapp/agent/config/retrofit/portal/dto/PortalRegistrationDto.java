package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalRegistrationDto {
    @SerializedName("agentLevel")
    @JsonProperty("agentLevel")
    private String agentLevel;

    @SerializedName("agentName")
    @JsonProperty("agentName")
    private String agentName;

    @SerializedName("agentStatus")
    @JsonProperty("agentStatus")
    private String agentStatus;

    @SerializedName("bankAccountName")
    @JsonProperty("bankAccountName")
    private String bankAccountName;

    @SerializedName("bankAccountNo")
    @JsonProperty("bankAccountNo")
    private String bankAccountNo;

    @SerializedName("bankBranch")
    @JsonProperty("bankBranch")
    private String bankBranch;

    @SerializedName("bankCity")
    @JsonProperty("bankCity")
    private String bankCity;

    @SerializedName("bankName")
    @JsonProperty("bankName")
    private String bankName;

    @SerializedName("branchCode")
    @JsonProperty("branchCode")
    private String branchCode;

    @SerializedName("citizenship")
    @JsonProperty("citizenship")
    private String citizenship;

    @SerializedName("clientNum")
    @JsonProperty("clientNum")
    private String clientNum;

    @SerializedName("clientStatus")
    @JsonProperty("clientStatus")
    private String clientStatus;

    @SerializedName("clientType")
    @JsonProperty("clientType")
    private String clientType;

    @SerializedName("creby")
    @JsonProperty("creby")
    private String creby;

    @SerializedName("education")
    @JsonProperty("education")
    private String education;

    @SerializedName("email")
    @JsonProperty("email")
    private String email;

    @SerializedName("dateOfBirth")
    @JsonProperty("dateOfBirth")
    private String dateOfBirth;

    @SerializedName("emergencyContactCity")
    @JsonProperty("emergencyContactCity")
    private String emergencyContactCity;

    @SerializedName("emergencyContactAddress")
    @JsonProperty("emergencyContactAddress")
    private String emergencyContactAddress;

    @SerializedName("emergencyContactAddress2")
    @JsonProperty("emergencyContactAddress2")
    private String emergencyContactAddress2;

    @SerializedName("emergencyContactDoB")
    @JsonProperty("emergencyContactDoB")
    private String emergencyContactDoB;

    @SerializedName("emergencyContactGender")
    @JsonProperty("emergencyContactGender")
    private String emergencyContactGender;

    @SerializedName("emergencyContactHP1")
    @JsonProperty("emergencyContactHP1")
    private String emergencyContactHP1;

    @SerializedName("emergencyContactHP2")
    @JsonProperty("emergencyContactHP2")
    private String emergencyContactHP2;

    @SerializedName("emergencyContactName")
    @JsonProperty("emergencyContactName")
    private String emergencyContactName;

    @SerializedName("emergencyContactPoB")
    @JsonProperty("emergencyContactPoB")
    private String emergencyContactPoB;

    @SerializedName("emergencyContactRelationship")
    @JsonProperty("emergencyContactRelationship")
    private String emergencyContactRelationship;

    @SerializedName("emergencyContactZipCode")
    @JsonProperty("emergencyContactZipCode")
    private String emergencyContactZipCode;

    @SerializedName("fieldOfWork")
    @JsonProperty("fieldOfWork")
    private String fieldOfWork;

    @SerializedName("flagLicenseSyariah")
    @JsonProperty("flagLicenseSyariah")
    private Boolean flagLicenseSyariah;

    @SerializedName("gender")
    @JsonProperty("gender")
    private String gender;

    @SerializedName("grossIncome")
    @JsonProperty("grossIncome")
    private String grossIncome;

    @SerializedName("idExpired")
    @JsonProperty("idExpired")
    private String idExpired;

    @SerializedName("idType")
    @JsonProperty("idType")
    private String idType;

    @SerializedName("idNumber")
    @JsonProperty("idNumber")
    private String idNumber;

    @SerializedName("incompleteDocumentDate")
    @JsonProperty("incompleteDocumentDate")
    private String incompleteDocumentDate;

    @SerializedName("incompleteDocumentRemark")
    @JsonProperty("incompleteDocumentRemark")
    private String incompleteDocumentRemark;

    @SerializedName("jobType")
    @JsonProperty("jobType")
    private String jobType;

    @SerializedName("joinDate")
    @JsonProperty("joinDate")
    private String joinDate;

    @SerializedName("leaderCode")
    @JsonProperty("leaderCode")
    private String leaderCode;

    @SerializedName("licenseCategory")
    @JsonProperty("licenseCategory")
    private String licenseCategory;

    @SerializedName("licenseExpirationDate")
    @JsonProperty("licenseExpirationDate")
    private String licenseExpirationDate;

    @SerializedName("licenseType")
    @JsonProperty("licenseType")
    private String licenseType;

    @SerializedName("maritalStatus")
    @JsonProperty("maritalStatus")
    private String maritalStatus;

    @SerializedName("mobilePhone")
    @JsonProperty("mobilePhone")
    private String mobilePhone;

    @SerializedName("mobilePhone2")
    @JsonProperty("mobilePhone2")
    private String mobilePhone2;

    @SerializedName("noLicense")
    @JsonProperty("noLicense")
    private String noLicense;

    @SerializedName("npwp")
    @JsonProperty("npwp")
    private String npwp;

    @SerializedName("npwpaddress")
    @JsonProperty("npwpaddress")
    private String npwpaddress;

    @SerializedName("npwpcity")
    @JsonProperty("npwpcity")
    private String npwpcity;

    @SerializedName("npwpname")
    @JsonProperty("npwpname")
    private String npwpname;

    @SerializedName("npwpzipCode")
    @JsonProperty("npwpzipCode")
    private String npwpzipCode;

    @SerializedName("occupation")
    @JsonProperty("occupation")
    private String occupation;

    @SerializedName("placeOfBirth")
    @JsonProperty("placeOfBirth")
    private String placeOfBirth;

    @SerializedName("position")
    @JsonProperty("position")
    private String position;

    @SerializedName("positionLevel")
    @JsonProperty("positionLevel")
    private String positionLevel;

    @SerializedName("ptkpcode")
    @JsonProperty("ptkpcode")
    private String ptkpcode;

    @SerializedName("recruiterCode")
    @JsonProperty("recruiterCode")
    private String recruiterCode;

    @SerializedName("registrationDate")
    @JsonProperty("registrationDate")
    private String registrationDate;

    @SerializedName("religion")
    @JsonProperty("religion")
    private String religion;

    @SerializedName("salesChannel")
    @JsonProperty("salesChannel")
    private String salesChannel;

    @SerializedName("smsInfo")
    @JsonProperty("smsInfo")
    private String smsInfo;

    @SerializedName("sourceOfIncome")
    @JsonProperty("sourceOfIncome")
    private String sourceOfIncome;

    @SerializedName("statusName")
    @JsonProperty("statusName")
    private String statusName;

    @SerializedName("addresses")
    @JsonProperty("addresses")
    private List<Address> addresses;

    @Data
    public static class Address {
        @SerializedName("activeAddress")
        @JsonProperty("activeAddress")
        private String activeAddress;

        @SerializedName("address1")
        @JsonProperty("address1")
        private String address1;

        @SerializedName("address2")
        @JsonProperty("address2")
        private String address2;

        @SerializedName("address3")
        @JsonProperty("address3")
        private String address3;

        @SerializedName("address4")
        @JsonProperty("address4")
        private String address4;

        @SerializedName("addressType")
        @JsonProperty("addressType")
        private String addressType;

        @SerializedName("cityCode")
        @JsonProperty("cityCode")
        private String cityCode;

        @SerializedName("country")
        @JsonProperty("country")
        private String country;

        @SerializedName("fax1")
        @JsonProperty("fax1")
        private String fax1;

        @SerializedName("fax2")
        @JsonProperty("fax2")
        private String fax2;

        @SerializedName("fax3")
        @JsonProperty("fax3")
        private String fax3;

        @SerializedName("phone1")
        @JsonProperty("phone1")
        private String phone1;

        @SerializedName("phone2")
        @JsonProperty("phone2")
        private String phone2;

        @SerializedName("phone3")
        @JsonProperty("phone3")
        private String phone3;

        @SerializedName("region")
        @JsonProperty("region")
        private String region;
    }
}
