package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.Persistency;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface PersistencyRepository extends BaseRepository<Persistency, Long> {

    // Find by agent code
    Persistency findByAgentCode(String agentCode);

    // Find by branch code
    List<Persistency> findByBranchCode(String branchCode);

    // Find by main branch code
    List<Persistency> findByMainBranchCodeAndType(String mainBranchCode, String type);

    // Find by BDM code
    List<Persistency> findByBdmCode(String bdmCode);

    // Find by HOS code
    List<Persistency> findByHosCode(String hosCode);

    // Find team persistency records by agent codes
    List<Persistency> findByAgentCodeInAndType(List<String> agentCodes, String type);

    // Find persistency records by agent code and type
    List<Persistency> findByAgentCodeAndType(String agentCode, String type);

    @Modifying
    @Transactional
    @Query("DELETE FROM Persistency p WHERE p.year = :year")
    void deleteByYear(@Param("year") Integer year);

    long countByYear(Integer year);
}
