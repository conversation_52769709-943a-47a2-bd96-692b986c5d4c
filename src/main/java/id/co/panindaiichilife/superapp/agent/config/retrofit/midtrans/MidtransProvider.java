package id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans;


import id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.dto.MidtransChargeDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.dto.MidtransChargeResponseDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.dto.MidtransTransactionStatusDto;
import retrofit2.Call;
import retrofit2.http.*;

public interface MidtransProvider {
	
    @POST("v1/transactions")
    @Headers("X-Site: Notify")
    Call<MidtransChargeResponseDto> charge(@Body MidtransChargeDto request);

    @GET("v1/{transactionId}/status")
    Call<MidtransTransactionStatusDto> checkStatus(@Path(value = "transactionId") String transactionId);
    
}
