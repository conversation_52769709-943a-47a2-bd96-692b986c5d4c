package id.co.panindaiichilife.superapp.agent.core.data.batch;

import id.co.panindaiichilife.superapp.agent.core.support.AnnotatedBeanUtils;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CsvColumn;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.springframework.batch.item.file.transform.FieldExtractor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.NumberFormat;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.Temporal;
import java.util.List;

@Slf4j
public class AnnotatedBeanFieldExtractor<T> implements FieldExtractor<T> {

    @Override
    public Object[] extract(T item) {
        List<Field> fields = AnnotatedBeanUtils.getExportedFields(item.getClass());

        Object[] objects = new Object[fields.size()];
        for (int i = 0; i < fields.size(); i++) {
            Field field = fields.get(i);
            objects[i] = resolveFieldValue(field, item);

            CsvColumn csvColumn = field.getAnnotation(CsvColumn.class);
            if (objects[i] != null && csvColumn != null && csvColumn.preserveValue()) {
                switch (csvColumn.preserveStrategy()) {
                    case FORMULA:
                        objects[i] = "=\"" + objects[i] + "\"";
                        break;
                    case TAB:
                        objects[i] += "\t";
                        break;
                }
            }
        }

        return objects;
    }

    private String resolveFieldValue(Field field, T item) {
        try {
            Object value = PropertyUtils.getProperty(item, field.getName());

            if (value == null) {
                return null;
            } else if (value instanceof String) {
                return (String) value;
            } else if (value instanceof Number) {
                return formatNumeric(field, (Number) value);
            } else if (value instanceof Temporal) {
                return formatTemporal(field, (Temporal) value);
            } else {
                return value.toString();
            }
        } catch (ReflectiveOperationException ex) {
            ReflectionUtils.handleReflectionException(ex);
            return null;
        }
    }

    private String formatNumeric(Field field, Number value) {
        java.text.NumberFormat formatter;
        if (field.isAnnotationPresent(NumberFormat.class)) {
            NumberFormat numberFormat = field.getAnnotation(NumberFormat.class);
            formatter = new DecimalFormat(numberFormat.pattern());
        } else {
            formatter = new DecimalFormat("###0.###");
        }

        return formatter.format(value);
    }

    private String formatTemporal(Field field, Temporal value) {
        DateTimeFormatter formatter;
        if (field.isAnnotationPresent(DateTimeFormat.class)) {
            DateTimeFormat dateTimeFormat = field.getAnnotation(DateTimeFormat.class);
            formatter = DateTimeFormatter.ofPattern(dateTimeFormat.pattern());
        } else if (value instanceof LocalDate) {
            formatter = DateTimeFormatter.ISO_LOCAL_DATE;
        } else if (value instanceof LocalTime) {
            formatter = DateTimeFormatter.ISO_LOCAL_TIME;
        } else if (value instanceof LocalDateTime) {
            formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        } else if (value instanceof ZonedDateTime) {
            formatter = DateTimeFormatter.ISO_ZONED_DATE_TIME;
        } else if (value instanceof OffsetDateTime) {
            formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        } else if (value instanceof Instant) {
            formatter = DateTimeFormatter.ISO_INSTANT;
        } else {
            log.warn("Unsupported temporal type: {}", value.getClass());
            return value.toString();
        }

        return formatter.format(value);
    }
}
