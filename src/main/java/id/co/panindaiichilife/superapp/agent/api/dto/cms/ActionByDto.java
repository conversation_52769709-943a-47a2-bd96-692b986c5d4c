package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.model.User;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class ActionByDto extends BaseDto<User> {

    private String username;

    private String name;

    private Channel channel;

    private String agentCode;

    private String agentLevel;

    private String picture;

    @Override
    public void copy(User data) {
        super.copy(data);

        if (null != data.getIsAgent() && data.getIsAgent()) {
            agentCode = data.getAgent().getAgentCode();
            agentLevel = data.getAgent().getLevel();
        }
    }
}
