package id.co.panindaiichilife.superapp.agent.service.kafka;

import id.co.panindaiichilife.superapp.agent.config.kafka.KafkaConfig;
import id.co.panindaiichilife.superapp.agent.model.event.AgentTerminationEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class AgentTerminationConsumerService {
    private final AgentTerminationEventHandler eventHandler;

    @KafkaListener(
            topics = KafkaConfig.TERMINATION_TOPIC,
            groupId = KafkaConfig.TERMINATION_GROUP,
            containerFactory = "terminationKafkaListenerContainerFactory")
    public void consumeTerminationEvent(AgentTerminationEvent event) {
        log.info("Received termination event: {}", event);

        try {
            eventHandler.handleEvent(event);
        } catch (Exception e) {
            log.error("Error processing termination event: {}", e.getMessage(), e);
        }
    }
}
