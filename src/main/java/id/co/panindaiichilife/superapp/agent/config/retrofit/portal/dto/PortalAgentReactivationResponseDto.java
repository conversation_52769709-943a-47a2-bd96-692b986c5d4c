package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalAgentReactivationResponseDto {
    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("agentUpdate")
    @JsonProperty("agentUpdate")
    private AgentUpdateDto agentUpdate;

    @Data
    public static class AgentUpdateDto {
        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("remark")
        @JsonProperty("remark")
        private String remark;
    }
}
