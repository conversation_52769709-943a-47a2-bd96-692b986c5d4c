package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.SignatureDto;
import id.co.panindaiichilife.superapp.agent.api.dto.pdf.ApgenAgeDocumentDto;
import id.co.panindaiichilife.superapp.agent.core.service.DocumentGenerationService;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.service.ContractDocumentService;
import id.co.panindaiichilife.superapp.agent.service.SignatureService;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRecruitmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for testing contract document generation functionality.
 * This controller provides endpoints to manually trigger contract document generation
 * for testing and debugging purposes.
 */
@RestController("contractDocumentAgencyController")
@RequestMapping("/api/agency/contract-documents")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Agency - Contract Documents", description = "Contract document agency generation endpoints")
public class ContractDocumentController {

    // Constants for repeated string literals
    private static final String ERROR_KEY = "error";
    private static final String RECRUITMENT_ID_KEY = "recruitmentId";
    private static final String AGENT_CODE_KEY = "agentCode";
    private static final String NOT_SET_VALUE = "Not set";
    private static final String NOT_GENERATED_VALUE = "Not generated";

    private final ContractDocumentService contractDocumentService;
    private final TrxRecruitmentService trxRecruitmentService;
    private final SignatureService signatureService;
    private final DocumentGenerationService documentGenerationService;

    /**
     * Generate all contract documents for a specific recruitment.
     * This endpoint is useful for testing and manual document generation.
     *
     * @param recruitmentId The ID of the recruitment
     * @return ResponseEntity with the generated document URLs
     */
    @PostMapping("/generate/{recruitmentId}")
    @Operation(summary = "Generate Contract Documents",
            description = "Generate all contract documents (PKAJ, PMKAJ, ANTI TWISTING, KODE ETIK) for a recruitment")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public ResponseEntity<Map<String, String>> generateContractDocuments(@PathVariable Long recruitmentId) {
        try {
            log.info("Manual contract document generation requested for recruitment ID: {}", recruitmentId);
            return processContractDocumentGeneration(recruitmentId);
        } catch (Exception e) {
            log.error("Error generating contract documents for recruitment ID: {}", recruitmentId, e);
            return createErrorResponse("Failed to generate contract documents: " + e.getMessage());
        }
    }

    private ResponseEntity<Map<String, String>> processContractDocumentGeneration(Long recruitmentId) {
        TrxRecruitment recruitment = trxRecruitmentService.findById(recruitmentId);
        if (recruitment == null) {
            return ResponseEntity.notFound().build();
        }

        if (isAgentCodeMissing(recruitment)) {
            return createBadRequestResponse("Agent code is required for contract document generation");
        }

        Map<String, String> documentUrls = contractDocumentService.generateAllContractDocuments(recruitment);
        log.info("Successfully generated {} contract documents for recruitment ID: {}", documentUrls.size(), recruitmentId);

        return createSuccessResponse(recruitmentId, recruitment.getAgentCode(), documentUrls);
    }

    private boolean isAgentCodeMissing(TrxRecruitment recruitment) {
        return recruitment.getAgentCode() == null || recruitment.getAgentCode().trim().isEmpty();
    }

    private ResponseEntity<Map<String, String>> createSuccessResponse(Long recruitmentId, String agentCode, Map<String, String> documentUrls) {
        Map<String, String> response = new HashMap<>();
        response.put("message", "Contract documents generated successfully");
        response.put(RECRUITMENT_ID_KEY, recruitmentId.toString());
        response.put(AGENT_CODE_KEY, agentCode);
        response.put("documentUrls", documentUrls.toString());
        return ResponseEntity.ok(response);
    }

    private ResponseEntity<Map<String, String>> createErrorResponse(String errorMessage) {
        Map<String, String> response = new HashMap<>();
        response.put(ERROR_KEY, errorMessage);
        return ResponseEntity.internalServerError().body(response);
    }

    private ResponseEntity<Map<String, String>> createBadRequestResponse(String errorMessage) {
        Map<String, String> response = new HashMap<>();
        response.put(ERROR_KEY, errorMessage);
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Get contract document information for a specific recruitment.
     *
     * @param recruitmentId The ID of the recruitment
     * @return ResponseEntity with the contract document information
     */
    @GetMapping("/info/{recruitmentId}")
    @Operation(summary = "Get Contract Document Info",
            description = "Get information about contract documents for a recruitment")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public ResponseEntity<Map<String, String>> getContractDocumentInfo(@PathVariable Long recruitmentId) {
        try {
            log.info("Contract document info requested for recruitment ID: {}", recruitmentId);
            return processContractDocumentInfo(recruitmentId);
        } catch (Exception e) {
            log.error("Error getting contract document info for recruitment ID: {}", recruitmentId, e);
            return createErrorResponse("Failed to get contract document info: " + e.getMessage());
        }
    }

    private ResponseEntity<Map<String, String>> processContractDocumentInfo(Long recruitmentId) {
        TrxRecruitment recruitment = trxRecruitmentService.findById(recruitmentId);
        if (recruitment == null) {
            return ResponseEntity.notFound().build();
        }

        Map<String, String> response = buildContractDocumentInfoResponse(recruitmentId, recruitment);
        return ResponseEntity.ok(response);
    }

    private Map<String, String> buildContractDocumentInfoResponse(Long recruitmentId, TrxRecruitment recruitment) {
        Map<String, String> response = new HashMap<>();
        response.put(RECRUITMENT_ID_KEY, recruitmentId.toString());
        response.put(AGENT_CODE_KEY, getValueOrDefault(recruitment.getAgentCode(), "Not assigned"));
        response.put("positionLevel", getPositionLevelName(recruitment));
        response.put("candidateName", getValueOrDefault(recruitment.getFullName(), NOT_SET_VALUE));

        addDocumentInfo(response, recruitment);
        response.put("pmkajRequired", String.valueOf(isPmkajRequired(recruitment)));

        return response;
    }

    private void addDocumentInfo(Map<String, String> response, TrxRecruitment recruitment) {
        response.put("apgenFile", getValueOrDefault(recruitment.getApgenFile(), NOT_GENERATED_VALUE));
        response.put("pkajFile", getValueOrDefault(recruitment.getPkajFile(), NOT_GENERATED_VALUE));
        response.put("pmkajFile", getValueOrDefault(recruitment.getPmkajFile(), NOT_GENERATED_VALUE));
        response.put("antiTwistingFile", getValueOrDefault(recruitment.getAntiTwistingFile(), NOT_GENERATED_VALUE));
        response.put("kodeEtikFile", getValueOrDefault(recruitment.getKodeEtikFile(), NOT_GENERATED_VALUE));
    }

    private String getPositionLevelName(TrxRecruitment recruitment) {
        return recruitment.getPositionLevel() != null ? recruitment.getPositionLevel().name() : NOT_SET_VALUE;
    }

    private boolean isPmkajRequired(TrxRecruitment recruitment) {
        if (recruitment.getPositionLevel() == null) {
            return false;
        }
        String levelName = recruitment.getPositionLevel().name();
        return "BM".equals(levelName) || "BD".equals(levelName);
    }

    private String getValueOrDefault(String value, String defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * Generate contract number preview for a specific recruitment.
     * This endpoint shows what the contract numbers would be without generating the actual documents.
     *
     * @param recruitmentId The ID of the recruitment
     * @return ResponseEntity with the contract number previews
     */
    @GetMapping("/preview-numbers/{recruitmentId}")
    @Operation(summary = "Preview Contract Numbers",
            description = "Preview contract numbers that would be generated for a recruitment")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'view')")
    public ResponseEntity<Map<String, String>> previewContractNumbers(@PathVariable Long recruitmentId) {
        try {
            log.info("Contract number preview requested for recruitment ID: {}", recruitmentId);
            return processContractNumberPreview(recruitmentId);
        } catch (Exception e) {
            log.error("Error previewing contract numbers for recruitment ID: {}", recruitmentId, e);
            return createErrorResponse("Failed to preview contract numbers: " + e.getMessage());
        }
    }

    private ResponseEntity<Map<String, String>> processContractNumberPreview(Long recruitmentId) {
        TrxRecruitment recruitment = trxRecruitmentService.findById(recruitmentId);
        if (recruitment == null) {
            return ResponseEntity.notFound().build();
        }

        if (isAgentCodeMissing(recruitment)) {
            return createBadRequestResponse("Agent code is required for contract number generation");
        }

        Map<String, String> response = buildContractNumberPreviewResponse(recruitmentId, recruitment);
        return ResponseEntity.ok(response);
    }

    private Map<String, String> buildContractNumberPreviewResponse(Long recruitmentId, TrxRecruitment recruitment) {
        String pkajNumber = generatePkajNumber(recruitment.getAgentCode());
        String pmkajNumber = generatePmkajNumber(recruitment.getAgentCode());
        boolean pmkajRequired = isPmkajRequired(recruitment);

        Map<String, String> response = new HashMap<>();
        response.put(RECRUITMENT_ID_KEY, recruitmentId.toString());
        response.put(AGENT_CODE_KEY, recruitment.getAgentCode());
        response.put("positionLevel", getPositionLevelName(recruitment));
        response.put("pkajNumber", pkajNumber);
        response.put("pmkajNumber", pmkajNumber);
        response.put("pmkajRequired", String.valueOf(pmkajRequired));
        response.put("note", "These are preview numbers. Actual generation may vary based on the exact generation time.");

        return response;
    }

    private String generatePkajNumber(String agentCode) {
        return String.format("%s/SPK/HO/%02d/%d",
                agentCode,
                java.time.LocalDate.now().getMonthValue(),
                java.time.LocalDate.now().getYear());
    }

    private String generatePmkajNumber(String agentCode) {
        return String.format("%s/MNGR/%02d/%d",
                agentCode,
                java.time.LocalDate.now().getMonthValue(),
                java.time.LocalDate.now().getYear());
    }

    /**
     * Test CAS signature retrieval from Signature Management module.
     * This endpoint helps verify that signature integration is working correctly.
     * The signature's documentType field can contain comma-separated values like "PKAJ,PMKAJ".
     *
     * @param channel      The channel (AGE, BAN)
     * @param documentType The document type to search for (PKAJ, PMKAJ, etc.)
     * @return ResponseEntity with the CAS signature information
     */
    @GetMapping("/test-cas-signature")
    @Operation(summary = "Test CAS Signature Retrieval",
            description = "Test CAS signature retrieval from Signature Management module. Supports comma-separated document types in signature records.")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'read')")
    public ResponseEntity<Map<String, String>> testCasSignature(@RequestParam String channel, @RequestParam String documentType) {
        try {
            log.info("Testing CAS signature retrieval for channel: {} and document type: {}", channel, documentType);
            return processCasSignatureTest(channel, documentType);
        } catch (Exception e) {
            log.error("Error testing CAS signature retrieval for channel: {} and document type: {}", channel, documentType, e);
            return createErrorResponse("Failed to test CAS signature retrieval: " + e.getMessage());
        }
    }

    private ResponseEntity<Map<String, String>> processCasSignatureTest(String channel, String documentType) {
        Channel channelEnum = validateAndConvertChannel(channel);
        if (channelEnum == null) {
            return createBadRequestResponse("Invalid channel. Valid values: AGE, BAN");
        }

        SignatureDto signatureDto = signatureService.findByChannelAndDocumentType(channelEnum, documentType);

        if (signatureDto != null) {
            return ResponseEntity.ok(buildFoundSignatureResponse(channel, documentType, signatureDto));
        } else {
            return ResponseEntity.ok(buildNotFoundSignatureResponse(channel, documentType));
        }
    }

    private Channel validateAndConvertChannel(String channel) {
        try {
            return Channel.valueOf(channel.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    private Map<String, String> buildFoundSignatureResponse(String channel, String documentType, SignatureDto signatureDto) {
        Map<String, String> response = new HashMap<>();
        response.put("found", "true");
        response.put("channel", channel);
        response.put("requestedDocumentType", documentType);
        response.put("signature", getValueOrDefault(signatureDto.getSignature(), NOT_SET_VALUE));
        response.put("paraf", getValueOrDefault(signatureDto.getParaf(), NOT_SET_VALUE));
        response.put("supportedDocumentTypes", getValueOrDefault(signatureDto.getDocumentType(), NOT_SET_VALUE));
        response.put("userName", getUserName(signatureDto));
        response.put("userId", getUserId(signatureDto));
        response.put("note", "This signature supports the following document types: " +
                getValueOrDefault(signatureDto.getDocumentType(), "None"));
        return response;
    }

    private Map<String, String> buildNotFoundSignatureResponse(String channel, String documentType) {
        Map<String, String> response = new HashMap<>();
        response.put("found", "false");
        response.put("channel", channel);
        response.put("requestedDocumentType", documentType);
        response.put("message", "No CAS signature found for the specified channel and document type");
        response.put("note", "Make sure there is a signature record with documentType containing '" +
                documentType + "' (can be comma-separated like 'PKAJ,PMKAJ')");
        return response;
    }

    private String getUserName(SignatureDto signatureDto) {
        return (signatureDto.getUser() != null && signatureDto.getUser().getName() != null) ?
                signatureDto.getUser().getName() : NOT_SET_VALUE;
    }

    private String getUserId(SignatureDto signatureDto) {
        return (signatureDto.getUser() != null) ?
                String.valueOf(signatureDto.getUser().getId()) : NOT_SET_VALUE;
    }

    /**
     * Generate APGEN-AGE document for a specific recruitment.
     * This endpoint generates the agency application form (Aplikasi Keagenan) document.
     *
     * @param recruitmentId The ID of the recruitment
     * @return ResponseEntity with the generated document information
     */
    @PostMapping("/generate-apgen/{recruitmentId}")
    @Operation(summary = "Generate APGEN-AGE Document",
            description = "Generate agency application form (Aplikasi Keagenan) document for a recruitment")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Recruitment', 'read')")
    public ResponseEntity<Object> generateApgenAgeDocument(@PathVariable Long recruitmentId) {
        try {
            log.info("APGEN-AGE document generation requested for recruitment ID: {}", recruitmentId);
            return processApgenAgeDocumentGeneration(recruitmentId);
        } catch (Exception e) {
            log.error("Error generating APGEN-AGE document for recruitment ID: {}", recruitmentId, e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put(ERROR_KEY, "Failed to generate APGEN-AGE document: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    private ResponseEntity<Object> processApgenAgeDocumentGeneration(Long recruitmentId) throws Exception {
        TrxRecruitment recruitment = trxRecruitmentService.findById(recruitmentId);
        if (recruitment == null) {
            return ResponseEntity.notFound().build();
        }

        ApgenAgeDocumentDto dto = buildApgenAgeDocumentDto(recruitment);
        byte[] pdfBytes = documentGenerationService.generateApgenAgeDocument(dto);

        log.info("Successfully generated APGEN-AGE document for recruitment ID: {}", recruitmentId);

        return ResponseEntity.ok()
                .header("Content-Type", "application/pdf")
                .header("Content-Disposition", "attachment; filename=APGEN-AGE_" + recruitmentId + ".pdf")
                .body(pdfBytes);
    }

    private ApgenAgeDocumentDto buildApgenAgeDocumentDto(TrxRecruitment recruitment) {
        return ApgenAgeDocumentDto.builder()
                // Header Information
                .distributionChannel(getChannelName(recruitment))
                .groupBD("")
                .salesOfficeGA("")
                .recruiterAgent(getRecruiterName(recruitment))
                .agentCode(getValueOrDefault(recruitment.getAgentCode(), ""))
                .agentLevel(getPositionLevelName(recruitment))

                // Personal Information
                .fullName(getValueOrDefault(recruitment.getFullName(), ""))
                .birthPlace(getValueOrDefault(recruitment.getBirthPlace(), ""))
                .birthDate(getBirthDateString(recruitment))
                .gender(getGenderName(recruitment))
                .ktpNumber(getValueOrDefault(recruitment.getNik(), ""))
                .maritalStatus(getMaritalStatusName(recruitment))
                .homeAddress(getValueOrDefault(recruitment.getDomicileAddress(), ""))
                .city(getValueOrDefault(recruitment.getDomicileCity(), ""))
                .postalCode("")
                .homePhone(getValueOrDefault(recruitment.getPhoneNumber(), ""))
                .mobilePhone(getValueOrDefault(recruitment.getPhoneNumber(), ""))
                .faxNumber("")
                .email(getValueOrDefault(recruitment.getEmail(), ""))
                .numberOfDependents("")
                .occupation(getValueOrDefault(recruitment.getOccupation(), ""))
                .npwpNumber("")
                .incomeSource("")
                .npkpAddress("")

                // Education Information
                .lastEducation("")
                .educationCity("")
                .educationPostalCode("")

                // Insurance Experience
                .hasInsuranceExperience("")

                // Family Information
                .fatherName("")
                .motherName("")
                .spouseName("")
                .fatherBirthDate("")
                .motherBirthDate("")
                .spouseBirthDate("")

                // Banking Information
                .bankName(getBankName(recruitment))
                .accountNumber(getValueOrDefault(recruitment.getBankAccountNumber(), ""))
                .accountHolderName(getValueOrDefault(recruitment.getBankAccountName(), ""))
                .branchLocation("")

                // Signature Information
                .candidateSignature(getValueOrDefault(recruitment.getSignature(), ""))
                .candidateParaf(getValueOrDefault(recruitment.getParaf(), ""))
                .casSignature("")
                .casParaf("")
                .casName("")
                .casRole("")

                // Additional Information
                .applicationDate("")
                .applicationLocation("")
                .build();
    }

    private String getChannelName(TrxRecruitment recruitment) {
        return recruitment.getChannel() != null ? recruitment.getChannel().name() : "";
    }

    private String getRecruiterName(TrxRecruitment recruitment) {
        return (recruitment.getRecruiter() != null) ? recruitment.getRecruiter().getName() : "";
    }

    private String getBirthDateString(TrxRecruitment recruitment) {
        return recruitment.getBirthDate() != null ? recruitment.getBirthDate().toString() : "";
    }

    private String getGenderName(TrxRecruitment recruitment) {
        return recruitment.getGender() != null ? recruitment.getGender().name() : "";
    }

    private String getMaritalStatusName(TrxRecruitment recruitment) {
        return recruitment.getMaritalStatus() != null ? recruitment.getMaritalStatus().name() : "";
    }

    private String getBankName(TrxRecruitment recruitment) {
        return (recruitment.getBank() != null) ? recruitment.getBank().getBankName() : "";
    }
}
