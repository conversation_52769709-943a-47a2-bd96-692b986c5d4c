package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalAgentInfoResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("agentInfo")
    @JsonProperty("agentInfo")
    private AgentInfoDto agentInfo;


    @Data
    public static class AgentInfoDto {
        @SerializedName("address1")
        @JsonProperty("address1")
        private String address1;

        @SerializedName("address2")
        @JsonProperty("address2")
        private String address2;

        @SerializedName("address3")
        @JsonProperty("address3")
        private String address3;

        @SerializedName("phone")
        @JsonProperty("phone")
        private String phone;

        @SerializedName("email")
        @JsonProperty("email")
        private String email;

        @SerializedName("name")
        @JsonProperty("name")
        private String name;

        @SerializedName("bankName")
        @JsonProperty("bankName")
        private String bankName;

        @SerializedName("bankAccountNo")
        @JsonProperty("bankAccountNo")
        private String bankAccountNo;

        @SerializedName("maritalStatus")
        @JsonProperty("maritalStatus")
        private String maritalStatus;
    }
}
