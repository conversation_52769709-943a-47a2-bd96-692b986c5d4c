package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.ClaimDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ClaimFilter;
import id.co.panindaiichilife.superapp.agent.service.widget.ClaimService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.time.LocalDate;
import java.util.List;

@RestController("widgetClaimController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class ClaimController {

    private final ClaimService claimService;

    @Operation(summary = "Get widget Claim Tracking")
    @GetMapping(value = "claim-tracking")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ClaimTracking', 'view')")
    public List<ClaimDto> getClaimTracking(Principal principal,
                                           @ParameterObject @ModelAttribute("filter") ClaimFilter filter) {

        // Set date range from filter or default to 1 year from now
        LocalDate now = LocalDate.now();
        if (filter.getStartDate() == null) {
            filter.setStartDate(now.minusYears(1));
        }
        // Use endDate from filter if provided, otherwise default to today
        if (filter.getEndDate() == null) {
            filter.setEndDate(now);
        }

        // Log the status filter for debugging
        if (filter.getStatusGroup() != null) {
            log.info("Filtering claims by status group: {}", filter.getStatusGroup());
        } else if (filter.getStatus() != null && !filter.getStatus().isEmpty()) {
            log.info("Filtering claims by status string: {}", filter.getStatus());
        }

        return claimService.getClaimList(principal.getName(), filter);
    }
}
