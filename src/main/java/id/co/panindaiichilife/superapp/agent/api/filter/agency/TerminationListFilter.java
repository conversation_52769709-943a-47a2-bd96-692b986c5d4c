package id.co.panindaiichilife.superapp.agent.api.filter.agency;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TerminationListFilter {
    private String searchQuery = "";

    private List<TrxStatus> trxStatuses;
}
