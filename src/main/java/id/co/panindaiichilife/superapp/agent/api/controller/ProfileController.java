package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.TrxEditProfileDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.AgentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.SimpleAgentV2Dto;
import id.co.panindaiichilife.superapp.agent.api.filter.TrxEditProfileFilter;
import id.co.panindaiichilife.superapp.agent.api.form.ChangePasswordForm;
import id.co.panindaiichilife.superapp.agent.api.form.ProfileAgentForm;
import id.co.panindaiichilife.superapp.agent.api.validation.ChangePasswordValidator;
import id.co.panindaiichilife.superapp.agent.api.validation.ProfileAgentValidator;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.service.AgentService;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;

@RestController("apiProfileController")
@RequestMapping("/api/profile")
@Tag(name = "Profile", description = "API Profile Agent")
@Slf4j
@RequiredArgsConstructor
public class ProfileController {

    private final AgentService agentService;

    private final ProfileAgentValidator profileAgentValidator;

    private final ChangePasswordValidator changePasswordValidator;

    private final UserService userService;

    @Operation(summary = "View current agent information")
    @GetMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'view')")
    public Object getProfile(Principal principal) {
        return agentService.getProfileV2(principal.getName());
    }

    @Operation(summary = "View current agent information by agent code")
    @GetMapping(value = "simple/{agentCode}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'view')")
    public SimpleAgentV2Dto getProfileByAgentCode(Principal principal, @PathVariable String agentCode) {
        return agentService.getSimpleProfile(agentCode);
    }


    @Operation(summary = "Request Update Profile Agent Information")
    @PatchMapping(value = "request")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'edit')")
    public void requestUpdate(Principal principal, @Valid @RequestBody ProfileAgentForm profileAgentForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        profileAgentValidator.validate(profileAgentForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        agentService.requestEditProfile(principal.getName(), profileAgentForm);
    }

    @Operation(summary = "Revise Request Update Profile Agent Information")
    @PatchMapping(value = "request/revise/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'revise')")
    public void reviseUpdate(@PathVariable Long id, Principal principal, @Valid @RequestBody ProfileAgentForm profileAgentForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        profileAgentValidator.validate(profileAgentForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        agentService.reviseEditProfile(principal.getName(), id, profileAgentForm);
    }

    @Operation(summary = "Cancel Request Update Profile Agent Information")
    @PostMapping(value = "request/cancel/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'cancel')")
    public void cancelRequestUpdate(Principal principal, @PathVariable Long id) {
        agentService.cancelEditProfile(principal.getName(), id);
    }

    @Operation(summary = "List request update profile agents")
    @GetMapping("request")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'edit')")
    public Page<TrxEditProfileDto> findMyRequestEditProfile(Principal principal, @ParameterObject @ModelAttribute("filter") TrxEditProfileFilter filter,
                                                            @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        AgentDto agent = agentService.getProfile(principal.getName());
        if (null != agent) {
            filter.setAgent(agent.getId());
        }
        return agentService.findAllEditProfile(pageable, filter);
    }

    @Operation(summary = "Change current user password")
    @PostMapping(value = "change-password")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'change-password')")
    public void changePassword(Principal principal,
                               @Valid @RequestBody ChangePasswordForm changePasswordApiForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        changePasswordValidator.validate(changePasswordApiForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        userService.changePassword(principal.getName(), changePasswordApiForm);
    }

    @Operation(summary = "Upload profile picture")
    @PostMapping(value = "profile-picture", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'upload')")
    public FileinputResponse uploadPicture(Principal principal, @RequestParam("file") MultipartFile file) {
        return userService.upload(principal.getName(), "/profile-picture/", file);
    }

    @Operation(summary = "Upload Documents")
    @PostMapping(value = "upload/document", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Profile', 'upload')")
    public FileinputResponse uploadDocument(Principal principal, @RequestParam("file") MultipartFile file) {
        return userService.upload(principal.getName(), "/document/", file);
    }
}
