package id.co.panindaiichilife.superapp.agent.core.security;

import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;

import java.io.IOException;
import java.time.Instant;

@RequiredArgsConstructor
public class AuthenticationSuccessHandlerImpl extends
        SavedRequestAwareAuthenticationSuccessHandler {

    private final UserRepository userRepository;

    @Override
    public void onAuthenticationSuccess(
            HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException {
        super.onAuthenticationSuccess(request, response, authentication);

        if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
            UserDetails ud = (UserDetails) authentication.getPrincipal();

            User user = userRepository.findByUsername(ud.getUsername()).orElseThrow(() -> new UsernameNotFoundException(ud.getUsername()));
            user.setLastLogin(Instant.now());
            userRepository.save(user);
        }
    }
}
