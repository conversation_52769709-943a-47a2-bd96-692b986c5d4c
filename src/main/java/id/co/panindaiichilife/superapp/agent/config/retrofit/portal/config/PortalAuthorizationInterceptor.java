package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.config;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

@Slf4j
public class PortalAuthorizationInterceptor implements Interceptor {

    @Value("${portal.hmac.algorithm}")
    private String hmacAlgorithm;

    @Value("${portal.uuid}")
    private String uuid;

    @Value("${portal.hmac.user}")
    private String hmacUser;

    @Value("${portal.hmac.secret}")
    private String hmacSecret;

    @Value("${portal.api-key}")
    private String portalApiKey;

    // Generate the HMAC authorization header
    private String getHMACAuthHeader(String date, String httpMethod, String path, String body) throws Exception {
        return String.format(
                "hmac username=\"%s\", algorithm=\"hmac-sha256\", headers=\"x-date request-line x-uuid digest\", signature=\"%s\"",
                hmacUser, getHMACSignature(getSigningString(date, httpMethod, path, body))
        );
    }

    // Generate the HMAC signature
    private String getHMACSignature(String signingString) throws Exception {
        Mac hmac = Mac.getInstance(hmacAlgorithm);
        SecretKeySpec secretKey = new SecretKeySpec(hmacSecret.getBytes(StandardCharsets.UTF_8), hmacAlgorithm);
        hmac.init(secretKey);
        byte[] hmacBytes = hmac.doFinal(signingString.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hmacBytes);
    }

    // Generate the signing string with dynamic path and HTTP method
    private String getSigningString(String date, String httpMethod, String path, String body) throws Exception {
        return String.format(
                "x-date: %s\n%s %s HTTP/1.1\nx-uuid: %s\ndigest: %s",
                date, httpMethod, path, uuid, getDigestBodyHeader(body)
        );
    }

    // Generate the digest header
    private String getDigestBodyHeader(String body) throws Exception {
        MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = sha256.digest(body.getBytes(StandardCharsets.UTF_8));
        String digestBase64 = Base64.getEncoder().encodeToString(hashBytes);
        return "SHA-256=" + digestBase64;
    }

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Request request = chain.request();
        log.debug("======== PORTAL INTERCEPTOR HERE ============");

        // Get the path from the request
        String queryString = request.url().encodedQuery();
        String path = request.url().encodedPath();
        String httpMethod = request.method();
        // Properly append query string if it exists
        if (queryString != null && !queryString.isEmpty()) {
            path = path + "?" + queryString;
        }

        // Current LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.now();

        // Convert to ZonedDateTime in GMT
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneId.of("GMT"));

        // Format to desired pattern
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss z");
        String xDate = zonedDateTime.format(formatter);

        // Extract the body from the request
        String body = "";
        if (request.body() != null) {
            okio.Buffer buffer = new okio.Buffer();
            request.body().writeTo(buffer);
            body = buffer.readUtf8();
        }

        try {
            // Generate values
            String digest = getDigestBodyHeader(body);
            String signingString = getSigningString(xDate, httpMethod, path, body);
            String signature = getHMACSignature(signingString);
            String authorizationHeader = getHMACAuthHeader(xDate, httpMethod, path, body);

            request = request.newBuilder()
                    .header("Accept", "application/json")
                    .header("Content-Type", "application/json")
                    .header("Digest", digest)
                    .header("X-Uuid", uuid)
                    .header("X-Date", xDate)
                    .header("Authorization", authorizationHeader)
                    .header("Api-Key", portalApiKey)
                    .build();

            log.debug("=============== request {} ==============", request.toString());
        } catch (Exception e) {
            log.error("ERROR : {}", e.toString());
        }
        return chain.proceed(request);
    }
}