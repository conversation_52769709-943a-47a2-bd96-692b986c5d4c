package id.co.panindaiichilife.superapp.agent.config.kafka;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark classes that should be registered as Kafka event types.
 * Classes annotated with @KafkaEvent will be automatically included in the type mappings
 * for Kafka serialization/deserialization.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface KafkaEvent {
    /**
     * The alias to use for this event type in Kafka type mappings.
     * If not specified, the simple class name will be used.
     * @return The alias for this event type
     */
    String value() default "";
}
