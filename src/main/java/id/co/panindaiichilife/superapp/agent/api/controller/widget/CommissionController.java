package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.CommissionDetailDto;
import id.co.panindaiichilife.superapp.agent.api.dto.widget.CommissionDto;
import id.co.panindaiichilife.superapp.agent.api.filter.CommissionFilter;
import id.co.panindaiichilife.superapp.agent.service.widget.CommissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;

@RestController("widgetCommissionController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class CommissionController {

    private final CommissionService commissionService;

    @Operation(summary = "Get Commission")
    @GetMapping(value = "commission")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.Commission', 'view')")
    public CommissionDto getCommission(Principal principal,
                                       @ParameterObject @ModelAttribute("filter") CommissionFilter filter) {
        return commissionService.getCommissionAgent(principal.getName(), filter.getYear(), filter.getMonth());
    }

    @Operation(summary = "Get Commission Detail")
    @GetMapping(value = "detail-commission")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.Commission', 'view')")
    public CommissionDetailDto getCommissionDetail(Principal principal,
                                                   @ParameterObject @ModelAttribute("filter") CommissionFilter filter) {
        return commissionService.getCommissionDetail(principal.getName(), filter.getYear(), filter.getMonth());
    }

}
