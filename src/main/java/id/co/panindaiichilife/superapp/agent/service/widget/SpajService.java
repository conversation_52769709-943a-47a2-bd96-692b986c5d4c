package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.SpajDto;
import id.co.panindaiichilife.superapp.agent.api.filter.SpajFilter;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalSpajDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalSpajResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.enums.SpajStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SpajService {

    private final UserRepository userRepository;
    private final AgentRepository agentRepository;
    private final PortalProvider portalProvider;

    @CacheableWithTTL(cacheName = "spajListCache", key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + (T(java.util.Objects).nonNull(#filter.status) && !#filter.status.isEmpty() ? T(java.lang.String).join('-', #filter.status.![name()]) : '-') + ':' + #filter.startDate + ':' + #filter.endDate + ':' + #filter.bitFilter", ttl = 1600, db = 7)
    public List<SpajDto> getSpajList(String username, SpajFilter filter) {
        // Get the agent information from the username
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // If agentCode is not provided in the filter, use the agent's code
        String agentCode = StringUtils.isBlank(filter.getAgentCode())
                ? agent.getAgentCode()
                : filter.getAgentCode();

        // Trim -D suffix from agentCode if present
        agentCode = AgentCodeUtil.trimDSuffix(agentCode);

        // Create the request DTO for the portal API
        PortalSpajDto requestDto = new PortalSpajDto();
        requestDto.setAgentCode(agentCode);

        // Set date range from filter or default to 1 year from now
        requestDto.setDateFrom(filter.getFormattedStartDate());
        requestDto.setDateTo(filter.getFormattedEndDate());
        requestDto.setStatus(91);

        // Handle null status list
        String statusFilter = "pending_uw-pending_admin-pending_checker-terbit_polis-ditolak";
        if (filter.getStatus() != null && !filter.getStatus().isEmpty()) {
            statusFilter = filter.getStatus().stream()
                    .map(SpajStatus::name)
                    .collect(Collectors.joining("-"));
        }
        requestDto.setStatusFilter(statusFilter);

        // Set default values for other required fields
        requestDto.setBitFilter(filter.getBitFilter());
        requestDto.setValFilter("");
        requestDto.setPageIndex(1);
        requestDto.setPageSize(1000); // Large page size for original method
        requestDto.setCountRow(0);
        requestDto.setSortIndex(0);
        requestDto.setSortBy(1);
        requestDto.setBrCode("");
        requestDto.setChannel(agent.getDistributionCode().name());
        requestDto.setIsSpajStatus(1);

        // Call the portal API
        Call<PortalSpajResponseDto> call = portalProvider.getSpajList(requestDto);

        try {
            Response<PortalSpajResponseDto> response = call.execute();
            if (response.isSuccessful()) {
                PortalSpajResponseDto responseDto = response.body();
                if (responseDto != null && responseDto.getSpajDatas() != null) {
                    // Convert the response data to SpajDto objects
                    return responseDto.getSpajDatas().stream()
                            .map(this::convertToSpajDto)
                            .collect(Collectors.toList());
                }
            }
            // Return empty list if response is not successful or body is null
            return new ArrayList<>();
        } catch (IOException e) {
            log.error("Error occurred while retrieving SPAJ list", e);
            throw new InternalServerErrorException("Error occurred while retrieving SPAJ list");
        }
    }

    /**
     * Get SPAJ list for an agent with pagination support
     *
     * @param username The username of the agent
     * @param filter   The filter parameters
     * @param pageable The pagination parameters
     * @return Page of SPAJ DTOs
     */
    @CacheableWithTTL(cacheName = "spajListPageableCache",
            key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.status) && !#filter.status.isEmpty() ? T(java.lang.String).join('-', #filter.status.![name()]) : '-') + ':' + " +
                    "#filter.startDate + ':' + #filter.endDate + ':' + #filter.bitFilter + ':' + " +
                    "#pageable.pageNumber + ':' + #pageable.pageSize",
            ttl = 1600, db = 7)
    public Page<SpajDto> getSpajListPageable(String username, SpajFilter filter, Pageable pageable) {
        // Get the agent information from the username
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // If agentCode is not provided in the filter, use the agent's code
        String agentCode = StringUtils.isBlank(filter.getAgentCode())
                ? agent.getAgentCode()
                : filter.getAgentCode();

        // Trim -D suffix from agentCode if present
        agentCode = AgentCodeUtil.trimDSuffix(agentCode);

        // Create the request DTO for the portal API
        PortalSpajDto requestDto = new PortalSpajDto();
        requestDto.setAgentCode(agentCode);

        // Set date range from filter or default to 1 year from now
        requestDto.setDateFrom(filter.getFormattedStartDate());
        requestDto.setDateTo(filter.getFormattedEndDate());
        requestDto.setStatus(91);

        // Handle null status list
        String statusFilter = "pending_uw-pending_admin-pending_checker-terbit_polis-ditolak";
        if (filter.getStatus() != null && !filter.getStatus().isEmpty()) {
            statusFilter = filter.getStatus().stream()
                    .map(SpajStatus::name)
                    .collect(Collectors.joining("-"));
        }
        requestDto.setStatusFilter(statusFilter);

        // Set default values for other required fields
        requestDto.setBitFilter(filter.getBitFilter());
        requestDto.setValFilter("");
        requestDto.setPageIndex(pageable.getPageNumber() + 1); // Portal API uses 1-based indexing
        requestDto.setPageSize(pageable.getPageSize());
        requestDto.setCountRow(1); // Enable count for pagination
        requestDto.setSortIndex(0);
        requestDto.setSortBy(1);
        requestDto.setBrCode("");
        requestDto.setChannel(agent.getDistributionCode().name());
        requestDto.setIsSpajStatus(1);

        // Call the portal API
        Call<PortalSpajResponseDto> call = portalProvider.getSpajList(requestDto);

        try {
            Response<PortalSpajResponseDto> response = call.execute();
            if (response.isSuccessful()) {
                PortalSpajResponseDto responseDto = response.body();
                if (responseDto != null && responseDto.getSpajDatas() != null) {
                    // Convert the response data to SpajDto objects
                    List<SpajDto> spajDtos = responseDto.getSpajDatas().stream()
                            .map(this::convertToSpajDto)
                            .collect(Collectors.toList());

                    // Create Page object with total count from portal response
                    return new PageImpl<>(spajDtos, pageable, responseDto.getTotalData());
                }
            }
            // Return empty page if response is not successful or body is null
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        } catch (IOException e) {
            log.error("Error occurred while retrieving SPAJ list", e);
            throw new InternalServerErrorException("Error occurred while retrieving SPAJ list");
        }
    }

    /**
     * Convert PortalSpajResponseDto.SpajDataDto to SpajDto
     */
    private SpajDto convertToSpajDto(PortalSpajResponseDto.SpajDataDto spajData) {
        SpajDto spajDto = new SpajDto();
        BeanUtils.copyProperties(spajData, spajDto);
        return spajDto;
    }
}
