package id.co.panindaiichilife.superapp.agent.service.agency.validator.rejoin;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationBlacklistResponseDto;
import id.co.panindaiichilife.superapp.agent.enums.ValidationBlacklistStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;

import java.time.LocalDate;

/**
 * Validator for checking blacklist status
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RejoinBlacklistValidator extends AbstractRejoinValidator {

    private final PortalProvider portalProvider;

    @Override
    public boolean canValidate(TrxRejoinApplicationForm form, TrxRejoinApplication entity) {
        return entity.getDob() != null && StringUtils.isNotBlank(entity.getIdNumber());
    }

    @Override
    protected TrxRejoinApplication doValidate(TrxRejoinApplicationForm form, TrxRejoinApplication entity) {
        ValidationBlacklistStatus status = validateBlacklistStatus(entity.getIdNumber(), entity.getDob());

        if (status != null) {
            entity.setValidationBlacklistStatus(status);
            log.info("Blacklist validation result for NIK {}: {}", entity.getIdNumber(), status);
        } else {
            log.warn("Blacklist validation skipped or failed for NIK {}", entity.getIdNumber());
        }

        return entity;
    }

    @Override
    public String getValidatorName() {
        return "Rejoin Blacklist Validator";
    }

    private ValidationBlacklistStatus validateBlacklistStatus(String idNumber, LocalDate dateOfBirth) {
        try {
            if (dateOfBirth == null || StringUtils.isBlank(idNumber)) {
                log.warn("Blacklist validation skipped - missing required data");
                return null;
            }

            // Format date as yyyy-MM-dd
            String dobFormatted = dateOfBirth.toString();

            // Call the portal API to validate blacklist status
            Call<PortalValidationBlacklistResponseDto> call = portalProvider.validateBlacklist(dobFormatted, idNumber);
            Response<PortalValidationBlacklistResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalValidationBlacklistResponseDto blacklistResponse = response.body();

                // Check if the API call was successful
                if ("200".equals(blacklistResponse.getStatusCode())) {
                    // Convert the string result to ValidationBlacklistStatus enum
                    String result = blacklistResponse.getResult();
                    try {
                        return ValidationBlacklistStatus.valueOf(result.replace("-", ""));
                    } catch (IllegalArgumentException e) {
                        log.error("Unknown blacklist status: {}", result, e);
                        return null;
                    }
                } else {
                    log.error("Error in blacklist validation: {} - {}", blacklistResponse.getStatusCode(), blacklistResponse.getMessage());
                    return null;
                }
            } else {
                log.error("Failed to validate blacklist status: {}", response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                return null;
            }
        } catch (Exception e) {
            log.error("Error validating blacklist status", e);
            return null;
        }
    }
}
