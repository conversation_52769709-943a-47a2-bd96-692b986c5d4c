package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalDetail;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;

import java.util.List;

public interface TrxApprovalDetailRepository extends BaseRepository<TrxApprovalDetail, Long> {

    List<TrxApprovalDetail> findByApprovalHeaderOrderByCreatedAt(TrxApprovalHeader approvalHeader);

    List<TrxApprovalDetail> findByApprovalHeaderOrderByCreatedAtDesc(TrxApprovalHeader approvalHeader);

    int countByApprovalHeaderAndLevelNumberAndApprovalStatus(
            TrxApprovalHeader approvalHeader, Integer levelNumber, ApprovalStatus status);

}
