package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.BankDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BankFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.BankForm;
import id.co.panindaiichilife.superapp.agent.service.BankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController("cmsBankController")
@RequestMapping("/api/cms/bank")
@Tag(name = "Bank - CMS", description = "API CMS Bank")
@Slf4j
@RequiredArgsConstructor
public class BankCmsController {

    private final BankService bankService;

    @Operation(summary = "List banks")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Bank', 'view')")
    public Page<BankDto> index(@ParameterObject @ModelAttribute("filter") BankFilter filter,
                               @ParameterObject @PageableDefault(sort = "createdAt") Pageable pageable) {
        return bankService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific bank")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Bank', 'view')")
    public BankDto view(@PathVariable long id) {
        return bankService.findOne(id);
    }

    @Operation(summary = "Add new bank")
    @PostMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Bank', 'add')")
    public BankDto insert(@Valid @RequestBody BankForm bankForm) {
        return bankService.add(bankForm);
    }

    @Operation(summary = "Modify existing bank")
    @PutMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Bank', 'edit')")
    public BankDto edit(@PathVariable long id,
                        @Valid @RequestBody BankForm bankForm) {
        return bankService.update(id, bankForm);
    }

    @Operation(summary = "Delete existing bank")
    @DeleteMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Bank', 'delete')")
    public void delete(@PathVariable long id) {
        bankService.delete(id);
    }

    @Operation(summary = "Run Sync Banks from API")
    @GetMapping(value = "sync")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Bank', 'sync')")
    public void syncBanks() {
        bankService.syncBanks();
    }

    @Operation(summary = "Update AdvanceAI codes for all banks")
    @GetMapping(value = "update-advance-ai-codes")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Bank', 'sync')")
    public void updateAdvanceAiCodes() {
        bankService.updateBankAdvanceAiCodes();
    }
}
