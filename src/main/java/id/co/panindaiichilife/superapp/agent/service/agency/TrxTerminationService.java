package id.co.panindaiichilife.superapp.agent.service.agency;

import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import id.co.panindaiichilife.superapp.agent.api.filter.agency.CandidateTerminationFilter;
import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TerminationEligibleCandidateDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxTerminationDto;
import id.co.panindaiichilife.superapp.agent.api.filter.agency.TerminationListFilter;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxTerminationCancellationForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxTerminationForm;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.MailService;
import id.co.panindaiichilife.superapp.agent.core.support.TimeUtils;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import id.co.panindaiichilife.superapp.agent.model.TrxPolicyTransfer;
import id.co.panindaiichilife.superapp.agent.model.TrxTermination;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.model.projection.AgentWithBranchInfo;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxTerminationRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.ApprovalService;
import id.co.panindaiichilife.superapp.agent.service.FirebaseService;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.TerminationNotificationTemplates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class TrxTerminationService {
    private final TrxTerminationRepository trxTerminationRepository;
    private final UserRepository userRepository;
    private final AgentRepository agentRepository;
    private final ApprovalService approvalService;
    private final FirebaseService firebaseService;
    private final GlobalConfigService globalConfigService;
    private final MailService mailService;

    public Page<TerminationEligibleCandidateDto> retrieveEligibleCandidates(
            String username, CandidateTerminationFilter filter, Pageable pageable) {
        User currentUser = userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException(String.format("User %s not found", username)));

        Page<AgentWithBranchInfo> queryResult;
        if (currentUser.getIsAgent()) {
            queryResult = agentRepository.findAllEligibleAgentsForTerminationBasedOnLeader(
                    currentUser.getAgent().getAgentCode(),
                    filter.getSearchQuery(),
                    pageable);

            return new PageImpl<>(
                    queryResult.getContent()
                            .stream()
                            .map(TerminationEligibleCandidateDto::of).toList(),
                    pageable,
                    queryResult.getTotalElements());
        }

        if (filter.getBranchCode() == null || filter.getBranchCode().isEmpty()) {
            throw new BadRequestException("Branch code not supplied");
        }

        queryResult = agentRepository.findAllEligibleAgentsForTerminationBasedOnBranch(
                filter.getBranchCode(),
                filter.getSearchQuery(),
                pageable);

        return new PageImpl<>(
                queryResult.getContent()
                        .stream()
                        .map(TerminationEligibleCandidateDto::of).toList(),
                pageable,
                queryResult.getTotalElements());
    }

    public TrxTerminationDto getById(Long id) {
        TrxTermination terminationRequest = trxTerminationRepository.findById(id).orElseThrow(NotFoundException::new);
        return TrxTerminationDto.of(TrxTerminationDto.class, terminationRequest);
    }

    public Page<TrxTerminationDto> getCurrentUserTerminationRequests(
            String username, TerminationListFilter filter, Pageable pageable) {
        User user = userRepository.findByUsername(username).orElseThrow(() ->
                new NotFoundException(String.format("User %s not found", username)));

        Page<TrxTermination> queryResult = trxTerminationRepository
                .findTerminationRequestsByRequester(
                        user.getId(), filter.getSearchQuery(), filter.getTrxStatuses(), pageable);

        return new PageImpl<>(
                queryResult.getContent()
                        .stream()
                        .map(t -> TrxTerminationDto.of(TrxTerminationDto.class, t)).toList(),
                pageable,
                queryResult.getTotalElements());
    }

    @Transactional
    public void setPolicyTransfer(TrxPolicyTransfer policyTransfer, TrxTermination termination) {
        termination.setTrxPolicyTransfer(policyTransfer);
        trxTerminationRepository.save(termination);
    }

    public TrxTermination persistTermination(TrxTerminationForm form, User target, User requester) {
        TrxType trxType = TrxType.valueOf("TERMINASI_" + target.getAgent().getPositionLevel());

        TrxTermination newTrx = new TrxTermination();
        newTrx.setReason(form.getReason());
        newTrx.setRequester(requester);
        newTrx.setTarget(target);
        newTrx.setTrxType(trxType);
        newTrx.setLastActionAt(TimeUtils.zonedNow().toInstant());
        newTrx.setApprovalStatus(ApprovalStatus.BARU);
        newTrx.setStatus(TrxStatus.IN_PROGRESS);

        return trxTerminationRepository.save(newTrx);
    }

    public TrxTerminationDto submitRequest(String username, TrxTerminationForm form) {
        User requester = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        Agent terminationTarget = agentRepository.findTopByAgentCode(form.getTargetAgentCode()).orElseThrow(NotFoundException::new);
        User targetUser = terminationTarget.getUser();

        if (targetUser == null) {
            throw new InternalServerErrorException("Invalid agent data");
        }

        // persist to get id
        TrxTermination newTrx = persistTermination(form, targetUser, requester);

        TrxApprovalHeader approvalHeader = approvalService.requestApproval(newTrx.getTrxType(), newTrx.getId(), requester,
                null, requester.getChannel(), false);
        newTrx.setApprovalHeader(approvalHeader);
        newTrx.setApprovalStatus(approvalHeader.getApprovalStatus());

        // update the approval header information
        trxTerminationRepository.save(newTrx);

        boolean isRequestedByLeader = requester.getAgent().getAgentCode()
                .equals(terminationTarget.getLeader().getLeaderCode());
        if (isRequestedByLeader) {
            NotificationDto notification = new NotificationDto();

            notification.setTitle("Pengajuan terminasi anda");
            notification.setBody(
                    String.format(TerminationNotificationTemplates.Inbox
                                    .TERMINATION_TARGET_REQUEST_SUBMITTED_BY_LEADER,
                            terminationTarget.getLeader().getAgentName()));
            notification.setData(Map.of(
                    "approvalId", approvalHeader.getId().toString(),
                    "trxId", newTrx.getId().toString(),
                    "trxType", newTrx.getTrxType().name()
            ));
            notification.setInboxType(InboxType.INBOX);

            firebaseService.sendNotification(List.of(targetUser), notification);
            log.info("Notified target agent {} for termination", form.getTargetAgentCode());
        }

        return TrxTerminationDto.of(TrxTerminationDto.class, newTrx);
    }

    @Transactional
    public void cancel(String username, TrxTerminationCancellationForm form) {
        Long id = form.getId();

        User requesterUser = userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException(String.format("User %s not found", username)));

        // the one who is doing cancellation has to be the requester
        TrxTermination termination = trxTerminationRepository.findById(id).orElseThrow(() ->
                new NotFoundException(String.format("No termination was found with id %d", id)));

        if (!requesterUser.getId().equals(termination.getRequester().getId())) {
            throw new BadRequestException(String.format("Invalid actor for termination %d", id));
        }

        termination.setApprovalStatus(ApprovalStatus.DIBATALKAN);
        termination.setStatus(TrxStatus.CANCELLED);
        trxTerminationRepository.save(termination);

        approvalService.cancelApproval(termination.getTrxType(), termination.getId(), form.getRemarks());
    }

    @Transactional
    public void updateLastAction(TrxTermination termination) {
        termination.setLastActionAt(TimeUtils.zonedNow().toInstant());
        trxTerminationRepository.save(termination);
    }

    @Transactional
    public void expireStaleTerminations() {
        // select terminations that has no update since 30 days
        Instant cutoff = TimeUtils.zonedNow().minusDays(30L).toInstant();
        List<TrxTermination> terminations = trxTerminationRepository
                .findByLastActionAtBeforeAndStatusNotIn(cutoff,
                        List.of(TrxStatus.COMPLETE, TrxStatus.EXPIRED, TrxStatus.CANCELLED, TrxStatus.REJECTED));

        Set<TrxTermination> notificationEntries = new HashSet<>();
        List<TrxTermination> updatedTerminations = terminations.stream().peek((t) -> {
            Agent targetAgent = t.getTarget().getAgent();
            Agent requesterAgent = t.getRequester().getAgent();

            t.setStatus(TrxStatus.EXPIRED);
            t.setApprovalStatus(ApprovalStatus.DIBATALKAN);

            // check if requested by leader, put leader to notification list
            if (targetAgent.getAgentCode().equals(requesterAgent.getAgentCode())) {
                notificationEntries.add(t);
            }

            TrxApprovalHeader approvalHeader = t.getApprovalHeader();
            approvalHeader.setApprovalStatus(ApprovalStatus.DIBATALKAN);
            approvalService.cancelApproval(t.getTrxType(), t.getId());
        }).toList();

        trxTerminationRepository.saveAll(updatedTerminations);

        // send notification to eligible requester
        notificationEntries.forEach(this::sendExpiredTerminationInbox);
    }

    private void sendExpiredTerminationInbox(TrxTermination termination) {
        Agent targetAgent = termination.getTarget().getAgent();
        String nextApproverCode = "-";

        // try to resolve next approver code based on the approval configuration
        TrxApprovalHeader approvalHeader = termination.getApprovalHeader();
        String approverRole = approvalHeader.getApproverRole();
        List<ApprovalStatus> expectedApprovalStatus = List.of(
                ApprovalStatus.MENUNGGU_PERSETUJUAN,
                ApprovalStatus.TERTUNDA);
        if (expectedApprovalStatus.contains(approvalHeader.getApprovalStatus()) && approverRole != null) {
            // Check if this is an upline approval
            if (approvalHeader.getApproverRole().startsWith("UPLINE:")) {
                // Extract the upline agent code
                String uplineAgentCode = approvalHeader.getApproverRole().substring("UPLINE:".length());

                // Find the upline agent and their user
                Agent uplineAgent = agentRepository.findTopByAgentCode(uplineAgentCode).orElse(null);
                if (uplineAgent != null && uplineAgent.getUser() != null) {
                    User uplineUser = uplineAgent.getUser();
                    nextApproverCode = uplineAgentCode;
                }
            } else {
                // use standard role-based approval
                // For role-based approvals, find all users with the role
                List<User> usersWithRole = userRepository.findUsersByRoleCode(approverRole);
                if (usersWithRole != null && !usersWithRole.isEmpty()) {
                    log.info("Found {} users with role {}", usersWithRole.size(), approverRole);
                    // retrieve from first user on the list
                    nextApproverCode = usersWithRole.getFirst().getUsername();
                } else {
                    log.warn("No users found with role {}", approverRole);
                }
            }
        } else {
            log.warn("Invalid current approval header");
        }

        NotificationDto notification = new NotificationDto();
        notification.setData(Map.of(
                "approvalId", termination.getApprovalHeader().getId().toString(),
                "trxId", termination.getId().toString(),
                "trxType", termination.getTrxType().name()));
        notification.setInboxType(InboxType.INBOX);
        notification.setTitle("Pengajuan terminasi anda telah kadaluarsa");
        notification.setBody(String.format(TerminationNotificationTemplates.Inbox.REQUESTER_REQUEST_EXPIRED,
                targetAgent.getAgentName(), targetAgent.getAgentCode(), nextApproverCode));

        firebaseService.sendNotification(List.of(termination.getRequester()), notification);
    }

    @Transactional
    public TrxTerminationDto revise(String username, Long terminationId, TrxTerminationForm form) {
        TrxTermination termination = trxTerminationRepository.findById(terminationId)
                .orElseThrow(() -> new NotFoundException(String.format("No termination was found with id %d",
                        terminationId)));
        // verify submitter
        User submitter = userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException(String.format("User %s not found", username)));

        if (termination.getRequester().getId().equals(submitter.getId())) {
            throw new BadRequestException("Invalid requester");
        }

        TrxApprovalHeader approvalHeader = approvalService.resendApproval(termination.getTrxType(),
                terminationId);

        Agent targetAgent = agentRepository.findTopByAgentCode(form.getTargetAgentCode())
                .orElseThrow(() -> new NotFoundException(String.format("Agent %s not found", form.getTargetAgentCode())));

        termination.setReason(form.getReason());
        termination.setTarget(targetAgent.getUser());

        termination.setApprovalHeader(approvalHeader);
        termination.setStatus(TrxStatus.IN_PROGRESS);

        return TrxTerminationDto.of(TrxTerminationDto.class, trxTerminationRepository.save(termination));
    }

    @Transactional
    public TrxTermination resolveTerminationStatusOnEventReceived(
            Long id, ApprovalStatus approvalStatus) {
        log.info("Updating termination status upon event received for id {} to {}", id, approvalStatus);

        // Find termination record
        TrxTermination termination = trxTerminationRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format("Could not find termination request with id %d",
                        id)));

        termination.setApprovalStatus(approvalStatus);

        // Update termination status based on approval status
        switch (approvalStatus) {
            case DISETUJUI:
                termination.setStatus(TrxStatus.COMPLETE);
                log.info("Termination {} completed", id);
                break;

            case DITOLAK:
                termination.setStatus(TrxStatus.REJECTED);
                log.info("Termination {} rejected", id);
                break;

            case TERTUNDA:
                termination.setStatus(TrxStatus.DIKEMBALIKAN);
                log.info("Termination {} returned for revision and marked as DIKEMBALIKAN", id);
                break;

            case DIBATALKAN:
                termination.setStatus(TrxStatus.CANCELLED);
                log.info("Termination {} cancelled and marked as CANCELLED", id);
                break;

            case MENUNGGU_PERSETUJUAN:
            case BARU:
                // Keep the current transaction status for these approval statuses
                log.info("Termination {} status updated to {} without changing transaction status",
                        id, approvalStatus);
                break;
            default:
                log.warn("No handler for action {}", approvalStatus);
                break;
        }

        // Persist updated termination record
        termination.setLastActionAt(TimeUtils.zonedNow().toInstant());
        return trxTerminationRepository.save(termination);
    }

    public void sendEmail(List<String> recipients,
                          String key,
                          String defaultTemplate,
                          Map<String, Object> data,
                          Attachments... attachments) {

        String templateId = globalConfigService.getGlobalConfig(key, defaultTemplate);
        recipients.forEach((r) -> {
            try {
                Mail mail = mailService.createEmailWithAttachments(templateId, data, Set.of(attachments), r);
                mailService.sendEmail(mail);
            } catch (Exception e) {
                log.warn("Unable to send {} email notification to {}, due to error {}",
                        key, r, e.getMessage());
            }
        });
    }

    @Transactional
    public TrxTermination setEffectiveDate(TrxTermination termination) {
        termination.setLastActionAt(TimeUtils.zonedNow().toInstant());
        termination.setEffectiveDate(TimeUtils.zonedNow().toInstant());
        return trxTerminationRepository.save(termination);
    }
}
