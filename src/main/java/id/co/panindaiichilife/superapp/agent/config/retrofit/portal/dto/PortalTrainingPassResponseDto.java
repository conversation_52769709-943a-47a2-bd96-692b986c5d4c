package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

@Data
public class PortalTrainingPassResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("trainingResults")
    @JsonProperty("trainingResults")
    private List<TrainingResultDto> trainingResults;


    @Data
    public static class TrainingResultDto {

        @SerializedName("code")
        @JsonProperty("code")
        private String code;

        @SerializedName("name")
        @JsonProperty("name")
        private String name;

        @SerializedName("passDate")
        @JsonProperty("passDate")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate passDate;
    }
}
