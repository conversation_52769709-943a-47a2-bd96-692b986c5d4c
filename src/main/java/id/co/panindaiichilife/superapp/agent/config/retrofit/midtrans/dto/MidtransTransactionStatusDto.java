package id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class MidtransTransactionStatusDto  {

	@SerializedName("status_code")
	@JsonProperty("status_code")
	private String statusCode;

	@SerializedName("status_message")
	@JsonProperty("status_message")
	private String statusMessage;

	@SerializedName("masked_card")
	@JsonProperty("masked_card")
	private String maskedCard;

	@SerializedName("transaction_id")
	@JsonProperty("transaction_id")
	private String transactionId;

	@SerializedName("order_id")
	@JsonProperty("order_id")
	private String orderId;

	@SerializedName("transaction_time")
	@JsonProperty("transaction_time")
	private String transactionTime;

	@SerializedName("transaction_status")
	@JsonProperty("transaction_status")
	private String transactionStatus;

	@SerializedName("fraud_status")
	@JsonProperty("fraud_status")
	private String fraudStatus;

	@SerializedName("approval_code")
	@JsonProperty("approval_code")
	private String approvalCode;

	@SerializedName("signature_key")
	@JsonProperty("signature_key")
	private String signatureKey;

	@SerializedName("gross_amount")
	@JsonProperty("gross_amount")
	private String grossAmount;

	@SerializedName("bank")
	@JsonProperty("bank")
	private String bank;

	@SerializedName("channel_response_code")
	@JsonProperty("channel_response_code")
	private String channelResponseCode;

	@SerializedName("channel_response_message")
	@JsonProperty("channel_response_message")
	private String channelResponseMessage;

	@SerializedName("card_type")
	@JsonProperty("card_type")
	private String cardType;

}
