package id.co.panindaiichilife.superapp.agent.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "promosi_agent")
@Data
@ToString(of = {"id", "agentCode"})
public class PromosiAgent {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "promosi_agent_id_seq")
    @SequenceGenerator(name = "promosi_agent_id_seq", sequenceName = "promosi_agent_id_seq", allocationSize = 1)
    private Long id;

    private String agentCode;

    private Double apeTargetMin;

    private Double apeTargetMax;

    private Double netApe;

    private Double recruitApeTarget;

    private Double recruitApe;

    private Double p13Target;

    private Double p13;

    private Integer agentCountTarget;

    private Integer agentCount;

    private Integer newAgentCountTarget;

    private Integer newAgentCount;

    private Integer trainingCountTarget;

    private Integer trainingCount;

    private String remark;

    private String toAchieveRemark;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
