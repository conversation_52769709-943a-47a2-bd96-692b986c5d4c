package id.co.panindaiichilife.superapp.agent.api.batch.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import id.co.panindaiichilife.superapp.agent.api.batch.dto.ApprovalExportDto;
import id.co.panindaiichilife.superapp.agent.api.batch.processor.ApprovalExportProcessor;
import id.co.panindaiichilife.superapp.agent.api.filter.ApprovalFilter;
import id.co.panindaiichilife.superapp.agent.core.data.batch.ExcelItemWriter;
import id.co.panindaiichilife.superapp.agent.core.data.batch.ProgressListener;
import id.co.panindaiichilife.superapp.agent.core.data.repository.PagingRepositoryItemCounter;
import id.co.panindaiichilife.superapp.agent.core.data.repository.PagingRepositoryItemReader;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxApprovalHeaderRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxEditProfileRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRecruitmentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxTerminationRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRejoinApplicationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobScope;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.function.Supplier;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class ApprovalExportConfig {
    public static final String NAME = "approvalExport";

    private final JobRepository jobRepository;

    private final PlatformTransactionManager transactionManager;

    @Qualifier("dataSource")
    private final DataSource dataSource;

    private final ObjectMapper objectMapper;

    private final TrxApprovalHeaderRepository trxApprovalHeaderRepository;

    private final TrxEditProfileRepository trxEditProfileRepository;

    private final TrxRecruitmentRepository trxRecruitmentRepository;

    private final TrxTerminationRepository trxTerminationRepository;

    private final TrxRejoinApplicationRepository trxRejoinApplicationRepository;

    private final AgentRepository agentRepository;

    private final AmazonS3Service amazonS3Service;

    @Bean(NAME + "Job")
    public Job getJob() throws Exception {
        return new JobBuilder(NAME + "Job", jobRepository)
                .incrementer(new RunIdIncrementer())
                .preventRestart()
                .start(getStep())
                .listener(getProgressListener(null))
                .build();
    }

    @Bean(NAME + "Step")
    public Step getStep() throws Exception {
        return new StepBuilder(NAME + "Step", jobRepository)
                .<TrxApprovalHeader, ApprovalExportDto>chunk(50, transactionManager)
                .reader(getReader(null, null))
                .writer(getWriter(null, null))
                .processor(getProcessor())
                .listener(getProgressListener(null))
                .build();
    }

    @Bean(NAME + "Reader")
    @StepScope
    public PagingRepositoryItemReader<TrxApprovalHeader> getReader(
            @Value("#{jobParameters['filter']}") String filterParam,
            @Value("#{jobParameters['sort']}") String sortParam) throws Exception {
        // Deserialize filter from JSON
        ApprovalFilter filter = objectMapper.readValue(filterParam, ApprovalFilter.class);

        // Handle the Sort parameter safely
        Sort sort = Sort.unsorted();
        if (sortParam != null && !sortParam.trim().isEmpty()) {
            try {
                sort = objectMapper.readValue(sortParam, Sort.class);
                // Ensure the sort has at least one property
                if (!sort.iterator().hasNext()) {
                    sort = Sort.unsorted();
                }
            } catch (Exception e) {
                log.warn("Failed to parse sort parameter: {}, using unsorted instead. Error: {}",
                        sortParam, e.getMessage());
            }
        }

        return new PagingRepositoryItemReader<>(trxApprovalHeaderRepository, filter, sort);
    }

    @Bean(NAME + "Writer")
    @StepScope
    public ExcelItemWriter<ApprovalExportDto> getWriter(
            @Value("#{jobParameters['s3FolderPath']}") String s3FolderPath,
            @Value("#{jobParameters['s3FileName']}") String s3FileName) {
        return new ExcelItemWriter<>(ApprovalExportDto.class, amazonS3Service, s3FolderPath, s3FileName);
    }

    @Bean(NAME + "Processor")
    @StepScope
    public ApprovalExportProcessor getProcessor() {
        return new ApprovalExportProcessor(trxEditProfileRepository, trxRecruitmentRepository,
                trxTerminationRepository, trxRejoinApplicationRepository, agentRepository);
    }

    @Bean(NAME + "ProgressListener")
    @JobScope
    public ProgressListener getProgressListener(
            @Value("#{jobParameters['filter']}") String filterParam) throws JsonProcessingException {
        // Deserialize filter from JSON
        ApprovalFilter filter = objectMapper.readValue(filterParam, ApprovalFilter.class);
        Supplier<Integer> totalSupplier = new PagingRepositoryItemCounter<>(trxApprovalHeaderRepository, filter);
        return new ProgressListener(totalSupplier);
    }
}