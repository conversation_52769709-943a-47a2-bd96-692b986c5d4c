package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalForgotPasswordDto {
    @SerializedName("agentCode")
    @JsonProperty("agentCode")
    private String agentCode;
    
    @SerializedName("authCode")
    @JsonProperty("authCode")
    private String authCode;

    @SerializedName("newPassword")
    @JsonProperty("newPassword")
    private String newPassword;
}
