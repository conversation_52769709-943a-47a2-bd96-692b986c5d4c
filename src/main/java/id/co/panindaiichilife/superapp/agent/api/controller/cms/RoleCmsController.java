package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.RoleDto;
import id.co.panindaiichilife.superapp.agent.api.filter.RoleFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.RoleForm;
import id.co.panindaiichilife.superapp.agent.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController("cmsRoleController")
@RequestMapping("/api/cms/role")
@Tag(name = "Role - CMS", description = "API CMS Role")
@Slf4j
@RequiredArgsConstructor
public class RoleCmsController {

    private final RoleService roleService;

    @Operation(summary = "List roles")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Role', 'view')")
    public Page<RoleDto> index(@ParameterObject @ModelAttribute("filter") RoleFilter filter,
                               @ParameterObject @PageableDefault(sort = "name") Pageable pageable) {
        return roleService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific role")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Role', 'view')")
    public RoleDto view(@PathVariable long id) {
        return roleService.findOne(id);
    }

    @Operation(summary = "Add new roles")
    @PostMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Role', 'add')")
    public RoleDto insert(@Valid @RequestBody RoleForm roleForm) {
        return roleService.add(roleForm);
    }

    @Operation(summary = "Modify existing role")
    @PutMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Role', 'edit')")
    public RoleDto edit(@PathVariable long id,
                        @Valid @RequestBody RoleForm roleForm) {
        return roleService.update(id, roleForm);
    }

    @Operation(summary = "Delete existing role")
    @DeleteMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Role', 'delete')")
    public void delete(@PathVariable long id) {
        roleService.delete(id);
    }
}
