package id.co.panindaiichilife.superapp.agent.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "validasi_per_g1")
@Data
@ToString(of = {"id", "agentCode"})
public class ValidasiPerG1 {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "validasi_per_g1_id_seq")
    @SequenceGenerator(name = "validasi_per_g1_id_seq", sequenceName = "validasi_per_g1_id_seq", allocationSize = 1)
    private Long id;
    
    private String agentCode;

    private String branchCode;

    private String mainBranchCode;

    private String bdmCode;

    private String bdmName;

    private String abddCode;

    private String abddName;

    private String bddCode;

    private String bddName;

    private String hosCode;

    private String hosName;

    private Integer year;

    private Double netApe;

    private Double apeTarget;

    private Double g1NetApe;

    private Double g1ApeTarget;

    private Double p13;

    private Double p13Target;

    private Long g1CountTarget;

    private Long g1Count;

    private Double gNetApe;

    private Double gNetApeTarget;

    private Long trainingTarget;

    private Long trainingCount;

    @Column(columnDefinition = "TEXT")
    private String remark;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
