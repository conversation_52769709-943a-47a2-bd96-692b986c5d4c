package id.co.panindaiichilife.superapp.agent.service.agency.validator;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;

import java.util.concurrent.CompletableFuture;

/**
 * Interface for recruitment validators
 * Each validator is responsible for validating a specific aspect of a recruitment
 */
public interface RecruitmentValidator {

    /**
     * Checks if this validator can be applied to the given recruitment
     *
     * @param form   The recruitment form
     * @param entity The recruitment entity
     * @return true if this validator can be applied, false otherwise
     */
    boolean canValidate(TrxRecruitmentForm form, TrxRecruitment entity);

    /**
     * Performs the validation asynchronously
     *
     * @param form   The recruitment form
     * @param entity The recruitment entity
     * @return A CompletableFuture that will be completed with the updated entity
     */
    CompletableFuture<TrxRecruitment> validate(TrxRecruitmentForm form, TrxRecruitment entity);

    /**
     * Gets the name of this validator for logging purposes
     *
     * @return The validator name
     */
    String getValidatorName();
}
