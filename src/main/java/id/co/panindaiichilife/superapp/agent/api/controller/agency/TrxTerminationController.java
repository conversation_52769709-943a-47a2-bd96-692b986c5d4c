package id.co.panindaiichilife.superapp.agent.api.controller.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.agency.TerminationEligibleCandidateDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxTerminationDto;
import id.co.panindaiichilife.superapp.agent.api.filter.agency.CandidateTerminationFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.agency.TerminationListFilter;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxTerminationCancellationForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxTerminationForm;
import id.co.panindaiichilife.superapp.agent.api.validation.TrxTerminationValidator;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxTerminationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;

@RestController("agentTerminationController")
@RequestMapping("/api/agency/terminations")
@Tag(name = "Agency - Termination", description = "API Agent Termination")
@Slf4j
@RequiredArgsConstructor
public class TrxTerminationController {
    private final TrxTerminationValidator trxTerminationValidator;
    private final TrxTerminationService trxTerminationService;

    @Operation(summary = "Get eligible candidates for termination by current user")
    @GetMapping("/eligible-candidates")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Termination', 'view')")
    public Page<TerminationEligibleCandidateDto> getEligibleTerminationCandidates(
            Principal principal,
            @ParameterObject @ModelAttribute CandidateTerminationFilter filter,
            @ParameterObject @PageableDefault(sort = "agentName", direction = Sort.Direction.DESC) Pageable pageable) {
        return trxTerminationService.retrieveEligibleCandidates(principal.getName(), filter, pageable);
    }

    @Operation(summary = "Submit termination request form")
    @PostMapping
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Termination', 'submit')")
    public TrxTerminationDto submitTerminationRequest(Principal principal,
                                                      @Valid @RequestBody TrxTerminationForm form,
                                                      BindingResult bindingResult) throws MethodArgumentNotValidException {
        trxTerminationValidator.validate(form, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }

        return trxTerminationService.submitRequest(principal.getName(), form);
    }

    @Operation(summary = "Get detail termination")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Termination', 'view')")
    public TrxTerminationDto getTerminationInformationById(@PathVariable Long id) {
        return trxTerminationService.getById(id);
    }

    @Operation(summary = "Get list of terminations submitted by current user")
    @GetMapping
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Termination', 'view')")
    public Page<TrxTerminationDto> getMyTerminationRequests(
            Principal principal,
            @ParameterObject @ModelAttribute TerminationListFilter filter,
            @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        return trxTerminationService.getCurrentUserTerminationRequests(principal.getName(), filter, pageable);
    }

    @Operation(summary = "Cancel termination")
    @PutMapping("/cancel")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Termination', 'cancel')")
    public void cancelTermination(Principal principal, @RequestBody TrxTerminationCancellationForm form) {
        trxTerminationService.cancel(principal.getName(), form);
    }

    @Operation(summary = "Expire stale terminations")
    @PutMapping("/expire")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Termination', 'update')")
    public void expireTerminations() {
        trxTerminationService.expireStaleTerminations();
    }

    @Operation(summary = "Revise termination")
    @PatchMapping("/revise/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Termination', 'update')")
    public TrxTerminationDto revise(Principal principal,
                                    @PathVariable Long id,
                                    @Valid @RequestBody TrxTerminationForm form,
                                    BindingResult bindingResult) throws MethodArgumentNotValidException {
        trxTerminationValidator.validate(form, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        return trxTerminationService.revise(principal.getName(), id, form);
    }

}
