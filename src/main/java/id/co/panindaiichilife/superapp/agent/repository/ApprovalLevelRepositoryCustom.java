package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.ApprovalLevel;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ApprovalLevelRepositoryCustom {
    
    Optional<ApprovalLevel> findApplicableLevel(
            TrxType trxType,
            Integer levelNumber,
            Channel channel,
            Set<String> requesterRoles,
            Set<String> approverRoles,
            Set<String> branchCodes);
    
    List<ApprovalLevel> findByApproverRoleInAndBranchCodeIn(
            Set<String> roles,
            Set<String> branchCodes);
}
