package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalLevelDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ApprovalLevelFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ApprovalLevelForm;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.model.ApprovalLevel;
import id.co.panindaiichilife.superapp.agent.repository.AccessRepository;
import id.co.panindaiichilife.superapp.agent.repository.ApprovalLevelRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class ApprovalLevelService {

    private final ApprovalLevelRepository approvalLevelRepository;

    private final AccessRepository accessRepository;

    public Page<ApprovalLevelDto> findAll(Pageable pageable, ApprovalLevelFilter filter) {
        Page<ApprovalLevel> approvalLevels = approvalLevelRepository.findAll(filter, pageable);
        return BaseDto.of(ApprovalLevelDto.class, approvalLevels, pageable);
    }

    public ApprovalLevelDto findOne(Long id) {
        ApprovalLevel data = approvalLevelRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(ApprovalLevelDto.class, data);
    }

    @Transactional
    public ApprovalLevelDto add(ApprovalLevelForm approvalLevelForm) {
        ApprovalLevel data = new ApprovalLevel();
        BeanUtils.copyProperties(approvalLevelForm, data);
        approvalLevelRepository.save(data);

        return BaseDto.of(ApprovalLevelDto.class, data);
    }


    @Transactional
    public ApprovalLevelDto update(Long id, ApprovalLevelForm approvalLevelForm) {
        ApprovalLevel data = approvalLevelRepository.findById(id).orElseThrow(NotFoundException::new);
        BeanUtils.copyProperties(approvalLevelForm, data);
        approvalLevelRepository.save(data);

        return BaseDto.of(ApprovalLevelDto.class, data);
    }

    public void delete(Long id) {
        approvalLevelRepository.deleteById(id);
    }
}
