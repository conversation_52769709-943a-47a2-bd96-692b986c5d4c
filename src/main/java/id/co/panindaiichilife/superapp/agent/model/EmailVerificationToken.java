package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import java.time.Instant;
import java.time.LocalDateTime;

@Entity
@Table(name = "email_verification_tokens")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id", "token"})
@SQLDelete(sql = "UPDATE email_verification_tokens SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class EmailVerificationToken extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "email_verification_tokens_id_seq")
    @SequenceGenerator(name = "email_verification_tokens_id_seq", sequenceName = "email_verification_tokens_id_seq", allocationSize = 1)
    private Long id;
    
    @Column(nullable = false, unique = true)
    private String token;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recruitment_id", nullable = false)
    private TrxRecruitment recruitment;
    
    @Column(nullable = false)
    private LocalDateTime expiryDate;
    
    @Column(nullable = false)
    private Boolean used = false;
    
    @CreationTimestamp
    private Instant createdAt;
    
    @UpdateTimestamp
    private Instant updatedAt;
    
    /**
     * Check if the token has expired
     * @return true if the token has expired, false otherwise
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiryDate);
    }
}
