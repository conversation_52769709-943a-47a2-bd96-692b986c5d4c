package id.co.panindaiichilife.superapp.agent.model;


import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;

@Entity
@Table(name = "trx_edit_profiles")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@SQLDelete(sql = "UPDATE trx_edit_profiles SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class TrxEditProfile extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "trx_edit_profiles_id_seq")
    @SequenceGenerator(name = "trx_edit_profiles_id_seq", sequenceName = "trx_edit_profiles_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    private Agent agent;

    @Audited
    @OneToOne(targetEntity = TrxApprovalHeader.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "trx_approval_header_id")
    private TrxApprovalHeader approvalHeader;

    @Audited
    @Column(columnDefinition = "text")
    private String data;

    @Audited
    @Column(columnDefinition = "text")
    private String oldData;

    @Audited
    @Column(columnDefinition = "text")
    private String detailApproval;

    @Audited
    @Enumerated(EnumType.STRING)
    private Channel channel;

    @Audited
    @Enumerated(EnumType.STRING)
    private ApprovalStatus approvalStatus;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;
}
