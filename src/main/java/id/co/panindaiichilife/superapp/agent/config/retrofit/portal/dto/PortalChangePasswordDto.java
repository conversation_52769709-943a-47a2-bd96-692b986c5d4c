package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalChangePasswordDto {
    @SerializedName("username")
    @JsonProperty("username")
    private String username;

    @SerializedName("oldPassword")
    @JsonProperty("oldPassword")
    private String oldPassword;

    @SerializedName("newPassword")
    @JsonProperty("newPassword")
    private String newPassword;
}
