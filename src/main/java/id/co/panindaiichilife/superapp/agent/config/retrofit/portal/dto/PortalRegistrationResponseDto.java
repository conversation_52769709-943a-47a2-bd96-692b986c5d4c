package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalRegistrationResponseDto {
    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("fieldsError")
    @JsonProperty("fieldsError")
    private List<String> fieldsError;

    @SerializedName("agentReg")
    @JsonProperty("agentReg")
    private AgentReg agentReg;

    @Data
    public static class AgentReg {

        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;
    }
}
