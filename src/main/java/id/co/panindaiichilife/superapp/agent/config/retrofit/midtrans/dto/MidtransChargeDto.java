package id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MidtransChargeDto {

    @SerializedName("transaction_details")
    @JsonProperty("transaction_details")
    private TransactionDetails transactionDetails;

    @SerializedName("item_details")
    @JsonProperty("item_details")
    private List<ItemDetails> itemDetails;

    @SerializedName("customer_details")
    @JsonProperty("customer_details")
    private CustomerDetails customerDetails;

    @SerializedName("gopay")
    @JsonProperty("gopay")
    private Gopay gopay;

    @SerializedName("credit_card")
    @JsonProperty("credit_card")
    private CreditCard creditCard;

    @SerializedName("user_id")
    @JsonProperty("user_id")
    private String userId;

    @Data
    public static class TransactionDetails {

        @SerializedName("currency")
        @JsonProperty("currency")
        private String currency;

        @SerializedName("order_id")
        @JsonProperty("order_id")
        private String orderId;

        @SerializedName("gross_amount")
        @JsonProperty("gross_amount")
        private Long grossAmount;

    }

    @Data
    public static class ItemDetails {

        @SerializedName("id")
        @JsonProperty("id")
        private String id;

        @SerializedName("price")
        @JsonProperty("price")
        private Long price;

        @SerializedName("quantity")
        private Long quantity;

        @SerializedName("name")
        private String name;

    }

    @Data
    public static class CustomerDetails {

        @SerializedName("email")
        private String email;

        @SerializedName("first_name")
        @JsonProperty("first_name")
        private String firstName;

        @SerializedName("last_name")
        @JsonProperty("last_name")
        private String lastName;

        @SerializedName("phone")
        private String phone;
    }

    @Data
    public static class CreditCard {

        @SerializedName("authentication")
        @JsonProperty("authentication")
        private String authentication;

        @SerializedName("save_card")
        @JsonProperty("save_card")
        private Boolean saveCard;

        @SerializedName("secure")
        @JsonProperty("secure")
        private Boolean secure;

        @SerializedName("installment")
        @JsonProperty("installment")
        private Installment installment;

    }

    @Data
    public static class Installment {

        @SerializedName("required")
        @JsonProperty("required")
        private Boolean required;

        @SerializedName("terms")
        @JsonProperty("terms")
        private Map<String, List<Integer>> terms;
    }

    @Data
    public static class Gopay {

        @SerializedName("enable_callback")
        @JsonProperty("enable_callback")
        private Boolean enableCallback;

        @SerializedName("callback_url")
        @JsonProperty("callback_url")
        private String callbackUrl;

    }

}
