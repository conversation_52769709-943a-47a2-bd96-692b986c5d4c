package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.AccessCheckDto;
import id.co.panindaiichilife.superapp.agent.api.dto.ChannelCheckDto;
import id.co.panindaiichilife.superapp.agent.api.dto.ForgotPasswordDto;
import id.co.panindaiichilife.superapp.agent.api.dto.LoginDto;
import id.co.panindaiichilife.superapp.agent.api.form.AccessCheckForm;
import id.co.panindaiichilife.superapp.agent.api.form.ChannelCheckForm;
import id.co.panindaiichilife.superapp.agent.api.form.LoginForm;
import id.co.panindaiichilife.superapp.agent.api.form.ResetPasswordForm;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ForgetPasswordForm;
import id.co.panindaiichilife.superapp.agent.api.validation.ResetPasswordValidator;
import id.co.panindaiichilife.superapp.agent.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;

@RestController("authController")
@RequestMapping("/api")
@Tag(name = "Auth", description = "API Auth")
@Slf4j
@RequiredArgsConstructor
public class AuthController {

    private final ResetPasswordValidator resetPasswordValidator;

    private final AuthService authService;

    @Operation(summary = "Login APP", description = "Login with optional remember me functionality. If rememberMe is true, token expires at end of day (23:59)")
    @PostMapping("/auth/login")
    public LoginDto login(@RequestBody LoginForm request, @RequestHeader(value = "device_id", required = false) String deviceId) {
        request.setDeviceId(deviceId);
        return authService.login(request);
    }

    @Operation(summary = "Login CMS")
    @PostMapping("/cms/auth/login")
    public LoginDto cmsLogin(@RequestBody LoginForm request) {
        return authService.login(request);
    }

    @Operation(summary = "Check Access")
    @PostMapping("/auth/check-access")
    public AccessCheckDto checkAccess(@RequestBody AccessCheckForm request) {
        return authService.checkAccess(request);
    }

    @Operation(summary = "Check User Channel")
    @PostMapping("/auth/check-user-channel")
    public ChannelCheckDto checkUserChannel(@RequestBody ChannelCheckForm request) {
        return authService.checkChannel(request);
    }

    @Operation(summary = "Forget Password")
    @PostMapping("/auth/forget-password")
    public ForgotPasswordDto forgotPassword(
            @Valid @RequestBody ForgetPasswordForm forgetPasswordForm) {
        return authService.forgotPassword(forgetPasswordForm);
    }

    @PostMapping(value = "/auth/reset-password/{token:.+}")
    public void resetPassword(
            @PathVariable String token,
            @Valid @RequestBody ResetPasswordForm resetPasswordForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        resetPasswordValidator.validate(resetPasswordForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult); // NOSONAR - External system dependency
        }

        authService.resetPassword(token, resetPasswordForm);
    }

    @Operation(summary = "Logout - Clear firebase token from device(s)")
    @PostMapping("/auth/logout")
    public void logout(Principal principal, @RequestHeader(value = "device_id", required = false) String deviceId) {
        authService.logout(principal.getName(), deviceId);
    }

}
