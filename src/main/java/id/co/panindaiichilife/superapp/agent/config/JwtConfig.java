package id.co.panindaiichilife.superapp.agent.config;

import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.NimbusJwtEncoder;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Configuration
@RequiredArgsConstructor
public class JwtConfig {

    private static final String PUBLIC_KEY_PATH = "keys/public_key.pem";
    private static final String PRIVATE_KEY_PATH = "keys/private_key.pem";

    @Bean
    public JWKSource<SecurityContext> jwkSource() {
        KeyPair keyPair = loadKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();

        // Build the JWK with a static key ID
        RSAKey rsaKey = new RSAKey.Builder(publicKey)
                .privateKey(privateKey)
                .keyID("static-key-id") // Use a static key ID for consistency
                .build();

        JWKSet jwkSet = new JWKSet(rsaKey);
        return new ImmutableJWKSet<>(jwkSet);
    }

    @Bean
    public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
        return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
    }

    private KeyPair loadKeyPair() {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");

            // Load public key from file
            String publicKeyContent = loadKeyFromFile(PUBLIC_KEY_PATH);
            byte[] publicKeyBytes = Base64.getMimeDecoder().decode(
                    publicKeyContent.replaceAll("-----\\w+ PUBLIC KEY-----", "").replaceAll("\\s+", "")
            );
            PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(publicKeyBytes));

            // Load private key from file
            String privateKeyContent = loadKeyFromFile(PRIVATE_KEY_PATH);
            byte[] privateKeyBytes = Base64.getMimeDecoder().decode(
                    privateKeyContent.replaceAll("-----\\w+ PRIVATE KEY-----", "").replaceAll("\\s+", "")
            );
            PrivateKey privateKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(privateKeyBytes));

            return new KeyPair(publicKey, privateKey);
        } catch (Exception ex) {
            throw new IllegalStateException("Failed to load RSA key pair", ex);
        }
    }

    private String loadKeyFromFile(String keyPath) throws IOException {
        ClassPathResource resource = new ClassPathResource(keyPath);
        return new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
    }

    @Bean
    public JwtEncoder jwtEncoder() {
        return new NimbusJwtEncoder(jwkSource());
    }
}
