package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccessDto;
import id.co.panindaiichilife.superapp.agent.api.filter.AccessFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.AccessScanForm;
import id.co.panindaiichilife.superapp.agent.service.AccessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController("cmsAccessController")
@RequestMapping("/api/cms/access")
@Tag(name = "Access - CMS", description = "API CMS Access")
@Slf4j
@RequiredArgsConstructor
public class AccessCmsController {

    private final AccessService accessService;

    @Operation(summary = "List access")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Access', 'view')")
    public Page<AccessDto> index(@ParameterObject @ModelAttribute("filter") AccessFilter filter,
                                 @ParameterObject @PageableDefault(sort = "domain") Pageable pageable) {
        return accessService.findAll(filter, pageable);
    }

    @Operation(summary = "Scan access")
    @PostMapping(value = "scan")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Access', 'scan')")
    public void scanSave(@Valid @RequestBody AccessScanForm accessScanForm) {
        accessService.scanPackages(accessScanForm.getPackageName());
    }
}
