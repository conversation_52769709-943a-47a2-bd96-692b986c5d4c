package id.co.panindaiichilife.superapp.agent.service.agency.validator;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationKtpDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationKtpDtoBuilder;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationKtpResponseDto;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.enums.ValidationKtpStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;

/**
 * Validator for checking KTP (ID card) data
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KtpValidator extends AbstractRecruitmentValidator {

    private final PortalProvider portalProvider;
    private final GlobalConfigService globalConfigService;

    @Override
    public boolean canValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        return StringUtils.isNotBlank(entity.getNik()) &&
                StringUtils.isNotBlank(entity.getFullName()) &&
                entity.getBirthDate() != null;
    }

    @Override
    protected TrxRecruitment doValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        PortalValidationKtpResponseDto response = validateKtpData(entity);

        if (response != null) {
            // Determine KTP validation status based on response
            if ("200".equals(response.getStatusCode()) && "SUCCESS".equals(response.getCode())) {
                entity.setValidationKtpStatus(ValidationKtpStatus.PASS);
                log.info("KTP validation passed for NIK {}", entity.getNik());
            } else {
                entity.setValidationKtpStatus(ValidationKtpStatus.POSTPONED);
                log.info("KTP validation postponed for NIK {}: {}", entity.getNik(), response.getMessage());
            }
        } else {
            log.warn("KTP validation skipped or failed for NIK {}", entity.getNik());
        }

        return entity;
    }

    @Override
    public String getValidatorName() {
        return "KTP Validator";
    }

    /**
     * Validates KTP data for a recruitment candidate
     *
     * @param trxRecruitment The recruitment entity containing KTP data
     * @return The KTP validation response from the portal
     */
    private PortalValidationKtpResponseDto validateKtpData(TrxRecruitment trxRecruitment) {
        try {
            String user = globalConfigService.getGlobalConfig("advance-ai.user", "UAT-QX");
            boolean isActive = globalConfigService.getGlobalConfig("advance-ai.active", "false").equals("true");

            if (!isActive) {
                log.error("Failed to validate KTP data: {}", "Advance AI is not active");
                // Return an empty response to avoid blocking the recruitment process
                PortalValidationKtpResponseDto emptyResponse = new PortalValidationKtpResponseDto();
                emptyResponse.setStatusCode("500");
                emptyResponse.setCode("ERROR");
                emptyResponse.setMessage("Advance AI is not active");
                return emptyResponse;
            }
            // Create a transaction number using a timestamp and random UUID
            Long trxNum = trxRecruitment.getId();

            // Format birth date as string (YYYY-MM-DD)
            String birthDateStr = trxRecruitment.getBirthDate() != null ? trxRecruitment.getBirthDate().toString() : "";

            // Create the KTP validation request using the builder
            DistributionCode distributionCode = trxRecruitment.getChannel().equals(Channel.AGE) ? DistributionCode.A : DistributionCode.L;
            PortalValidationKtpDto request = new PortalValidationKtpDtoBuilder()
                    .withTrxNum(trxNum.toString())
                    .withDesc1(distributionCode.name(), trxRecruitment.getPositionLevel().name())
                    .withIdNumber(trxRecruitment.getNik())
                    .withName(trxRecruitment.getFullName())
                    .withBirthDate(birthDateStr)
                    .withKtpImage("")
                    .withFaceImage("")
                    .withEmail(trxRecruitment.getEmail())
                    .withPhoneNumber(trxRecruitment.getPhoneNumber())
                    .withCaller(user)
                    .build();

            // Call the portal API to validate KTP data
            Call<PortalValidationKtpResponseDto> call = portalProvider.validateKtp(request);
            Response<PortalValidationKtpResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                return response.body();
            } else {
                log.error("Failed to validate KTP data: {}", response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                // Return an empty response to avoid blocking the recruitment process
                PortalValidationKtpResponseDto emptyResponse = new PortalValidationKtpResponseDto();
                emptyResponse.setStatusCode("500");
                emptyResponse.setCode("ERROR");
                emptyResponse.setMessage("Failed to validate KTP data");
                return emptyResponse;
            }
        } catch (Exception e) {
            log.error("Error validating KTP data", e);
            // Return an empty response to avoid blocking the recruitment process
            PortalValidationKtpResponseDto emptyResponse = new PortalValidationKtpResponseDto();
            emptyResponse.setStatusCode("500");
            emptyResponse.setCode("ERROR");
            emptyResponse.setMessage("Error validating KTP data: " + e.getMessage());
            return emptyResponse;
        }
    }
}
