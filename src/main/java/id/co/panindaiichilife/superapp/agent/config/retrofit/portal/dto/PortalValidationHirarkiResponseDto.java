package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * DTO for the response from the agent upline validation API
 */
@Data
public class PortalValidationHirarkiResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("uplineDTO")
    @JsonProperty("uplineDTO")
    private UplineDTO uplineDTO;

    @Data
    public static class UplineDTO {
        @SerializedName("recruiterCode")
        @JsonProperty("recruiterCode")
        private String recruiterCode;

        @SerializedName("recruiterName")
        @JsonProperty("recruiterName")
        private String recruiterName;

        @SerializedName("directLeaderCode")
        @JsonProperty("directLeaderCode")
        private String directLeaderCode;

        @SerializedName("directLeaderName")
        @JsonProperty("directLeaderName")
        private String directLeaderName;

        @SerializedName("indirectLeaderCode")
        @JsonProperty("indirectLeaderCode")
        private String indirectLeaderCode;

        @SerializedName("indirectLeaderName")
        @JsonProperty("indirectLeaderName")
        private String indirectLeaderName;
    }
}
