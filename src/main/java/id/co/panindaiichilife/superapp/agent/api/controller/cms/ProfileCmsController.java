package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccountDto;
import id.co.panindaiichilife.superapp.agent.api.form.ChangePasswordForm;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ProfileForm;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;

@RestController("cmsProfileController")
@RequestMapping("/api/cms/profile")
@Tag(name = "Profile - CMS", description = "API Profile CMS")
@Slf4j
@RequiredArgsConstructor
public class ProfileCmsController {

    private final UserService userService;

    @Operation(summary = "View current user information")
    @GetMapping(value = "")
    public AccountDto getProfile(Principal principal) {
        return userService.findByUsername(principal.getName());
    }

    @Operation(summary = "View current user information and their access control list")
    @GetMapping(value = "acl")
    public AccountDto acl(Principal principal) {
        return userService.findByUsernameWithAcl(principal.getName());
    }

    @Operation(summary = "Update current user information")
    @PostMapping(value = "")
    public AccountDto update(Principal principal, @Valid @RequestBody ProfileForm profileForm) {
        return userService.update(principal.getName(), profileForm);
    }

    @Operation(summary = "Change current user password")
    @PostMapping(value = "change-password")
    public void changePassword(Principal principal,
                               @Valid @RequestBody ChangePasswordForm changePasswordApiForm) {
        userService.changePassword(principal.getName(), changePasswordApiForm);
    }


    @Operation(summary = "Upload profile picture")
    @PostMapping(value = "profile-picture", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    public FileinputResponse uploadPicture(Principal principal, @RequestParam("file") MultipartFile file) {
        return userService.upload(principal.getName(), "/profile-picture/", file);
    }
}
