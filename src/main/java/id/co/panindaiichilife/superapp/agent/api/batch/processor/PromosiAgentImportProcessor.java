package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.PromosiAgentImportDto;
import id.co.panindaiichilife.superapp.agent.model.PromosiAgent;
import id.co.panindaiichilife.superapp.agent.repository.PromosiAgentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class PromosiAgentImportProcessor implements ItemProcessor<PromosiAgentImportDto, PromosiAgent> {

    private final PromosiAgentRepository promosiAgentRepository;
    
    @Override
    public PromosiAgent process(PromosiAgentImportDto item) {
        return findOrCreatePromosiAgent(item);
    }

    private PromosiAgent findOrCreatePromosiAgent(PromosiAgentImportDto item) {
        PromosiAgent promosiAgent = new PromosiAgent();
        BeanUtils.copyProperties(item, promosiAgent);
        return promosiAgent;
    }
}