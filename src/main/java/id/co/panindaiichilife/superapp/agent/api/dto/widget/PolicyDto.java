package id.co.panindaiichilife.superapp.agent.api.dto.widget;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolicyDto {
    private String policyNumber;
    private String policyHolderName;
    private String insuredName;
    private String agentCode;
    private String agentName;
    private String product;
    private String policyStatus;
    private String riskCommencementDate;
    private String paymentFrequency;
    private String dueDate;
    private double basicPremium;
    private double rtu;
    private String currency;
    private String twisting;
    private String nlgStatus;
    private String lastPaidDate;
    private String lapseDate;
}
