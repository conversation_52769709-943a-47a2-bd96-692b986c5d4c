package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.TrainingPassDto;
import id.co.panindaiichilife.superapp.agent.service.TrainingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.util.List;

@RestController("trainingController")
@RequestMapping("/api/training")
@Tag(name = "Training", description = "API Training")
@Slf4j
@RequiredArgsConstructor
public class TrainingController {

    private final TrainingService trainingService;

    @Operation(summary = "Get Training Agent")
    @GetMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Training', 'view')")
    public List<TrainingPassDto> getMyTraining(Principal principal) {
        return trainingService.getMyTraining(principal.getName());
    }
}
