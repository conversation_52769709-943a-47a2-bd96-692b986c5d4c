package id.co.panindaiichilife.superapp.agent.config;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.server.authorization.client.InMemoryRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;

import java.time.Duration;

@Configuration
@RequiredArgsConstructor
public class AuthorizationServerConfig {

    private final PasswordEncoder passwordEncoder;
    @Value("${rest.security.resource-id}")
    private String resourceId;
    @Value("${rest.security.client-id}")
    private String clientId;
    @Value("${rest.security.client-secret}")
    private String clientSecret;
    @Value("${rest.security.access-token.duration}")
    private int accessTokenDuration = 3600; //in seconds
    @Value("${rest.security.refresh-token.duration}")
    private int refreshTokenDuration = 604800; //in seconds
    @Value("${jwt.secret}")
    private String jwtSecret;

    @Bean
    public RegisteredClientRepository registeredClientRepository() {
        TokenSettings tokenSettings = TokenSettings.builder()
                .accessTokenTimeToLive(Duration.ofSeconds(accessTokenDuration))  // Access token validity (e.g., 60 minutes)
                .refreshTokenTimeToLive(Duration.ofSeconds(refreshTokenDuration))     // Refresh token validity (e.g., 7 days)
                .build();

        RegisteredClient registeredClient = RegisteredClient.withId(resourceId)
                .clientId(clientId)
                .clientSecret(passwordEncoder.encode(clientSecret))
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                .authorizationGrantType(AuthorizationGrantType.PASSWORD)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                .scope("read")
                .scope("write")
                .tokenSettings(tokenSettings)
                .build();

        return new InMemoryRegisteredClientRepository(registeredClient);
    }

    @Bean
    public AuthorizationServerSettings authorizationServerSettings() {
        return AuthorizationServerSettings.builder()
                .tokenEndpoint("/oauth/token")
                .build();
    }
}