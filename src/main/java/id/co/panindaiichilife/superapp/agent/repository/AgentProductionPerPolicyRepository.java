package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.AgentProductionPerPolicy;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface AgentProductionPerPolicyRepository extends BaseRepository<AgentProductionPerPolicy, Long> {

    AgentProductionPerPolicy findByAgentCode(String agentCode);

    List<AgentProductionPerPolicy> findByLeaderCode(String leaderCode);

    @Modifying
    @Transactional
    @Query("DELETE FROM AgentProductionPerPolicy a WHERE a.year = :year AND a.month = :month")
    void deleteByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

    long countByYearAndMonth(Integer year, Integer month);

    long countByAgentCodeAndStatus(String agentCode, String status);

}
