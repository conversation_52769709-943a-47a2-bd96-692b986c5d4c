package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalAgentTerminationResponseDto {
    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("agentReg")
    @JsonProperty("agentReg")
    private AgentUpdate agentUpdate;

    public static class AgentUpdate {
        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("remarks")
        @JsonProperty("remarks")
        private String remarks;
    }
}
