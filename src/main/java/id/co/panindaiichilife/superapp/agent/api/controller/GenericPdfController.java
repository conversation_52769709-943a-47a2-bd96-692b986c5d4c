package id.co.panindaiichilife.superapp.agent.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import id.co.panindaiichilife.superapp.agent.api.dto.pdf.*;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.service.DocumentGenerationService;
import id.co.panindaiichilife.superapp.agent.core.service.PdfService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * Generic controller for generating PDF documents with variable injection.
 * Supports any document type by specifying the document type in the URL path.
 */
@RestController("publicGenericPdfController")
@RequestMapping("/api/public/pdf")
@RequiredArgsConstructor
@Slf4j
public class GenericPdfController {

    // Constants for repeated string literals
    private static final String ATTACHMENT_VALUE = "attachment";
    private static final String APPLICATION_PDF_VALUE = "application/pdf";
    private static final String CONTENT_DISPOSITION_HEADER = "Content-Disposition";

    private final PdfService pdfService;
    private final DocumentGenerationService documentGenerationService;
    private final ObjectMapper objectMapper;

    // DTO-based endpoints for specific document types

    /**
     * Generate PKAJ-AGE document using DTO.
     *
     * @param dto PKAJ-AGE document parameters
     * @return ResponseEntity with PDF content
     */
    @PostMapping("/pkaj-age")
    public ResponseEntity<byte[]> generatePkajAgeDocument(@Valid @RequestBody PkajAgeDocumentDto dto) {
        try {
            byte[] pdfBytes = documentGenerationService.generatePkajAgeDocument(dto);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData(ATTACHMENT_VALUE, "PKAJ-AGE.pdf");

            log.info("Successfully generated PKAJ-AGE PDF");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("Error generating PKAJ-AGE PDF", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Generate PKAJ-AGE document to response stream.
     *
     * @param dto      PKAJ-AGE document parameters
     * @param response HTTP response
     */
    @PostMapping("/pkaj-age/stream")
    public void generatePkajAgeDocumentStream(@Valid @RequestBody PkajAgeDocumentDto dto, HttpServletResponse response) {
        try {
            response.setContentType(APPLICATION_PDF_VALUE);
            response.setHeader(CONTENT_DISPOSITION_HEADER, ATTACHMENT_VALUE + "; filename=PKAJ-AGE.pdf");

            documentGenerationService.generatePkajAgeDocument(dto, response.getOutputStream());

            log.info("Successfully generated PKAJ-AGE PDF to stream");

        } catch (Exception e) {
            log.error("Error generating PKAJ-AGE PDF to stream", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generate PMKAJ-AGE document using DTO.
     *
     * @param dto PMKAJ-AGE document parameters
     * @return ResponseEntity with PDF content
     */
    @PostMapping("/pmkaj-age")
    public ResponseEntity<byte[]> generatePmkajAgeDocument(@Valid @RequestBody PmkajAgeDocumentDto dto) {
        try {
            byte[] pdfBytes = documentGenerationService.generatePmkajAgeDocument(dto);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData(ATTACHMENT_VALUE, "PMKAJ-AGE.pdf");

            log.info("Successfully generated PMKAJ-AGE PDF");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("Error generating PMKAJ-AGE PDF", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Generate KODE-ETIK-AGE document using DTO.
     *
     * @param dto KODE-ETIK-AGE document parameters
     * @return ResponseEntity with PDF content
     */
    @PostMapping("/kode-etik-age")
    public ResponseEntity<byte[]> generateKodeEtikAgeDocument(@Valid @RequestBody KodeEtikAgeDocumentDto dto) {
        try {
            byte[] pdfBytes = documentGenerationService.generateKodeEtikAgeDocument(dto);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData(ATTACHMENT_VALUE, "KODE-ETIK-AGE.pdf");

            log.info("Successfully generated KODE-ETIK-AGE PDF");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("Error generating KODE-ETIK-AGE PDF", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Generate ANTI-TWISTING-AGE document using DTO.
     *
     * @param dto ANTI-TWISTING-AGE document parameters
     * @return ResponseEntity with PDF content
     */
    @PostMapping("/anti-twisting-age")
    public ResponseEntity<byte[]> generateAntiTwistingAgeDocument(@Valid @RequestBody AntiTwistingAgeDocumentDto dto) {
        try {
            byte[] pdfBytes = documentGenerationService.generateAntiTwistingAgeDocument(dto);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData(ATTACHMENT_VALUE, "ANTI-TWISTING-AGE.pdf");

            log.info("Successfully generated ANTI-TWISTING-AGE PDF");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("Error generating ANTI-TWISTING-AGE PDF", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Generate any document type using generic DTO.
     *
     * @param dto Generic document parameters
     * @return ResponseEntity with PDF content
     */
    @PostMapping("/generic")
    public ResponseEntity<byte[]> generateGenericDocument(@Valid @RequestBody GenericDocumentDto dto) {
        try {
            byte[] pdfBytes = documentGenerationService.generateDocument(dto);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData(ATTACHMENT_VALUE, dto.getDocumentType().getDefaultFileName());

            log.info("Successfully generated {} PDF", dto.getDocumentType().getTemplateName());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("Error generating {} PDF", dto.getDocumentType().getTemplateName(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // Legacy generic endpoints

    /**
     * Generate PDF for any document type with JSON payload.
     *
     * @param documentType The document type (e.g., "PKAJ-AGE", "CONTRACT", "INVOICE")
     * @param variables    Map containing the variables to inject into the template
     * @return ResponseEntity with PDF content
     */
    @PostMapping("/{documentType}")
    public ResponseEntity<byte[]> generatePdfWithJson(
            @PathVariable String documentType,
            @RequestBody(required = false) Map<String, Object> variables) {

        try {
            byte[] pdfBytes = pdfService.generateDocumentPdfAsBytes(documentType, variables);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData(ATTACHMENT_VALUE, documentType + ".pdf");

            log.info("Successfully generated PDF for document type: {}", documentType);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("Error generating PDF for document type: {}", documentType, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Generate PDF for any document type with query parameters.
     *
     * @param documentType The document type (e.g., "PKAJ-AGE", "CONTRACT", "INVOICE")
     * @param variables    Map containing the variables from query parameters
     * @param response     HTTP response
     */
    @GetMapping("/{documentType}")
    public void generatePdfWithQueryParams(
            @PathVariable String documentType,
            @RequestParam Map<String, Object> variables,
            HttpServletResponse response) {

        try {
            response.setContentType(APPLICATION_PDF_VALUE);
            response.setHeader(CONTENT_DISPOSITION_HEADER, ATTACHMENT_VALUE + "; filename=" + documentType + ".pdf");

            pdfService.generateDocumentPdf(documentType, variables, response.getOutputStream());

            log.info("Successfully generated PDF for document type: {} with variables: {}", documentType, variables);

        } catch (Exception e) {
            log.error("Error generating PDF for document type: {}", documentType, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generate PDF for any document type without variables (static template).
     *
     * @param documentType The document type (e.g., "PKAJ-AGE", "CONTRACT", "INVOICE")
     * @param response     HTTP response
     */
    @GetMapping("/{documentType}/static")
    public void generateStaticPdf(
            @PathVariable String documentType,
            HttpServletResponse response) {

        try {
            response.setContentType(APPLICATION_PDF_VALUE);
            response.setHeader(CONTENT_DISPOSITION_HEADER, ATTACHMENT_VALUE + "; filename=" + documentType + "-static.pdf");

            pdfService.generateDocumentPdf(documentType, null, response.getOutputStream());

            log.info("Successfully generated static PDF for document type: {}", documentType);

        } catch (Exception e) {
            log.error("Error generating static PDF for document type: {}", documentType, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get PDF as byte array for any document type.
     *
     * @param documentType The document type (e.g., "PKAJ-AGE", "CONTRACT", "INVOICE")
     * @param variables    Map containing the variables to inject into the template
     * @return ResponseEntity with PDF content as byte array
     */
    @PostMapping("/{documentType}/bytes")
    public ResponseEntity<byte[]> generatePdfAsBytes(
            @PathVariable String documentType,
            @RequestBody(required = false) Map<String, Object> variables) {

        try {
            byte[] pdfBytes = pdfService.generateDocumentPdfAsBytes(documentType, variables);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);

            log.info("Successfully generated PDF bytes for document type: {}", documentType);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("Error generating PDF bytes for document type: {}", documentType, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Health check endpoint to verify PDF service is working.
     *
     * @return Simple response indicating service status
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("PDF Service is running");
    }

    /**
     * Testing endpoint to generate PDF from uploaded HTML template.
     * This endpoint allows you to upload an HTML file and optionally provide variables
     * to test your HTML template before integrating it into the system.
     *
     * @param htmlFile  HTML template file to upload
     * @param variables Optional JSON string containing variables for substitution (use {{variableName}} in HTML)
     * @param filename  Optional filename for the generated PDF (defaults to "test-template.pdf")
     * @return ResponseEntity with PDF content
     */
    @PostMapping(value = "/test-template", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<byte[]> testHtmlTemplate(
            @RequestParam("htmlFile") MultipartFile htmlFile,
            @RequestParam(value = "variables", required = false) String variables,
            @RequestParam(value = "filename", required = false, defaultValue = "test-template.pdf") String filename) {

        try {
            // Validate HTML file
            if (htmlFile.isEmpty()) {
                log.error("HTML file is empty");
                return ResponseEntity.badRequest().build();
            }

            // Read HTML content
            String htmlContent = new String(htmlFile.getBytes(), "UTF-8");
            log.info("Processing HTML template with size: {} bytes", htmlContent.length());

            // Parse variables if provided
            Map<String, Object> variablesMap = null;
            if (variables != null && !variables.trim().isEmpty()) {
                try {
                    variablesMap = objectMapper.readValue(variables, Map.class);
                    log.info("Parsed variables: {}", variablesMap.keySet());
                } catch (Exception e) {
                    log.error("Error parsing variables JSON: {}", variables, e);
                    return ResponseEntity.badRequest().build();
                }
            }

            // Generate PDF
            byte[] pdfBytes;
            if (variablesMap != null && !variablesMap.isEmpty()) {
                pdfBytes = pdfService.generatePdfFromRawHtmlWithVariablesAsBytes(htmlContent, variablesMap);
                log.info("Generated PDF with variables substitution");
            } else {
                pdfBytes = pdfService.generatePdfFromRawHtmlAsBytes(htmlContent);
                log.info("Generated PDF without variables");
            }

            // Prepare response
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData(ATTACHMENT_VALUE, filename);

            log.info("Successfully generated test PDF: {}", filename);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("Error generating PDF from uploaded HTML template", e);
            throw new BadRequestException(e.getMessage());
        }
    }
}
