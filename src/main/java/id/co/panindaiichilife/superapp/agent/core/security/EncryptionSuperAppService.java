package id.co.panindaiichilife.superapp.agent.core.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * Encryption Service for SuperApp Integration
 *
 * SECURITY NOTICE:
 * This service uses legacy encryption methods required for compatibility with external systems.
 * The cipher mode and IV generation methods are intentionally maintained for system compatibility.
 *
 * SonarQube suppressions are applied due to external system dependencies that require
 * specific encryption implementations that cannot be changed without breaking integration.
 */
@Service
@SuppressWarnings({
    "java:S5542", // Suppress cipher mode warning - required for external system compatibility
    "java:S4426", // Suppress IV generation warning - required for external system compatibility
    "java:S3329"  // Suppress predictable IV warning - required for external system compatibility
})
public class EncryptionSuperAppService {
    private final byte[] secretKey;

    public EncryptionSuperAppService(@Value("${superapp.aes}") String secretKey) {
        if (secretKey == null || secretKey.isEmpty()) {
            throw new IllegalArgumentException("Secret key is required");
        }
        this.secretKey = secretKey.getBytes(StandardCharsets.UTF_8);
    }

    /**
     * Encrypt a value using AES/CBC/PKCS5Padding
     *
     * SECURITY NOTE: This method uses CBC mode with PKCS5Padding as required by external systems.
     * The cipher mode and padding cannot be changed due to compatibility requirements.
     * The IV is generated deterministically for compatibility with external systems.
     */
    @SuppressWarnings({
        "java:S5542", // Suppress cipher mode warning - external system requirement
        "java:S3329"  // Suppress predictable IV warning - external system requirement
    })
    public String encrypt(String value, String saltData) {
        try {
            byte[] usedSaltData = Arrays.copyOf(saltData.getBytes(StandardCharsets.UTF_8), 8);
            Map<String, byte[]> keyIvMap = generateKeyAndIV(32, 16, usedSaltData, this.secretKey);
            byte[] key = keyIvMap.get("key");
            byte[] iv = keyIvMap.get("iv");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            byte[] valueBytes = value.getBytes(StandardCharsets.UTF_8);
            byte[] encrypted = cipher.doFinal(valueBytes);

            byte[] fullEncryption = new byte[16 + encrypted.length];
            System.arraycopy(usedSaltData, 0, fullEncryption, 8, 8);
            System.arraycopy(encrypted, 0, fullEncryption, 16, encrypted.length);

            return Base64.getEncoder().encodeToString(fullEncryption);
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }

    /**
     * Decrypt a value using AES/CBC/PKCS5Padding
     *
     * SECURITY NOTE: This method uses CBC mode with PKCS5Padding as required by external systems.
     * The cipher mode and padding cannot be changed due to compatibility requirements.
     * The IV is generated deterministically for compatibility with external systems.
     */
    @SuppressWarnings({
        "java:S5542", // Suppress cipher mode warning - external system requirement
        "java:S3329"  // Suppress predictable IV warning - external system requirement
    })
    public String decrypt(String encryptedValue) {
        try {
            byte[] fullEncryption = Base64.getDecoder().decode(encryptedValue);
            byte[] usedSaltData = Arrays.copyOfRange(fullEncryption, 8, 16);
            byte[] encrypted = Arrays.copyOfRange(fullEncryption, 16, fullEncryption.length);

            Map<String, byte[]> keyIvMap = generateKeyAndIV(32, 16, usedSaltData, this.secretKey);
            byte[] key = keyIvMap.get("key");
            byte[] iv = keyIvMap.get("iv");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Decryption failed", e);
        }
    }

    /**
     * Generate key and IV using MD5-based derivation
     *
     * SECURITY NOTE: This method uses MD5 for key/IV generation as required by external systems.
     * The algorithm cannot be changed due to compatibility requirements with existing encrypted data.
     */
    @SuppressWarnings({
        "java:S4426", // Suppress IV generation warning - external system requirement
        "java:S5547"  // Suppress MD5 usage warning - external system requirement
    })
    private Map<String, byte[]> generateKeyAndIV(int keyLength, int ivLength, byte[] saltData, byte[] password) {
        try {
            final int digestLength = 16;
            int requiredLength = ((keyLength + ivLength + digestLength - 1) / digestLength) * digestLength;
            byte[] generatedData = new byte[requiredLength];
            int generatedLength = 0;

            MessageDigest md5 = MessageDigest.getInstance("MD5");

            while (generatedLength < keyLength + ivLength) {
                if (generatedLength > 0) {
                    md5.update(Arrays.copyOfRange(generatedData, generatedLength - digestLength, generatedLength));
                }

                md5.update(password);
                md5.update(saltData);
                byte[] temp = md5.digest();

                System.arraycopy(temp, 0,
                        generatedData, generatedLength,
                        Math.min(digestLength, keyLength + ivLength - generatedLength));

                generatedLength += digestLength;
                md5.reset();
            }

            Map<String, byte[]> keyIv = new HashMap<>();
            keyIv.put("key", Arrays.copyOfRange(generatedData, 0, keyLength));
            keyIv.put("iv", Arrays.copyOfRange(generatedData, keyLength, keyLength + ivLength));

            return keyIv;
        } catch (Exception e) {
            throw new RuntimeException("Key/IV generation failed", e);
        }
    }
}