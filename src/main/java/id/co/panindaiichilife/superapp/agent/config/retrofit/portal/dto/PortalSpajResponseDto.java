package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalSpajResponseDto {

    @SerializedName("responseCode")
    @JsonProperty("responseCode")
    private String responseCode;

    @SerializedName("responseStatus")
    @JsonProperty("responseStatus")
    private String responseStatus;

    @SerializedName("totalData")
    @JsonProperty("totalData")
    private int totalData;

    @SerializedName("spajDatas")
    @JsonProperty("spajDatas")
    private List<SpajDataDto> spajDatas;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @Data
    public static class SpajDataDto {

        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("agentName")
        @JsonProperty("agentName")
        private String agentName;

        @SerializedName("spajNumber")
        @JsonProperty("spajNumber")
        private String spajNumber;

        @SerializedName("entryDate")
        @JsonProperty("entryDate")
        private String entryDate;

        @SerializedName("policyHolderName")
        @JsonProperty("policyHolderName")
        private String policyHolderName;

        @SerializedName("insuredName")
        @JsonProperty("insuredName")
        private String insuredName;

        @SerializedName("sumInsured")
        @JsonProperty("sumInsured")
        private double sumInsured;

        @SerializedName("basicPremium")
        @JsonProperty("basicPremium")
        private double basicPremium;

        @SerializedName("regularTopUp")
        @JsonProperty("regularTopUp")
        private double regularTopUp;

        @SerializedName("singleTopUp")
        @JsonProperty("singleTopUp")
        private double singleTopUp;

        @SerializedName("currency")
        @JsonProperty("currency")
        private String currency;

        @SerializedName("frequency")
        @JsonProperty("frequency")
        private String frequency;

        @SerializedName("paymentMethod")
        @JsonProperty("paymentMethod")
        private String paymentMethod;

        @SerializedName("spajStatus")
        @JsonProperty("spajStatus")
        private String spajStatus;

        @SerializedName("descriptionPending")
        @JsonProperty("descriptionPending")
        private String descriptionPending;

        @SerializedName("letterNo")
        @JsonProperty("letterNo")
        private String letterNo;

        @SerializedName("spbType")
        @JsonProperty("spbType")
        private String spbType;

        @SerializedName("acceptanceDate")
        @JsonProperty("acceptanceDate")
        private String acceptanceDate;

        @SerializedName("submitDate")
        @JsonProperty("submitDate")
        private String submitDate;

        @SerializedName("flagEM")
        @JsonProperty("flagEM")
        private String flagEM;

        @SerializedName("collCode")
        @JsonProperty("collCode")
        private String collCode;

        @SerializedName("policyNumber")
        @JsonProperty("policyNumber")
        private String policyNumber;

        @SerializedName("autogenDate")
        @JsonProperty("autogenDate")
        private String autogenDate;

        @SerializedName("remarkPending")
        @JsonProperty("remarkPending")
        private String remarkPending;

        @SerializedName("product")
        @JsonProperty("product")
        private String product;
    }
}
