package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.UserType;
import id.co.panindaiichilife.superapp.agent.model.User;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.Collection;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@Slf4j
public class AccountDto extends BaseDto<User> {

    private Long id;

    private String username;

    private String name;

    private String email;

    private String phone;

    private String timezone;

    private String picture;

    private UserType userType;

    private Channel channel;

    private Collection<RoleDto> roles;

    private Collection<AccessDto> accesses;

    private Collection<BranchDto> branches;

    private Instant created;

    private Instant modified;

    @Override
    public void copy(User data) {
        super.copy(data);
        roles = BaseDto.of(RoleDto.class, data.getRoles());
        branches = BaseDto.of(BranchDto.class, data.getBranches());
    }
}
