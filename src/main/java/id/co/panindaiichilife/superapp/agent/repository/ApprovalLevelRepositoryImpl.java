package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.ApprovalLevel;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class ApprovalLevelRepositoryImpl implements ApprovalLevelRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Optional<ApprovalLevel> findApplicableLevel(
            TrxType trxType,
            Integer levelNumber,
            Channel channel,
            Set<String> requesterRoles,
            Set<String> approverRoles,
            Set<String> branchCodes) {

        StringBuilder jpql = new StringBuilder();
        jpql.append("SELECT al FROM ApprovalLevel al WHERE ");
        jpql.append("al.trxType = :trxType AND ");
        jpql.append("al.levelNumber = :levelNumber AND ");
        jpql.append("(al.requesterRole IN :requesterRoles OR al.requesterRole IS NULL) AND ");
        jpql.append("(al.channel = :channel OR al.channel IS NULL) AND ");

        // Handle branch codes conditionally
        if (branchCodes != null && !branchCodes.isEmpty()) {
            jpql.append("(al.branchCode IN :branchCodes OR al.branchCode IS NULL) AND ");
        } else {
            // If no branch codes provided, include all levels (both with and without branch restrictions)
            jpql.append("1=1 AND ");
        }

        jpql.append("al.isActive = true ");
        jpql.append("ORDER BY CASE WHEN al.branchCode IS NULL THEN 1 ELSE 0 END");

        TypedQuery<ApprovalLevel> query = entityManager.createQuery(jpql.toString(), ApprovalLevel.class);
        query.setParameter("trxType", trxType);
        query.setParameter("levelNumber", levelNumber);
        query.setParameter("channel", channel);
        query.setParameter("requesterRoles", requesterRoles);

        // Only set branchCodes parameter if it's not null and not empty
        if (branchCodes != null && !branchCodes.isEmpty()) {
            query.setParameter("branchCodes", branchCodes);
        }

        List<ApprovalLevel> results = query.getResultList();

        log.debug("Query : {}", query);

        // Filter results to match approver roles (supporting comma-separated roles)
        if (approverRoles != null && !approverRoles.isEmpty()) {
            results = results.stream()
                    .filter(level -> hasMatchingApproverRole(level.getApproverRole(), approverRoles))
                    .toList();
        }

        return results.isEmpty() ? Optional.empty() : Optional.of(results.getFirst());
    }

    @Override
    public List<ApprovalLevel> findByApproverRoleInAndBranchCodeIn(
            Set<String> roles,
            Set<String> branchCodes) {

        StringBuilder jpql = new StringBuilder();
        jpql.append("SELECT al FROM ApprovalLevel al WHERE ");

        // Handle branch codes conditionally
        if (branchCodes != null && !branchCodes.isEmpty()) {
            jpql.append("(al.branchCode IN :branchCodes OR al.branchCode IS NULL)");
        } else {
            // If no branch codes provided, include all levels (both with and without branch restrictions)
            jpql.append("1=1");
        }

        TypedQuery<ApprovalLevel> query = entityManager.createQuery(jpql.toString(), ApprovalLevel.class);

        // Only set branchCodes parameter if it's not null and not empty
        if (branchCodes != null && !branchCodes.isEmpty()) {
            query.setParameter("branchCodes", branchCodes);
        }

        List<ApprovalLevel> results = query.getResultList();

        // Filter results to match approver roles (supporting comma-separated roles)
        if (roles != null && !roles.isEmpty()) {
            results = results.stream()
                    .filter(level -> hasMatchingApproverRole(level.getApproverRole(), roles))
                    .collect(Collectors.toList());
        }

        return results;
    }

    /**
     * Checks if any of the user's roles match the approver role(s) defined in the approval level.
     * Supports both single roles and comma-separated multiple roles.
     *
     * @param approverRoleConfig The approver role configuration (can be single role or comma-separated)
     * @param userRoles          The set of roles the user has
     * @return true if there's a match, false otherwise
     */
    private boolean hasMatchingApproverRole(String approverRoleConfig, Set<String> userRoles) {
        if (approverRoleConfig == null || userRoles == null || userRoles.isEmpty()) {
            return false;
        }

        // Split the approver role configuration by comma and trim whitespace
        Set<String> configuredRoles = Arrays.stream(approverRoleConfig.split(","))
                .map(String::trim)
                .filter(role -> !role.isEmpty())
                .collect(Collectors.toSet());

        // Check if any of the user's roles match any of the configured roles
        return configuredRoles.stream().anyMatch(userRoles::contains);
    }
}
