package id.co.panindaiichilife.superapp.agent.api.controller.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRejoinApplicationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRejoinEligibleCandidateDto;
import id.co.panindaiichilife.superapp.agent.api.filter.agency.CandidateRejoinFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.agency.RejoinListFilter;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinCancellationForm;
import id.co.panindaiichilife.superapp.agent.api.validation.TrxRejoinApplicationValidator;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRejoinService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;

@RestController("agentRejoinController")
@RequestMapping("/api/agency/rejoin")
@Tag(name = "Agency - Rejoin", description = "API Agent Rejoin")
@Slf4j
@RequiredArgsConstructor
public class TrxRejoinController {
    private final TrxRejoinService rejoinService;
    private final TrxRejoinApplicationValidator rejoinApplicationValidator;

    @Operation(summary = "Submit rejoin request")
    @PostMapping
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Rejoin', 'submit')")
    public TrxRejoinApplicationDto submit(Principal principal,
                                          @Valid @RequestBody TrxRejoinApplicationForm form,
                                          BindingResult bindingResult) throws MethodArgumentNotValidException {
        rejoinApplicationValidator.validate(form, bindingResult);

        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }

        return rejoinService.submitApplication(principal.getName(), form);
    }

    @Operation(summary = "Get eligible candidates for rejoin by current user")
    @GetMapping("/eligible-candidates")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Rejoin', 'view')")
    public Page<TrxRejoinEligibleCandidateDto> eligibleCandidates(
            Principal principal,
            @ParameterObject @ModelAttribute CandidateRejoinFilter filter,
            @ParameterObject @PageableDefault(sort = "agentName", direction = Sort.Direction.DESC) Pageable pageable) {
        return rejoinService.retrieveEligibleCandidates(principal.getName(), filter, pageable);
    }

    @Operation(summary = "Fetch detail rejoin application by ID")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Rejoin', 'view')")
    public TrxRejoinApplicationDto getDetailApplication(@PathVariable Long id) {
        return rejoinService.getDetailApplication(id);
    }

    @Operation(summary = "Retrieve all active rejoin applications by current user")
    @GetMapping
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Rejoin', 'view')")
    public Page<TrxRejoinApplicationDto> getAllRejoinApplicationsByCurrentUser(
            Principal principal,
            @ParameterObject @ModelAttribute RejoinListFilter filter,
            @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        return rejoinService.getAllRejoinApplicationsByCurrentUser(principal.getName(), filter, pageable);
    }

    @Operation(summary = "Cancel rejoin application")
    @PutMapping("/cancel")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Rejoin', 'update')")
    public void cancelApplication(Principal principal, @RequestBody TrxRejoinCancellationForm form) {
        rejoinService.cancelApplication(principal.getName(), form);
    }

    @Operation(summary = "Expire stale rejoin applications")
    @PutMapping("/expire")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Rejoin', 'update')")
    public void expireStaleApplications() {
        rejoinService.expireStaleApplications();
    }

    @Operation(summary = "Revise rejoin application")
    @PatchMapping(value = "/revise/{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Rejoin', 'revise')")
    public TrxRejoinApplicationDto revise(Principal principal,
                                          @PathVariable Long id,
                                          @Valid @RequestBody TrxRejoinApplicationForm form,
                                          BindingResult bindingResult) throws MethodArgumentNotValidException {
        rejoinApplicationValidator.validate(form, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }

        return rejoinService.revise(principal.getName(), id, form);
    }

    @Operation(summary = "Upload Documents")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.Rejoin', 'upload')")
    public FileinputResponse uploadDocument(Principal principal, @RequestParam("file") MultipartFile file) {
        return rejoinService.upload(principal.getName(), file);
    }

}
