package id.co.panindaiichilife.superapp.agent.core.data.filter;

import id.co.panindaiichilife.superapp.agent.core.support.type.IntRange;
import id.co.panindaiichilife.superapp.agent.core.support.type.LocalDateRange;
import id.co.panindaiichilife.superapp.agent.core.support.type.LongRange;
import id.co.panindaiichilife.superapp.agent.core.support.type.StringRange;
import id.co.panindaiichilife.superapp.agent.core.util.ExpressionUtils;
import id.co.panindaiichilife.superapp.agent.core.view.FilterMode;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.persistence.criteria.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class FieldFilter<E> extends AbstractFilter<E> {

    @Setter
    @Getter
    private Map<String, FilterMode> modes = new HashMap<>();

    protected FieldFilter() {
    }

    @Override
    public Predicate getFilterPredicate(Root<E> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        List<Predicate> predicates = new ArrayList<>();

        //iterate over all declared fields in this filter
        Field[] fields = getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                Predicate p = getFieldPredicate(field, root, query, cb);
                if (p != null) {
                    predicates.add(p);
                }
            } catch (Exception ex) {
                log.warn("Unable to perform filtering on field: " + field.getName(), ex);
            }
        }

        return predicates.isEmpty()
                ? null
                : cb.and(predicates.toArray(new Predicate[predicates.size()]));
    }

    @SuppressWarnings("unchecked")
    private Predicate getFieldPredicate(Field field,
                                        Root root, CriteriaQuery<?> query, CriteriaBuilder cb) throws ReflectiveOperationException {
        Class type = field.getType();

        //read FilterParam annotation
        FilterParam fp = field.getAnnotation(FilterParam.class);
        if (fp == null) {
            return null;
        }

        //read filter param value, skip if null
        Object value = PropertyUtils.getProperty(this, field.getName());
        if (value == null) {
            return null;
        } else if (Serializable.class.isAssignableFrom(type) && "".equals(value)) {
            return null;
        }

        //read filter param list value, skip if null
        if (value instanceof List && (CollectionUtils.isEmpty((List) value))) {
            return null;
        }

        if (!fp.hasField()) {
            return cb.conjunction();
        }

        //determine column name from annotation's value, use field name for default
        String column = field.getName();
        if (!StringUtils.isBlank(fp.value())) {
            column = fp.value();
        }

        //parse '.' notation in column name as JOIN relation
        //for example: 'user.name' or 'user.supervisor.name'
        From from;
        if (column.indexOf('.') < 0) {
            from = root;
        } else {
            String joinPath = StringUtils.substringBeforeLast(column, ".");
            column = StringUtils.substringAfterLast(column, ".");

            from = ExpressionUtils.transitiveJoin(root, joinPath);
        }

        //resolve path to the specified column
        Expression path = from.get(column);
        if (fp.typeCast()) {
            path = path.as(type);
        }

        //build predicate based on selected mode
        FilterMode mode = getEffectiveModeForField(field);
        return getFieldPredicate(column, value, mode, path, query, cb);

    }

    /**
     * Obtain Predicate for a given field. Subclasses can override this method to provide specific
     * implementation for their fields.
     *
     * @param column the column part of a given path, e.g.: 'name'
     * @param value  the value to look for
     * @param mode   filtering mode chosen by the user
     * @param path   JPA Expression object referring to the entity's column
     * @param query  JPA CriteriaQuery object
     * @param cb     JPA CriteriaBuilder object
     */
    @SuppressWarnings("unchecked")
    protected Predicate getFieldPredicate(
            String column, Object value, FilterMode mode,
            Expression path, CriteriaQuery query, CriteriaBuilder cb) {
        switch (mode) {
            case LIKE:
                return handleLikeMode(value, path, cb);
            case LE:
                return handleLeMode(value, path, cb);
            case GE:
                return handleGeMode(value, path, cb);
            case DATE:
                return handleDateMode(value, path, cb);
            case TIME:
                return handleTimeMode(value, path, cb);
            case DATE_RANGE:
                return handleDateRangeMode(value, path, cb);
            case BETWEEN:
                return handleBetweenMode(value, path, cb);
            default:
                return handleDefaultMode(value, path, cb);
        }
    }

    @SuppressWarnings("unchecked")
    private Predicate handleLikeMode(Object value, Expression path, CriteriaBuilder cb) {
        Expression<String> fieldVal = cb.lower(path);
        if (value instanceof StringRange) {
            StringRange range = (StringRange) value;
            String valueString = range.getMinimum().toLowerCase();
            return cb.like(fieldVal, "%" + valueString + "%");
        } else {
            String valueString = (String) value;
            valueString = valueString.toLowerCase();
            return cb.like(fieldVal, "%" + valueString + "%");
        }
    }

    @SuppressWarnings("unchecked")
    private Predicate handleLeMode(Object value, Expression path, CriteriaBuilder cb) {
        if (value instanceof Number) {
            return cb.le(path, (Number) value);
        } else if (value instanceof Comparable) {
            return cb.lessThanOrEqualTo(path, (Comparable) value);
        } else if (value instanceof LocalDateRange) {
            LocalDateRange range = (LocalDateRange) value;
            LocalDate min = range.getMinimum();
            return cb.lessThanOrEqualTo(path, min);
        } else if (value instanceof IntRange) {
            IntRange range = (IntRange) value;
            Integer number = range.getMinimum();
            return cb.lessThanOrEqualTo(path, number);
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    private Predicate handleGeMode(Object value, Expression path, CriteriaBuilder cb) {
        if (value instanceof Number) {
            return cb.ge(path, (Number) value);
        } else if (value instanceof Comparable) {
            return cb.greaterThanOrEqualTo(path, (Comparable) value);
        } else if (value instanceof LocalDateRange) {
            LocalDateRange range = (LocalDateRange) value;
            LocalDate max = range.getMaximum();
            return cb.greaterThanOrEqualTo(path, max);
        } else if (value instanceof IntRange) {
            IntRange range = (IntRange) value;
            Integer number = range.getMaximum();
            return cb.greaterThanOrEqualTo(path, number);
        } else {
            return null;
        }
    }

    private Predicate handleDateMode(Object value, Expression path, CriteriaBuilder cb) {
        if (value instanceof LocalDateRange) {
            LocalDateRange range = (LocalDateRange) value;
            LocalDate min = range.getMinimum();
            return cb.equal(path, min);
        } else {
            return cb.equal(path, value);
        }
    }

    private Predicate handleTimeMode(Object value, Expression path, CriteriaBuilder cb) {
        if (value instanceof LocalTime) {
            LocalTime start = (LocalTime) value;
            LocalTime end = start.plusMinutes(1).minusNanos(1);
            return cb.between(path, start, end);
        } else {
            return null;
        }
    }

    private Predicate handleDateRangeMode(Object value, Expression path, CriteriaBuilder cb) {
        if (value instanceof LocalDateRange) {
            LocalDateRange range = (LocalDateRange) value;
            LocalDate min = range.getMinimum();
            LocalDate max = range.getMaximum();
            return cb.and(
                    cb.greaterThanOrEqualTo(path, min),
                    cb.lessThanOrEqualTo(path, max)
            );
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    private Predicate handleBetweenMode(Object value, Expression path, CriteriaBuilder cb) {
        if (value instanceof IntRange) {
            IntRange range = (IntRange) value;
            return cb.and(
                    cb.greaterThanOrEqualTo(path, range.getMinimum()),
                    cb.lessThanOrEqualTo(path, range.getMaximum())
            );
        } else if (value instanceof LongRange) {
            LongRange range = (LongRange) value;
            return cb.and(
                    cb.greaterThanOrEqualTo(path, range.getMinimum()),
                    cb.lessThanOrEqualTo(path, range.getMaximum())
            );
        } else if (value instanceof StringRange) {
            StringRange range = (StringRange) value;
            return cb.and(
                    cb.greaterThanOrEqualTo(path, range.getMinimum()),
                    cb.lessThanOrEqualTo(path, range.getMaximum())
            );
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    private Predicate handleDefaultMode(Object value, Expression path, CriteriaBuilder cb) {
        if (value instanceof IntRange) {
            IntRange range = (IntRange) value;
            return cb.equal(path, range.getMinimum());
        } else if (value instanceof LongRange) {
            LongRange range = (LongRange) value;
            return cb.equal(path, range.getMinimum());
        } else if (value instanceof StringRange) {
            StringRange range = (StringRange) value;
            return cb.equal(path, range.getMinimum());
        } else if (value instanceof List) {
            List<Object> objects = (List) value;
            return cb.in(path).value(objects);
        }
        return cb.equal(path, value);
    }

    public FilterMode getModeForField(Field field) {
        return modes.get(field.getName());
    }

    public FilterMode getEffectiveModeForField(Field field) {
        FilterMode mode = getModeForField(field);

        if (mode != null) {
            return mode;
        } else {
            FilterMode[] modes = getModeOptionsForField(field);
            return modes[0];
        }
    }

    public FilterMode[] getModeOptionsForField(Field field) {
        FilterParam fp = field.getAnnotation(FilterParam.class);
        if (fp != null && fp.modes().length > 0) {
            return fp.modes();
        } else {
            return FilterMode.getDefaultModesForType(field.getType());
        }
    }

    @Override
    @Parameter(hidden = true)
    public String getFilterQuery() {
        List<String> params = new ArrayList<>();

        addFieldParams(params);
        addModeParams(params);

        return params.isEmpty() ? null : StringUtils.join(params, "&");
    }

    private void addFieldParams(List<String> params) {
        Field[] fields = getClass().getDeclaredFields();

        for (Field field : fields) {
            FilterParam fp = field.getAnnotation(FilterParam.class);
            if (fp == null) {
                continue;
            }

            try {
                Object value = PropertyUtils.getProperty(this, field.getName());
                if (value == null) {
                    continue;
                }

                addFieldValueParams(params, field, value);

            } catch (ReflectiveOperationException | ClassCastException ex) {
                // Ignore exceptions during field processing
            }
        }
    }

    @SuppressWarnings("unchecked")
    private void addFieldValueParams(List<String> params, Field field, Object value) {
        if (value instanceof List) {
            List<Object> objects = (List) value;
            for (Object row : objects) {
                params.add(field.getName() + "=" + row);
            }
        } else {
            params.add(field.getName() + "=" + value.toString());
        }
    }

    private void addModeParams(List<String> params) {
        for (Map.Entry<String, FilterMode> entry : modes.entrySet()) {
            if (entry.getValue() != null) {
                params.add("modes[" + entry.getKey() + "]" + "=" + entry.getValue());
            }
        }
    }
}
