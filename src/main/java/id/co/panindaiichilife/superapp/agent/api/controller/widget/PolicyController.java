package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.PolicyDto;
import id.co.panindaiichilife.superapp.agent.api.filter.PolicyFilter;
import id.co.panindaiichilife.superapp.agent.enums.PolicyStatus;
import id.co.panindaiichilife.superapp.agent.service.widget.PolicyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.time.LocalDate;
import java.util.List;

@RestController("widgetPolicyController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class PolicyController {

    private final PolicyService policyService;

    @Operation(summary = "Get widget Policy Lapsed")
    @GetMapping(value = "policy-lapsed")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.PolicyLapsed', 'view')")
    public List<PolicyDto> getPolicyLapsed(Principal principal,
                                           @ParameterObject @ModelAttribute("filter") PolicyFilter filter) {

        LocalDate now = LocalDate.now();
        if (filter.getStartDate() == null) {
            filter.setStartDate(now.minusYears(1).withDayOfMonth(1));
        }

        if (filter.getEndDate() == null) {
            filter.setEndDate(now.withDayOfMonth(now.lengthOfMonth()));
        }

        filter.setValueStatus(PolicyStatus.LAPSE);
        return policyService.getPolicyList(principal.getName(), filter);
    }

    @Operation(summary = "Get widget Policy Lapsed with Pagination")
    @GetMapping(value = "policy-lapsed/pageable")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.PolicyLapsed', 'view')")
    public Page<PolicyDto> getPolicyLapsedPageable(Principal principal,
                                                   @ParameterObject @ModelAttribute("filter") PolicyFilter filter,
                                                   @ParameterObject @PageableDefault(sort = "lapseDate", direction = Sort.Direction.DESC) Pageable pageable) {

        LocalDate now = LocalDate.now();
        if (filter.getStartDate() == null) {
            filter.setStartDate(now.minusYears(1).withDayOfMonth(1));
        }

        if (filter.getEndDate() == null) {
            filter.setEndDate(now.withDayOfMonth(now.lengthOfMonth()));
        }

        filter.setValueStatus(PolicyStatus.LAPSE);
        return policyService.getPolicyListPageable(principal.getName(), filter, pageable);
    }
}
