package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalValidationAmlResponseDto {
    @SerializedName("code")
    @JsonProperty("code")
    private String code;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("datas")
    @JsonProperty("datas")
    private List<AmlData> datas;

    @SerializedName("checkCount")
    @JsonProperty("checkCount")
    private Integer checkCount;

    @SerializedName("matchCount")
    @JsonProperty("matchCount")
    private Integer matchCount;

    @Data
    public static class AmlData {
        @SerializedName("checkId")
        @JsonProperty("checkId")
        private String checkId;

        @SerializedName("checkName")
        @JsonProperty("checkName")
        private String checkName;

        @SerializedName("checkDob")
        @JsonProperty("checkDob")
        private String checkDob;

        @SerializedName("checkedPolicyNo")
        @JsonProperty("checkedPolicyNo")
        private String checkedPolicyNo;

        @SerializedName("checkBranch")
        @JsonProperty("checkBranch")
        private String checkBranch;

        @SerializedName("publishDate")
        @JsonProperty("publishDate")
        private String publishDate;

        @SerializedName("idInternal")
        @JsonProperty("idInternal")
        private String idInternal;

        @SerializedName("sdnType")
        @JsonProperty("sdnType")
        private String sdnType;

        @SerializedName("name")
        @JsonProperty("name")
        private String name;

        @SerializedName("placeOfBirth")
        @JsonProperty("placeOfBirth")
        private String placeOfBirth;

        @SerializedName("dateOfBirth")
        @JsonProperty("dateOfBirth")
        private String dateOfBirth;

        @SerializedName("source")
        @JsonProperty("source")
        private String source;

        @SerializedName("address")
        @JsonProperty("address")
        private String address;

        @SerializedName("remark")
        @JsonProperty("remark")
        private String remark;

        @SerializedName("position")
        @JsonProperty("position")
        private String position;

        @SerializedName("kasus")
        @JsonProperty("kasus")
        private String kasus;

        @SerializedName("alias1")
        @JsonProperty("alias1")
        private String alias1;

        @SerializedName("alias2")
        @JsonProperty("alias2")
        private String alias2;

        @SerializedName("alias3")
        @JsonProperty("alias3")
        private String alias3;

        @SerializedName("alias4")
        @JsonProperty("alias4")
        private String alias4;

        @SerializedName("alias5")
        @JsonProperty("alias5")
        private String alias5;

        @SerializedName("alias6")
        @JsonProperty("alias6")
        private String alias6;

        @SerializedName("alias7")
        @JsonProperty("alias7")
        private String alias7;

        @SerializedName("alias8")
        @JsonProperty("alias8")
        private String alias8;

        @SerializedName("alias9")
        @JsonProperty("alias9")
        private String alias9;

        @SerializedName("alias10")
        @JsonProperty("alias10")
        private String alias10;
    }
}
