package id.co.panindaiichilife.superapp.agent.model;


import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;

@Entity
@Table(name = "trx_approval_details")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@SQLDelete(sql = "UPDATE trx_approval_details SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class TrxApprovalDetail extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "trx_approval_details_id_seq")
    @SequenceGenerator(name = "trx_approval_details_id_seq", sequenceName = "trx_approval_details_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approval_header_id", nullable = false)
    private TrxApprovalHeader approvalHeader;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "action_by_id", nullable = false)
    private User actionBy;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String remarks;
    
    @Audited
    @Column(columnDefinition = "text")
    private String detailApproval;

    @Audited
    @Enumerated(EnumType.STRING)
    private ApprovalStatus approvalStatus;

    @Audited
    @Column(name = "level_number", nullable = false)
    private Integer levelNumber;

    @Audited
    @CreationTimestamp
    private Instant createdAt;

    @Audited
    @UpdateTimestamp
    private Instant updatedAt;
}
