package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.SignaturePageType;
import id.co.panindaiichilife.superapp.agent.model.Signature;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
public class SignatureDto extends BaseDto<Signature> {

    private Long id;

    private UserDto user;

    private Channel channel;

    private String documentType;

    private String signature;

    private String paraf;

    private SignaturePageType pageType;

    private Instant createdAt;

    private Instant updatedAt;

    @Override
    public void copy(Signature data) {
        super.copy(data);
        if (data.getUser() != null) {
            user = BaseDto.of(UserDto.class, data.getUser());
        }
    }
}
