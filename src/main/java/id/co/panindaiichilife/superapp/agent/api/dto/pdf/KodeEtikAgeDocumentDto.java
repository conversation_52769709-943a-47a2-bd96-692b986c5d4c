package id.co.panindaiichilife.superapp.agent.api.dto.pdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * DTO for KODE-ETIK-AGE (Kode Etik Agen) document generation parameters.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KodeEtikAgeDocumentDto {

    private String agentName;

    private String company;

    private String ktpNumber;

    private String signature;

    /**
     * Additional custom variables for the template.
     */
    private Map<String, Object> additionalVariables;

    /**
     * Convert DTO to variables map for template processing.
     *
     * @return Map of variables for template injection
     */
    public Map<String, Object> toVariablesMap() {
        Map<String, Object> variables = new HashMap<>();

        addBasicVariables(variables);
        PdfDocumentUtils.addStandardDateTimeVariables(variables);
        PdfDocumentUtils.addAdditionalVariables(variables, additionalVariables);

        return variables;
    }

    /**
     * Add basic document variables to the map.
     */
    private void addBasicVariables(Map<String, Object> variables) {
        variables.put("agentName", PdfDocumentUtils.getStringOrEmpty(agentName));
        variables.put("company", PdfDocumentUtils.getStringOrEmpty(company));
        variables.put("ktpNumber", PdfDocumentUtils.getStringOrEmpty(ktpNumber));
        variables.put("signature", PdfDocumentUtils.getStringOrEmpty(signature));
    }
}
