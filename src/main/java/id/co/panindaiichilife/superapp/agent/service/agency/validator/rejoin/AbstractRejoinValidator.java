package id.co.panindaiichilife.superapp.agent.service.agency.validator.rejoin;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

@Slf4j
public abstract class AbstractRejoinValidator implements RejoinValidator {
    @Override
    public CompletableFuture<TrxRejoinApplication> validate(TrxRejoinApplicationForm form,
                                                            TrxRejoinApplication entity) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("Starting validation: {}", getValidatorName());
                TrxRejoinApplication result = doValidate(form, entity);
                log.info("Completed validation: {}", getValidatorName());
                return result;
            } catch (Exception e) {
                log.error("Error during validation {}: {}", getValidatorName(), e.getMessage(), e);
                // Return the original entity unchanged in case of error
                return entity;
            }
        });
    }

    protected abstract TrxRejoinApplication doValidate(TrxRejoinApplicationForm form, TrxRejoinApplication entity);
}
