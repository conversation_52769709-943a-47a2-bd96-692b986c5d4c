package id.co.panindaiichilife.superapp.agent.api.batch.dto;

import id.co.panindaiichilife.superapp.agent.core.support.annotation.Header;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.Instant;

@Data
public class ApprovalExportDto {

    @Header("Tipe Transaksi")
    private String trxType;

    @Header("Kode Agent")
    private String agentCode;

    @Header("Level")
    private String agentLevel;

    @Header("Nama")
    private String agentName;

    @Header("Perekrut")
    private String recruiterInfo;

    @Header("Leader Langsung")
    private String leaderInfo;

    @Header("Branch Code")
    private String branchCode;

    @Header("Channel")
    private Channel channel;

    @Header("Perubahan Data")
    private String data;

    @Header("Tanggal Pengajuan")
    @DateTimeFormat(pattern = "d mmmm yyyy hh:mm:ss")
    private Instant createdAt;

    @Header("Tanggal Efektif")
    @DateTimeFormat(pattern = "d mmmm yyyy hh:mm:ss")
    private Instant approvedAt;

    @Header("Processed by")
    private String processedBy;

    @Header("Status")
    private ApprovalStatus approvalStatus;

    @Header("Ket")
    private String remarks;
}
