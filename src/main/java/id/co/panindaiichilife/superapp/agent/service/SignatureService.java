package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.SignatureDto;
import id.co.panindaiichilife.superapp.agent.api.filter.SignatureFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.SignatureForm;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.model.Signature;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.SignatureRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class SignatureService {

    private final SignatureRepository signatureRepository;
    private final UserRepository userRepository;
    private final AmazonS3Service amazonS3Service;

    public Page<SignatureDto> findAll(Pageable pageable, SignatureFilter filter) {
        Page<Signature> signatures = signatureRepository.findAll(filter, pageable);
        return SignatureDto.of(SignatureDto.class, signatures, pageable);
    }

    public SignatureDto findOne(Long id) {
        Signature data = signatureRepository.findById(id).orElseThrow(NotFoundException::new);
        return SignatureDto.of(SignatureDto.class, data);
    }

    @Transactional
    public SignatureDto add(SignatureForm signatureForm) {
        Signature data = new Signature();
        BeanUtils.copyProperties(signatureForm, data);

        // Set user
        User user = userRepository.findById(signatureForm.getUserId())
                .orElseThrow(() -> new NotFoundException("User not found"));
        data.setUser(user);

        signatureRepository.save(data);
        return SignatureDto.of(SignatureDto.class, data);
    }

    @Transactional
    public SignatureDto update(Long id, SignatureForm signatureForm) {
        Signature data = signatureRepository.findById(id).orElseThrow(NotFoundException::new);

        // Copy properties except userId (user should not be changed in update)
        BeanUtils.copyProperties(signatureForm, data, "userId");
        User user = userRepository.findById(signatureForm.getUserId())
                .orElseThrow(() -> new NotFoundException("User not found"));
        data.setUser(user);
        
        signatureRepository.save(data);
        return SignatureDto.of(SignatureDto.class, data);
    }

    @Transactional
    public void delete(Long id) {
        signatureRepository.deleteById(id);
    }

    public List<SignatureDto> findByUserId(Long userId) {
        List<Signature> signatures = signatureRepository.findByUserId(userId);
        return SignatureDto.of(SignatureDto.class, signatures);
    }

    public List<SignatureDto> findByUser(User user) {
        List<Signature> signatures = signatureRepository.findByUser(user);
        return SignatureDto.of(SignatureDto.class, signatures);
    }

    /**
     * Find signature by channel and document type for CAS signature retrieval
     *
     * @param channel      The channel (AGE, BAN)
     * @param documentType The document type (PKAJ, PMKAJ, etc.)
     * @return SignatureDto if found, null otherwise
     */
    public SignatureDto findByChannelAndDocumentType(Channel channel, String documentType) {
        log.info("Finding CAS signature for channel: {} and document type: {}", channel, documentType);

        // Find signatures by channel
        List<Signature> signatures = signatureRepository.findByChannel(channel);
        log.debug("Found {} signatures for channel: {}", signatures.size(), channel);

        // Filter by document type (signature's documentType field can contain comma-separated values like "PKAJ,PMKAJ")
        Optional<Signature> signature = signatures.stream()
                .filter(s -> isDocumentTypeSupported(s.getDocumentType(), documentType))
                .findFirst();

        if (signature.isPresent()) {
            Signature foundSignature = signature.get();
            log.info("Found CAS signature for channel: {} and document type: {}. Signature supports: {}",
                    channel, documentType, foundSignature.getDocumentType());
            return SignatureDto.of(SignatureDto.class, foundSignature);
        } else {
            log.warn("No CAS signature found for channel: {} and document type: {}. Available signatures: {}",
                    channel, documentType,
                    signatures.stream()
                            .map(Signature::getDocumentType)
                            .filter(dt -> dt != null)
                            .toArray());
            return null;
        }
    }

    /**
     * Check if a signature's document type supports the requested document type.
     * The signature's documentType can contain comma-separated values like "PKAJ,PMKAJ".
     *
     * @param signatureDocumentType The document type from the signature (can be comma-separated)
     * @param requestedDocumentType The requested document type
     * @return true if the signature supports the requested document type
     */
    private boolean isDocumentTypeSupported(String signatureDocumentType, String requestedDocumentType) {
        if (signatureDocumentType == null || requestedDocumentType == null) {
            return false;
        }

        // Split the signature's documentType by comma and check if any matches the requested type
        String[] supportedTypes = signatureDocumentType.split(",");
        for (String supportedType : supportedTypes) {
            if (supportedType.trim().equalsIgnoreCase(requestedDocumentType.trim())) {
                log.debug("Document type match found: '{}' supports '{}'", supportedType.trim(), requestedDocumentType.trim());
                return true;
            }
        }

        log.debug("No document type match: '{}' does not support '{}'", signatureDocumentType, requestedDocumentType);
        return false;
    }

    public FileinputResponse upload(String username, MultipartFile file) {
        User data = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        ;
        String filePath = "/assets/signature/" + data.getUsername() + "/" + UUID.randomUUID() + "."
                + FilenameUtils.getExtension(file.getOriginalFilename());
        if (amazonS3Service.store(file, filePath)) {
            return FileinputResponse.success(amazonS3Service.getUrl(filePath));
        } else {
            throw new BadRequestException("Upload failed");
        }
    }
}
