package id.co.panindaiichilife.superapp.agent.api.form.agency;

import id.co.panindaiichilife.superapp.agent.enums.Gender;
import id.co.panindaiichilife.superapp.agent.enums.MaritalStatus;
import id.co.panindaiichilife.superapp.agent.enums.PositionLevel;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class TrxRecruitmentForm {

    @NotBlank
    private String recruiterCode;

    @NotBlank
    private String ktpPhoto;

    @NotBlank
    private String selfiePhoto;

    @NotBlank
    private String passPhoto;

    @NotBlank
    private PositionLevel positionLevel;

    @NotNull
    private Long branch;

    @NotNull
    private Long bank;

    @NotBlank
    private String nik;

    @NotBlank
    private String fullName;

    @NotBlank
    private String birthPlace;

    @NotNull
    private LocalDate birthDate;

    private Gender gender;

    @NotBlank
    private String ktpProvince;

    @NotBlank
    private String ktpCity;

    @NotBlank
    private String ktpDistrict;

    @NotBlank
    private String ktpSubDistrict;

    @NotBlank
    private String ktpRt;

    @NotBlank
    private String ktpRw;

    @NotBlank
    private String ktpAddress;

    @NotBlank
    private Boolean isDomicileSameAsKtp;

    @NotBlank
    private String domicileProvince;

    @NotBlank
    private String domicileCity;

    @NotBlank
    private String domicileDistrict;

    @NotBlank
    private String domicileSubDistrict;

    @NotBlank
    private String domicileRt;

    @NotBlank
    private String domicileRw;

    @NotBlank
    private String domicileAddress;

    @NotBlank
    private String phoneNumber;

    @NotNull
    private MaritalStatus maritalStatus;

    @NotBlank
    private String occupation;

    @NotBlank
    private String occupationCode;

    @NotBlank
    private String email;

    @NotBlank
    private String emergencyContactName;

    @NotBlank
    private String emergencyContactRelation;

    @NotBlank
    private String emergencyContactPhone;

    @NotBlank
    private String bankAccountName;

    @NotBlank
    private String bankAccountNumber;

    @NotBlank
    private String lastJob;

    @NotBlank
    private String signature;

    @NotBlank
    private String paraf;

    private String notes;

    @NotNull
    private List<JobHistory> last5YearJobData; // 5 year job history as list of objects

    private List<Production> last2YearProductionData; // Last 2 year production (not mandatory)

    private CompanyManPower lastCompanyManPowerData; // Company manpower details (not mandatory)

    private List<Reward> rewardInfoData; // Rewards/recognitions (not mandatory)

    /**
     * Represents a job history entry for the last 5 years
     */
    @Data
    public static class JobHistory {
        @NotNull
        private Integer year;

        @NotBlank
        private String company;

        @NotBlank
        private String position;
    }

    /**
     * Represents production details for the last 2 years
     */
    @Data
    public static class Production {
        private Long personalProduction; // Individual production figures
        private Long teamProduction; // Team production figures
    }

    /**
     * Represents company manpower details
     */
    @Data
    public static class CompanyManPower {
        private Integer agentCount; // Number of agents
        private Integer leaderCount; // Number of leaders
    }

    /**
     * Represents awards and recognitions received
     */
    @Data
    public static class Reward {
        private Integer year;
        private String description; // Award description (MDRT, multilevel, etc)
    }
}

