package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.time.LocalDate;

@Entity
@Table(name = "agent_production_per_policy")
@Data
@ToString(of = {"id", "agentCode"})
public class AgentProductionPerPolicy {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "agent_production_per_policy_id_seq")
    @SequenceGenerator(name = "agent_production_per_policy_id_seq", sequenceName = "agent_production_per_policy_id_seq", allocationSize = 1)
    private Long id;

    @Enumerated(EnumType.STRING)
    private DistributionCode distributionCode;

    @Column(name = "agent_code")
    private String agentCode;

    @Column(name = "leader_code")
    private String leaderCode;

    @Column(name = "year")
    private Integer year;

    @Column(name = "month")
    private Integer month;

    @Column(name = "policy_no")
    private String policyNo;

    @Column(name = "policy_holder_name")
    private String policyHolderName;

    @Column(name = "policy_comm_date")
    private LocalDate policyCommDate;

    @Column(name = "status")
    private String status;

    @Column(name = "net_ape")
    private Double netApe;

    @Column(name = "net_api")
    private Double netApi;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
