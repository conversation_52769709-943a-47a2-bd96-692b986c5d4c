package id.co.panindaiichilife.superapp.agent.api.dto.agency;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for email verification operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailVerificationResponseDto {

    private boolean success;
    private String message;
    private String email;
    private String uuid;

    /**
     * Create a success response
     *
     * @param message The success message
     * @param email   The verified email
     * @return The response DTO
     */
    public static EmailVerificationResponseDto success(String message, String email, String uuid) {
        return EmailVerificationResponseDto.builder()
                .success(true)
                .message(message)
                .email(email)
                .uuid(uuid)
                .build();
    }

    /**
     * Create an error response
     *
     * @param message The error message
     * @return The response DTO
     */
    public static EmailVerificationResponseDto error(String message) {
        return EmailVerificationResponseDto.builder()
                .success(false)
                .message(message)
                .build();
    }
}
