package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.ValidasiPerHirarki;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ValidasiPerHirarkiRepository extends BaseRepository<ValidasiPerHirarki, Long> {

    // Find by agent code
    ValidasiPerHirarki findByAgentCode(String agentCode);

    // Find by branch code
    List<ValidasiPerHirarki> findByBranchCode(String branchCode);

    // Find by BDM code
    List<ValidasiPerHirarki> findByBdmCode(String bdmCode);

    // Find by HOS code
    List<ValidasiPerHirarki> findByHosCode(String hosCode);

    @Modifying
    @Transactional
    @Query("DELETE FROM ValidasiPerHirarki v WHERE v.year = :year")
    void deleteByYear(@Param("year") Integer year);

    long countByYear(Integer year);

}
