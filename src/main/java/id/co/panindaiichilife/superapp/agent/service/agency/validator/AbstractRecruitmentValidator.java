package id.co.panindaiichilife.superapp.agent.service.agency.validator;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

/**
 * Abstract base class for recruitment validators
 * Provides common functionality and error handling
 */
@Slf4j
public abstract class AbstractRecruitmentValidator implements RecruitmentValidator {

    @Override
    public CompletableFuture<TrxRecruitment> validate(TrxRecruitmentForm form, TrxRecruitment entity) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("Starting validation: {}", getValidatorName());
                TrxRecruitment result = doValidate(form, entity);
                log.info("Completed validation: {}", getValidatorName());
                return result;
            } catch (Exception e) {
                log.error("Error during validation {}: {}", getValidatorName(), e.getMessage(), e);
                // Return the original entity unchanged in case of error
                return entity;
            }
        });
    }

    /**
     * Performs the actual validation logic
     *
     * @param form   The recruitment form
     * @param entity The recruitment entity
     * @return The updated entity
     */
    protected abstract TrxRecruitment doValidate(TrxRecruitmentForm form, TrxRecruitment entity);
}
