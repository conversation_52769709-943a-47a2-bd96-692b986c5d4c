package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortalAgentReactivationRequestDto {
    @SerializedName("agentCode")
    @JsonProperty("agentCode")
    private String agentCode;

    @SerializedName("leaderCode")
    @JsonProperty("leaderCode")
    private String leaderCode;

    @SerializedName("targetLevel")
    @JsonProperty("targetLevel")
    private String targetLevel;

    @SerializedName("branchCode")
    @JsonProperty("branchCode")
    private String branchCode;

    @SerializedName("creby")
    @JsonProperty("creby")
    private String creby;
}
