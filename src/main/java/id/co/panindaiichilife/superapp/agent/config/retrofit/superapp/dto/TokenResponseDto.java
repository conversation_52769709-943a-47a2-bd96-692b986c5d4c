package id.co.panindaiichilife.superapp.agent.config.retrofit.superapp.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class TokenResponseDto {
    @SerializedName("access_token")
    private String accessToken;

    @SerializedName("token_type")
    private String tokenType;

    @SerializedName("refresh_token")
    private String refreshToken;

    @SerializedName("expires_in")
    private Long expiresIn;

    @SerializedName("needPassReset")
    private Boolean needPassReset;

    @SerializedName("remainingDays")
    private Integer remainingDays;

    private String scope;
}
