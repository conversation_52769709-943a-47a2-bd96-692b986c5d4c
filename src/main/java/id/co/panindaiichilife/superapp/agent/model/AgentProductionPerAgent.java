package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "agent_production_per_agent")
@Data
@ToString(of = {"id", "agentCode"})
public class AgentProductionPerAgent {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "agent_production_per_agent_id_seq")
    @SequenceGenerator(name = "agent_production_per_agent_id_seq", sequenceName = "agent_production_per_agent_id_seq", allocationSize = 1)
    private Long id;

    @Enumerated(EnumType.STRING)
    private DistributionCode distributionCode;

    @Column(name = "agent_code")
    private String agentCode;

    @Column(name = "leader_code")
    private String leaderCode;

    @Column(name = "type")
    private String type;

    @Column(name = "main_branch_code")
    private String mainBranchCode;

    @Column(name = "branch_code")
    private String branchCode;

    @Column(name = "bdm_code")
    private String bdmCode;

    @Column(name = "bdm_name")
    private String bdmName;

    @Column(name = "abdd_code")
    private String abddCode;

    @Column(name = "abdd_name")
    private String abddName;

    @Column(name = "abdd_region")
    private String abddRegion;

    @Column(name = "bdd_code")
    private String bddCode;

    @Column(name = "bdd_name")
    private String bddName;

    @Column(name = "bdd_region")
    private String bddRegion;

    @Column(name = "hos_code")
    private String hosCode;

    @Column(name = "territory")
    private String territory;

    @Column(name = "area")
    private String area;

    @Column(name = "hos_name")
    private String hosName;

    @Column(name = "year")
    private Integer year;

    @Column(name = "month")
    private Integer month;

    @Column(name = "net_ape")
    private Double netApe;

    @Column(name = "net_case")
    private Double netCase;

    @Column(name = "net_case_target")
    private Double netCaseTarget;

    @Column(name = "net_api_target")
    private Double netApiTarget;

    @Column(name = "net_api")
    private Double netApi;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
