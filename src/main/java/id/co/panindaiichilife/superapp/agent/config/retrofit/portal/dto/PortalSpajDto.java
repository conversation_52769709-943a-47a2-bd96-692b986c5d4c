package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalSpajDto {

    @SerializedName("agentCode")
    @JsonProperty("agentCode")
    private String agentCode;

    @SerializedName("dateFrom")
    @JsonProperty("dateFrom")
    private String dateFrom;

    @SerializedName("dateTo")
    @JsonProperty("dateTo")
    private String dateTo;

    @SerializedName("status")
    @JsonProperty("status")
    private int status;

    @SerializedName("bitFilter")
    @JsonProperty("bitFilter")
    private int bitFilter;

    @SerializedName("valFilter")
    @JsonProperty("valFilter")
    private String valFilter;

    @SerializedName("pageIndex")
    @JsonProperty("pageIndex")
    private int pageIndex;

    @SerializedName("pageSize")
    @JsonProperty("pageSize")
    private int pageSize;

    @SerializedName("countRow")
    @JsonProperty("countRow")
    private int countRow;

    @SerializedName("sortIndex")
    @JsonProperty("sortIndex")
    private int sortIndex;

    @SerializedName("sortBy")
    @JsonProperty("sortBy")
    private int sortBy;

    @SerializedName("brCode")
    @JsonProperty("brCode")
    private String brCode;

    @SerializedName("channel")
    @JsonProperty("channel")
    private String channel;

    @SerializedName("isSpajStatus")
    @JsonProperty("isSpajStatus")
    private int isSpajStatus;

    @SerializedName("statusFilter")
    @JsonProperty("statusFilter")
    private String statusFilter;
}
