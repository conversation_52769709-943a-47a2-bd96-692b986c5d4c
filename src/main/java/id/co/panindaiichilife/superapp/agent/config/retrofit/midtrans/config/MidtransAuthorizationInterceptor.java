package id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.config;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.util.Base64;

@Slf4j
public class MidtransAuthorizationInterceptor implements Interceptor {

  private static final String APPLICATION_JSON = "application/json";

  @Value("${midtrans.server-key}")
  private String midtransServerKey;

  @Value("${midtrans.notify-url}")
  private String midtransNotifyUrl;

  @NotNull
  @Override
  public Response intercept(@NotNull Chain chain) throws IOException {
	  
	String encoding = Base64.getEncoder().encodeToString(midtransServerKey.concat(":").getBytes("UTF-8"));
    
    log.debug("========MIDTRANS INTERCEPTOR HERE ============");
    log.debug("=============== authorization basic {}============",encoding);
    
    Request request = chain.request();
    
    String site = request.header("X-Site");
    
    if ("Notify".equals(site)) {
    	
    	request = request.newBuilder()
    			.header("Accept", APPLICATION_JSON)
    	    	.header("Content-Type", APPLICATION_JSON)
    	        .header("Authorization", "Basic ".concat(encoding))
    	        .header("X-Override-Notification", midtransNotifyUrl)
                .removeHeader("X-Site")
                .build();

    } else {

    	request = request.newBuilder()
    	    	.header("Accept", APPLICATION_JSON)
    	    	.header("Content-Type", APPLICATION_JSON)
    	        .header("Authorization", "Basic ".concat(encoding))
    	        .build();
    	
    }

    log.debug("=============== request {} ==============",request.toString());
    
    return chain.proceed(request);
    
  }
  
}
