package id.co.panindaiichilife.superapp.agent.api.filter;

import id.co.panindaiichilife.superapp.agent.core.data.filter.FieldFilter;
import id.co.panindaiichilife.superapp.agent.core.data.filter.FilterParam;
import id.co.panindaiichilife.superapp.agent.core.view.FilterMode;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.Inbox;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class InboxFilter extends FieldFilter<Inbox> {

    // Keyword search parameter
    private String q;

    private List<TrxType> trxType;

    @FilterParam
    private InboxType inboxType;

    @FilterParam
    private Boolean isArchived;

    @FilterParam
    private Boolean isRead;

    @FilterParam
    private Boolean deleted;

    @Parameter(hidden = true)
    @FilterParam(value = "user.username", modes = FilterMode.FK)
    private String user;

    @Override
    public Predicate getFilterPredicate(Root<Inbox> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        List<Predicate> predicates = new ArrayList<>();

        // Transaction type predicate
        if (trxType != null && !trxType.isEmpty()) {
            predicates.add(root.get("trxType").in(trxType));
        }

        // Get the standard field filter predicate from parent
        Predicate fieldPredicate = super.getFilterPredicate(root, query, cb);

        // Add keyword search predicate if q is provided
        Predicate keywordPredicate = null;
        if (StringUtils.isNotBlank(q)) {
            String[] keywords = q.trim().split("\\s+");
            List<Predicate> keywordAnds = new ArrayList<>();

            for (String keyword : keywords) {
                List<Predicate> keywordOrs = new ArrayList<>();
                String lowerKeyword = keyword.toLowerCase();

                // Search in title field
                keywordOrs.add(cb.like(cb.lower(root.get("title")), "%" + lowerKeyword + "%"));

                // Search in body field
                keywordOrs.add(cb.like(cb.lower(root.get("body")), "%" + lowerKeyword + "%"));

                if (!keywordOrs.isEmpty()) {
                    keywordAnds.add(cb.or(keywordOrs.toArray(new Predicate[0])));
                }
            }

            if (!keywordAnds.isEmpty()) {
                keywordPredicate = cb.and(keywordAnds.toArray(new Predicate[0]));
            }
        }

        // Add field predicate if exists
        if (fieldPredicate != null) {
            predicates.add(fieldPredicate);
        }

        // Add keyword predicate if exists
        if (keywordPredicate != null) {
            predicates.add(keywordPredicate);
        }

        // Combine all predicates
        if (predicates.isEmpty()) {
            return null;
        } else if (predicates.size() == 1) {
            return predicates.get(0);
        } else {
            return cb.and(predicates.toArray(new Predicate[0]));
        }
    }

    @Override
    public String getFilterQuery() {
        List<String> params = new ArrayList<>();

        // Add keyword search query
        if (StringUtils.isNotBlank(q)) {
            params.add("q=" + q);
        }

        // Add transaction type query parameters
        if (trxType != null && !trxType.isEmpty()) {
            for (TrxType type : trxType) {
                params.add("trxType=" + type.name());
            }
        }

        // Add field-based filter queries from parent
        String parentQuery = super.getFilterQuery();
        if (StringUtils.isNotBlank(parentQuery)) {
            params.add(parentQuery);
        }

        return params.isEmpty() ? null : String.join("&", params);
    }
}
