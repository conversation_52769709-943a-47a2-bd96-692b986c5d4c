package id.co.panindaiichilife.superapp.agent.api.dto;

import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotificationDto {
    private String title;

    private String body;

    private Map<String, String> data;

    private InboxType inboxType;
}
