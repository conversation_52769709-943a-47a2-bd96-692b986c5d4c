package id.co.panindaiichilife.superapp.agent.api.filter;

import id.co.panindaiichilife.superapp.agent.core.data.filter.FieldFilter;
import id.co.panindaiichilife.superapp.agent.core.data.filter.FilterParam;
import id.co.panindaiichilife.superapp.agent.core.view.FilterMode;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.SignaturePageType;
import id.co.panindaiichilife.superapp.agent.model.Signature;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SignatureFilter extends FieldFilter<Signature> {

    @FilterParam(value = "user.username", modes = FilterMode.LIKE)
    private String username;

    @FilterParam
    private Channel channel;

    @FilterParam(modes = FilterMode.LIKE)
    private String documentType;

    @FilterParam
    private SignaturePageType pageType;
}
