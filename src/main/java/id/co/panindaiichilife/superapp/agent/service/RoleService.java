package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.RoleDto;
import id.co.panindaiichilife.superapp.agent.api.filter.RoleFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.RoleForm;
import id.co.panindaiichilife.superapp.agent.core.data.association.ManyToManyUtils;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.model.Role;
import id.co.panindaiichilife.superapp.agent.repository.AccessRepository;
import id.co.panindaiichilife.superapp.agent.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class RoleService {

    private final RoleRepository roleRepository;

    private final AccessRepository accessRepository;

    public Page<RoleDto> findAll(Pageable pageable, RoleFilter filter) {
        Page<Role> roles = roleRepository.findAll(filter, pageable);
        return BaseDto.of(RoleDto.class, roles, pageable);
    }

    public RoleDto findOne(Long id) {
        Role data = roleRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(RoleDto.class, data);
    }

    @Transactional
    public RoleDto add(RoleForm roleForm) {
        Role data = new Role();
        BeanUtils.copyProperties(roleForm, data);
        roleRepository.save(data);

        ManyToManyUtils.save(roleForm, data, "accesses", accessRepository);
        return BaseDto.of(RoleDto.class, data);
    }


    @Transactional
    public RoleDto update(Long id, RoleForm roleForm) {
        Role data = roleRepository.findById(id).orElseThrow(NotFoundException::new);
        BeanUtils.copyProperties(roleForm, data);
        roleRepository.save(data);

        ManyToManyUtils.save(roleForm, data, "accesses", accessRepository);
        return BaseDto.of(RoleDto.class, data);
    }

    public void delete(Long id) {
        roleRepository.deleteById(id);
    }
}
