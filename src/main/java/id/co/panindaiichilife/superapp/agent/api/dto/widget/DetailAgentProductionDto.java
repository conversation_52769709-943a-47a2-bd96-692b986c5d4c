package id.co.panindaiichilife.superapp.agent.api.dto.widget;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.AgentProductionPerPolicy;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
public class DetailAgentProductionDto extends BaseDto<AgentProductionPerPolicy> {

    private DistributionCode distributionCode;

    private String agentCode;

    private String agentName;

    private String agentPhoto;

    private String agentLevel;

    private String leaderCode;

    private Integer year;

    private Integer month;

    private String policyNo;

    private String policyHolderName;

    private LocalDate policyCommDate;

    private String status;

    private Double netApe;

    private Double netApi;
}
