package id.co.panindaiichilife.superapp.agent.api.dto.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.ActionByDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalDetailDto;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxTermination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.Set;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
public class TrxTerminationDto extends BaseDto<TrxTermination> {
    private Long id;

    private String reason;

    private TrxStatus status;

    private ApprovalStatus approvalStatus;

    private ActionByDto requestedBy;

    private ActionByDto target;

    private Instant createdAt;

    private Instant updatedAt;

    private Set<ApprovalDetailDto> approvalDetails;

    private TrxPolicyTransferDto policyTransferInfo;

    @Override
    public void copy(TrxTermination data) {
        super.copy(data);

        this.requestedBy = BaseDto.of(ActionByDto.class, data.getRequester());
        this.target = BaseDto.of(ActionByDto.class, data.getTarget());

        if (data.getApprovalHeader() != null) {
            this.approvalDetails = BaseDto.of(ApprovalDetailDto.class,
                    data.getApprovalHeader().getApprovalDetails());
        }

        this.policyTransferInfo = BaseDto.of(TrxPolicyTransferDto.class, data.getTrxPolicyTransfer());
    }
}
