package id.co.panindaiichilife.superapp.agent.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Service for managing Redis cache operations
 * Provides manual cache clearing and other Redis operations that work reliably
 * even in contexts where Spring AOP might not be properly applied (e.g., Kafka consumers)
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RedisService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisConnectionFactory redisConnectionFactory;

    /**
     * Manually clears a specific cache key from Redis
     * This method works reliably even when called from Kafka consumers or internal method calls
     * where Spring AOP might not be properly applied
     *
     * @param cacheName The name of the cache
     * @param key The cache key to clear
     * @param dbIndex The Redis database index
     * @return true if the key was deleted, false otherwise
     */
    public boolean clearCache(String cacheName, String key, int dbIndex) {
        try {
            // Create database-specific Redis template
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);
            
            // Generate the cache key using the same pattern as the caching aspect
            String cacheKey = cacheName + "::" + key;
            
            // Delete the cache key
            Boolean deleted = dbSpecificTemplate.delete(cacheKey);
            log.info("Manual cache eviction - Key: {}, DB: {}, Deleted: {}", cacheKey, dbIndex, deleted != null ? deleted : false);
            
            return deleted != null && deleted;
            
        } catch (Exception e) {
            log.warn("Failed to manually clear cache for key: {} in cache: {} on DB: {}. Error: {}", 
                    key, cacheName, dbIndex, e.getMessage());
            return false;
        }
    }

    /**
     * Clears multiple cache keys with the same cache name and database
     *
     * @param cacheName The name of the cache
     * @param keys The cache keys to clear
     * @param dbIndex The Redis database index
     * @return the number of keys that were successfully deleted
     */
    public long clearMultipleCache(String cacheName, Set<String> keys, int dbIndex) {
        if (keys == null || keys.isEmpty()) {
            return 0;
        }

        try {
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);
            
            // Generate cache keys
            String[] cacheKeys = keys.stream()
                    .map(key -> cacheName + "::" + key)
                    .toArray(String[]::new);
            
            // Delete all keys at once
            Long deleted = dbSpecificTemplate.delete(Set.of(cacheKeys));
            log.info("Manual cache eviction - Cache: {}, DB: {}, Keys: {}, Deleted: {}", 
                    cacheName, dbIndex, keys.size(), deleted != null ? deleted : 0);
            
            return deleted != null ? deleted : 0;
            
        } catch (Exception e) {
            log.warn("Failed to manually clear multiple cache keys in cache: {} on DB: {}. Error: {}", 
                    cacheName, dbIndex, e.getMessage());
            return 0;
        }
    }

    /**
     * Clears all keys matching a pattern in a specific cache and database
     *
     * @param cacheName The name of the cache
     * @param keyPattern The pattern to match (e.g., "user:*")
     * @param dbIndex The Redis database index
     * @return the number of keys that were successfully deleted
     */
    public long clearCacheByPattern(String cacheName, String keyPattern, int dbIndex) {
        try {
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);
            
            // Generate the cache pattern
            String cachePattern = cacheName + "::" + keyPattern;
            
            // Find all keys matching the pattern
            Set<String> keys = dbSpecificTemplate.keys(cachePattern);
            
            if (keys == null || keys.isEmpty()) {
                log.info("No keys found matching pattern: {} in cache: {} on DB: {}", keyPattern, cacheName, dbIndex);
                return 0;
            }
            
            // Delete all matching keys
            Long deleted = dbSpecificTemplate.delete(keys);
            log.info("Manual cache eviction by pattern - Cache: {}, Pattern: {}, DB: {}, Found: {}, Deleted: {}", 
                    cacheName, keyPattern, dbIndex, keys.size(), deleted != null ? deleted : 0);
            
            return deleted != null ? deleted : 0;
            
        } catch (Exception e) {
            log.warn("Failed to clear cache by pattern: {} in cache: {} on DB: {}. Error: {}", 
                    keyPattern, cacheName, dbIndex, e.getMessage());
            return 0;
        }
    }

    /**
     * Sets a value in Redis cache with TTL
     *
     * @param cacheName The name of the cache
     * @param key The cache key
     * @param value The value to cache
     * @param ttl Time to live in seconds
     * @param dbIndex The Redis database index
     * @return true if the operation was successful
     */
    public boolean setCache(String cacheName, String key, Object value, long ttl, int dbIndex) {
        try {
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);
            
            String cacheKey = cacheName + "::" + key;
            dbSpecificTemplate.opsForValue().set(cacheKey, value, ttl, TimeUnit.SECONDS);
            
            log.debug("Cache set - Key: {}, DB: {}, TTL: {} seconds", cacheKey, dbIndex, ttl);
            return true;
            
        } catch (Exception e) {
            log.warn("Failed to set cache for key: {} in cache: {} on DB: {}. Error: {}", 
                    key, cacheName, dbIndex, e.getMessage());
            return false;
        }
    }

    /**
     * Gets a value from Redis cache
     *
     * @param cacheName The name of the cache
     * @param key The cache key
     * @param dbIndex The Redis database index
     * @return the cached value or null if not found
     */
    public Object getCache(String cacheName, String key, int dbIndex) {
        try {
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);
            
            String cacheKey = cacheName + "::" + key;
            Object value = dbSpecificTemplate.opsForValue().get(cacheKey);
            
            log.debug("Cache get - Key: {}, DB: {}, Found: {}", cacheKey, dbIndex, value != null);
            return value;
            
        } catch (Exception e) {
            log.warn("Failed to get cache for key: {} in cache: {} on DB: {}. Error: {}", 
                    key, cacheName, dbIndex, e.getMessage());
            return null;
        }
    }

    /**
     * Checks if a cache key exists
     *
     * @param cacheName The name of the cache
     * @param key The cache key
     * @param dbIndex The Redis database index
     * @return true if the key exists
     */
    public boolean existsCache(String cacheName, String key, int dbIndex) {
        try {
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);
            
            String cacheKey = cacheName + "::" + key;
            Boolean exists = dbSpecificTemplate.hasKey(cacheKey);
            
            return exists != null && exists;
            
        } catch (Exception e) {
            log.warn("Failed to check cache existence for key: {} in cache: {} on DB: {}. Error: {}", 
                    key, cacheName, dbIndex, e.getMessage());
            return false;
        }
    }

    /**
     * Gets the TTL (time to live) of a cache key
     *
     * @param cacheName The name of the cache
     * @param key The cache key
     * @param dbIndex The Redis database index
     * @return TTL in seconds, -1 if key exists but has no TTL, -2 if key doesn't exist
     */
    public long getCacheTTL(String cacheName, String key, int dbIndex) {
        try {
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);
            
            String cacheKey = cacheName + "::" + key;
            Long ttl = dbSpecificTemplate.getExpire(cacheKey, TimeUnit.SECONDS);
            
            return ttl != null ? ttl : -2;
            
        } catch (Exception e) {
            log.warn("Failed to get TTL for cache key: {} in cache: {} on DB: {}. Error: {}", 
                    key, cacheName, dbIndex, e.getMessage());
            return -2;
        }
    }

    /**
     * Creates a Redis template configured for a specific database
     * This is used for manual cache operations
     *
     * @param dbIndex The Redis database index
     * @return RedisTemplate configured for the specified database
     */
    private RedisTemplate<String, Object> createDatabaseSpecificTemplate(int dbIndex) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        
        try {
            // Get the current connection factory configuration
            LettuceConnectionFactory currentFactory = (LettuceConnectionFactory) redisConnectionFactory;
            RedisStandaloneConfiguration currentConfig = currentFactory.getStandaloneConfiguration();
            
            // Create new configuration with the specified database
            RedisStandaloneConfiguration newConfig = new RedisStandaloneConfiguration();
            newConfig.setHostName(currentConfig.getHostName());
            newConfig.setPort(currentConfig.getPort());
            if (currentConfig.getPassword().isPresent()) {
                newConfig.setPassword(currentConfig.getPassword().get());
            }
            newConfig.setDatabase(dbIndex);
            
            // Create new connection factory
            LettuceConnectionFactory newFactory = new LettuceConnectionFactory(newConfig);
            newFactory.afterPropertiesSet();
            
            // Configure the template
            template.setConnectionFactory(newFactory);
            template.setKeySerializer(redisTemplate.getKeySerializer());
            template.setValueSerializer(redisTemplate.getValueSerializer());
            template.setHashKeySerializer(redisTemplate.getHashKeySerializer());
            template.setHashValueSerializer(redisTemplate.getHashValueSerializer());
            template.afterPropertiesSet();
            
        } catch (Exception e) {
            log.error("Failed to create database-specific Redis template for DB: {}. Error: {}", dbIndex, e.getMessage());
            throw e;
        }
        
        return template;
    }

    // Convenience methods for common cache operations in the application

    /**
     * Clears agent cache for a specific username
     *
     * @param username The username of the agent
     * @return true if the cache was cleared successfully
     */
    public boolean clearAgentCache(String username) {
        return clearCache("agentCache", username, 7);
    }

    /**
     * Clears user ACL cache for a specific username
     *
     * @param username The username of the user
     * @return true if the cache was cleared successfully
     */
    public boolean clearUserAclCache(String username) {
        return clearCache("userAclCache", username, 7);
    }

    /**
     * Clears branch city cache
     *
     * @return true if the cache was cleared successfully
     */
    public boolean clearBranchCityCache() {
        return clearCacheByPattern("branchCityCache", "*", 7) > 0;
    }

    /**
     * Clears device cache for a specific device ID
     *
     * @param deviceId The device ID
     * @return true if the cache was cleared successfully
     */
    public boolean clearDeviceCache(String deviceId) {
        return clearCache("deviceCache", deviceId, 7);
    }

    /**
     * Clears production agent cache for a specific user
     *
     * @param username The username
     * @return the number of cache entries cleared
     */
    public long clearProductionAgentCache(String username) {
        return clearCacheByPattern("productionAgentMainBranchCache", username + ":*", 7);
    }

    /**
     * Clears production agent HOS cache for a specific user
     *
     * @param username The username
     * @return the number of cache entries cleared
     */
    public long clearProductionAgentHosCache(String username) {
        return clearCacheByPattern("productionAgentHosByMainBranchCache", username + ":*", 7);
    }

    /**
     * Clears claim list cache for a specific user or agent code
     *
     * @param userOrAgentCode The username or agent code
     * @return the number of cache entries cleared
     */
    public long clearClaimListCache(String userOrAgentCode) {
        return clearCacheByPattern("claimListCache", userOrAgentCode + ":*", 7);
    }

    /**
     * Clears training city cache
     *
     * @return true if the cache was cleared successfully
     */
    public boolean clearTrainingCityCache() {
        return clearCacheByPattern("trainingCityCache", "*", 7) > 0;
    }

    /**
     * Clears all caches for a specific user (useful when user data changes significantly)
     *
     * @param username The username
     * @return the total number of cache entries cleared
     */
    public long clearAllUserCaches(String username) {
        long totalCleared = 0;

        // Clear agent cache
        if (clearAgentCache(username)) {
            totalCleared++;
        }

        // Clear user ACL cache
        if (clearUserAclCache(username)) {
            totalCleared++;
        }

        // Clear production caches
        totalCleared += clearProductionAgentCache(username);
        totalCleared += clearProductionAgentHosCache(username);

        // Clear claim cache
        totalCleared += clearClaimListCache(username);

        log.info("Cleared {} cache entries for user: {}", totalCleared, username);
        return totalCleared;
    }
}
