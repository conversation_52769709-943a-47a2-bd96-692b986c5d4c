package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.AgentStatus;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Collection;


@EqualsAndHashCode(callSuper = true)
@Data
public class AgentDto extends BaseDto<Agent> {

    private Long id;

    private String leaderCode;

    private String leaderName;

    private String agentCode;

    private String agentName;

    private DistributionCode distributionCode;

    private String roleName;

    private String level;

    private String positionLevel;

    private String regionCode;

    private String regionName;

    private String subRegionCode;

    private String subRegionName;

    private String areaCode;

    private String areaName;

    private String branchCode;

    private String branchName;

    private String groupCode;

    private String groupName;

    private String mBranchCode;

    private String mBranchName;

    private String sBranchCode;

    private String sBranchName;

    private String licenseNumberAAJI;

    private LocalDate licenseExpiredDateAAJI;

    private String licenseNumberAASI;

    private LocalDate licenseExpiredDateAASI;

    private String leaderG2G;

    private String recruiterCode;

    private LocalDate dob;

    private String gender;

    private String education;

    private AgentStatus status;

    private Channel channel;

    private String email;

    private String phoneNumber;

    private String address;

    private String bankAccountNumber;

    private String bank;

    private String maritalStatus;

    private String bankAttachment;

    private String ktpAttachment;

    private String kkAttachment;

    private String photo;

    private Instant createdAt;

    private Instant updatedAt;

    private Collection<RoleDto> roles;

    private Collection<BranchDto> branches;

    @Override
    public void copy(Agent data) {
        super.copy(data);
        roles = BaseDto.of(RoleDto.class, data.getUser().getRoles());
        branches = BaseDto.of(BranchDto.class, data.getUser().getBranches());
        if (null != data.getLeader()) {
            leaderName = data.getLeader().getAgentName();
        }
    }

}
