package id.co.panindaiichilife.superapp.agent.core.data.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.*;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.LineCallbackHandler;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.batch.item.file.transform.FieldSet;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;

import java.io.File;
import java.net.MalformedURLException;

@Slf4j
public class CsvItemReader<T> extends FlatFileItemReader<T> {

    private final String resourceUri;
    private final Class<T> type;
    private final String delimiter;
    private boolean initialized = false;

    public CsvItemReader(Class<T> type, String resourceUri) {
        this(type, resourceUri, DelimitedLineTokenizer.DELIMITER_COMMA);
    }

    public CsvItemReader(Class<T> type, String resourceUri, String delimiter) {
        super();
        this.resourceUri = resourceUri;
        this.type = type;
        this.delimiter = delimiter;
        setName("csvItemReader-" + System.currentTimeMillis());  // Unique name
    }

    @Override
    public T read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        if (!initialized) {
            // Force initialization if not done already
            ExecutionContext executionContext = new ExecutionContext();
            this.open(executionContext);
        }
        return super.read();
    }

    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        if (initialized) {
            log.debug("Reader already initialized, skipping initialization");
            return;
        }

        log.info("Opening CSV file reader for resource: {}", resourceUri);
        validateResourceUri();

        try {
            Resource resource = createResource();
            validateResource(resource);
            setResource(resource);

            DelimitedLineTokenizer tokenizer = createTokenizer();
            setupLineMapper(tokenizer);

            super.open(executionContext);
            initialized = true;
            log.info("Reader initialization completed successfully");
        } catch (Exception ex) {
            log.error("Failed to initialize reader: {}", ex.getMessage(), ex);
            throw new ItemStreamException("Failed to initialize reader: " + ex.getMessage(), ex);
        }
    }

    private void validateResourceUri() throws ItemStreamException {
        if (resourceUri == null || resourceUri.isEmpty()) {
            throw new ItemStreamException("Resource URI cannot be null or empty.");
        }
    }

    private Resource createResource() throws ItemStreamException {
        if (resourceUri.startsWith("file:") || resourceUri.startsWith("http:") || resourceUri.startsWith("https:")) {
            return createUrlResource();
        } else {
            return createFileSystemResource();
        }
    }

    private Resource createUrlResource() throws ItemStreamException {
        try {
            return new UrlResource(resourceUri);
        } catch (MalformedURLException e) {
            throw new ItemStreamException("Cannot create URL resource for: " + resourceUri, e);
        }
    }

    private Resource createFileSystemResource() throws ItemStreamException {
        File file = new File(resourceUri);
        if (file.exists()) {
            return new FileSystemResource(file);
        } else {
            return createUrlResource();
        }
    }

    private void validateResource(Resource resource) throws ItemStreamException {
        if (!resource.exists()) {
            throw new ItemStreamException("Resource does not exist: " + resourceUri);
        }
        if (!resource.isReadable()) {
            throw new ItemStreamException("Resource is not readable: " + resourceUri);
        }
    }

    private DelimitedLineTokenizer createTokenizer() {
        DelimitedLineTokenizer tokenizer = new DelimitedLineTokenizer(delimiter);
        tokenizer.setQuoteCharacter('"');
        tokenizer.setStrict(false);
        return tokenizer;
    }

    private void setupLineMapper(DelimitedLineTokenizer tokenizer) {
        setSkippedLinesCallback(new HeaderLineCallback(tokenizer));

        AnnotatedBeanFieldSetMapper<T> fieldSetMapper = new AnnotatedBeanFieldSetMapper<>(type);

        DefaultLineMapper<T> defaultLineMapper = new DefaultLineMapper<>();
        defaultLineMapper.setLineTokenizer(tokenizer);
        defaultLineMapper.setFieldSetMapper(fieldSetMapper);
        setLineMapper(defaultLineMapper);

        setLinesToSkip(1);
    }

    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {
        super.update(executionContext);
    }

    @Override
    public void close() {
        log.info("Closing CSV file reader.");
        initialized = false;
        super.close();
    }

    private class HeaderLineCallback implements LineCallbackHandler {
        private final DelimitedLineTokenizer tokenizer;

        public HeaderLineCallback(DelimitedLineTokenizer tokenizer) {
            this.tokenizer = tokenizer;
        }

        @Override
        public void handleLine(String line) {
            if (line == null || line.trim().isEmpty()) {
                log.warn("Skipping empty header line.");
                return;
            }
            log.debug("Processing header line: {}", line);
            // Use the same tokenizer configuration for header processing
            FieldSet fs = tokenizer.tokenize(line);
            String[] names = fs.getValues();
            log.debug("Extracted column names: {}", (Object) names);
            tokenizer.setNames(names);
        }
    }
}