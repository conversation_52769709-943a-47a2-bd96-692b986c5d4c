package id.co.panindaiichilife.superapp.agent.core.util;

/**
 * Utility class for handling agent code operations
 */
public class AgentCodeUtil {

    /**
     * Trims the "-D" suffix from agent code if present
     * 
     * @param agentCode The agent code to trim
     * @return The trimmed agent code, or the original if no "-D" suffix found
     */
    public static String trimDSuffix(String agentCode) {
        if (agentCode != null && agentCode.endsWith("-D")) {
            return agentCode.substring(0, agentCode.length() - 2);
        }
        return agentCode;
    }
}
