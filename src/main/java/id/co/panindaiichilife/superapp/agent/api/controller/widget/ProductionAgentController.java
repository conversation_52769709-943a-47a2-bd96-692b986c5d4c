package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.*;
import id.co.panindaiichilife.superapp.agent.api.filter.AgentProductionGroupFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.AgentProductionPerAgentFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.AgentProductionPerPolicyFilter;
import id.co.panindaiichilife.superapp.agent.service.widget.ProductionAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;

@RestController("widgetProductionAgentController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class ProductionAgentController {

    private final ProductionAgentService productionAgentService;


    @Operation(summary = "Get widget Summary Produksi Saya")
    @GetMapping(value = "summary-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiSaya', 'view')")
    public AgentProductionSummaryDto getSummaryProductionMonthly(Principal principal,
                                                                 @RequestParam(value = "month", required = false) Integer month,
                                                                 @RequestParam(value = "year", required = false) Integer year) {
        if (null == month) return productionAgentService.getYearlyProductionSummary(principal.getName(), year);
        return productionAgentService.getMonthlyProductionSummary(principal.getName(), month, year);
    }

    @Operation(summary = "Get widget Produksi Saya")
    @GetMapping(value = "my-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiSaya', 'view')")
    public List<AgentProductionDto> getMyProduction(Principal principal,
                                                    @ParameterObject @ModelAttribute("filter") AgentProductionPerAgentFilter filter) {
        return productionAgentService.getMyProduction(principal.getName(), filter);
    }

    @Operation(summary = "Get widget Produksi Team")
    @GetMapping(value = "team-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiTeam', 'view')")
    public Page<AgentProductionDto> getTeamProduction(Principal principal,
                                                      @ParameterObject @ModelAttribute("filter") AgentProductionPerAgentFilter filter,
                                                      @ParameterObject @PageableDefault(sort = "netApe", direction = Sort.Direction.DESC) Pageable pageable) {
        return productionAgentService.getTeamProduction(principal.getName(), filter, pageable);
    }

    @Operation(summary = "Get widget Detail Produksi")
    @GetMapping(value = "detail-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiSaya', 'view')")
    public Page<DetailAgentProductionDto> getDetailProduction(Principal principal,
                                                              @ParameterObject @ModelAttribute("filter") AgentProductionPerPolicyFilter filter,
                                                              @ParameterObject @PageableDefault(sort = "netApe", direction = Sort.Direction.DESC) Pageable pageable) {
        return productionAgentService.getDetailProduction(principal.getName(), filter, pageable);
    }

    @Operation(summary = "Get widget Produksi Group")
    @GetMapping(value = "group-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiGroup', 'view')")
    public Page<AgentProductionDto> getGroupProductionByMainBranch(Principal principal,
                                                                   @ParameterObject @ModelAttribute("filter") AgentProductionGroupFilter filter,
                                                                   @ParameterObject @PageableDefault(sort = "netApe", direction = Sort.Direction.DESC) Pageable pageable) {
        return productionAgentService.getGroupProduction(principal.getName(), filter, pageable);
    }

    @Operation(summary = "Get monthly summary production for January - December in current year")
    @GetMapping(value = "monthly-summary-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiSaya', 'view')")
    public List<AgentProductionSummaryDto> getAllMonthlyProductionSummaries(Principal principal,
                                                                            @RequestParam(value = "agentCode", required = false) String agentCode,
                                                                            @RequestParam(value = "year", required = false) Integer year) {

        String username = principal.getName();
        if (agentCode != null) {
            username = agentCode;
        }
        
        return productionAgentService.getAllMonthlyProductionSummaries(username, year);
    }

    @Operation(summary = "Get yearly summary production for last 5 years")
    @GetMapping(value = "yearly-summary-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiSaya', 'view')")
    public List<AgentProductionSummaryDto> getLastFiveYearsProductionSummaries(Principal principal,
                                                                               @RequestParam(value = "agentCode", required = false) String agentCode) {

        String username = principal.getName();
        if (agentCode != null) {
            username = agentCode;
        }

        return productionAgentService.getLastFiveYearsProductionSummaries(username);
    }

    @Operation(summary = "Get production summary aggregated by main branch code")
    @GetMapping(value = "branch-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiBranch', 'view')")
    public List<BranchProductionSummaryDto> getProductionByMainBranchCode(Principal principal,
                                                                          @RequestParam(value = "month", required = false) Integer month,
                                                                          @RequestParam(value = "year", required = false) Integer year) {
        return productionAgentService.getProductionByMainBranchCode(principal.getName(), month, year);
    }

    @Operation(summary = "Get widget Produksi BDM")
    @GetMapping(value = "bdm-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiArea', 'view')")
    public List<BdmProductionSummaryDto> getBdmProduction(Principal principal,
                                                          @RequestParam(value = "month", required = false) Integer month,
                                                          @RequestParam(value = "year", required = false) Integer year) {
        return productionAgentService.getProductionBdmByMainBranchCode(principal.getName(), month, year);
    }

    @Operation(summary = "Get widget Produksi ABDD")
    @GetMapping(value = "abdd-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiArea', 'view')")
    public List<AbddProductionSummaryDto> getAbddProduction(Principal principal,
                                                            @RequestParam(value = "month", required = false) Integer month,
                                                            @RequestParam(value = "year", required = false) Integer year) {
        return productionAgentService.getProductionAbddByMainBranchCode(principal.getName(), month, year);
    }

    @Operation(summary = "Get widget Produksi BDD")
    @GetMapping(value = "bdd-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiArea', 'view')")
    public List<BddProductionSummaryDto> getBddProduction(Principal principal,
                                                          @RequestParam(value = "month", required = false) Integer month,
                                                          @RequestParam(value = "year", required = false) Integer year) {
        return productionAgentService.getProductionBddByMainBranchCode(principal.getName(), month, year);
    }

    @Operation(summary = "Get widget Produksi HOS")
    @GetMapping(value = "hos-production")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ProduksiArea', 'view')")
    public List<HosProductionSummaryDto> getHosProduction(Principal principal,
                                                          @RequestParam(value = "month", required = false) Integer month,
                                                          @RequestParam(value = "year", required = false) Integer year) {
        return productionAgentService.getProductionHosByMainBranchCode(principal.getName(), month, year);
    }

}
