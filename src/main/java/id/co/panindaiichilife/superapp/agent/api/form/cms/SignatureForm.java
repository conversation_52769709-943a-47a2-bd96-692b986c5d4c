package id.co.panindaiichilife.superapp.agent.api.form.cms;

import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.SignaturePageType;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class SignatureForm {

    @NotNull(message = "User ID is required")
    private Long userId;

    private Channel channel;

    private String documentType;

    @NotBlank(message = "Signature is required")
    private String signature;

    private String paraf;

    private SignaturePageType pageType;
}
