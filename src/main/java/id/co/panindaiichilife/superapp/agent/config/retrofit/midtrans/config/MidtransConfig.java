package id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.config;

import id.co.panindaiichilife.superapp.agent.core.retrofit.GsonContext;
import id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.MidtransProvider;
import id.co.panindaiichilife.superapp.agent.core.retrofit.HttpClientContext;
import id.co.panindaiichilife.superapp.agent.core.retrofit.LoggingContext;
import id.co.panindaiichilife.superapp.agent.core.retrofit.SuccessErrorInterceptor;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.concurrent.TimeUnit;

@Configuration
public class MidtransConfig {

  @Value("${midtrans.base-url}")
  private String baseUrl = "";

  @Value("${provider.timeout:60}")
  private Integer timeout;

  @Value("${ssl.validation.disabled:false}")
  private Boolean sslValidationDisabled;

  @Bean("midtransRetrofit")
  public Retrofit retrofit() {
    return new Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(httpClient())
            .addConverterFactory(GsonConverterFactory.create(GsonContext.getInstance()))
            .build();
  }

  @Bean
  public MidtransProvider midtransProvider() {
    return retrofit().create(MidtransProvider.class);
  }

  @Bean("midtransAuthorizationInterceptor")
  public Interceptor authorizationInterceptor() {
    return new MidtransAuthorizationInterceptor();
  }

  @Bean("midtransLoggingInterceptor")
  public Interceptor loggingInterceptor() {
    return LoggingContext.createLoggingInterceptor();
  }

  @Bean("midtransSuccessErrorInterceptor")
  public Interceptor successErrorInterceptor() {
    return new SuccessErrorInterceptor();
  }

  @Bean("midtransHttpClient")
  public OkHttpClient httpClient() {
    OkHttpClient.Builder builder = new OkHttpClient.Builder();
    HttpClientContext.configureSsl(builder, sslValidationDisabled);
    builder.addInterceptor(authorizationInterceptor());
    builder.addInterceptor(successErrorInterceptor());
    builder.addInterceptor(loggingInterceptor());
    builder.connectTimeout(timeout, TimeUnit.SECONDS);
    builder.readTimeout(timeout, TimeUnit.SECONDS);
    return builder.build();
  }
}
