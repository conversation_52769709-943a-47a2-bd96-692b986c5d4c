package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.PersistencyDto;
import id.co.panindaiichilife.superapp.agent.api.filter.PersistencyFilter;
import id.co.panindaiichilife.superapp.agent.service.widget.PersistencyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.time.LocalDate;
import java.util.List;

@RestController("widgetPersistencyController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class PersistencyController {

    private final PersistencyService persistencyService;

    @Operation(summary = "Get widget Persistency Individual")
    @GetMapping(value = "persistency-individual")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.PersistencyIndividual', 'view')")
    public PersistencyDto getPersistencyAgent(Principal principal,
                                              @ParameterObject @ModelAttribute("filter") PersistencyFilter filter) {
        if (filter.getYear() == null) {
            filter.setYear(LocalDate.now().getYear());
        }
        return persistencyService.getPersistencyIndividual(principal.getName(), filter);
    }

    @Operation(summary = "Get widget Persistency Team")
    @GetMapping(value = "persistency-team")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.PersistencyTeam', 'view')")
    public List<PersistencyDto> getPersistencyTeam(Principal principal,
                                                   @ParameterObject @ModelAttribute("filter") PersistencyFilter filter) {
        if (filter.getYear() == null) {
            filter.setYear(LocalDate.now().getYear());
        }
        return persistencyService.getPersistencyTeam(principal.getName(), filter);
    }
}
