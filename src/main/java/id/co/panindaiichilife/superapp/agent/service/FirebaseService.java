package id.co.panindaiichilife.superapp.agent.service;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.*;
import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.Device;
import id.co.panindaiichilife.superapp.agent.model.Inbox;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.DeviceRepository;
import id.co.panindaiichilife.superapp.agent.repository.InboxRepository;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class FirebaseService {

    private final DeviceRepository deviceRepository;
    private final InboxRepository inboxRepository;
    @Value("${firebase.credentials.path}")
    private String firebaseCredentialsPath;
    private FirebaseMessaging firebaseMessaging;

    @PostConstruct
    public void initialize() {
        try {
            log.info("Initializing Firebase with credentials path: {}", firebaseCredentialsPath);

            Resource resource = new ClassPathResource(firebaseCredentialsPath);
            if (!resource.exists()) {
                log.error("Firebase credentials file not found at path: {}", firebaseCredentialsPath);
                return;
            }

            // Validate the service account file
            if (!validateServiceAccountFile(resource)) {
                log.error("Firebase service account file validation failed");
                return;
            }

            GoogleCredentials credentials = GoogleCredentials.fromStream(resource.getInputStream());
            log.info("Firebase credentials loaded successfully");

            if (FirebaseApp.getApps().isEmpty()) {
                FirebaseOptions options = FirebaseOptions.builder()
                        .setCredentials(credentials)
                        // Explicitly set the project ID to ensure proper endpoint resolution
                        .setProjectId(extractProjectIdFromCredentials(credentials))
                        .build();

                FirebaseApp app = FirebaseApp.initializeApp(options);
                log.info("Firebase app initialized with project ID: {}", app.getOptions().getProjectId());

                // Verify the project ID is correctly set
                String projectId = app.getOptions().getProjectId();
                if (projectId == null || projectId.trim().isEmpty()) {
                    log.error("Firebase project ID is null or empty after initialization");
                    throw new IllegalStateException("Firebase project ID not properly configured");
                }
            } else {
                log.info("Firebase app already initialized");
            }

            firebaseMessaging = FirebaseMessaging.getInstance();
            log.info("Firebase messaging instance created successfully");

        } catch (IOException e) {
            log.error("Firebase initialization failed due to IO error: {}", e.getMessage(), e);
            firebaseMessaging = null;
        } catch (Exception e) {
            log.error("Firebase initialization failed due to unexpected error: {}", e.getMessage(), e);
            firebaseMessaging = null;
        }
    }

    private boolean validateServiceAccountFile(Resource resource) {
        try {
            // Read and parse the JSON to validate basic structure
            String content = new String(resource.getInputStream().readAllBytes());

            // Basic validation - check if it contains required fields
            if (!content.contains("\"type\"") || !content.contains("\"project_id\"") ||
                !content.contains("\"private_key\"") || !content.contains("\"client_email\"")) {
                log.error("Firebase service account file is missing required fields");
                return false;
            }

            // Check if it's a service account type
            if (!content.contains("\"type\": \"service_account\"")) {
                log.error("Firebase credentials file is not a service account type");
                return false;
            }

            log.info("Firebase service account file validation passed");
            return true;

        } catch (Exception e) {
            log.error("Error validating Firebase service account file: {}", e.getMessage());
            return false;
        }
    }

    private void handleFailedTokens(BatchResponse response, List<Device> devices, List<String> tokens) {
        List<SendResponse> responses = response.getResponses();
        int invalidTokensCleared = 0;

        for (int i = 0; i < responses.size(); i++) {
            SendResponse sendResponse = responses.get(i);
            if (!sendResponse.isSuccessful()) {
                FirebaseMessagingException exception = sendResponse.getException();
                String token = tokens.get(i);
                Device device = devices.get(i);

                log.warn("Failed to send notification to token: {} for device: {} user: {}. Error: {} - {}",
                        token.substring(0, Math.min(token.length(), 20)) + "...",
                        device.getId(),
                        device.getUser().getId(),
                        exception.getMessagingErrorCode(),
                        exception.getMessage());

                if (isTokenInvalid(exception)) {
                    device.setFirebaseToken(null);
                    deviceRepository.save(device);
                    invalidTokensCleared++;
                    log.info("Cleared invalid firebase token for device: {} user: {} error code: {}",
                            device.getId(), device.getUser().getId(), exception.getMessagingErrorCode());
                }
            }
        }

        if (invalidTokensCleared > 0) {
            log.info("Cleared {} invalid Firebase tokens", invalidTokensCleared);
        }
    }

    private boolean isTokenInvalid(FirebaseMessagingException exception) {
        return exception.getMessagingErrorCode() == MessagingErrorCode.UNREGISTERED ||
                exception.getMessagingErrorCode() == MessagingErrorCode.INVALID_ARGUMENT ||
                exception.getMessagingErrorCode() == MessagingErrorCode.SENDER_ID_MISMATCH;
    }

    private Long parseLongSafely(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return Long.valueOf(value);
        } catch (NumberFormatException e) {
            log.warn("Failed to parse Long value: {}", value);
            return null;
        }
    }

    private TrxType parseTrxTypeSafely(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return TrxType.valueOf(value);
        } catch (IllegalArgumentException e) {
            log.warn("Failed to parse TrxType value: {}", value);
            return null;
        }
    }

    private boolean isFirebaseInitialized() {
        if (firebaseMessaging == null) {
            log.warn("Firebase messaging is not initialized. Cannot send notifications.");
            return false;
        }

        if (FirebaseApp.getApps().isEmpty()) {
            log.warn("No Firebase apps found. Firebase may not be properly initialized.");
            return false;
        }

        return true;
    }

    /**
     * Health check method to verify Firebase connectivity
     * @return true if Firebase is properly configured and accessible
     */
    public boolean isFirebaseHealthy() {
        if (!isFirebaseInitialized()) {
            return false;
        }

        try {
            // Try to get the Firebase app to verify it's accessible
            FirebaseApp app = FirebaseApp.getInstance();
            String projectId = app.getOptions().getProjectId();
            log.info("Firebase health check passed. Project ID: {}", projectId);
            return true;
        } catch (Exception e) {
            log.error("Firebase health check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Comprehensive Firebase diagnostic method
     * @return Map containing diagnostic information
     */
    public Map<String, Object> getFirebaseDiagnostics() {
        Map<String, Object> diagnostics = new HashMap<>();

        try {
            // Check if credentials file exists
            Resource resource = new ClassPathResource(firebaseCredentialsPath);
            diagnostics.put("credentialsFileExists", resource.exists());
            diagnostics.put("credentialsPath", firebaseCredentialsPath);

            if (resource.exists()) {
                // Read and parse service account info
                String content = new String(resource.getInputStream().readAllBytes());
                if (content.contains("\"project_id\"")) {
                    String projectId = extractProjectIdFromJson(content);
                    diagnostics.put("projectIdFromFile", projectId);
                }

                if (content.contains("\"client_email\"")) {
                    String clientEmail = extractClientEmailFromJson(content);
                    diagnostics.put("serviceAccountEmail", clientEmail);
                }
            }

            // Check Firebase app status
            diagnostics.put("firebaseAppsCount", FirebaseApp.getApps().size());
            diagnostics.put("firebaseMessagingInitialized", firebaseMessaging != null);

            if (!FirebaseApp.getApps().isEmpty()) {
                FirebaseApp app = FirebaseApp.getInstance();
                diagnostics.put("firebaseProjectId", app.getOptions().getProjectId());
                diagnostics.put("firebaseAppName", app.getName());
            }

        } catch (Exception e) {
            diagnostics.put("error", e.getMessage());
            log.error("Error getting Firebase diagnostics", e);
        }

        // Add API version information
        diagnostics.put("usingNewAPI", true);
        diagnostics.put("apiVersion", "sendEachForMulticast (recommended)");
        diagnostics.put("deprecatedAPI", "sendMulticast (causes 404 /batch error)");

        return diagnostics;
    }

    private String extractProjectIdFromJson(String json) {
        try {
            int start = json.indexOf("\"project_id\"") + 15;
            int end = json.indexOf("\"", start);
            return json.substring(start, end);
        } catch (Exception e) {
            return "unknown";
        }
    }

    private String extractClientEmailFromJson(String json) {
        try {
            int start = json.indexOf("\"client_email\"") + 17;
            int end = json.indexOf("\"", start);
            return json.substring(start, end);
        } catch (Exception e) {
            return "unknown";
        }
    }

    private String extractProjectIdFromCredentials(GoogleCredentials credentials) {
        try {
            // Try to get project ID from credentials
            if (credentials instanceof ServiceAccountCredentials) {
                ServiceAccountCredentials serviceAccount = (ServiceAccountCredentials) credentials;
                return serviceAccount.getProjectId();
            }

            // Fallback: read from the credentials file again
            Resource resource = new ClassPathResource(firebaseCredentialsPath);
            String content = new String(resource.getInputStream().readAllBytes());
            return extractProjectIdFromJson(content);

        } catch (Exception e) {
            log.warn("Could not extract project ID from credentials: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Test method to validate Firebase configuration by attempting to send a test message
     * This method doesn't actually send to any device, but validates the Firebase setup
     * @return true if Firebase configuration is valid
     */
    public boolean testFirebaseConfiguration() {
        if (!isFirebaseInitialized()) {
            log.warn("Firebase not initialized for configuration test");
            return false;
        }

        try {
            // Create a test multicast message with invalid token to test the API
            MulticastMessage testMessage = MulticastMessage.builder()
                    .setNotification(Notification.builder()
                            .setTitle("Test")
                            .setBody("Configuration test")
                            .build())
                    .addToken("test-token-that-does-not-exist")
                    .build();

            // This will fail with UNREGISTERED token, but if we get that specific error,
            // it means Firebase configuration is working
            try {
                BatchResponse response = firebaseMessaging.sendEachForMulticast(testMessage);
                log.info("Firebase configuration test - got response with {} failures", response.getFailureCount());

                // Check if we got the expected error for invalid token
                if (response.getFailureCount() > 0) {
                    SendResponse sendResponse = response.getResponses().get(0);
                    if (!sendResponse.isSuccessful()) {
                        FirebaseMessagingException exception = sendResponse.getException();
                        if (exception.getMessagingErrorCode() == MessagingErrorCode.UNREGISTERED ||
                            exception.getMessagingErrorCode() == MessagingErrorCode.INVALID_ARGUMENT) {
                            log.info("Firebase configuration test passed - got expected error: {}",
                                    exception.getMessagingErrorCode());
                            return true;
                        } else {
                            log.error("Firebase configuration test failed with unexpected error: {} - {}",
                                    exception.getMessagingErrorCode(), exception.getMessage());
                            return false;
                        }
                    }
                }

                log.info("Firebase configuration test passed - unexpected success");
                return true;

            } catch (FirebaseMessagingException e) {
                log.error("Firebase configuration test failed with messaging error: {} - {}",
                        e.getMessagingErrorCode(), e.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("Firebase configuration test failed with unexpected error", e);
            return false;
        }
    }

    public void sendNotification(List<User> users, NotificationDto request) {
        if (users == null || users.isEmpty()) {
            log.warn("No users provided for notification");
            return;
        }

        if (request == null) {
            log.warn("Notification request is null");
            return;
        }

        log.info("Sending notification to {} users: {}", users.size(), request.getTitle());

        // Save to Inbox for each user
        for (User user : users) {
            Inbox inbox = new Inbox();
            inbox.setUser(user);
            inbox.setInboxType(request.getInboxType());
            inbox.setTitle(request.getTitle());
            inbox.setBody(request.getBody());

            // Safely extract data from request, handling null data map
            Map<String, String> data = request.getData();
            if (data != null) {
                inbox.setApprovalId(parseLongSafely(data.get("approvalId")));
                inbox.setTrxId(parseLongSafely(data.get("trxId")));
                inbox.setTrxType(parseTrxTypeSafely(data.get("trxType")));
            }

            inboxRepository.save(inbox);
        }
        log.info("Saved {} inbox entries", users.size());

        // Check if Firebase is properly initialized before attempting to send push notifications
        if (!isFirebaseInitialized()) {
            log.warn("Firebase is not initialized. Skipping push notification sending. Inbox entries have been saved.");
            return;
        }

        List<Device> devices = users.stream()
                .flatMap(user -> deviceRepository.findByUserAndStatus(user, Device.Status.Active).stream())
                .collect(Collectors.toList());

        List<String> tokens = devices.stream()
                .map(Device::getFirebaseToken)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        if (tokens.isEmpty()) {
            log.info("No valid device tokens found for users");
            return;
        }

        log.info("Found {} valid device tokens", tokens.size());

        MulticastMessage.Builder messageBuilder = MulticastMessage.builder()
                .setNotification(Notification.builder()
                        .setTitle(request.getTitle())
                        .setBody(request.getBody())
                        .build())
                .addAllTokens(tokens);

        // Add data only if it's not null
        if (request.getData() != null) {
            messageBuilder.putAllData(request.getData());
        }

        MulticastMessage message = messageBuilder.build();

        try {
            // Using sendEachForMulticast instead of deprecated sendMulticast to avoid 404 /batch endpoint error
            log.info("Attempting to send multicast message to Firebase using sendEachForMulticast API");
            BatchResponse response = firebaseMessaging.sendEachForMulticast(message);
            log.info("Notification sent - success: {}, failure: {}",
                    response.getSuccessCount(), response.getFailureCount());

            if (response.getFailureCount() > 0) {
                log.warn("Some notifications failed to send. Processing failed tokens...");
                handleFailedTokens(response, devices, tokens);
            } else {
                log.info("All notifications sent successfully");
            }
        } catch (FirebaseMessagingException e) {
            log.error("Failed to send firebase notification. Error code: {}, Message: {}, HTTP Status: {}",
                    e.getMessagingErrorCode(), e.getMessage(),
                    e.getHttpResponse() != null ? e.getHttpResponse().getStatusCode() : "unknown", e);
        } catch (Exception e) {
            log.error("Unexpected error while sending firebase notification", e);
        }
    }
}