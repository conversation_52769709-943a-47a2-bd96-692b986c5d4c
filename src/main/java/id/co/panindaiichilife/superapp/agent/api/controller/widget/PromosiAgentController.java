package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.PromosiAgentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.widget.PromosiLeaderDto;
import id.co.panindaiichilife.superapp.agent.api.filter.PromosiAgentFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.PromosiLeaderFilter;
import id.co.panindaiichilife.superapp.agent.service.widget.PromosiAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.util.List;

@RestController("widgetPromosiAgentController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class PromosiAgentController {

    private final PromosiAgentService promosiAgentService;

    @Operation(summary = "Get widget Promosi Agent")
    @GetMapping(value = "promosi-agent")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.PromosiAgent', 'view')")
    public List<PromosiAgentDto> getPromosiAgent(Principal principal,
                                                 @ParameterObject @ModelAttribute("filter") PromosiAgentFilter filter) {
        return promosiAgentService.getPromosiAgent(principal.getName(), filter);
    }

    @Operation(summary = "Get widget Promosi Leader")
    @GetMapping(value = "promosi-leader")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.PromosiLeader', 'view')")
    public List<PromosiLeaderDto> getPromosiLeader(Principal principal,
                                                   @ParameterObject @ModelAttribute("filter") PromosiLeaderFilter filter) {
        return promosiAgentService.getPromosiLeader(principal.getName(), filter);
    }
}
