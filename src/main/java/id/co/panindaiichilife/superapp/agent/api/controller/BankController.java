package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.BankDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BankFilter;
import id.co.panindaiichilife.superapp.agent.service.BankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;

@RestController("bankController")
@RequestMapping("/api/bank")
@Tag(name = "Bank", description = "API Bank")
@Slf4j
@RequiredArgsConstructor
public class BankController {
    
    private final BankService bankService;

    @Operation(summary = "Get Bank")
    @GetMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Bank', 'view')")
    public Page<BankDto> getBank(Principal principal, @ParameterObject @ModelAttribute("filter") BankFilter filter,
                                 @ParameterObject @PageableDefault(sort = "bankName", direction = Sort.Direction.ASC) Pageable pageable) {

        return bankService.findAll(pageable, filter);
    }
}
