package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;

@Entity
@Table(name = "devices", uniqueConstraints = {
        @UniqueConstraint(name = "uk_devices_device_user", columnNames = {"deviceId", "user_id"})
})
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id", "deviceId"})
@SQLDelete(sql = "UPDATE devices SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class Device extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "devices_id_seq")
    @SequenceGenerator(name = "devices_id_seq", sequenceName = "devices_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Audited
    @Column(name = "device_id", nullable = false)
    private String deviceId;

    @Audited
    private String deviceModel;

    @Audited
    @Column(name = "os_type", nullable = false)
    private String osType;

    @Audited
    private String osVersion;

    @Audited
    private String appVersion;

    @Audited
    private String appBuildNumber;

    @Audited
    private String deviceLanguage;

    @Audited
    private Integer screenWidth;

    @Audited
    private Integer screenHeight;

    @Audited
    private String connectionType;

    @Audited
    private String timezone;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String firebaseToken;

    @Audited
    private String manufacturer;

    @Audited
    private Instant lastLogin;

    @Audited
    private Instant lastLogout;

    @Audited
    @Enumerated(EnumType.STRING)
    private Status status;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

    @SuppressWarnings("java:S115") // Suppress naming convention rule - enum values depend on external system
    public enum Status {
        Active, Inactive
    }
}
