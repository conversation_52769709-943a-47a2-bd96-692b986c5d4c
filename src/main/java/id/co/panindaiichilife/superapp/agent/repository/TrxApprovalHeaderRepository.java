package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface TrxApprovalHeaderRepository extends BaseRepository<TrxApprovalHeader, Long> {

    Page<TrxApprovalHeader> findByTrxTypeAndApprovalStatusInAndCurrentLevelInAndApproverRoleIn(
            TrxType trxType, List<ApprovalStatus> status, List<Integer> levels, Set<String> roles, Pageable pageable);

    Page<TrxApprovalHeader> findByApprovalStatusAndCurrentLevelIn(
            ApprovalStatus status, List<Integer> levels, Pageable pageable);

    Optional<TrxApprovalHeader> findTopByTrxTypeAndTrxId(TrxType trxType, Long trxId);

}
