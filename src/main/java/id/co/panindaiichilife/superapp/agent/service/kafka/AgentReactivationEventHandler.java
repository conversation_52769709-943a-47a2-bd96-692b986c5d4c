package id.co.panindaiichilife.superapp.agent.service.kafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sendgrid.helpers.mail.objects.Attachments;
import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalAgentReactivationRequestDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalAgentReactivationResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.PdfService;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.DocumentType;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.Branch;
import id.co.panindaiichilife.superapp.agent.model.Role;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalDetail;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.model.event.AgentReactivationEvent;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRejoinApplicationRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.FirebaseService;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRejoinService;
import id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.RejoinNotificationTemplates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.RejoinNotificationTemplates.Email.Keys.NEW_SUBMISSION_TO_CAS_REVIEWER;
import static id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.RejoinNotificationTemplates.Email.Keys.TO_BDM_ABDD_BDD_APPROVED_BY_CAS;
import static id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.RejoinNotificationTemplates.Email.Keys.TO_BDM_ABDD_BDD_REJECTED_BY_CAS;

@Service
@Slf4j
@RequiredArgsConstructor
public class AgentReactivationEventHandler {
    private final GlobalConfigService globalConfigService;
    private final TrxRejoinService rejoinService;
    private final PortalProvider portalProvider;
    private final UserRepository userRepository;
    private final FirebaseService firebaseService;
    private final AgentRepository agentRepository;
    private final PdfService pdfService;
    private final TrxRejoinApplicationRepository rejoinApplicationRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String DEFAULT_TEMPLATE = "mail.template.rejoin.default";

    public void handleEvent(AgentReactivationEvent event) {
        // update rejoin application status
        TrxRejoinApplication application = rejoinApplicationRepository.findById(event.getApplicationId())
                .orElseThrow(() -> new NotFoundException(String.format("Application %d not found",
                        event.getApplicationId())));

        application = rejoinService.resolveRejoinApplicationStatusOnEventReceived(
                application, event.getApprovalStatus());
        Agent proposedAgent = application.getAgent();

        Long rejoinApplicationId = application.getId();

        log.info("Handling agent reactivation event for ID: {}", rejoinApplicationId);

        TrxStatus latestApplicationStatus = application.getStatus();

        if ((event.getApprovalStatus() == ApprovalStatus.MENUNGGU_PERSETUJUAN || event.getApprovalStatus() == ApprovalStatus.BARU)
                && event.getNextApproverUserIds() != null
                && !event.getNextApproverUserIds().isEmpty()) {
            // Send notification to the next approvers
            log.info("Sending notification to {} next approvers for rejoin application ID: {}",
                    event.getNextApproverUserIds().size(), rejoinApplicationId);

            sendNextApproverNotifications(event);
            sendEmailToCasReviewerIfNeeded(application);
        }

        switch (latestApplicationStatus) {
            case COMPLETE:
                log.info("Rejoin application {} was approved and completed.", rejoinApplicationId);
                submitApplicationToPortal(event, application);

                sendEmailToRelatedDirectors(application);
                sendApprovedEmailToProposedAgent(application);

                break;

            case REJECTED:
                log.info("Rejoin application {} was rejected ", rejoinApplicationId);
                sendInboxNotificationToRequester(event, application, proposedAgent);
                sendRejectionEmailToProposedAgent(application);
                sendEmailToRelatedDirectors(application);

                return;

            case DIKEMBALIKAN:
                sendInboxNotificationToRequester(event, application, proposedAgent);
                break;

            case CANCELLED:
                log.info("Transaction {} cancelled", rejoinApplicationId);
                break;

            case EXPIRED:
                log.info("Transaction {} expired", rejoinApplicationId);
                break;

            default:
                log.info("Status {} of rejoin application {} has no handler yet", latestApplicationStatus,
                        rejoinApplicationId);
                break;

        }
    }

    private void sendApprovedEmailToProposedAgent(TrxRejoinApplication application) {
        Agent proposedAgent = application.getAgent();
        Agent leader = agentRepository.findTopByAgentCode(application.getProposedLeaderCode())
                .orElseThrow(() -> new NotFoundException(String.format("No agent was found with code %s",
                        application.getProposedLeaderCode())));

        Branch branch = application.getBranch();

        String proposedLevelName = "";
        switch(application.getProposedLevel()) {
            case "BP" -> proposedLevelName = "Business Partner";
            case "BM" -> proposedLevelName = "Business Manager";
            case "BD" -> proposedLevelName = "Business Director";
        }

        String bankInfo = String.format("%s %s",
                proposedAgent.getBankAccountNumber() != null ? proposedAgent.getBankAccountNumber() : "",
                proposedAgent.getBank() != null ? proposedAgent.getBank() : "");

        try {
            Map<String, Object> pdfData = new HashMap<>();
            pdfData.put("agentName", proposedAgent.getAgentName());
            pdfData.put("agentCode", proposedAgent.getAgentCode());
            pdfData.put("email", proposedAgent.getEmail());
            pdfData.put("bankAccountInfo", bankInfo);
            pdfData.put("level", proposedLevelName);
            pdfData.put("directLeaderInfo", String.format("%s - %s", leader.getAgentCode(), leader.getAgentName()));
            pdfData.put("branchOfficeInfo", String.format("%s (%s)", branch.getBranchName(), branch.getBranchCode()));
            pdfData.put("branchAddress", branch.getAddress());

            byte[] pdfBytes = pdfService.generateDocumentPdfAsBytes(DocumentType.REJOIN_LETTER, pdfData);

            Attachments attachments = new Attachments();
            attachments.setFilename("Rejoin Letter.pdf"); // TODO confirm nama file
            attachments.setType("application/pdf");
            attachments.setDisposition("attachment");
            attachments.setContent(Base64.getEncoder().encodeToString(pdfBytes));

            Map<String, Object> emailData = Map.of("agent_name", proposedAgent.getAgentName());
            rejoinService.sendEmail(List.of(proposedAgent.getEmail()),
                    RejoinNotificationTemplates.Email.Keys.TO_REJOINED_AGENT_APPROVED_BY_CAS, DEFAULT_TEMPLATE,
                    emailData, attachments);
        } catch (Exception ex) {
            log.warn("Failed to send approval rejoin email for agent {} ({})", proposedAgent.getAgentCode(),
                    proposedAgent.getEmail());
        }
    }

    private void sendRejectionEmailToProposedAgent(TrxRejoinApplication application) {
        Agent proposedAgent = application.getAgent();

        Map<String, String> lastApproverInfo = getLastApproverInfo(application);

        try {
            Map<String, Object> emailData = Map.of(
                    "agent_name", proposedAgent.getAgentName(),
                    "approver_name", lastApproverInfo.get("lastApproverName"),
                    "remarks", lastApproverInfo.get("approvalRemarks"));

            rejoinService.sendEmail(List.of(proposedAgent.getEmail()),
                    RejoinNotificationTemplates.Email.Keys.TO_REJOINED_AGENT_REJECTED_BY_CAS, DEFAULT_TEMPLATE,
                    emailData);
        } catch (Exception e) {
            log.warn("Unable to send rejection email to agent {} due to: {}", proposedAgent.getAgentCode(),
                    e.getMessage());
        }
    }

    private Map<String, String> getLastApproverInfo(TrxRejoinApplication application) {
        Set<TrxApprovalDetail> approvalDetails = application.getApprovalHeader().getApprovalDetails();
        int approvalDetailsSize = approvalDetails.size();

        TrxApprovalDetail lastApproval = approvalDetailsSize > 0 ?
                approvalDetails.stream().toList().get(approvalDetailsSize - 1) : null;
        String lastApproverName = "";
        String approvalRemarks = "";
        if (lastApproval != null && lastApproval.getActionBy() != null) {
            lastApproverName = lastApproval.getActionBy().getName();
            approvalRemarks = lastApproval.getRemarks();
        }

        return Map.of("lastApproverName", lastApproverName, "approvalRemarks", approvalRemarks);
    }

    private void sendInboxNotificationToRequester(AgentReactivationEvent event,
                                                  TrxRejoinApplication application,
                                                  Agent proposedAgent) {
        NotificationDto notification = new NotificationDto();
        notification.setInboxType(InboxType.INBOX);
        notification.setData(Map.of(
                "approvalId", application.getApprovalHeader().getId().toString(),
                "trxId", application.getId().toString(),
                "trxType", event.getTrxType().name()));

        boolean isOkToSend = true;

        switch (application.getStatus()) {
            case COMPLETE -> {
                notification.setTitle("Pengajuan rejoin telah disetujui");
                notification.setBody(
                        String.format(
                                RejoinNotificationTemplates.Inbox.REQUESTER_REJOIN_REQUEST_APPROVED,
                                proposedAgent.getAgentName(),
                                proposedAgent.getAgentCode()));
            }
            case REJECTED -> {
                Map<String, String> lastApprovalDetails = getLastApproverInfo(application);
                notification.setTitle("Pengajuan rejoin telah ditolak");
                notification.setBody(
                        String.format(
                                RejoinNotificationTemplates.Inbox.REQUESTER_REJOIN_REQUEST_REJECTED,
                                proposedAgent.getAgentName(),
                                lastApprovalDetails.get("lastApproverName"),
                                lastApprovalDetails.get("approvalRemarks")));
            }
            case DIKEMBALIKAN -> {
                notification.setTitle("Pengajuan rejoin diterima");
                notification.setBody(
                        String.format(
                                RejoinNotificationTemplates.Inbox.REQUESTER_REJOIN_REQUEST_RETURNED,
                                proposedAgent.getAgentName(),
                                proposedAgent.getAgentCode()));
            }
            default -> {
                log.warn("No mapped inbox notification setup for rejoin application requester, status {}",
                        application.getStatus());
                isOkToSend = false;
            }
        }

        if (isOkToSend) {
            firebaseService.sendNotification(List.of(application.getSubmittedBy()), notification);
        }
    }

    private void sendNextApproverNotifications(AgentReactivationEvent event) {
        Long rejoinApplicationId = event.getApplicationId();
        // Get the list of next approver user IDs
        List<String> approverUserIds = event.getNextApproverUserIds();

        if (approverUserIds != null && !approverUserIds.isEmpty()) {
            // For each approver user ID
            for (String userId : approverUserIds) {
                // Find the user by username
                User approver = userRepository.findByUsername(userId).orElse(null);

                if (approver != null) {
                    // Send Firebase notification if the user has a device token
                    NotificationDto notification = NotificationDto.builder()
                            .data(Map.of(
                                    "approvalId", event.getApprovalHeaderId().toString(),
                                    "trxId", rejoinApplicationId.toString(),
                                    "trxType", event.getTrxType().name()))
                            .title(event.getNotificationTitle())
                            .body(event.getNotificationBody())
                            .inboxType(event.getInboxType())
                            .build();

                    firebaseService.sendNotification(List.of(approver), notification);

                    log.info("Approval request notification sent to approver: {} ({})", approver.getUsername(),
                            approver.getEmail());
                } else {
                    log.warn("Could not find approver with ID: {} or email is missing", userId);
                }
            }
        } else {
            log.warn("No next approver user IDs found for rejoin application ID: {}", rejoinApplicationId);
        }
    }

    private void submitApplicationToPortal(AgentReactivationEvent event, TrxRejoinApplication application) {
        log.info("Submitting agent reactivation request {} to portal...", event.getApplicationId());
        String channelSystemName = globalConfigService.getGlobalConfig("compass.user", "UAT-QX");

        Agent proposedAgent = application.getAgent();

        PortalAgentReactivationRequestDto request = PortalAgentReactivationRequestDto.builder()
                .agentCode(proposedAgent.getAgentCode())
                .leaderCode(proposedAgent.getLeaderCode())
                .targetLevel(application.getProposedLevel())
                .branchCode(proposedAgent.getBranchCode())
                .creby(channelSystemName)
                .build();

        try {
            Call<PortalAgentReactivationResponseDto> call = portalProvider.submitAgentReactivation(request);
            Response<PortalAgentReactivationResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalAgentReactivationResponseDto body = response.body();

                if (body.getStatusCode().equals("200") && body.getAgentUpdate().getRemark().equals("SUCCESS")) {
                    log.info("Agent {} was successfully reactivated in portal", proposedAgent.getAgentCode());
                } else {
                    log.warn("Failed to reactivate agent in portal. Details: {}",
                            objectMapper.writeValueAsString(body));
                    String failedMessage = body.getAgentUpdate() != null ? body.getAgentUpdate().getRemark() : "";
                    throw new BadRequestException(failedMessage);
                }
            } else {
                log.error("Agent reactivation in portal was failed.");
                throw new BadRequestException("Agent reactivation in portal was failed.");
            }
        } catch (Exception e) {
            log.error("Error sending agent reactivation request to portal system for application ID: {}",
                    event.getApplicationId());
            throw new InternalServerErrorException(e.getMessage());
        }
    }

    private void sendEmailToRelatedDirectors(TrxRejoinApplication application) {
        try {
            Agent proposedAgent = application.getAgent();

            List<String> directorsRoleCodes = List.of("ROLE_AGE_BDM", "ROLE_AGE_ABDD", "ROLE_AGE_BDD");
            Set<TrxApprovalDetail> approvalDetails = application.getApprovalHeader().getApprovalDetails();

            List<String> recipients = approvalDetails.stream()
                    .map(TrxApprovalDetail::getActionBy)
                    .filter(actionBy -> {
                        for (Role actorRole : actionBy.getRoles()) {
                            if (directorsRoleCodes.contains(actorRole.getCode())) {
                                return true;
                            }
                        }
                        return false;
                    })
                    .map(User::getEmail)
                    .toList();

            if (recipients.isEmpty()) {
                log.info("No related directors found to be notified about rejoin application {} with status {}",
                        application.getId(), application.getStatus());
                return;
            }

            switch (application.getStatus()) {
                case COMPLETE -> {
                    Map<String, String> lastApproverInfo = getLastApproverInfo(application);
                    rejoinService.sendEmail(
                            recipients,
                            TO_BDM_ABDD_BDD_APPROVED_BY_CAS,
                            DEFAULT_TEMPLATE,
                            Map.of(
                                    "agent_name", proposedAgent.getAgentName(),
                                    "approver_name", lastApproverInfo.get("lastApproverName"),
                                    "agent_code", proposedAgent.getAgentCode()));
                }
                case REJECTED -> {
                    Map<String, String> lastApproverInfo = getLastApproverInfo(application);
                    rejoinService.sendEmail(
                            recipients,
                            TO_BDM_ABDD_BDD_REJECTED_BY_CAS,
                            DEFAULT_TEMPLATE,
                            Map.of(
                                    "agent_name", proposedAgent.getAgentName(),
                                    "approver_name", lastApproverInfo.get("lastApproverName"),
                                    "remarks", lastApproverInfo.get("approvalRemarks")));
                }
            }
        } catch (Exception e) {
            log.warn("Unable to send email to directors, application status {}, due to: {}",
                    application.getStatus(), e.getMessage());
        }
    }

    private void sendEmailToCasReviewerIfNeeded(TrxRejoinApplication application) {
        // check if last approver was before cas reviewer
        if (application.getApprovalHeader() != null &&
                !application.getApprovalHeader().getApproverRole().equals("ROLE_AGE_BDD")) {
            log.debug("Skipping sending email new submission to CAS Reviewer");
            return;
        }

        Agent proposedAgent = application.getAgent();

        // find cas reviewer
        List<User> casReviewerUsers = userRepository.findUsersByRoleCode("ROLE_CAS_REVIEW_AGE");

        if (casReviewerUsers.isEmpty()) {
            log.warn("No CAS reviewer found!");
            return;
        }

        List<String> recipients = casReviewerUsers.stream().map(User::getEmail).toList();

        try {
            rejoinService.sendEmail(
                    recipients,
                    NEW_SUBMISSION_TO_CAS_REVIEWER,
                    DEFAULT_TEMPLATE,
                    Map.of(
                            "agent_code", proposedAgent.getAgentCode(),
                            "agent_name", proposedAgent.getAgentName()));
        } catch (Exception e) {
            log.warn("Unable to send email to CAS Reviewe due to {}", e.getMessage());
        }
    }
}
