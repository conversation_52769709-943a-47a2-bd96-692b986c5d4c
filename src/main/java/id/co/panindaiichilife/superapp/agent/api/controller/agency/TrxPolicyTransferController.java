package id.co.panindaiichilife.superapp.agent.api.controller.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxPolicyTransferDto;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxPolicyTransferForm;
import id.co.panindaiichilife.superapp.agent.api.validation.TrxPolicyTransferValidator;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxPolicyTransferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;

@RestController("agentPolicyTransferController")
@RequestMapping("/api/agency/policy-transfer")
@Tag(name = "Agency - Policy Transfer", description = "API Agent Policy Transfer")
@Slf4j
@RequiredArgsConstructor
public class TrxPolicyTransferController {
    private final TrxPolicyTransferService service;
    private final TrxPolicyTransferValidator validator;

    @Operation(summary = "Get assigned active policy transfer for approval")
    @GetMapping("/assigned/{agentCode}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.PolicyTransfer', 'view')")
    public Page<TrxPolicyTransferDto> getPolicyTransferByTarget(
            @PathVariable String agentCode,
            @RequestParam(required = false) String searchQuery,
            @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        return service.getPolicyTransferByTarget(agentCode, searchQuery, pageable);
    }

    @Operation(summary = "Get policy transfer")
    @GetMapping("/{trxTerminationId}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.PolicyTransfer', 'view')")
    public TrxPolicyTransferDto getByTrxTerminationId(@PathVariable Long trxTerminationId) {
        return service.getPolicyTransferByTerminationId(trxTerminationId);
    }

    @Operation(summary = "Set policy transfer target")
    @PostMapping
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.PolicyTransfer', 'create')")
    public TrxPolicyTransferDto createPolicyTransfer(Principal principal,
                                     @RequestBody @Valid TrxPolicyTransferForm form,
                                     BindingResult bindingResult) throws MethodArgumentNotValidException {
        validator.validate(form, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        return service.createPolicyTransfer(principal.getName(), form);
    }

    @Operation(summary = "Update policy transfer target")
    @PutMapping("/{trxTerminationId}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.PolicyTransfer', 'update')")
    public TrxPolicyTransferDto updateTarget(Principal principal,
                             @PathVariable Long trxTerminationId,
                             @RequestParam String targetAgentCode) {
        return service.updateTarget(principal.getName(), trxTerminationId, targetAgentCode);
    }

    @Operation(summary = "Approve policy transfer")
    @PutMapping("/{trxTerminationId}/approve")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.PolicyTransfer', 'approve')")
    public void approve(Principal principal, @PathVariable Long trxTerminationId) {
        service.approve(principal.getName(), trxTerminationId);
    }

    @Operation(summary = "Reject policy transfer")
    @PutMapping("/{trxTerminationId}/reject")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Agency.PolicyTransfer', 'reject')")
    public void reject(Principal principal, @PathVariable Long trxTerminationId) {
        service.reject(principal.getName(), trxTerminationId);
    }
}
