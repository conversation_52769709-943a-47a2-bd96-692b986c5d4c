package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.config.retrofit.superappcommon.SuperAppCommonProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.superappcommon.dto.GlobalConfigDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;

@Service
@Slf4j
@RequiredArgsConstructor
public class GlobalConfigService {

    private final SuperAppCommonProvider superAppCommonProvider;

    /**
     * Get a configuration value from global configuration by key with a default value
     * if the configuration is not found or if there's an error
     *
     * @param key          The configuration key to retrieve
     * @param defaultValue The default value to return if the configuration is not found or if there's an error
     * @return The configuration value or the default value
     */
    public String getGlobalConfig(String key, String defaultValue) {
        Call<GlobalConfigDto> call = superAppCommonProvider.getConfigByKey(key);

        try {
            Response<GlobalConfigDto> response = call.execute();
            if (response.isSuccessful() && response.body() != null) {
                return response.body().getValue();
            } else {
                log.warn("Config not found or empty for key: {}, using default value: {}", key, defaultValue);
                return defaultValue;
            }
        } catch (IOException e) {
            log.warn("Error retrieving global config for key: {}, using default value: {}", key, defaultValue, e);
            return defaultValue;
        }
    }
}
