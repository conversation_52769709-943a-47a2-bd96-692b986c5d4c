package id.co.panindaiichilife.superapp.agent.config.kafka;


import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.*;
import org.springframework.kafka.support.serializer.ErrorHandlingDeserializer;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

/**
 * Simplified Kafka configuration class that centralizes all Kafka-related configuration.
 * This class provides a clean, maintainable approach to configuring Kafka producers and consumers.
 */
@Configuration
@Slf4j
public class KafkaConfig {

    // Topic names
    public static final String RECRUITMENT_TOPIC = "recruitment-topic";
    public static final String EDIT_PROFILE_TOPIC = "edit-profile-topic";
    public static final String TERMINATION_TOPIC = "termination-topic";
    public static final String PROMOTION_TOPIC = "promotion-topic";
    public static final String REJOIN_TOPIC = "rejoin-topic";

    // Consumer group IDs
    public static final String RECRUITMENT_GROUP = "recruitment-group";
    public static final String EDIT_PROFILE_GROUP = "edit-profile-group";
    public static final String TERMINATION_GROUP = "termination-group";
    public static final String PROMOTION_GROUP = "promotion-group";
    public static final String REJOIN_GROUP = "rejoin-group";

    // Event package and type mappings
    private static final String EVENT_PACKAGE = "id.co.panindaiichilife.superapp.agent.model.event";

    // Type mappings are now dynamically generated by scanning for @KafkaEvent annotations
    private static final String TYPE_MAPPINGS = KafkaEventScanner.scanForTypeMappings(EVENT_PACKAGE);

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    //-------------------------------------------------------------------------
    // Topic Configuration
    //-------------------------------------------------------------------------

    @Bean
    public NewTopic recruitmentTopic() {
        return TopicBuilder.name(RECRUITMENT_TOPIC).partitions(3).replicas(1).build();
    }

    @Bean
    public NewTopic editProfileTopic() {
        return TopicBuilder.name(EDIT_PROFILE_TOPIC).partitions(3).replicas(1).build();
    }

    @Bean
    public NewTopic terminationTopic() {
        return TopicBuilder.name(TERMINATION_TOPIC).partitions(3).replicas(1).build();
    }

    @Bean
    public NewTopic promotionTopic() {
        return TopicBuilder.name(PROMOTION_TOPIC).partitions(3).replicas(1).build();
    }

    @Bean
    public NewTopic rejoinTopic() {
        return TopicBuilder.name(REJOIN_TOPIC).partitions(3).replicas(1).build();
    }

    //-------------------------------------------------------------------------
    // Producer Configuration
    //-------------------------------------------------------------------------

    @Bean
    @Primary
    public ProducerFactory<String, Object> producerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        props.put(JsonSerializer.TYPE_MAPPINGS, TYPE_MAPPINGS);

        return new DefaultKafkaProducerFactory<>(props);
    }

    @Bean
    @Primary
    public KafkaTemplate<String, Object> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    //-------------------------------------------------------------------------
    // Consumer Configuration
    //-------------------------------------------------------------------------

    private Map<String, Object> consumerProps(String groupId) {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ErrorHandlingDeserializer.class);
        props.put(ErrorHandlingDeserializer.VALUE_DESERIALIZER_CLASS, JsonDeserializer.class);
        props.put(JsonDeserializer.TRUSTED_PACKAGES, EVENT_PACKAGE);
        props.put(JsonDeserializer.TYPE_MAPPINGS, TYPE_MAPPINGS);
        props.put(JsonDeserializer.VALUE_DEFAULT_TYPE, "java.lang.Object");
        return props;
    }

    private ConsumerFactory<String, Object> consumerFactory(String groupId) {
        return new DefaultKafkaConsumerFactory<>(consumerProps(groupId));
    }

    private ConcurrentKafkaListenerContainerFactory<String, Object> containerFactory(String groupId) {
        ConcurrentKafkaListenerContainerFactory<String, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory(groupId));
        return factory;
    }

    @Bean
    @Primary
    public ConsumerFactory<String, Object> recruitmentConsumerFactory() {
        return consumerFactory(RECRUITMENT_GROUP);
    }

    @Bean
    @Primary
    public ConsumerFactory<String, Object> editProfileConsumerFactory() {
        return consumerFactory(EDIT_PROFILE_GROUP);
    }

    @Bean
    @Primary
    public ConsumerFactory<String, Object> terminationConsumerFactory() {
        return consumerFactory(TERMINATION_GROUP);
    }

    @Bean
    @Primary
    public ConsumerFactory<String, Object> promotionConsumerFactory() {
        return consumerFactory(PROMOTION_GROUP);
    }

    @Bean
    @Primary
    public ConsumerFactory<String, Object> rejoinConsumerFactory() {
        return consumerFactory(REJOIN_GROUP);
    }

    @Bean
    @Primary
    public ConcurrentKafkaListenerContainerFactory<String, Object> recruitmentKafkaListenerContainerFactory() {
        return containerFactory(RECRUITMENT_GROUP);
    }

    @Bean
    @Primary
    public ConcurrentKafkaListenerContainerFactory<String, Object> editProfileKafkaListenerContainerFactory() {
        return containerFactory(EDIT_PROFILE_GROUP);
    }

    @Bean
    @Primary
    public ConcurrentKafkaListenerContainerFactory<String, Object> terminationKafkaListenerContainerFactory() {
        return containerFactory(TERMINATION_GROUP);
    }

    @Bean
    @Primary
    public ConcurrentKafkaListenerContainerFactory<String, Object> promotionKafkaListenerContainerFactory() {
        return containerFactory(PROMOTION_GROUP);
    }

    @Bean
    @Primary
    public ConcurrentKafkaListenerContainerFactory<String, Object> rejoinKafkaListenerContainerFactory() {
        return containerFactory(REJOIN_GROUP);
    }
}
