package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.BankDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BankFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.BankForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalBankResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.model.Bank;
import id.co.panindaiichilife.superapp.agent.repository.BankRepository;
import id.co.panindaiichilife.superapp.agent.util.BankAdvanceAiCodeMapping;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BankService {

    private final BankRepository bankRepository;

    private final PortalProvider portalProvider;

    public Page<BankDto> findAll(Pageable pageable, BankFilter filter) {
        Page<Bank> banks = bankRepository.findAll(filter, pageable);
        return BaseDto.of(BankDto.class, banks, pageable);
    }

    public BankDto findOne(Long id) {
        Bank data = bankRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(BankDto.class, data);
    }

    @Transactional
    public BankDto add(BankForm bankForm) {
        Bank data = new Bank();
        BeanUtils.copyProperties(bankForm, data);
        bankRepository.save(data);

        return BaseDto.of(BankDto.class, data);
    }

    @Transactional
    public BankDto update(Long id, BankForm bankForm) {
        Bank data = bankRepository.findById(id).orElseThrow(NotFoundException::new);
        BeanUtils.copyProperties(bankForm, data);
        bankRepository.save(data);

        return BaseDto.of(BankDto.class, data);
    }

    public void delete(Long id) {
        bankRepository.deleteById(id);
    }

    public void syncBanks() {
        // Sync Banks
        Call<PortalBankResponseDto> callBanks = portalProvider.getBankList("Bank");

        try {
            Response<PortalBankResponseDto> response = callBanks.execute();
            if (response.isSuccessful()) {
                PortalBankResponseDto portalBankResponseDto = response.body();
                List<PortalBankResponseDto.KeyValueDto> banks = portalBankResponseDto.getKeyValue();
                int totalBanks = banks.size();
                int matchedBanks = 0;

                log.info("Starting bank sync with {} banks from API", totalBanks);

                for (PortalBankResponseDto.KeyValueDto bank : banks) {
                    Bank bankData = bankRepository.findByBankCode(bank.getKey()).orElse(null);
                    if (null == bankData) {
                        bankData = new Bank();
                    }
                    bankData.setBankCode(bank.getKey());

                    // Get the bank name from the API response
                    String bankName = bank.getValue2();
                    if (bankName == null || bankName.trim().isEmpty()) {
                        log.warn("Empty bank name for bank code: {}, skipping AdvanceAI code lookup", bank.getKey());
                        bankData.setBankAdvanceAiCode(bank.getValue());
                    } else {
                        // Find the bank advance AI code by bank name
                        String advanceAiCode = BankAdvanceAiCodeMapping.findBankCodeByName(bankName);

                        // If found, set it; otherwise use the value from API
                        if (advanceAiCode != null) {
                            log.info("Found AdvanceAI code for bank: {} -> {}", bankName, advanceAiCode);
                            bankData.setBankAdvanceAiCode(advanceAiCode);
                            matchedBanks++;
                        } else {
                            log.warn("No AdvanceAI code found for bank: {}, using value from API: {}", bankName, bank.getValue());
                        }
                    }

                    bankData.setBankName(bankName);
                    bankRepository.save(bankData);
                }

                log.info("Bank sync completed. Matched {}/{} banks with AdvanceAI codes", matchedBanks, totalBanks);
            }
        } catch (IOException e) {
            throw new InternalServerErrorException("Error occurred while syncing banks");
        }
    }

    /**
     * Update all existing banks with AdvanceAI codes based on their names
     */
    @Transactional
    public void updateBankAdvanceAiCodes() {
        log.info("Starting update of bank AdvanceAI codes");
        Page<Bank> banks = bankRepository.findAll(Pageable.unpaged());

        int totalBanks = banks.getContent().size();
        int updatedCount = 0;
        int skippedCount = 0;

        log.info("Found {} banks in database to process", totalBanks);

        for (Bank bank : banks) {
            String bankName = bank.getBankName();

            if (bankName == null || bankName.trim().isEmpty()) {
                log.warn("Skipping bank with empty name, code: {}", bank.getBankCode());
                skippedCount++;
                continue;
            }

            String advanceAiCode = BankAdvanceAiCodeMapping.findBankCodeByName(bankName);

            if (advanceAiCode != null) {
                log.info("Updating AdvanceAI code for bank: {} -> {}", bankName, advanceAiCode);
                bank.setBankAdvanceAiCode(advanceAiCode);
                bankRepository.save(bank);
                updatedCount++;
            } else {
                log.warn("No AdvanceAI code found for bank: {}", bankName);
                skippedCount++;
            }
        }

        log.info("Completed update of bank AdvanceAI codes. Updated: {}, Skipped: {}, Total: {}",
                updatedCount, skippedCount, totalBanks);
    }
}
