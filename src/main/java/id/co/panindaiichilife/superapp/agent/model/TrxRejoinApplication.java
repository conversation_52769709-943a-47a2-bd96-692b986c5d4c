package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.enums.ValidationAdministrationAgentStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationAmlStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationBankAccountStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationBlacklistStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationHirarkiStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationKtpStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAajiStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAasiStatus;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;
import java.time.LocalDate;

@Entity
@Table(name = "trx_rejoin_applications")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@SQLDelete(sql = "UPDATE trx_rejoin_applications SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class TrxRejoinApplication extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "rejoin_applications_id_seq")
    @SequenceGenerator(name = "rejoin_applications_id_seq", sequenceName = "rejoin_applications_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    private Agent agent;

    @Audited
    private String proposedLevel;

    @Audited
    private String proposedLeaderCode;

    @Audited
    @OneToOne(targetEntity = TrxTermination.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private TrxTermination lastTermination;

    @Audited
    @Enumerated(EnumType.STRING)
    private TrxType trxType;

    @Audited
    private String uploadedKtpPath;

    @Audited
    @Enumerated(EnumType.STRING)
    private TrxStatus status;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "submitter_id", nullable = false)
    private User submittedBy;

    @Audited
    @OneToOne(targetEntity = TrxApprovalHeader.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "approval_header_id")
    private TrxApprovalHeader approvalHeader;

    @Audited
    @Enumerated(EnumType.STRING)
    private ApprovalStatus approvalStatus;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "branch_id", nullable = false)
    private Branch branch;

    @Audited
    private String remarks;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

    @Audited
    private String idNumber;

    @Audited
    private String bankAccountName;

    @Audited
    private String bankAccountNumber;

    @Audited
    private LocalDate dob;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationBlacklistStatus validationBlacklistStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationKtpStatus validationKtpStatus;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String resulValidationKtp;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationBankAccountStatus validationBankAccountStatus;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String resultValidationBankAccount;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationHirarkiStatus validationHirarkiStatus;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String resultValidationHirarki;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationAmlStatus validationAmlStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationAdministrationAgentStatus validationAdministrationAgentStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationLicenseAajiStatus validationLicenseAajiStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationLicenseAasiStatus validationLicenseAasiStatus;
}
