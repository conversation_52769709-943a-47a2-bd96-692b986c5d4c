package id.co.panindaiichilife.superapp.agent.core.support;

import id.co.panindaiichilife.superapp.agent.core.support.annotation.Export;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.Header;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AnnotatedBeanUtils {

  public static Map<String, Field> getImportedFields(Class<?> type) {
    Map<String, Field> fieldMapping = new HashMap<>();
    Field[] fields = type.getDeclaredFields();
    for (Field field : fields) {
      if (field.isAnnotationPresent(Header.class)) {
        Header header = field.getAnnotation(Header.class);
        fieldMapping.put(header.value(), field);
      } else {
        String fieldName = field.getName().replaceAll("(.)(\\p{Upper})", "$1 $2").toLowerCase();
        fieldMapping.put(fieldName, field);
      }
    }

    return fieldMapping;
  }

  public static List<Field> getExportedFields(Class<?> type) {
    if (!type.isAnnotationPresent(Export.class)) {
      return getAllDeclaredFields(type, null);
    }

    Export export = type.getAnnotation(Export.class);
    List<String> exclude = Arrays.asList(export.exclude());
    String[] fieldNames = export.value();

    if (fieldNames.length > 0) {
      return getSpecificFields(type, fieldNames, exclude);
    }

    return getAllDeclaredFields(type, exclude);
  }

  private static List<Field> getSpecificFields(Class<?> type, String[] fieldNames, List<String> exclude) {
    List<Field> fields = new ArrayList<>();
    for (String fieldName : fieldNames) {
      if (exclude.contains(fieldName)) {
        continue;
      }

      try {
        Field field = type.getDeclaredField(fieldName);
        fields.add(field);
      } catch (NoSuchFieldException ex) {
        log.warn("Field {} not found on type {}", fieldName, type);
      }
    }
    return fields;
  }

  private static List<Field> getAllDeclaredFields(Class<?> type, List<String> exclude) {
    Field[] declaredFields = type.getDeclaredFields();
    List<Field> fields = new ArrayList<>();

    for (Field declaredField : declaredFields) {
      if (exclude != null && exclude.contains(declaredField.getName())) {
        continue;
      }
      fields.add(declaredField);
    }
    return fields;
  }

  public static List<ExportedHeader> getExportedFieldNames(Class<?> type) {
    List<Field> fields = getExportedFields(type);
    return fields.stream()
        .map(x -> {
          if (x.isAnnotationPresent(Header.class)) {
            
            String value = x.getAnnotation(Header.class).value();
            String comment = x.getAnnotation(Header.class).comment();
            return new ExportedHeader(value, comment);
                
                
          } else {
            String value =  x.getName().replaceAll("(.)(\\p{Upper})", "$1 $2").toLowerCase();
            return new ExportedHeader(value,"");
                
          }
        })
        .collect(Collectors.toList());
  }
  
  @Data
  public static class ExportedHeader {
    private String value;
    private String comment;
    
    public ExportedHeader(String value) {
      this(value,null);
    }
    
    public ExportedHeader(String value, String comment) {
      super();
      this.value = value;
      this.comment = comment;
    }
  }
}
