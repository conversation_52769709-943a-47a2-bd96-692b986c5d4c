package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class PortalAgentTerminationRequestDto {
    @SerializedName("agentCode")
    @JsonProperty("agentCode")
    private String agentCode;

    @SerializedName("creby")
    @JsonProperty("creby")
    private String creby;

    @SerializedName("isServicing")
    @JsonProperty("isServicing")
    private boolean isServicing;

    @SerializedName("servicingAgent")
    @JsonProperty("servicingAgent")
    private String servicingAgent;

}
