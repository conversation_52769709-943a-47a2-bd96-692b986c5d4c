package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalClaimResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("claimsTrackings")
    @JsonProperty("claimsTrackings")
    private List<ClaimTrackingDto> claimsTrackings;

    @Data
    public static class ClaimTrackingDto {

        @SerializedName("claimId")
        @JsonProperty("claimId")
        private String claimId;

        @SerializedName("policyNumber")
        @JsonProperty("policyNumber")
        private String policyNumber;

        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("agentName")
        @JsonProperty("agentName")
        private String agentName;

        @SerializedName("amount")
        @JsonProperty("amount")
        private double amount;

        @SerializedName("product")
        @JsonProperty("product")
        private String product;

        @SerializedName("policyHolder")
        @JsonProperty("policyHolder")
        private String policyHolder;

        @SerializedName("insuredName")
        @JsonProperty("insuredName")
        private String insuredName;

        @SerializedName("claimStatus")
        @JsonProperty("claimStatus")
        private String claimStatus;

        @SerializedName("claimType")
        @JsonProperty("claimType")
        private String claimType;

        @SerializedName("dateOfDeath")
        @JsonProperty("dateOfDeath")
        private String dateOfDeath;

        @SerializedName("admissionDate")
        @JsonProperty("admissionDate")
        private String admissionDate;

        @SerializedName("dischargeDate")
        @JsonProperty("dischargeDate")
        private String dischargeDate;

        @SerializedName("remark")
        @JsonProperty("remark")
        private String remark;

        @SerializedName("remarkDate")
        @JsonProperty("remarkDate")
        private String remarkDate;

        @SerializedName("requestedDate")
        @JsonProperty("requestedDate")
        private String requestedDate;

        @SerializedName("documentCompleteDate")
        @JsonProperty("documentCompleteDate")
        private String documentCompleteDate;

        @SerializedName("lastUpdateDate")
        @JsonProperty("lastUpdateDate")
        private String lastUpdateDate;

        @SerializedName("currency")
        @JsonProperty("currency")
        private String currency;

        @SerializedName("letter")
        @JsonProperty("letter")
        private String letter;

        @SerializedName("rowSpan")
        @JsonProperty("rowSpan")
        private int rowSpan;

        @SerializedName("diagnose")
        @JsonProperty("diagnose")
        private String diagnose;

        @SerializedName("sumInsured")
        @JsonProperty("sumInsured")
        private double sumInsured;

        @SerializedName("mainBranch")
        @JsonProperty("mainBranch")
        private String mainBranch;

        @SerializedName("productName")
        @JsonProperty("productName")
        private String productName;

        @SerializedName("bpa")
        @JsonProperty("bpa")
        private double bpa;
    }
}
