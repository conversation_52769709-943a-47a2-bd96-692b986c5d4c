package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.batch.JobCreationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.AgentDto;
import id.co.panindaiichilife.superapp.agent.api.filter.AgentFilter;
import id.co.panindaiichilife.superapp.agent.service.AgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController("cmsAgentController")
@RequestMapping("/api/cms/agent")
@Tag(name = "Agent - CMS", description = "API CMS Agent")
@Slf4j
@RequiredArgsConstructor
public class AgentCmsController {

    private final AgentService agentService;

    @Operation(summary = "List agents")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Agent', 'view')")
    public Page<AgentDto> index(@ParameterObject @ModelAttribute("filter") AgentFilter filter,
                                @ParameterObject @PageableDefault(sort = "agentCode", direction = Sort.Direction.ASC) Pageable pageable) {
        return agentService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific agent")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Agent', 'view')")
    public AgentDto view(@PathVariable long id) {
        return agentService.findOne(id);
    }

    @Operation(summary = "Run Job Import Agent")
    @GetMapping(value = "sync")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Agent', 'sync')")
    public List<JobCreationDto> syncAgent() {
        return agentService.runImportAgent();
    }
//
//    @Operation(summary = "Add new agents")
//    @PostMapping(value = "")
//    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Agent', 'add')")
//    public AgentDto insert(@Valid @RequestBody AgentForm agentForm) {
//        return agentService.add(agentForm);
//    }
//
//    @Operation(summary = "Modify existing agent")
//    @PutMapping(value = "{id}")
//    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Agent', 'edit')")
//    public AgentDto edit(@PathVariable long id,
//                         @Valid @RequestBody AgentForm agentForm) {
//        return agentService.update(id, agentForm);
//    }
//
//    @Operation(summary = "Delete existing agent")
//    @DeleteMapping(value = "{id}")
//    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Agent', 'delete')")
//    public void delete(@PathVariable long id) {
//        agentService.delete(id);
//    }
}
