package id.co.panindaiichilife.superapp.agent.core.service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
public class AmazonS3Service {

    private S3Client s3Client;

    @Value("${aws.s3.endpoint-url}")
    private String endPointUrl;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    @Value("${aws.s3.access-key}")
    private String accessKey;

    @Value("${aws.s3.secret-key}")
    private String secretKey;

    @PostConstruct
    protected void init() {
        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretKey);

        s3Client = S3Client.builder()
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.AP_SOUTHEAST_3)
                .build();
    }

    /**
     * Get absolute URL of S3 objects.
     */
    public String getUrl(String file) {
        if (!file.startsWith("/")) {
            file = "/" + file;
        }

        return endPointUrl + file;
    }

    /**
     * Store a multipart file to a specified destination.
     *
     * @param file        a multipart file to be stored
     * @param destination path/to/destination
     * @return true if the file is successfully stored, false otherwise
     */
    public boolean store(MultipartFile file, String destination) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        if (StringUtils.isBlank(destination)) {
            return false;
        }

        try (InputStream in = file.getInputStream()) {
            if (destination.startsWith("/")) {
                destination = destination.substring(1);
            }

            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(destination)
                    .contentLength(file.getSize())
                    .contentType(file.getContentType())
                    .build();

            s3Client.putObject(request, RequestBody.fromInputStream(in, file.getSize()));

            return true;
        } catch (Exception ex) {
            log.error("Error storing file", ex);
            return false;
        }
    }

    /**
     * Store a file to a specified destination.
     *
     * @param file        a file to be stored
     * @param destination path/to/destination
     * @param contentType MIME type of the content
     * @return true if the file is successfully stored, false otherwise
     */
    public boolean storeFile(File file, String destination, String contentType) {
        if (file == null || !file.exists()) {
            return false;
        }

        if (StringUtils.isBlank(destination)) {
            return false;
        }

        try (InputStream in = new FileInputStream(file)) {
            if (destination.startsWith("/")) {
                destination = destination.substring(1);
            }

            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(destination)
                    .contentLength(file.length())
                    .contentType(contentType)
                    .build();

            s3Client.putObject(request, RequestBody.fromInputStream(in, file.length()));

            return true;
        } catch (Exception ex) {
            log.error("Error storing file", ex);
            return false;
        }
    }

    /**
     * Store byte array content to a specified destination.
     *
     * @param content     byte array content to be stored
     * @param destination path/to/destination
     * @param contentType MIME type of the content
     * @return true if the content is successfully stored, false otherwise
     */
    public boolean storeBytes(byte[] content, String destination, String contentType) {
        if (content == null || content.length == 0) {
            return false;
        }

        if (StringUtils.isBlank(destination)) {
            return false;
        }

        try (InputStream in = new ByteArrayInputStream(content)) {
            if (destination.startsWith("/")) {
                destination = destination.substring(1);
            }

            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(destination)
                    .contentLength((long) content.length)
                    .contentType(contentType)
                    .build();

            s3Client.putObject(request, RequestBody.fromInputStream(in, content.length));

            return true;
        } catch (Exception ex) {
            log.error("Error storing byte content", ex);
            return false;
        }
    }

    /**
     * Helper function to save multipart file from a form object to a model object. Will use
     * <code>field + 'Deleted'</code> as deleted field.
     *
     * @param form        form object
     * @param model       model object
     * @param field       property name in the form object which holds a MultipartFile object and in the
     *                    model object to be set with the resulting path
     * @param destination path/to/destination
     */
    public void saveFile(Object form, Object model, String field, String destination) {
        saveFile(form, model, field, field, field + "Deleted", destination);
    }

    /**
     * Helper function to save multipart file from a form object to a model object.
     *
     * @param form        form object
     * @param model       model object
     * @param formField   property name in the form object which holds a MultipartFile object
     * @param modelField  property name in the model object to be set with the resulting path
     * @param destination path/to/destination
     */
    public void saveFile(Object form, Object model,
                         String formField, String modelField, String deletedField, String destination) {
        try {
            Object data = PropertyUtils.getProperty(form, formField);
            MultipartFile file = (MultipartFile) data;

            if (file != null && !file.isEmpty()) {
                if (store(file, destination)) {
                    PropertyUtils.setProperty(model, modelField, getUrl(destination));
                }
            } else {
                Object deleted = PropertyUtils.getProperty(form, deletedField);
                if (Boolean.TRUE.equals(deleted)) {
                    PropertyUtils.setProperty(model, modelField, null);
                }
            }
        } catch (Exception ex) {
            log.error("Error saving file", ex);
            throw new RuntimeException(ex);
        }
    }

    /**
     * Downloads an object from the specified S3 bucket.
     *
     * @param objectKey the key of the object to download
     * @return the downloaded file
     * @throws IOException if an error occurs during download
     */
    public ResponseInputStream<GetObjectResponse> downloadObject(String folder, String objectKey) throws IOException {
        log.info("Downloading object {} from bucket {}", objectKey, bucketName);

        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(folder + objectKey)
                .build();

        return s3Client.getObject(getObjectRequest);
    }

    public List<String> scanFiles(String folder, String module) {
        String prefix = folder + module;
        List<String> processedFiles = new ArrayList<>();

        try {
            log.info("Scanning for {} files in {}/{}", module, bucketName, prefix);

            ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .build();

            ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);

            // Filter and sort S3 objects by last modified date
            List<S3Object> sortedObjects = listResponse.contents().stream()
                    .filter(obj -> obj.key().endsWith(".zip"))
                    .sorted(Comparator.comparing(S3Object::lastModified))
                    .toList();

            // Take only the oldest file (first in the sorted list)
            if (!sortedObjects.isEmpty()) {
                S3Object oldestFile = sortedObjects.get(0);
                String key = oldestFile.key();
                String fileName = key.substring(key.lastIndexOf('/') + 1);

                log.info("Found oldest Agent zip file: {}", key);
                processedFiles.add(fileName);
            }

            log.info("Processed {} zip files : {}", module, processedFiles.size());
        } catch (Exception e) {
            log.error("Error scanning or processing files", e);
        }

        return processedFiles;
    }

    public void moveFile(String source, String destination, String fileName) {
        String sourceKey = source + fileName;
        String destinationKey = destination + fileName;
        // Copy the file from initial to progress folder
        CopyObjectRequest copyRequest = CopyObjectRequest.builder()
                .sourceBucket(bucketName)
                .sourceKey(sourceKey)
                .destinationBucket(bucketName)
                .destinationKey(destinationKey)
                .build();

        s3Client.copyObject(copyRequest);

        // Delete the file from the initial folder
        DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(sourceKey)
                .build();

        s3Client.deleteObject(deleteRequest);
    }
}