package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.AgentProductive;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AgentProductiveRepository extends BaseRepository<AgentProductive, Long> {

    // Find by agent code
    AgentProductive findByAgentCode(String agentCode);

    // Find by leader code
    List<AgentProductive> findByLeaderCode(String leaderCode);

    // Find by branch code
    List<AgentProductive> findByBranchCode(String branchCode);

    // Find by year
    List<AgentProductive> findByYear(Integer year);

    // Find by type
    List<AgentProductive> findByType(String type);

    // Find by BDM
    List<AgentProductive> findByBdmCode(String bdmCode);

    // Find by HOS
    List<AgentProductive> findByHosCode(String hosCode);

    // Find agents who met or exceeded P13 target
    @Query("SELECT a FROM AgentProductive a WHERE a.p13 >= a.p13Target")
    List<AgentProductive> findAgentsWhoMetP13Target();

    // Find agents who met or exceeded APE target
    @Query("SELECT a FROM AgentProductive a WHERE a.netApe >= a.apeTarget")
    List<AgentProductive> findAgentsWhoMetApeTarget();

    // Find agents who met or exceeded case target
    @Query("SELECT a FROM AgentProductive a WHERE a.netCase >= a.caseTarget")
    List<AgentProductive> findAgentsWhoMetCaseTarget();

    // Find top performers by APE achievement percentage
    @Query("SELECT a FROM AgentProductive a WHERE a.apeTarget > 0 ORDER BY (a.netApe / a.apeTarget) DESC")
    List<AgentProductive> findTopPerformersByApeAchievement();

    // Find agents by APE range
    @Query("SELECT a FROM AgentProductive a WHERE a.netApe BETWEEN :minApe AND :maxApe")
    List<AgentProductive> findByNetApeBetween(@Param("minApe") Double minApe, @Param("maxApe") Double maxApe);

    // Find agents by achievement range
    @Query("SELECT a FROM AgentProductive a WHERE a.p13Target > 0 AND (a.p13 / a.p13Target * 100) BETWEEN :minPercentage AND :maxPercentage")
    List<AgentProductive> findByP13AchievementBetween(@Param("minPercentage") Double minPercentage, @Param("maxPercentage") Double maxPercentage);

    // Count agents by type
    Long countByType(String type);

    // Sum total NET_APE by branch
    @Query("SELECT SUM(a.netApe) FROM AgentProductive a WHERE a.branchCode = :branchCode")
    Double sumNetApeByBranch(@Param("branchCode") String branchCode);

    // Sum total NET_CASE by branch
    @Query("SELECT SUM(a.netCase) FROM AgentProductive a WHERE a.branchCode = :branchCode")
    Integer sumNetCaseByBranch(@Param("branchCode") String branchCode);

    // Calculate branch P13 achievement average
    @Query("SELECT AVG(a.p13 / a.p13Target * 100) FROM AgentProductive a WHERE a.branchCode = :branchCode AND a.p13Target > 0")
    Double calculateAverageP13AchievementByBranch(@Param("branchCode") String branchCode);

}
