package id.co.panindaiichilife.superapp.agent.model.projection;

import id.co.panindaiichilife.superapp.agent.enums.AgentStatus;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class AgentWithBranchInfo {

    private String agentCode;

    private String agentName;

    private String branchCode;

    private String branchName;

    private String branchCity;

    private String branchAddress;

    private String level;

    private String leaderCode;

    private String leaderName;

    private String picture;

    private AgentStatus status;

}
