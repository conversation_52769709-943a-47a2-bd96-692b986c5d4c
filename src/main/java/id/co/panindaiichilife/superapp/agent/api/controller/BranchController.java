package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccountDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.BranchDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BranchFilter;
import id.co.panindaiichilife.superapp.agent.service.BranchService;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;

@RestController("branchController")
@RequestMapping("/api/branch")
@Tag(name = "Branch", description = "API Branch")
@Slf4j
@RequiredArgsConstructor
public class BranchController {

    private final BranchService branchService;
    private final UserService userService;

    @Operation(summary = "Get Branch")
    @GetMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Branch', 'view')")
    public Page<BranchDto> getBranch(Principal principal, @ParameterObject @ModelAttribute("filter") BranchFilter filter,
                                     @ParameterObject @PageableDefault(sort = "branchName", direction = Sort.Direction.ASC) Pageable pageable) {

        AccountDto user = userService.findByUsername(principal.getName());
        if (null != user && null == filter.getChannel()) {
            filter.setChannel(user.getChannel());
        }

        return branchService.findAll(pageable, filter);
    }
}
