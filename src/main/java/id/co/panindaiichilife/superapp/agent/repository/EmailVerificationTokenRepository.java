package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.EmailVerificationToken;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;

import java.util.Optional;

public interface EmailVerificationTokenRepository extends BaseRepository<EmailVerificationToken, Long> {
    
    /**
     * Find a token by its value
     * @param token The token value
     * @return Optional containing the token if found, empty otherwise
     */
    Optional<EmailVerificationToken> findByToken(String token);
    
    /**
     * Find the most recent token for a recruitment
     * @param recruitment The recruitment
     * @return Optional containing the token if found, empty otherwise
     */
    Optional<EmailVerificationToken> findTopByRecruitmentOrderByCreatedAtDesc(TrxRecruitment recruitment);
    
    /**
     * Find all tokens for a recruitment
     * @param recruitment The recruitment
     * @return List of tokens
     */
    java.util.List<EmailVerificationToken> findByRecruitment(TrxRecruitment recruitment);
}
