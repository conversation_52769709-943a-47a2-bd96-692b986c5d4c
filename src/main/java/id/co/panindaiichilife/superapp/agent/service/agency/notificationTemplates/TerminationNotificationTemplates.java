package id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates;

public final class TerminationNotificationTemplates {
    public static final class Inbox {
        public static final String REQUESTER_REQUEST_APPROVED = "Pengajuan terminasi untuk agen %s dengan kode agen " +
                "%s telah disetujui.";
        public static final String REQUESTER_REQUEST_REJECTED = "Pengajuan terminasi untuk agen %s dengan kode agen " +
                "%s telah ditolak oleh %s dengan alasan %s.";
        public static final String REQUESTER_REQUEST_RETURNED = "Pengajuan terminasi untuk agen %s dengan kode agen " +
                "%s telah dikembalikan oleh %s dengan alasan %s, harap memperbaiki data pengajuan dan submit kembali.";
        public static final String REQUESTER_REQUEST_EXPIRED = "Pengajuan terminasi untuk agen %s dengan kode agen " +
                "%s kadaluarsa karena menunggu persetujuan dari %s.";
        public static final String REQUESTER_POLICY_TRANSFER_APPROVED = "Pengalihan polis dari agen %s dengan kode " +
                "agen %s telah diterima oleh %s dengan kode agen %s";
        public static final String REQUESTER_POLICY_TRANSFER_REJECTED = "Pengalihan polis dari agen %s dengan kode " +
                "agen %s telah ditolak oleh %s dengan kode agen %s";
        public static final String TERMINATION_TARGET_REQUEST_SUBMITTED_BY_LEADER = "Pengajuan terminasi atas diri " +
                "Anda telah diajukan oleh %s.";
        public static final String POLICY_TRANSFER_TARGET_ASSIGNED = "Anda telah dipilih sebagai penerima pengalihan " +
                "sejumlah %d polis dari agen %s dengan kode agen %s.";
    }

    public static final class Email {
        public static final class Keys {
            public static final String NEW_SUBMISSION_TO_CAS_REVIEWER =
                    "mail.termination.submission.to.cas_reviewer.template.id";
            public static final String TO_TERMINATION_TARGET_APPROVED_BY_CAS =
                    "mail.termination.approved_by_cas.to.target_agent.template.id";
            public static final String TO_BDM_ABDD_BDD_APPROVED_BY_CAS =
                    "mail.termination.approved_by_cas.to.bdm_abdd_bdd.template.id";
            public static final String TO_BDM_ABDD_BDD_REJECTED_BY_CAS =
                    "mail.termination.rejected_by_cas.to.bdm_abdd_bdd.template.id";
            public static final String TO_BDM_ABDD_BDD__POLICY_TRANSFER__APPROVED_BY_CAS =
                    "mail.termination.approved_by_cas.policy_transfer.to.bdm_abdd_bdd.template.id";
        }
    }
}
