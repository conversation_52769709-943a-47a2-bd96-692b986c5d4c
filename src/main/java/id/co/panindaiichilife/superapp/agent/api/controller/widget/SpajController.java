package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.SpajDto;
import id.co.panindaiichilife.superapp.agent.api.filter.SpajFilter;
import id.co.panindaiichilife.superapp.agent.service.widget.SpajService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.time.LocalDate;
import java.util.List;

@RestController("widgetSpajController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class SpajController {

    private final SpajService spajService;

    @Operation(summary = "Get widget Spaj Individual")
    @GetMapping(value = "spaj-individual")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.Spaj', 'view')")
    public List<SpajDto> getSpajIndividual(Principal principal,
                                           @ParameterObject @ModelAttribute("filter") SpajFilter filter) {

        // Set date range from filter or default to 1 year from now
        LocalDate now = LocalDate.now();
        if (filter.getStartDate() == null) {
            filter.setStartDate(now.minusYears(1));
        }
        // Use endDate from filter if provided, otherwise default to today
        if (filter.getEndDate() == null) {
            filter.setEndDate(now);
        }
        filter.setBitFilter(7);

        return spajService.getSpajList(principal.getName(), filter);
    }

    @Operation(summary = "Get widget Spaj Team")
    @GetMapping(value = "spaj-team")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.SpajTeam', 'view')")
    public List<SpajDto> getSpajTeam(Principal principal,
                                     @ParameterObject @ModelAttribute("filter") SpajFilter filter) {

        // Set date range from filter or default to 1 year from now
        LocalDate now = LocalDate.now();
        if (filter.getStartDate() == null) {
            filter.setStartDate(now.minusYears(1));
        }
        // Use endDate from filter if provided, otherwise default to today
        if (filter.getEndDate() == null) {
            filter.setEndDate(now);
        }
        filter.setBitFilter(6);

        return spajService.getSpajList(principal.getName(), filter);
    }

    @Operation(summary = "Get widget Spaj Individual with Pagination")
    @GetMapping(value = "spaj-individual/pageable")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.Spaj', 'view')")
    public Page<SpajDto> getSpajIndividualPageable(Principal principal,
                                                   @ParameterObject @ModelAttribute("filter") SpajFilter filter,
                                                   @ParameterObject @PageableDefault(sort = "entryDate", direction = Sort.Direction.DESC) Pageable pageable) {

        // Set date range from filter or default to 1 year from now
        LocalDate now = LocalDate.now();
        if (filter.getStartDate() == null) {
            filter.setStartDate(now.minusYears(1));
        }
        // Use endDate from filter if provided, otherwise default to today
        if (filter.getEndDate() == null) {
            filter.setEndDate(now);
        }
        filter.setBitFilter(7);

        return spajService.getSpajListPageable(principal.getName(), filter, pageable);
    }

    @Operation(summary = "Get widget Spaj Team with Pagination")
    @GetMapping(value = "spaj-team/pageable")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.SpajTeam', 'view')")
    public Page<SpajDto> getSpajTeamPageable(Principal principal,
                                             @ParameterObject @ModelAttribute("filter") SpajFilter filter,
                                             @ParameterObject @PageableDefault(sort = "entryDate", direction = Sort.Direction.DESC) Pageable pageable) {

        // Set date range from filter or default to 1 year from now
        LocalDate now = LocalDate.now();
        if (filter.getStartDate() == null) {
            filter.setStartDate(now.minusYears(1));
        }
        // Use endDate from filter if provided, otherwise default to today
        if (filter.getEndDate() == null) {
            filter.setEndDate(now);
        }
        filter.setBitFilter(6);

        return spajService.getSpajListPageable(principal.getName(), filter, pageable);
    }
}
