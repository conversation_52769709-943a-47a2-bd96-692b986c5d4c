package id.co.panindaiichilife.superapp.agent.api.dto.pdf;

import id.co.panindaiichilife.superapp.agent.enums.DocumentType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * DTO for generic document generation with custom variables.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenericDocumentDto {

    /**
     * The document type to generate.
     */
    @NotNull(message = "Document type is required")
    private DocumentType documentType;

    /**
     * Custom variables for the template.
     */
    private Map<String, Object> variables;

    /**
     * Agent name (optional, will be added to variables if provided).
     */
    private String agentName;

    /**
     * Agent code (optional, will be added to variables if provided).
     */
    private String agentCode;

    /**
     * Location (optional, will be added to variables if provided).
     */
    private String location;

    /**
     * Whether to include current date variables automatically.
     */
    @Builder.Default
    private boolean includeCurrentDate = true;

    /**
     * Create a builder for PKAJ-AGE document type.
     *
     * @return Builder configured for PKAJ-AGE
     */
    public static GenericDocumentDtoBuilder forPkajAge() {
        return GenericDocumentDto.builder().documentType(DocumentType.PKAJ_AGE);
    }

    /**
     * Create a builder for PMKAJ-AGE document type.
     *
     * @return Builder configured for PMKAJ-AGE
     */
    public static GenericDocumentDtoBuilder forPmkajAge() {
        return GenericDocumentDto.builder().documentType(DocumentType.PMKAJ_AGE);
    }

    /**
     * Create a builder for KODE-ETIK-AGE document type.
     *
     * @return Builder configured for KODE-ETIK-AGE
     */
    public static GenericDocumentDtoBuilder forKodeEtikAge() {
        return GenericDocumentDto.builder().documentType(DocumentType.KODE_ETIK_AGE);
    }

    /**
     * Create a builder for ANTI-TWISTING-AGE document type.
     *
     * @return Builder configured for ANTI-TWISTING-AGE
     */
    public static GenericDocumentDtoBuilder forAntiTwistingAge() {
        return GenericDocumentDto.builder().documentType(DocumentType.ANTI_TWISTING_AGE);
    }

    /**
     * Create a builder with agent information pre-filled.
     *
     * @param documentType The document type
     * @param agentName    Name of the agent
     * @param agentCode    Agent code
     * @param location     Location
     * @return Builder with agent information pre-filled
     */
    public static GenericDocumentDtoBuilder withAgentInfo(DocumentType documentType, String agentName, String agentCode, String location) {
        return GenericDocumentDto.builder()
                .documentType(documentType)
                .agentName(agentName)
                .agentCode(agentCode)
                .location(location);
    }

    /**
     * Convert DTO to variables map for template processing.
     *
     * @return Map of variables for template injection
     */
    public Map<String, Object> toVariablesMap() {
        Map<String, Object> result = new HashMap<>();

        // Add custom variables first
        if (variables != null) {
            result.putAll(variables);
        }

        // Add agent-specific variables if provided
        result.put("agentName", PdfDocumentUtils.getStringOrEmpty(agentName));
        result.put("agentCode", PdfDocumentUtils.getStringOrEmpty(agentCode));
        result.put("location", PdfDocumentUtils.getStringOrEmpty(location));

        // Add current date variables if requested
        if (includeCurrentDate) {
            java.time.LocalDate now = java.time.LocalDate.now();
            result.putIfAbsent("currentDate", now.format(java.time.format.DateTimeFormatter.ofPattern("dd MMMM yyyy")));
            result.putIfAbsent("currentYear", String.valueOf(now.getYear()));
            result.putIfAbsent("currentMonth", now.format(java.time.format.DateTimeFormatter.ofPattern("MMMM")));
            result.putIfAbsent("currentDay", String.valueOf(now.getDayOfMonth()));
            result.putIfAbsent("date", String.valueOf(now.getDayOfMonth()));
            result.putIfAbsent("month", now.format(java.time.format.DateTimeFormatter.ofPattern("MMMM")));
            result.putIfAbsent("year", String.valueOf(now.getYear()));
        }

        return result;
    }
}
