package id.co.panindaiichilife.superapp.agent.service.kafka;

import id.co.panindaiichilife.superapp.agent.config.kafka.KafkaConfig;
import id.co.panindaiichilife.superapp.agent.model.event.EditProfileEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class EditProfileConsumerService {

    private final EditProfileEventHandler editProfileEventHandler;

    /**
     * Listens for edit profile events on the edit profile topic
     *
     * @param event The edit profile event received from Kafka
     */
    @KafkaListener(
            topics = KafkaConfig.EDIT_PROFILE_TOPIC,
            groupId = KafkaConfig.EDIT_PROFILE_GROUP,
            containerFactory = "editProfileKafkaListenerContainerFactory")
    public void consumeEditProfileEvent(EditProfileEvent event) {
        log.info("Received edit profile event: {}", event);

        try {
            editProfileEventHandler.handleEditProfileEvent(event);
        } catch (Exception e) {
            log.error("Error processing edit profile event: {}", e.getMessage(), e);
        }
    }
}
