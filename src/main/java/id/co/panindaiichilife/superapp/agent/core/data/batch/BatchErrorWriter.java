package id.co.panindaiichilife.superapp.agent.core.data.batch;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.springframework.batch.item.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@Slf4j
public class BatchErrorWriter implements ItemStreamWriter<BatchError> {

    private String resourceUri;

    private String errorUri;

    private BufferedWriter writer;

    @Autowired
    private MessageSource messageSource;

    public BatchErrorWriter(String resourceUri) {
        this(resourceUri, ".log");
    }

    public BatchErrorWriter(String resourceUri, String suffix) {
        this.resourceUri = resourceUri;
        this.errorUri = resourceUri + suffix;
    }

    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        try {
            Resource resource = new UrlResource(errorUri);
            File file = resource.getFile();
            writer = new BufferedWriter(new FileWriter(file.getAbsolutePath()));
        } catch (MalformedURLException ex) {
            log.error("Failed to load resource {}", resourceUri);
            throw new RuntimeException(ex);
        } catch (IOException ex) {
            log.error("I/O error occured when attempting to open file: " + errorUri);
            throw new RuntimeException(ex);
        }
    }

    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {
        // No state updates are required for this error writer implementation
        // The writer maintains its state through the BufferedWriter instance
    }

    @Override
    public void close() throws ItemStreamException {
        try {
            writer.close();
        } catch (IOException ex) {
            log.error("I/O error occured when attempting to close file: " + errorUri);
            throw new RuntimeException(ex);
        }
    }

    @Override
    public void write(Chunk<? extends BatchError> chunk) throws Exception {
        for (BatchError batchError : chunk.getItems()) {
            Object item = batchError.getItem();
            Errors errors = batchError.getErrors();

            if (errors.hasErrors()) {
                writer.newLine();
                writer.write("Row ");
                if (item instanceof ItemCountAware) {
                    Object itemCount = PropertyUtils.getProperty(item, "itemCount");
                    writer.write("#" + itemCount);
                } else {
                    writer.write("???");
                }

                List<String> messages = new ArrayList<>();
                for (FieldError fe : errors.getFieldErrors()) {
                    writer.newLine();
                    writer.write("- " + fe.getField() + ": ");
                    writer.write(messageSource.getMessage(fe, Locale.getDefault()));
                }
            }

            writer.newLine();
        }

        writer.flush();
    }
}
