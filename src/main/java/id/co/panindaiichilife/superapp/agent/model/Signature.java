package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.SignaturePageType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;

@Entity
@Table(name = "signatures", uniqueConstraints = {
        @UniqueConstraint(name = "uk_signatures_signature_user", columnNames = {"id", "user_id"})
})
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@SQLDelete(sql = "UPDATE signatures SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class Signature extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "signatures_id_seq")
    @SequenceGenerator(name = "signatures_id_seq", sequenceName = "signatures_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Audited
    @Enumerated(EnumType.STRING)
    private Channel channel;

    @Audited
    private String documentType;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String signature;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String paraf;

    @Audited
    @Enumerated(EnumType.STRING)
    private SignaturePageType pageType;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;
}
