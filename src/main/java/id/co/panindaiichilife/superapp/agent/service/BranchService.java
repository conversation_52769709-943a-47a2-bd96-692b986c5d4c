package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.BranchDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BranchFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.BranchForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalBranchResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheEvictWithTTL;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.BranchStatus;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.Branch;
import id.co.panindaiichilife.superapp.agent.repository.BranchRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BranchService {

    private final BranchRepository branchRepository;
    private final PortalProvider portalProvider;

    public Page<BranchDto> findAll(Pageable pageable, BranchFilter filter) {
        Page<Branch> branchs = branchRepository.findAll(filter, pageable);
        return BaseDto.of(BranchDto.class, branchs, pageable);
    }

    public BranchDto findOne(Long id) {
        Branch data = branchRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(BranchDto.class, data);
    }


    @CacheableWithTTL(cacheName = "branchCityCache", ttl = 86400, db = 7)
    public List<String> getCity() {
        return branchRepository.findDistinctCity();
    }

    @Transactional
    @CacheEvictWithTTL(cacheName = "branchCityCache", db = 7)
    public BranchDto add(BranchForm branchForm) {
        Branch data = new Branch();
        BeanUtils.copyProperties(branchForm, data);
        branchRepository.save(data);

        return BaseDto.of(BranchDto.class, data);
    }


    @Transactional
    @CacheEvictWithTTL(cacheName = "branchCityCache", db = 7)
    public BranchDto update(Long id, BranchForm branchForm) {
        Branch data = branchRepository.findById(id).orElseThrow(NotFoundException::new);
        BeanUtils.copyProperties(branchForm, data);
        branchRepository.save(data);

        return BaseDto.of(BranchDto.class, data);
    }


    @CacheEvictWithTTL(cacheName = "branchCityCache", db = 7)
    public void delete(Long id) {
        branchRepository.deleteById(id);
    }

    @CacheEvictWithTTL(cacheName = "branchCityCache", db = 7)
    public void syncBranch() {

        //Sync Branch Agent
        Call<PortalBranchResponseDto> callBranchAgent = portalProvider.getBranch(DistributionCode.A.name());

        try {
            Response<PortalBranchResponseDto> response = callBranchAgent.execute();
            if (response.isSuccessful()) {
                PortalBranchResponseDto portalBranchResponseDto = response.body();
                List<PortalBranchResponseDto.MainBranchDto> branchAgent = portalBranchResponseDto.getMainBranches();
                for (PortalBranchResponseDto.MainBranchDto branch : branchAgent) {
                    Branch branchData = branchRepository.findByBranchCode(branch.getBranchCode()).orElse(null);
                    if (null == branchData) {
                        branchData = new Branch();
                    }
                    BeanUtils.copyProperties(branch, branchData);
                    branchData.setAddress(branch.getAddress1());
                    branchData.setSecondAddress(branch.getAddress2());
                    branchData.setThirdAddress(branch.getAddress3());
                    branchData.setChannel(Channel.AGE);
                    branchData.setAreaCode(branch.getBdmCode());
                    branchData.setAreaName(branch.getBdmRegion());
                    branchData.setRegionCode(branch.getBddCode());
                    branchData.setRegionName(branch.getBddRegion());
                    branchData.setSubRegionCode(branch.getAbddCode());
                    branchData.setSubRegionName(branch.getAbddRegion());
                    branchData.setHosCode(branch.getHosCode());
                    branchData.setHosName(branch.getHosRegion());
                    branchData.setIsActive(BranchStatus.valueOf(branch.getStatus()).equals(BranchStatus.A));
                    branchRepository.save(branchData);

                }
            }
        } catch (IOException e) {
            throw new InternalServerErrorException("Error occurred while sync branch agent");
        }

        //Sync Branch BAN
        Call<PortalBranchResponseDto> callBranchBan = portalProvider.getBranch(DistributionCode.L.name());

        try {
            Response<PortalBranchResponseDto> response = callBranchBan.execute();
            if (response.isSuccessful()) {
                PortalBranchResponseDto portalBranchResponseDto = response.body();
                List<PortalBranchResponseDto.MainBranchDto> branchBans = portalBranchResponseDto.getMainBranches();
                for (PortalBranchResponseDto.MainBranchDto branch : branchBans) {
                    for (PortalBranchResponseDto.SubBranchDto subBranch : branch.getSubBranches()) {
                        Branch branchData = branchRepository.findByBranchCode(subBranch.getBranchCode()).orElse(null);
                        if (null == branchData) {
                            branchData = new Branch();
                        }
                        BeanUtils.copyProperties(subBranch, branchData);
                        branchData.setParentBranchCode(branch.getBranchCode());
                        branchData.setParentBranchName(branch.getBranchName());
                        branchData.setAddress(subBranch.getAddress1());
                        branchData.setSecondAddress(subBranch.getAddress2());
                        branchData.setThirdAddress(subBranch.getAddress3());
                        branchData.setStaffCount(subBranch.getStaffCount());
                        branchData.setChannel(Channel.BAN);
                        branchData.setAreaCode(branch.getBdmCode());
                        branchData.setAreaName(branch.getBdmRegion());
                        branchData.setRegionCode(branch.getBddCode());
                        branchData.setRegionName(branch.getBddRegion());
                        branchData.setSubRegionCode(branch.getAbddCode());
                        branchData.setSubRegionName(branch.getAbddRegion());
                        branchData.setHosCode(branch.getHosCode());
                        branchData.setHosName(branch.getHosRegion());
                        branchData.setIsActive(BranchStatus.valueOf(subBranch.getStatus()).equals(BranchStatus.A));
                        branchRepository.save(branchData);
                    }
                }
            }
        } catch (IOException e) {
            throw new InternalServerErrorException("Error occurred while sync branch agent");
        }
    }
}
