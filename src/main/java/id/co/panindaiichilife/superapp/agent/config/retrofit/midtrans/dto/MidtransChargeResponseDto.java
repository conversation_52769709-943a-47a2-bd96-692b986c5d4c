package id.co.panindaiichilife.superapp.agent.config.retrofit.midtrans.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class MidtransChargeResponseDto {

    @SerializedName("status_code")
    @JsonProperty("status_code")
    private String statusCode;

    @SerializedName("status_message")
    @JsonProperty("status_message")
    private String statusMessage;

    @SerializedName("channel_response_code")
    @JsonProperty("channel_response_code")
    private String channelResponseCode;

    @SerializedName("channel_response_message")
    @JsonProperty("channel_response_message")
    private String channelResponseMessage;

    @SerializedName("transaction_id")
    @JsonProperty("transaction_id")
    private String transactionId;

    @SerializedName("order_id")
    @JsonProperty("order_id")
    private String orderId;

    @SerializedName("redirect_url")
    @JsonProperty("redirect_url")
    private String redirectUrl;

    @SerializedName("gross_amount")
    @JsonProperty("gross_amount")
    private String grossAmount;

    @SerializedName("currency")
    @JsonProperty("currency")
    private String currency;

    @SerializedName("payment_type")
    @JsonProperty("payment_type")
    private String paymentType;

    @SerializedName("transaction_time")
    @JsonProperty("transaction_time")
    private String transactionTime;

    @SerializedName("transaction_status")
    @JsonProperty("transaction_status")
    private String transactionStatus;

    @SerializedName("fraud_status")
    @JsonProperty("fraud_status")
    private String fraudStatus;

    @SerializedName("masked_card")
    @JsonProperty("masked_card")
    private String maskedCard;

    @SerializedName("bank")
    @JsonProperty("bank")
    private String bank;

    @SerializedName("eci")
    @JsonProperty("eci")
    private String eci;

    @SerializedName("card_type")
    @JsonProperty("card_type")
    private String cardType;

    @SerializedName("installment_term")
    @JsonProperty("installment_term")
    private String installmentTerm;

    @SerializedName("permata_va_number")
    @JsonProperty("permata_va_number")
    private String permataVaNumber;

    @SerializedName("va_numbers")
    @JsonProperty("va_numbers")
    private List<VaNumber> vaNumbers;

    @Data
    public static class VaNumber {
        @SerializedName("bank")
        @JsonProperty("bank")
        private String bank;

        @SerializedName("va_number")
        @JsonProperty("va_number")
        private String vaNumber;

    }
}
