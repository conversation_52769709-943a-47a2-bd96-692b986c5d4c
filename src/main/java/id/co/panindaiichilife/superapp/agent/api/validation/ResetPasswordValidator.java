package id.co.panindaiichilife.superapp.agent.api.validation;

import id.co.panindaiichilife.superapp.agent.api.form.ResetPasswordForm;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

@Component
@RequiredArgsConstructor
public class ResetPasswordValidator implements Validator {

    private static final String PASSWORD_PATTERN = "^(?=.*[A-Za-z])(?=.*\\d)(?=.*[@$!%*#?&])[A-Za-z\\d@$!%*#?&]{8,}$";

    private final EncryptionService encryptionService;

    @Override
    public boolean supports(Class<?> clazz) {
        return ResetPasswordForm.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        ResetPasswordForm form = (ResetPasswordForm) target;

        if (!errors.hasFieldErrors("password")) {
            String decryptedPassword = encryptionService.decrypt(form.getPassword());
            String decryptedConfirmPassword = encryptionService.decrypt(form.getPasswordConfirm());

            // Validate password criteria (min length, letters, numbers, special chars)
            if (!decryptedPassword.matches(PASSWORD_PATTERN)) {
                errors.rejectValue("password", "password.invalid",
                        "Password baru harus minimal 8 character, mengandung huruf, angka dan special character");
            }

            // Validate password confirmation matches
            if (!decryptedPassword.equals(decryptedConfirmPassword)) {
                errors.rejectValue("passwordConfirm", "password.mismatch",
                        "Konfirmasi password baru harus sama dengan password baru");
            }
        }
    }
}