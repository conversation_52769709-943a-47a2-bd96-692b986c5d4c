package id.co.panindaiichilife.superapp.agent.api.dto.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalHeaderDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.BranchDto;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.*;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
public class TrxRejoinApplicationDto extends BaseDto<TrxRejoinApplication> {
    private Long id;

    private String agentName;

    private String agentCode;

    private String agentPicture;

    private String previousLevel;

    private String proposedLevel;

    private String previousLeaderCode;

    private Instant terminationDate;

    private String submitterCode;

    private String submitterName;

    private ApprovalStatus approvalStatus;

    private BranchDto branch;

    private String remarks;

    private TrxStatus status;

    private ApprovalHeaderDto approvalHeader;

    private TrxType trxType;

    private Instant createdAt;

    private Instant updatedAt;

    private String uploadedKtpPath;

    private String idNumber;

    private String bankAccountName;

    private String bankAccountNumber;

    private LocalDate dob;

    private String resulValidationKtp;

    private ValidationKtpStatus validationKtpStatus;

    private String resultValidationHirarki;

    private ValidationHirarkiStatus validationHirarkiStatus;

    private ValidationAmlStatus validationAmlStatus;

    private ValidationBlacklistStatus validationBlacklistStatus;

    private String resultValidationBankAccount;

    private ValidationBankAccountStatus validationBankAccountStatus;

    private ValidationAdministrationAgentStatus validationAdministrationAgentStatus;

    private ValidationLicenseAajiStatus validationLicenseAajiStatus;

    private ValidationLicenseAasiStatus validationLicenseAasiStatus;

    @Override
    public void copy(TrxRejoinApplication entity) {
        super.copy(entity);

        if (entity.getApprovalHeader() != null) {
            this.approvalHeader = BaseDto.of(ApprovalHeaderDto.class, entity.getApprovalHeader());
        }

        if (entity.getSubmittedBy() != null) {
            this.submitterCode = entity.getSubmittedBy().getUsername();
            this.submitterName = entity.getSubmittedBy().getName();
        }

        Agent targetAgent = entity.getAgent();
        if (targetAgent != null) {
            this.agentName = targetAgent.getAgentName();
            this.agentCode = targetAgent.getAgentCode();
            this.agentPicture = targetAgent.getPhoto();

            if (this.agentPicture == null && targetAgent.getUser() != null) {
                this.agentPicture = targetAgent.getUser().getPicture();
            }

            this.previousLevel = targetAgent.getLevel();

            if (targetAgent.getLeader() != null) {
                this.previousLeaderCode = targetAgent.getLeader().getLeaderCode();
            }
        }

        if (entity.getLastTermination() != null) {
            this.terminationDate = entity.getLastTermination().getEffectiveDate();
        }

        if (entity.getBranch() != null) {
            this.branch = BaseDto.of(BranchDto.class, entity.getBranch());
        }
    }
}
