package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.config;

import id.co.panindaiichilife.superapp.agent.core.retrofit.GsonContext;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.core.retrofit.HttpClientContext;
import id.co.panindaiichilife.superapp.agent.core.retrofit.LoggingContext;
import id.co.panindaiichilife.superapp.agent.core.retrofit.SuccessErrorInterceptor;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.concurrent.TimeUnit;

@Configuration
public class PortalConfig {

  @Value("${portal.base-url}")
  private String baseUrl = "";

  @Value("${provider.timeout:60}")
  private Integer timeout;

  @Value("${ssl.validation.disabled:false}")
  private Boolean sslValidationDisabled;

  @Bean("portalRetrofit")
  public Retrofit retrofit() {
    return new Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(httpClient())
            .addConverterFactory(GsonConverterFactory.create(GsonContext.getInstance()))
            .build();
  }

  @Bean
  public PortalProvider portalProvider() {
    return retrofit().create(PortalProvider.class);
  }

  @Bean("portalAuthorizationInterceptor")
  public Interceptor authorizationInterceptor() {
    return new PortalAuthorizationInterceptor();
  }

  @Bean("portalLoggingInterceptor")
  public Interceptor loggingInterceptor() {
    return LoggingContext.createLoggingInterceptor();
  }

  @Bean("portalSuccessErrorInterceptor")
  public Interceptor successErrorInterceptor() {
    return new SuccessErrorInterceptor();
  }

  @Bean("portalHttpClient")
  public OkHttpClient httpClient() {
    OkHttpClient.Builder builder = new OkHttpClient.Builder();
    HttpClientContext.configureSsl(builder, sslValidationDisabled);
    builder.addInterceptor(authorizationInterceptor());
    builder.addInterceptor(successErrorInterceptor());
    builder.addInterceptor(loggingInterceptor());
    builder.connectTimeout(timeout, TimeUnit.SECONDS);
    builder.readTimeout(timeout, TimeUnit.SECONDS);
    return builder.build();
  }
}
