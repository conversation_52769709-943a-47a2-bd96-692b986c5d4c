package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.Signature;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.SignaturePageType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface SignatureRepository extends BaseRepository<Signature, Long> {

    /**
     * Find signature by user
     */
    List<Signature> findByUser(User user);

    /**
     * Find signature by user and channel
     */
    Optional<Signature> findByUserAndChannel(User user, Channel channel);

    /**
     * Find signature by user and document type
     */
    Optional<Signature> findByUserAndDocumentType(User user, String documentType);

    /**
     * Find signature by user, channel and document type
     */
    Optional<Signature> findByUserAndChannelAndDocumentType(User user, Channel channel, String documentType);

    /**
     * Find signature by user and page type
     */
    List<Signature> findByUserAndPageType(User user, SignaturePageType pageType);

    /**
     * Find signatures by channel
     */
    List<Signature> findByChannel(Channel channel);

    /**
     * Find signatures by document type
     */
    List<Signature> findByDocumentType(String documentType);

    /**
     * Find signatures by page type
     */
    List<Signature> findByPageType(SignaturePageType pageType);

    /**
     * Check if user has signature for specific channel and document type
     */
    @Query("SELECT COUNT(s) > 0 FROM Signature s WHERE s.user = :user AND s.channel = :channel AND s.documentType = :documentType")
    boolean existsByUserAndChannelAndDocumentType(@Param("user") User user, 
                                                  @Param("channel") Channel channel, 
                                                  @Param("documentType") String documentType);

    /**
     * Find all signatures by user ID
     */
    @Query("SELECT s FROM Signature s WHERE s.user.id = :userId")
    List<Signature> findByUserId(@Param("userId") Long userId);
}
