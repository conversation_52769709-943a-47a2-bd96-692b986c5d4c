package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.DeviceDto;
import id.co.panindaiichilife.superapp.agent.api.form.DeviceForm;
import id.co.panindaiichilife.superapp.agent.service.DeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;

@RestController("deviceController")
@RequestMapping("/api/device")
@Tag(name = "Device", description = "API Device")
@Slf4j
@RequiredArgsConstructor
public class DeviceController {

    private final DeviceService deviceService;

    @Operation(summary = "List current user device")
    @GetMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Device', 'view')")
    public List<DeviceDto> list(Principal principal) {
        return deviceService.findByUser(principal.getName());
    }

    @Operation(summary = "register or update devices")
    @PostMapping(value = "register")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Device', 'register')")
    public DeviceDto register(Principal principal, @Valid @RequestBody DeviceForm deviceForm) {
        return deviceService.register(principal.getName(), deviceForm);
    }

    @Operation(summary = "revoke device by device id")
    @PostMapping(value = "revoke/{deviceId}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Device', 'revoke')")
    public void revoke(Principal principal, @PathVariable String deviceId) {
        deviceService.revoke(principal.getName(), deviceId);
    }
}
