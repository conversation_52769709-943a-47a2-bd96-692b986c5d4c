package id.co.panindaiichilife.superapp.agent.api.dto.agency;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * DTO for productivity recruitment response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductivityRecruitmentDto {
    
    /**
     * Summary data for the requested period
     */
    private ProductivitySummaryDto summary;
    
    /**
     * Chart data grouped by time period
     */
    private List<ProductivityChartDataDto> chartData;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductivitySummaryDto {
        /**
         * Total number of approved recruitments
         */
        private Long totalRecruitments;
        
        /**
         * Number of "rekrut berkode agen" (agent code but license not active)
         */
        private Long rekrutBerkodeAgen;
        
        /**
         * Number of "rekrut baru berlisensi" (agent code and license active)
         */
        private Long rekrutBaruBerlisensi;
        
        /**
         * Period description (e.g., "January 2024", "2024")
         */
        private String period;
        
        /**
         * Percentage of rekrut berkode agen
         */
        private Double percentageRekrutBerkodeAgen;
        
        /**
         * Percentage of rekrut baru berlisensi
         */
        private Double percentageRekrutBaruBerlisensi;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductivityChartDataDto {
        /**
         * Date or period label
         */
        private String label;
        
        /**
         * Date value for sorting and filtering
         */
        private LocalDate date;
        
        /**
         * Number of rekrut berkode agen for this period
         */
        private Long rekrutBerkodeAgen;
        
        /**
         * Number of rekrut baru berlisensi for this period
         */
        private Long rekrutBaruBerlisensi;
        
        /**
         * Total recruitments for this period
         */
        private Long total;
    }
    

}
