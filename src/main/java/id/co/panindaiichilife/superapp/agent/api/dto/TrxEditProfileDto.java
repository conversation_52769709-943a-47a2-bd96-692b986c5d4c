package id.co.panindaiichilife.superapp.agent.api.dto;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalHeaderDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.SimpleAgentDto;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxEditProfile;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
public class TrxEditProfileDto extends BaseDto<TrxEditProfile> {

    private Long id;

    private SimpleAgentDto agent;

    private ApprovalHeaderDto approvalHeader;

    private String data;

    private String oldData;

    private ApprovalStatus approvalStatus;

    private String detailApproval;

    private Instant createdAt;

    private Instant updatedAt;

    @Override
    public void copy(TrxEditProfile data) {
        super.copy(data);
        agent = BaseDto.of(SimpleAgentDto.class, data.getAgent());
        approvalHeader = BaseDto.of(ApprovalHeaderDto.class, data.getApprovalHeader());
    }
}
