package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.AgentProductionPerAgentImportDto;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.AgentProductionPerAgent;
import id.co.panindaiichilife.superapp.agent.repository.AgentProductionPerAgentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class AgentProductionPerAgentImportProcessor implements ItemProcessor<AgentProductionPerAgentImportDto, AgentProductionPerAgent> {

    private final AgentProductionPerAgentRepository agentProductionPerAgentRepository;

    @Override
    public AgentProductionPerAgent process(AgentProductionPerAgentImportDto item) {
        return findOrCreateAgentProductionPerAgent(item);
    }

    private AgentProductionPerAgent findOrCreateAgentProductionPerAgent(AgentProductionPerAgentImportDto item) {
        AgentProductionPerAgent agentProductionPerAgent = agentProductionPerAgentRepository.findByAgentCodeAndYearAndMonthAndTypeAndBranchCodeAndLeaderCode(item.getAgentCode(), item.getYear(), item.getMonth(), item.getType(), item.getBranchCode(), item.getLeaderCode())
                .orElse(new AgentProductionPerAgent());

        // Copy properties
        BeanUtils.copyProperties(item, agentProductionPerAgent);
        agentProductionPerAgent.setDistributionCode(DistributionCode.valueOf(item.getDistributionCode()));
        return agentProductionPerAgent;
    }
}