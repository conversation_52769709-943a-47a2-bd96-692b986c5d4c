package id.co.panindaiichilife.superapp.agent.core.service;

import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.IOException;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

@Service
@Slf4j
@RequiredArgsConstructor
public class MailService {

    private final SpringTemplateEngine templateEngine;
    @Value("${mail.from}")
    private String from;
    @Value("${sendgrid.api.key}")
    private String sendGridApiKey;

    /**
     * Resolves the Thymeleaf template with the provided data.
     *
     * @param template Thymeleaf template file
     * @param data     Map of data objects
     * @return Resolved template content as a string
     */
    private String resolveMailTemplate(String template, Map<String, Object> data) {
        Context context = new Context();
        for (Entry<String, Object> entry : data.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }
        return templateEngine.process(template, context);
    }

    /**
     * Creates an email object using a given template populated with data objects.
     *
     * @param template Thymeleaf template file
     * @param data     Map of data objects
     * @param to       List of recipient emails
     * @return SendGrid Mail object
     */
    private Mail createBaseEmail(String template, Map<String, Object> data, String... to) throws MessagingException {
        Email fromEmail = new Email(from);
        Mail mail = new Mail();

        // Set the from email
        mail.setFrom(fromEmail);

        // Set the template ID
        mail.setTemplateId(template);

        // Add recipients and dynamic data
        Personalization personalization = new Personalization();
        for (String recipient : to) {
            personalization.addTo(new Email(recipient));
        }

        // Add dynamic data to the template
        for (Entry<String, Object> entry : data.entrySet()) {
            personalization.addDynamicTemplateData(entry.getKey(), entry.getValue());
        }

        mail.addPersonalization(personalization);

        return mail;
    }

    /**
     * Creates an email object using a given template populated with data objects.
     *
     * @param template Thymeleaf template file
     * @param data     Map of data objects
     * @param to       List of recipient emails
     * @return SendGrid Mail object
     */
    public Mail createEmail(String template, Map<String, Object> data, String... to) throws MessagingException {
        return createBaseEmail(template, data, to);
    }

    /**
     * Creates an email object using a given template populated with data objects.
     *
     * @param template        Thymeleaf template file
     * @param data            Map of data objects
     * @param attachments  List of attachments to be added in the email
     * @param to              List of email recipients
     * @return SendGrid Mail object
     * @throws MessagingException
     */
    public Mail createEmailWithAttachments(String template, Map<String, Object> data, Set<Attachments> attachments,
                            String... to) throws MessagingException {

        Mail baseMail = createBaseEmail(template, data, to);
        if (attachments != null && !attachments.isEmpty()) {
            attachments.forEach(baseMail::addAttachments);
        }
        return baseMail;
    }

    /**
     * Sends an email asynchronously using SendGrid.
     *
     * @param mail SendGrid Mail object
     */
    @Async
    public void sendEmail(Mail mail) {
        SendGrid sg = new SendGrid(sendGridApiKey);
        Request request = new Request();
        try {
            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());
            log.debug("Request {}", request.getBody());
            Response response = sg.api(request);
            log.debug("Response {}", response);

            if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
                log.info("Email sent successfully: {}", response.getBody());
            } else {
                log.error("Failed to send email: {}", response.getBody());
            }
        } catch (IOException ex) {
            log.error("Error sending email: {}", ex.getMessage(), ex);
        }
    }
}
