package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PortalGetPolicyServicingResponseDto {
    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("policyServicing")
    @JsonProperty("policyServicing")
    private List<PolicyServicingItemDto> policyServicing;

    @SerializedName("totalRecords")
    @JsonProperty("totalRecords")
    private Long totalRecords;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PolicyServicingItemDto {
        @SerializedName("policyHolderName")
        @JsonProperty("policyHolderName")
        private String policyHolderName;

        @SerializedName("polisStatus")
        @JsonProperty("polisStatus")
        private String polisStatus;

        @SerializedName("polisCode")
        @JsonProperty("polisCode")
        private String polisCode;
    }
}
