package id.co.panindaiichilife.superapp.agent.api.dto;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.Inbox;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class InboxDto extends BaseDto<Inbox> {
    private Long id;

    private UserInboxDto user;

    private TrxType trxType;

    private InboxType inboxType;

    private Long trxId;

    private Long approvalId;

    private String title;

    private String body;

    private String footer;

    private Boolean isArchived = Boolean.FALSE;

    private Boolean isRead = Boolean.FALSE;

    private Boolean deleted = Boolean.FALSE;

    private Object detailData;

    private Instant createdAt;

    private Instant updatedAt;

    private LocalDateTime deletedAt;

    @Override
    public void copy(Inbox data) {
        super.copy(data);
        user = BaseDto.of(UserInboxDto.class, data.getUser());
    }
}
