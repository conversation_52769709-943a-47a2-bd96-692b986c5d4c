package id.co.panindaiichilife.superapp.agent.config;

import id.co.panindaiichilife.superapp.agent.core.http.UnauthorizedException;
import id.co.panindaiichilife.superapp.agent.service.DeviceService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@RequiredArgsConstructor
public class DeviceStatusInterceptor implements HandlerInterceptor {

    private final DeviceService deviceService;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {

        String deviceId = request.getHeader("device_id");
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            if (deviceId != null) {
                // Get the current authentication
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

                // Get principal name (username) if authentication exists
                String username = null;
                if (authentication != null) {
                    username = authentication.getName();
                }

                boolean isActive = deviceService.isDeviceActive(deviceId, username);

                if (!isActive) {
                    throw new UnauthorizedException("Perangkat anda sudah tidak aktif. Silahkan login ulang kembali");
                }
            }
        }
        return true;
    }
}