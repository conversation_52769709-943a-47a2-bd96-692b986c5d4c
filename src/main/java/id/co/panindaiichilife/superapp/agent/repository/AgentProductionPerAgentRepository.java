package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.*;
import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.AgentProductionPerAgent;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface AgentProductionPerAgentRepository extends BaseRepository<AgentProductionPerAgent, Long> {

    // Find by agent code
    AgentProductionPerAgent findByAgentCode(String agentCode);

    // Find by leader code
    List<AgentProductionPerAgent> findByLeaderCode(String leaderCode);

    // Find by agent type
    List<AgentProductionPerAgent> findByType(String type);

    // Find by branch code
    List<AgentProductionPerAgent> findByBranchCode(String branchCode);

    // Find by year and month
    List<AgentProductionPerAgent> findByYearAndMonth(Integer year, Integer month);

    // Find by year and month
    Optional<AgentProductionPerAgent> findByAgentCodeAndYearAndMonthAndTypeAndBranchCodeAndLeaderCode(String agentCode, Integer year, Integer month, String type, String branchCode, String leaderCode);

    // Find by BDM code
    List<AgentProductionPerAgent> findByBdmCode(String bdmCode);

    // Find by HOS code
    List<AgentProductionPerAgent> findByHosCode(String hosCode);

    // Find top producers by NET_APE
    @Query("SELECT a FROM AgentProductionPerAgent a ORDER BY a.netApe DESC")
    List<AgentProductionPerAgent> findTopProducersByNetApe();

    // Find top producers by NET_CASE
    @Query("SELECT a FROM AgentProductionPerAgent a ORDER BY a.netCase DESC")
    List<AgentProductionPerAgent> findTopProducersByNetCase();

    // Find top producers by NET_API
    @Query("SELECT a FROM AgentProductionPerAgent a ORDER BY a.netApi DESC")
    List<AgentProductionPerAgent> findTopProducersByNetApi();

    // Find agents by NET_APE range
    List<AgentProductionPerAgent> findByNetApeBetween(Double minNetApe, Double maxNetApe);

    // Find agents by NET_API achievement percentage
    @Query("SELECT a FROM AgentProductionPerAgent a WHERE a.netApiTarget > 0 AND (a.netApi / a.netApiTarget * 100) BETWEEN :minPercentage AND :maxPercentage")
    List<AgentProductionPerAgent> findByNetApiAchievementBetween(@Param("minPercentage") Double minPercentage, @Param("maxPercentage") Double maxPercentage);

    // Sum NET_APE by branch code
    @Query("SELECT SUM(a.netApe) FROM AgentProductionPerAgent a WHERE a.branchCode = :branchCode")
    Double sumNetApeByBranch(@Param("branchCode") String branchCode);

    // Sum NET_CASE by branch code
    @Query("SELECT SUM(a.netCase) FROM AgentProductionPerAgent a WHERE a.branchCode = :branchCode")
    Integer sumNetCaseByBranch(@Param("branchCode") String branchCode);

    // Sum NET_API by branch code
    @Query("SELECT SUM(a.netApi) FROM AgentProductionPerAgent a WHERE a.branchCode = :branchCode")
    Double sumNetApiByBranch(@Param("branchCode") String branchCode);

    // Count agents by type and branch
    @Query("SELECT COUNT(a) FROM AgentProductionPerAgent a WHERE a.type = :type AND a.branchCode = :branchCode")
    Long countAgentsByTypeAndBranch(@Param("type") String type, @Param("branchCode") String branchCode);

    // Get average NET_APE by agent type
    @Query("SELECT AVG(a.netApe) FROM AgentProductionPerAgent a WHERE a.type = :type")
    Double getAverageNetApeByType(@Param("type") String type);

    // Get average NET_CASE by agent type
    @Query("SELECT AVG(a.netCase) FROM AgentProductionPerAgent a WHERE a.type = :type")
    Double getAverageNetCaseByType(@Param("type") String type);

    // Get average NET_API by agent type
    @Query("SELECT AVG(a.netApi) FROM AgentProductionPerAgent a WHERE a.type = :type")
    Double getAverageNetApiByType(@Param("type") String type);

    // Find agents who achieved API target
    @Query("SELECT a FROM AgentProductionPerAgent a WHERE a.netApi >= a.netApiTarget")
    List<AgentProductionPerAgent> findAgentsWhoAchievedApiTarget();

    // Find by HOS and year
    List<AgentProductionPerAgent> findByHosCodeAndYear(String hosCode, Integer year);

    // Find all production records for a specific agent, filtered by month and year
    List<AgentProductionPerAgent> findByAgentCodeAndMonthAndYear(String agentCode, Integer month, Integer year);

    // Monthly query with type as parameter
    @Query("SELECT SUM(p.netApe) FROM AgentProductionPerAgent p WHERE p.agentCode = :agentCode " +
            "AND p.month = :month AND p.year = :year AND p.type = :type")
    Double findMonthlyNetApeByType(
            @Param("agentCode") String agentCode,
            @Param("month") Integer month,
            @Param("year") Integer year,
            @Param("type") String type);

    // Yearly query with type as parameter
    @Query("SELECT SUM(p.netApe) FROM AgentProductionPerAgent p WHERE p.agentCode = :agentCode " +
            "AND p.year = :year AND p.type = :type")
    Double findYearlyNetApeByType(
            @Param("agentCode") String agentCode,
            @Param("year") Integer year,
            @Param("type") String type);

    // Monthly query with type as parameter
    @Query("SELECT SUM(p.netApe) FROM AgentProductionPerAgent p WHERE p.mainBranchCode in :branchCodes " +
            "AND p.month = :month AND p.year = :year AND p.type = :type")
    Double findMonthlyNetApeBranchByType(
            @Param("branchCodes") List<String> branchCodes,
            @Param("month") Integer month,
            @Param("year") Integer year,
            @Param("type") String type);

    // Yearly query with type as parameter
    @Query("SELECT SUM(p.netApe) FROM AgentProductionPerAgent p WHERE p.mainBranchCode in :branchCodes " +
            "AND p.year = :year AND p.type = :type")
    Double findYearlyNetApBranchByType(
            @Param("branchCodes") List<String> branchCodes,
            @Param("year") Integer year,
            @Param("type") String type);

    @Modifying
    @Transactional
    @Query("DELETE FROM AgentProductionPerAgent a WHERE a.year = :year AND a.month = :month")
    void deleteByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

    long countByYearAndMonth(Integer year, Integer month);

    // Aggregate netApe by main_branch_code
    @Query("SELECT new id.co.panindaiichilife.superapp.agent.api.dto.widget.BranchProductionSummaryDto(a.mainBranchCode, SUM(a.netApe)) FROM AgentProductionPerAgent a WHERE a.mainBranchCode in :branchCodes AND (:month IS NULL OR a.month = :month) AND a.year = :year AND a.type='Group' GROUP BY a.mainBranchCode ORDER BY SUM(a.netApe) DESC")
    List<BranchProductionSummaryDto> aggregateNetApeByMainBranchCode(@Param("branchCodes") List<String> branchCodes, @Param("month") Integer month, @Param("year") Integer year);

    // Aggregate netApe by main_branch_code
    @Query("SELECT new id.co.panindaiichilife.superapp.agent.api.dto.widget.BdmProductionSummaryDto(a.area, a.territory, a.abddRegion, SUM(a.netApe)) FROM AgentProductionPerAgent a WHERE a.mainBranchCode in :branchCodes AND (:month IS NULL OR a.month = :month) AND a.year = :year AND a.type='Group' GROUP BY a.area, a.territory, a.abddRegion ORDER BY SUM(a.netApe) DESC")
    List<BdmProductionSummaryDto> aggregateBdmNetApeByMainBranchCode(@Param("branchCodes") List<String> branchCodes, @Param("month") Integer month, @Param("year") Integer year);

    // Aggregate netApe by main_branch_code
    @Query("SELECT new id.co.panindaiichilife.superapp.agent.api.dto.widget.AbddProductionSummaryDto(a.area, a.territory, a.abddRegion, SUM(a.netApe)) FROM AgentProductionPerAgent a WHERE a.mainBranchCode in :branchCodes AND (:month IS NULL OR a.month = :month) AND a.year = :year AND a.type='Group' GROUP BY a.area, a.territory, a.abddRegion ORDER BY SUM(a.netApe) DESC")
    List<AbddProductionSummaryDto> aggregateAbddNetApeByMainBranchCode(@Param("branchCodes") List<String> branchCodes, @Param("month") Integer month, @Param("year") Integer year);

    // Aggregate netApe by main_branch_code
    @Query("SELECT new id.co.panindaiichilife.superapp.agent.api.dto.widget.BddProductionSummaryDto(a.bddRegion,a.territory,SUM(a.netApe)) FROM AgentProductionPerAgent a WHERE a.mainBranchCode in :branchCodes AND (:month IS NULL OR a.month = :month) AND a.year = :year AND a.type='Group' GROUP BY  a.bddRegion, a.territory ORDER BY SUM(a.netApe) DESC")
    List<BddProductionSummaryDto> aggregateBddNetApeByMainBranchCode(@Param("branchCodes") List<String> branchCodes, @Param("month") Integer month, @Param("year") Integer year);

    // Aggregate netApe by main_branch_code
    @Query("SELECT new id.co.panindaiichilife.superapp.agent.api.dto.widget.HosProductionSummaryDto(a.territory, SUM(a.netApe)) FROM AgentProductionPerAgent a WHERE a.mainBranchCode in :branchCodes AND (:month IS NULL OR a.month = :month) AND a.year = :year AND a.type='Group' GROUP BY a.territory ORDER BY SUM(a.netApe) DESC")
    List<HosProductionSummaryDto> aggregateHosNetApeByMainBranchCode(@Param("branchCodes") List<String> branchCodes, @Param("month") Integer month, @Param("year") Integer year);

}
