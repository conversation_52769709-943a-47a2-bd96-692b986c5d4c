package id.co.panindaiichilife.superapp.agent.config;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * Custom authentication token that includes remember me functionality
 */
public class RememberMeUsernamePasswordAuthenticationToken extends UsernamePasswordAuthenticationToken {

    private final Boolean rememberMe;

    public RememberMeUsernamePasswordAuthenticationToken(Object principal, Object credentials, Boolean rememberMe) {
        super(principal, credentials);
        this.rememberMe = rememberMe;
    }

    public RememberMeUsernamePasswordAuthenticationToken(Object principal, Object credentials, 
                                                        Collection<? extends GrantedAuthority> authorities, Boolean rememberMe) {
        super(principal, credentials, authorities);
        this.rememberMe = rememberMe;
    }

    public Boolean getRememberMe() {
        return rememberMe;
    }

    public boolean isRememberMe() {
        return Boolean.TRUE.equals(rememberMe);
    }
}
