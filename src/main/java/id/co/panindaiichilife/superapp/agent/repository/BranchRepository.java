package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.Branch;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface BranchRepository extends BaseRepository<Branch, Long> {
    Optional<Branch> findByBranchCode(String branchCode);

    @Query(nativeQuery = true, value = "select distinct city from agent.branches where city != '' order by city asc")
    List<String> findDistinctCity();
}
