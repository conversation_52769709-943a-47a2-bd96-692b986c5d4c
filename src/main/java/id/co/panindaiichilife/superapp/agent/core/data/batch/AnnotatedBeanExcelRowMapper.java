package id.co.panindaiichilife.superapp.agent.core.data.batch;

import id.co.panindaiichilife.superapp.agent.core.support.AnnotatedBeanUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.util.NumberUtils;
import org.springframework.util.ReflectionUtils;
import org.thymeleaf.util.StringUtils;

import java.lang.reflect.Field;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.Temporal;
import java.util.*;

public class AnnotatedBeanExcelRowMapper<T> implements ExcelRowMapper<T> {

    private Class<T> type;

    private Map<String, Field> fieldMapping;

    private DataFormatter formatter;

    public AnnotatedBeanExcelRowMapper(Class<T> type) {
        this.type = type;
        fieldMapping = AnnotatedBeanUtils.getImportedFields(type);
    }

    @Override
    public T mapRow(Row row, List<String> headers) {
        Map<String, Object> mappingResult = new HashMap<>();

        for (int i = 0; i < headers.size(); i++) {
            String fieldName = headers.get(i);
            if (StringUtils.isEmpty(fieldName)) {
                continue;
            }

            if (!fieldMapping.containsKey(fieldName)) {
                continue;
            }

            Field field = fieldMapping.get(fieldName);
            Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            Object value = resolveCellValue(field, cell);
            mappingResult.put(field.getName(), value);
        }

        //construct bean
        try {
            T bean = type.newInstance();
            BeanWrapper beanWrapper = new BeanWrapperImpl(bean);
            beanWrapper.setPropertyValues(mappingResult);
            return bean;
        } catch (ReflectiveOperationException ex) {
            ReflectionUtils.handleReflectionException(ex);
            return null;
        }
    }

    private DataFormatter getFormatter() {
        if (formatter == null) {
            formatter = new DataFormatter();
        }

        return formatter;
    }

    private Object resolveCellValue(Field field, Cell cell) {
        Class fieldType = field.getType();
        if (String.class.isAssignableFrom(fieldType)) {
            if (cell.getCellType() == CellType.STRING) {
                return cell.getStringCellValue();
            } else {
                return getFormatter().formatCellValue(cell);
            }
        } else if (Number.class.isAssignableFrom(fieldType)) {
            return parseNumber(field, field.getType().asSubclass(Number.class), cell);
        } else if (Temporal.class.isAssignableFrom(fieldType)) {
            return parseTemporal(field, field.getType().asSubclass(Temporal.class), cell);
        } else {
            throw new UnsupportedOperationException("Unsupported field type: " + fieldType);
        }
    }

    private <T extends Number> T parseNumber(Field field, Class<T> fieldType, Cell cell) {
        try {
            Double value = cell.getNumericCellValue();
            return NumberUtils.convertNumberToTargetClass(value, fieldType);
        } catch (IllegalStateException | NumberFormatException ex) {
            throw new IllegalArgumentException("Unable to parse value", ex);
        }
    }

    @SuppressWarnings("unchecked")
    private <T extends Temporal> T parseTemporal(Field field, Class<T> fieldType, Cell cell) {
        try {
            Date date = cell.getDateCellValue();
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);

            if (LocalDate.class.isAssignableFrom(fieldType)) {
                int year = cal.get(Calendar.YEAR);
                int month = cal.get(Calendar.MONTH);
                int day = cal.get(Calendar.DAY_OF_MONTH);
                return (T) LocalDate.of(year, month + 1, day);
            } else if (LocalTime.class.isAssignableFrom(fieldType)) {
                int hour = cal.get(Calendar.HOUR_OF_DAY);
                int minute = cal.get(Calendar.MINUTE);
                int second = cal.get(Calendar.SECOND);
                return (T) LocalTime.of(hour, minute, second);
            } else if (LocalDateTime.class.isAssignableFrom(fieldType)) {
                int year = cal.get(Calendar.YEAR);
                int month = cal.get(Calendar.MONTH);
                int day = cal.get(Calendar.DAY_OF_MONTH);
                int hour = cal.get(Calendar.HOUR_OF_DAY);
                int minute = cal.get(Calendar.MINUTE);
                int second = cal.get(Calendar.SECOND);
                return (T) LocalDateTime.of(year, month + 1, day, hour, minute, second);
            } else if (Instant.class.isAssignableFrom(fieldType)) {
                return (T) date.toInstant();
            } else {
                throw new UnsupportedOperationException("Unsupported temporal type: " + fieldType);
            }
        } catch (IllegalStateException | NumberFormatException ex) {
            throw new IllegalArgumentException("Unable to parse value", ex);
        }
    }
}
