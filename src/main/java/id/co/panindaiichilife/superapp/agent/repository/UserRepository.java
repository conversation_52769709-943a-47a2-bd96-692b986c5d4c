package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.User;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface UserRepository extends BaseRepository<User, Long> {

    Optional<User> findByUsername(String username);

    Boolean existsByUsernameAndIsAgentTrue(String username);

    @Query(value = "SELECT u.* FROM users u " +
            "JOIN user_role ur ON u.id = ur.id_user " +
            "JOIN roles r ON ur.id_role = r.id " +
            "WHERE r.code = :roleCode", nativeQuery = true)
    List<User> findUsersByRoleCode(@Param("roleCode") String roleCode);

    @Query(value = "SELECT DISTINCT u.* FROM users u " +
            "JOIN user_role ur ON u.id = ur.id_user " +
            "JOIN roles r ON ur.id_role = r.id " +
            "WHERE r.code IN :roleCodes", nativeQuery = true)
    List<User> findUsersByRoleCodes(@Param("roleCodes") Set<String> roleCodes);
}
