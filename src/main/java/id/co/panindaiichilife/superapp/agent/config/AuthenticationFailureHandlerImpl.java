package id.co.panindaiichilife.superapp.agent.config;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.util.Assert;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;

public class AuthenticationFailureHandlerImpl implements AuthenticationFailureHandler {
  
  protected final Log logger = LogFactory.getLog(getClass());
  
  private String defaultFailureUrl;
  private boolean forwardToDestination = false;
  private boolean allowSessionCreation = true;
  private RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();
  
  public AuthenticationFailureHandlerImpl() {
    setDefaultFailureUrl("/login?error=");
  }

  public AuthenticationFailureHandlerImpl(String defaultFailureUrl) {
      setDefaultFailureUrl(defaultFailureUrl);
  }

  @Override
  public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
      AuthenticationException exception) throws IOException, ServletException {
    if (defaultFailureUrl == null) {
        logger.debug("No failure URL set, sending 401 Unauthorized error");
  
        response.sendError(HttpStatus.UNAUTHORIZED.value(),
            HttpStatus.UNAUTHORIZED.getReasonPhrase());
    }
    else {
        saveException(request, exception);
        defaultFailureUrl = "/login?error="+exception.getMessage();
        if (forwardToDestination) {
            logger.debug("Forwarding to " + defaultFailureUrl);
  
            request.getRequestDispatcher(defaultFailureUrl)
                    .forward(request, response);
        }
        else {
            logger.debug("Redirecting to " + defaultFailureUrl);
            redirectStrategy.sendRedirect(request, response, defaultFailureUrl);
        }
    }
  }
  
  protected final void saveException(HttpServletRequest request,
      AuthenticationException exception) {
    if (forwardToDestination) {
        request.setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION, exception);
    }
    else {
        HttpSession session = request.getSession(false);
  
        if (session != null || allowSessionCreation) {
            request.getSession().setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION,
                    exception);
        }
    }
  }
  
  public void setDefaultFailureUrl(String defaultFailureUrl) {
      Assert.isTrue(UrlUtils.isValidRedirectUrl(defaultFailureUrl),
              () -> "'" + defaultFailureUrl + "' is not a valid redirect URL");
      this.defaultFailureUrl = defaultFailureUrl;
  }
  
  protected boolean isUseForward() {
      return forwardToDestination;
  }
  
  /**
   * If set to <tt>true</tt>, performs a forward to the failure destination URL instead
   * of a redirect. Defaults to <tt>false</tt>.
   */
  public void setUseForward(boolean forwardToDestination) {
      this.forwardToDestination = forwardToDestination;
  }
  
  /**
   * Allows overriding of the behaviour when redirecting to a target URL.
   */
  public void setRedirectStrategy(RedirectStrategy redirectStrategy) {
      this.redirectStrategy = redirectStrategy;
  }
  
  protected RedirectStrategy getRedirectStrategy() {
      return redirectStrategy;
  }
  
  protected boolean isAllowSessionCreation() {
      return allowSessionCreation;
  }
  
  public void setAllowSessionCreation(boolean allowSessionCreation) {
      this.allowSessionCreation = allowSessionCreation;
  }

}
