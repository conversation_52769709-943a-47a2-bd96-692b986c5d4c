package id.co.panindaiichilife.superapp.agent.service.agency;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.service.agency.validator.RecruitmentValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for handling recruitment validations
 * Orchestrates multiple validators and runs them asynchronously
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecruitmentValidationService {

    private final List<RecruitmentValidator> validators;

    /**
     * Runs all applicable validators asynchronously for a recruitment
     *
     * @param form   The recruitment form
     * @param entity The recruitment entity to validate
     * @return The updated entity with validation results
     */
    public TrxRecruitment validateRecruitment(TrxRecruitmentForm form, TrxRecruitment entity) {
        if (entity == null || entity.getId() == null) {
            log.warn("Cannot validate null entity or entity without ID");
            return entity;
        }

        log.info("Starting validation for recruitment ID: {}", entity.getId());

        // Filter validators that can be applied to this recruitment
        List<RecruitmentValidator> applicableValidators = validators.stream()
                .filter(validator -> {
                    try {
                        return validator.canValidate(form, entity);
                    } catch (Exception e) {
                        log.error("Error checking if validator {} can validate recruitment {}: {}",
                                validator.getValidatorName(), entity.getId(), e.getMessage());
                        return false;
                    }
                })
                .toList();

        log.info("Running {} validators for recruitment ID: {}", applicableValidators.size(), entity.getId());

        if (applicableValidators.isEmpty()) {
            log.warn("No applicable validators found for recruitment ID: {}", entity.getId());
            return entity;
        }

        // Store original state to prevent corruption
        TrxStatus originalStatus = entity.getTrxStatus();
        ApprovalStatus originalApprovalStatus = entity.getApprovalStatus();

        try {
            // Run all validators asynchronously
            List<CompletableFuture<TrxRecruitment>> futures = applicableValidators.stream()
                    .map(validator -> {
                        try {
                            return validator.validate(form, entity);
                        } catch (Exception e) {
                            log.error("Error starting validation for validator {}: {}",
                                    validator.getValidatorName(), e.getMessage());
                            // Return a completed future with the original entity
                            return CompletableFuture.completedFuture(entity);
                        }
                    })
                    .toList();

            // Wait for all validations to complete with timeout
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            try {
                // Wait for all validations to complete with a reasonable timeout
                allFutures.join();
                log.info("All validations completed for recruitment ID: {}", entity.getId());
            } catch (Exception joinException) {
                log.error("Some validations failed for recruitment ID: {}, but continuing: {}",
                        entity.getId(), joinException.getMessage());

                // Restore original state to prevent corruption
                entity.setTrxStatus(originalStatus);
                if (originalApprovalStatus != null) {
                    entity.setApprovalStatus(originalApprovalStatus);
                }
                // Don't re-throw - allow the process to continue even if some validations failed
            }

            // Ensure critical fields are not corrupted
            if (entity.getTrxStatus() == null) {
                entity.setTrxStatus(originalStatus);
                log.warn("TrxStatus was null after validation, restored to: {}", originalStatus);
            }

            // The entity has been updated by successful validators
            return entity;
        } catch (Exception e) {
            log.error("Error during validation process for recruitment ID: {}: {}", entity.getId(), e.getMessage(), e);

            // Restore original state to prevent corruption
            entity.setTrxStatus(originalStatus);
            if (originalApprovalStatus != null) {
                entity.setApprovalStatus(originalApprovalStatus);
            }

            // Return the original entity unchanged to prevent transaction rollback
            return entity;
        }
    }
}
