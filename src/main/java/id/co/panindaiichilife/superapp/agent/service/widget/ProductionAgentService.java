package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.*;
import id.co.panindaiichilife.superapp.agent.api.filter.AgentProductionGroupFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.AgentProductionPerAgentFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.AgentProductionPerPolicyFilter;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.model.*;
import id.co.panindaiichilife.superapp.agent.repository.AgentProductionPerAgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.AgentProductionPerPolicyRepository;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.AgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductionAgentService {

    private static final String TYPE_AGENT = "Agent";
    private static final String TYPE_LEADER = "Leader";
    private static final String TYPE_GROUP = "Group";

    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    private final AgentService agentService;

    private final AgentProductionPerAgentRepository agentProductionPerAgentRepository;

    private final AgentProductionPerPolicyRepository agentProductionPerPolicyRepository;

    /**
     * Get monthly production summary
     */
    @CacheableWithTTL(cacheName = "productionAgentMonthlyCache",
            key = "#username + ':' + " +
                    "#month + ':' + " +
                    "#year",
            ttl = 1200, db = 7)
    public AgentProductionSummaryDto getMonthlyProductionSummary(String username, Integer month, Integer year) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElse(null);

        // If month and year are not provided, use current month and year
        if (month == null || year == null) {
            LocalDate now = LocalDate.now();
            month = month != null ? month : now.getMonthValue();
            year = year != null ? year : now.getYear();
        }

        // Get monthly NET APE values for each level using the type parameter
        Double individuNetApe = 0.0;
        Double teamNetApe = 0.0;
        Double groupNetApe = 0.0;
        Double branchNetApe = 0.0;
        Double areaNetApe = 0.0;

        // Lists for BDM, ABDD, BDD, and HOS data
        List<AgentProductionSummaryDto.AreaNetApeDto> bdmList = new ArrayList<>();
        List<AgentProductionSummaryDto.AreaNetApeDto> abddList = new ArrayList<>();
        List<AgentProductionSummaryDto.AreaNetApeDto> bddList = new ArrayList<>();
        List<AgentProductionSummaryDto.AreaNetApeDto> hosList = new ArrayList<>();

        if (null != agent) {
            String agentCode = agent.getAgentCode();
            // Trim -D suffix from agentCode if present
            agentCode = AgentCodeUtil.trimDSuffix(agentCode);
            individuNetApe = agentProductionPerAgentRepository.findMonthlyNetApeByType(agentCode, month, year, TYPE_AGENT);
            teamNetApe = agentProductionPerAgentRepository.findMonthlyNetApeByType(agentCode, month, year, TYPE_LEADER);
            groupNetApe = agentProductionPerAgentRepository.findMonthlyNetApeByType(agentCode, month, year, TYPE_GROUP);
        } else if (user.getBranches() != null && !user.getBranches().isEmpty()) {
            List<String> branchCodes = user.getBranches().stream()
                    .map(Branch::getBranchCode)
                    .collect(Collectors.toList());

            individuNetApe = agentProductionPerAgentRepository.findMonthlyNetApeBranchByType(branchCodes, month, year, TYPE_AGENT);
            teamNetApe = agentProductionPerAgentRepository.findMonthlyNetApeBranchByType(branchCodes, month, year, TYPE_LEADER);
            groupNetApe = agentProductionPerAgentRepository.findMonthlyNetApeBranchByType(branchCodes, month, year, TYPE_GROUP);
            branchNetApe = agentProductionPerAgentRepository.findMonthlyNetApeBranchByType(branchCodes, month, year, TYPE_GROUP);
            areaNetApe = agentProductionPerAgentRepository.findMonthlyNetApeBranchByType(branchCodes, month, year, TYPE_GROUP);

            // BDM: Group by area
            List<BdmProductionSummaryDto> bdmSummaries = agentProductionPerAgentRepository.aggregateBdmNetApeByMainBranchCode(branchCodes, month, year);
            if (!bdmSummaries.isEmpty()) {
                // Group by area and sum netApe values
                Map<String, Double> areaNetApeMap = bdmSummaries.stream()
                        .collect(Collectors.groupingBy(BdmProductionSummaryDto::getArea,
                                Collectors.summingDouble(BdmProductionSummaryDto::getNetApe)));

                // Convert to AreaNetApeDto list
                bdmList = areaNetApeMap.entrySet().stream()
                        .map(entry -> AgentProductionSummaryDto.AreaNetApeDto.builder()
                                .area(entry.getKey())
                                .total(entry.getValue())
                                .build())
                        .collect(Collectors.toList());
            }

            // ABDD: Group by area
            List<AbddProductionSummaryDto> abddSummaries = agentProductionPerAgentRepository.aggregateAbddNetApeByMainBranchCode(branchCodes, month, year);
            if (!abddSummaries.isEmpty()) {
                // Group by area and sum netApe values
                Map<String, Double> areaNetApeMap = abddSummaries.stream()
                        .collect(Collectors.groupingBy(AbddProductionSummaryDto::getArea,
                                Collectors.summingDouble(AbddProductionSummaryDto::getNetApe)));

                // Convert to AreaNetApeDto list
                abddList = areaNetApeMap.entrySet().stream()
                        .map(entry -> AgentProductionSummaryDto.AreaNetApeDto.builder()
                                .area(entry.getKey())
                                .total(entry.getValue())
                                .build())
                        .collect(Collectors.toList());
            }

            // BDD: Group by bddRegion
            List<BddProductionSummaryDto> bddSummaries = agentProductionPerAgentRepository.aggregateBddNetApeByMainBranchCode(branchCodes, month, year);
            if (!bddSummaries.isEmpty()) {
                // Group by region and sum netApe values
                Map<String, Double> regionNetApeMap = bddSummaries.stream()
                        .collect(Collectors.groupingBy(BddProductionSummaryDto::getRegion,
                                Collectors.summingDouble(BddProductionSummaryDto::getNetApe)));

                // Convert to AreaNetApeDto list
                bddList = regionNetApeMap.entrySet().stream()
                        .map(entry -> AgentProductionSummaryDto.AreaNetApeDto.builder()
                                .area(entry.getKey())
                                .total(entry.getValue())
                                .build())
                        .collect(Collectors.toList());
            }

            // HOS: Group by territory
            List<HosProductionSummaryDto> hosSummaries = agentProductionPerAgentRepository.aggregateHosNetApeByMainBranchCode(branchCodes, month, year);
            if (!hosSummaries.isEmpty()) {
                // Group by territory and sum netApe values
                Map<String, Double> territoryNetApeMap = hosSummaries.stream()
                        .collect(Collectors.groupingBy(HosProductionSummaryDto::getTerritory,
                                Collectors.summingDouble(HosProductionSummaryDto::getNetApe)));

                // Convert to AreaNetApeDto list
                hosList = territoryNetApeMap.entrySet().stream()
                        .map(entry -> AgentProductionSummaryDto.AreaNetApeDto.builder()
                                .area(entry.getKey())
                                .total(entry.getValue())
                                .build())
                        .collect(Collectors.toList());
            }
        }

        // Handle null values (default to zero)
        individuNetApe = individuNetApe != null ? individuNetApe : 0.0;
        teamNetApe = teamNetApe != null ? teamNetApe : 0.0;
        groupNetApe = groupNetApe != null ? groupNetApe : 0.0;
        branchNetApe = branchNetApe != null ? branchNetApe : 0.0;
        areaNetApe = areaNetApe != null ? areaNetApe : 0.0;

        // Create and return the DTO with monthly data
        AgentProductionSummaryDto.NetApeDto netApeDTO = AgentProductionSummaryDto.NetApeDto.builder()
                .individu(individuNetApe)
                .team(teamNetApe)
                .group(groupNetApe)
                .branch(branchNetApe)
                .area(areaNetApe)
                .bdm(bdmList)
                .abdd(abddList)
                .bdd(bddList)
                .hos(hosList)
                .build();

        return AgentProductionSummaryDto.builder()
                .month(month)
                .year(year)
                .netApe(netApeDTO)
                .build();
    }

    /**
     * Get yearly production summary
     */

    @CacheableWithTTL(cacheName = "productionAgentYearlyCache",
            key = "#username + ':' + #year",
            ttl = 1200, db = 7)
    public AgentProductionSummaryDto getYearlyProductionSummary(String username, Integer year) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElse(null);

        // If year is not provided, use current year
        if (year == null) {
            year = LocalDate.now().getYear();
        }

        // Get yearly NET APE values for each level using the type parameter
        Double individuNetApe = 0.0;
        Double teamNetApe = 0.0;
        Double groupNetApe = 0.0;
        Double branchNetApe = 0.0;
        Double areaNetApe = 0.0;

        // Lists for BDM, ABDD, BDD, and HOS data
        List<AgentProductionSummaryDto.AreaNetApeDto> bdmList = new ArrayList<>();
        List<AgentProductionSummaryDto.AreaNetApeDto> abddList = new ArrayList<>();
        List<AgentProductionSummaryDto.AreaNetApeDto> bddList = new ArrayList<>();
        List<AgentProductionSummaryDto.AreaNetApeDto> hosList = new ArrayList<>();

        if (null != agent) {
            String agentCode = agent.getAgentCode();
            // Trim -D suffix from agentCode if present
            agentCode = AgentCodeUtil.trimDSuffix(agentCode);
            individuNetApe = agentProductionPerAgentRepository.findYearlyNetApeByType(agentCode, year, TYPE_AGENT);
            teamNetApe = agentProductionPerAgentRepository.findYearlyNetApeByType(agentCode, year, TYPE_LEADER);
            groupNetApe = agentProductionPerAgentRepository.findYearlyNetApeByType(agentCode, year, TYPE_GROUP);
        } else if (user.getBranches() != null && !user.getBranches().isEmpty()) {
            List<String> branchCodes = user.getBranches().stream()
                    .map(Branch::getBranchCode)
                    .collect(Collectors.toList());

            individuNetApe = agentProductionPerAgentRepository.findYearlyNetApBranchByType(branchCodes, year, TYPE_AGENT);
            teamNetApe = agentProductionPerAgentRepository.findYearlyNetApBranchByType(branchCodes, year, TYPE_LEADER);
            groupNetApe = agentProductionPerAgentRepository.findYearlyNetApBranchByType(branchCodes, year, TYPE_GROUP);
            branchNetApe = agentProductionPerAgentRepository.findYearlyNetApBranchByType(branchCodes, year, TYPE_GROUP);
            areaNetApe = agentProductionPerAgentRepository.findYearlyNetApBranchByType(branchCodes, year, TYPE_GROUP);

            // BDM: Group by area
            List<BdmProductionSummaryDto> bdmSummaries = agentProductionPerAgentRepository.aggregateBdmNetApeByMainBranchCode(branchCodes, null, year);
            if (!bdmSummaries.isEmpty()) {
                // Group by area and sum netApe values
                Map<String, Double> areaNetApeMap = bdmSummaries.stream()
                        .collect(Collectors.groupingBy(BdmProductionSummaryDto::getArea,
                                Collectors.summingDouble(BdmProductionSummaryDto::getNetApe)));

                // Convert to AreaNetApeDto list
                bdmList = areaNetApeMap.entrySet().stream()
                        .map(entry -> AgentProductionSummaryDto.AreaNetApeDto.builder()
                                .area(entry.getKey())
                                .total(entry.getValue())
                                .build())
                        .collect(Collectors.toList());
            }

            // ABDD: Group by area
            List<AbddProductionSummaryDto> abddSummaries = agentProductionPerAgentRepository.aggregateAbddNetApeByMainBranchCode(branchCodes, null, year);
            if (!abddSummaries.isEmpty()) {
                // Group by area and sum netApe values
                Map<String, Double> areaNetApeMap = abddSummaries.stream()
                        .collect(Collectors.groupingBy(AbddProductionSummaryDto::getArea,
                                Collectors.summingDouble(AbddProductionSummaryDto::getNetApe)));

                // Convert to AreaNetApeDto list
                abddList = areaNetApeMap.entrySet().stream()
                        .map(entry -> AgentProductionSummaryDto.AreaNetApeDto.builder()
                                .area(entry.getKey())
                                .total(entry.getValue())
                                .build())
                        .collect(Collectors.toList());
            }

            // BDD: Group by bddRegion
            List<BddProductionSummaryDto> bddSummaries = agentProductionPerAgentRepository.aggregateBddNetApeByMainBranchCode(branchCodes, null, year);
            if (!bddSummaries.isEmpty()) {
                // Group by region and sum netApe values
                Map<String, Double> regionNetApeMap = bddSummaries.stream()
                        .collect(Collectors.groupingBy(BddProductionSummaryDto::getRegion,
                                Collectors.summingDouble(BddProductionSummaryDto::getNetApe)));

                // Convert to AreaNetApeDto list
                bddList = regionNetApeMap.entrySet().stream()
                        .map(entry -> AgentProductionSummaryDto.AreaNetApeDto.builder()
                                .area(entry.getKey())
                                .total(entry.getValue())
                                .build())
                        .collect(Collectors.toList());
            }

            // HOS: Group by territory
            List<HosProductionSummaryDto> hosSummaries = agentProductionPerAgentRepository.aggregateHosNetApeByMainBranchCode(branchCodes, null, year);
            if (!hosSummaries.isEmpty()) {
                // Group by territory and sum netApe values
                Map<String, Double> territoryNetApeMap = hosSummaries.stream()
                        .collect(Collectors.groupingBy(HosProductionSummaryDto::getTerritory,
                                Collectors.summingDouble(HosProductionSummaryDto::getNetApe)));

                // Convert to AreaNetApeDto list
                hosList = territoryNetApeMap.entrySet().stream()
                        .map(entry -> AgentProductionSummaryDto.AreaNetApeDto.builder()
                                .area(entry.getKey())
                                .total(entry.getValue())
                                .build())
                        .collect(Collectors.toList());
            }
        }

        // Handle null values (default to zero)
        individuNetApe = individuNetApe != null ? individuNetApe : 0.0;
        teamNetApe = teamNetApe != null ? teamNetApe : 0.0;
        groupNetApe = groupNetApe != null ? groupNetApe : 0.0;
        branchNetApe = branchNetApe != null ? branchNetApe : 0.0;
        areaNetApe = areaNetApe != null ? areaNetApe : 0.0;

        // Create and return the DTO with yearly data (month is null)
        AgentProductionSummaryDto.NetApeDto netApeDTO = AgentProductionSummaryDto.NetApeDto.builder()
                .individu(individuNetApe)
                .team(teamNetApe)
                .group(groupNetApe)
                .branch(branchNetApe)
                .area(areaNetApe)
                .bdm(bdmList)
                .abdd(abddList)
                .bdd(bddList)
                .hos(hosList)
                .build();

        return AgentProductionSummaryDto.builder()
                .month(null)
                .year(year)
                .netApe(netApeDTO)
                .build();
    }

    public List<AgentProductionDto> getMyProduction(String username, AgentProductionPerAgentFilter filter) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        if (StringUtils.isBlank(filter.getAgentCode())) {
            Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);
            filter.setAgentCode(agent.getAgentCode());
        }
        List<AgentProductionPerAgent> agentProductionPerAgents = agentProductionPerAgentRepository.findAll(filter);
        List<AgentProductionDto> result = BaseDto.of(AgentProductionDto.class, agentProductionPerAgents);

        // Populate agent name, level, and photo for each AgentProductionDto
        result.forEach(dto -> {
            if (dto.getAgentCode() != null) {
                agentRepository.findTopByAgentCode(dto.getAgentCode()).ifPresent(agentData -> {
                    dto.setAgentName(agentData.getAgentName());
                    dto.setAgentLevel(agentData.getLevel());
                    dto.setAgentPhoto(agentData.getPhoto());
                });
            }
        });

        return result;
    }

    public Page<AgentProductionDto> getTeamProduction(String username, AgentProductionPerAgentFilter filter, Pageable pageable) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);
        if (StringUtils.isBlank(filter.getLeaderCode())) {
            filter.setLeaderCode(agent.getAgentCode());
        }

        Page<AgentProductionPerAgent> agentProductionPerAgents = agentProductionPerAgentRepository.findAll(filter, pageable);
        Page<AgentProductionDto> result = BaseDto.of(AgentProductionDto.class, agentProductionPerAgents, pageable);

        // Populate agent name, level, and photo for each AgentProductionDto
        result.getContent().forEach(dto -> {
            if (dto.getAgentCode() != null) {
                agentRepository.findTopByAgentCode(dto.getAgentCode()).ifPresent(agentData -> {
                    dto.setAgentName(agentData.getAgentName());
                    dto.setAgentLevel(agentData.getLevel());
                    dto.setAgentPhoto(agentData.getPhoto());
                });
            }
        });

        return result;
    }

    public Page<AgentProductionDto> getAreaProduction(String username, AgentProductionPerAgentFilter filter, Pageable pageable) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        Page<AgentProductionPerAgent> agentProductionPerAgents = agentProductionPerAgentRepository.findAll(filter, pageable);
        Page<AgentProductionDto> result = BaseDto.of(AgentProductionDto.class, agentProductionPerAgents, pageable);

        // Populate agent name, level, and photo for each AgentProductionDto
        result.getContent().forEach(dto -> {
            if (dto.getAgentCode() != null) {
                agentRepository.findTopByAgentCode(dto.getAgentCode()).ifPresent(agentData -> {
                    dto.setAgentName(agentData.getAgentName());
                    dto.setAgentLevel(agentData.getLevel());
                    dto.setAgentPhoto(agentData.getPhoto());
                });
            }
        });

        return result;
    }

    public Page<DetailAgentProductionDto> getDetailProduction(String username, AgentProductionPerPolicyFilter filter, Pageable pageable) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        if (StringUtils.isBlank(filter.getAgentCode())) {
            Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);
            filter.setAgentCode(agent.getAgentCode());
        }

        Page<AgentProductionPerPolicy> agentProductionPerPolicies = agentProductionPerPolicyRepository.findAll(filter, pageable);
        Page<DetailAgentProductionDto> result = BaseDto.of(DetailAgentProductionDto.class, agentProductionPerPolicies, pageable);
        // Populate agent name, level, and photo for each AgentProductionDto
        result.getContent().forEach(dto -> {
            if (dto.getAgentCode() != null) {
                agentRepository.findTopByAgentCode(dto.getAgentCode()).ifPresent(agentData -> {
                    dto.setAgentName(agentData.getAgentName());
                    dto.setAgentLevel(agentData.getLevel());
                    dto.setAgentPhoto(agentData.getPhoto());
                });
            }
        });
        return result;
    }

    /**
     * Get group production data filtered by month, year, type, and main branch codes
     *
     * @param username The username of the current user
     * @param filter   Filter containing month, year, type, and main branch codes
     * @param pageable Pagination information
     * @return Page of AgentProductionDto
     */
    public Page<AgentProductionDto> getGroupProduction(String username, AgentProductionGroupFilter filter, Pageable pageable) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        // Set type to Group if not specified
        if (StringUtils.isBlank(filter.getType())) {
            filter.setType(TYPE_GROUP);
        }

        // Get branch codes from user's branches if available
        if (user.getBranches() != null && !user.getBranches().isEmpty()) {
            List<String> branchCodes = user.getBranches().stream()
                    .map(Branch::getBranchCode)
                    .collect(Collectors.toList());

            // Set the main branch codes in the filter
            filter.setMainBranchCode(branchCodes);
        }

        Page<AgentProductionPerAgent> agentProductionPerAgents = agentProductionPerAgentRepository.findAll(filter, pageable);
        Page<AgentProductionDto> result = BaseDto.of(AgentProductionDto.class, agentProductionPerAgents, pageable);

        // Populate agent name, level, and photo for each AgentProductionDto
        result.getContent().forEach(dto -> {
            if (dto.getAgentCode() != null) {
                agentRepository.findTopByAgentCode(dto.getAgentCode()).ifPresent(agentData -> {
                    dto.setAgentName(agentData.getAgentName());
                    dto.setAgentLevel(agentData.getLevel());
                    dto.setAgentPhoto(agentData.getPhoto());
                });
            }
        });

        return result;
    }

    /**
     * Get monthly production summaries for all months in the current year
     *
     * @param username The username of the current user
     * @param year     The year to get summaries for (defaults to current year if null)
     * @return List of AgentProductionSummaryDto for all months in the specified year
     */
    @CacheableWithTTL(cacheName = "productionAgentAllMonthlyCache",
            key = "#username + ':' + " +
                    "#year + ':'",
            ttl = 1200, db = 7)
    public List<AgentProductionSummaryDto> getAllMonthlyProductionSummaries(String username, Integer year) {
        // If year is not provided, use current year
        if (year == null) {
            year = LocalDate.now().getYear();
        }

        List<AgentProductionSummaryDto> monthlySummaries = new ArrayList<>();

        // Get summaries for all 12 months
        for (int month = 1; month <= 12; month++) {
            AgentProductionSummaryDto monthlySummary = getMonthlyProductionSummary(username, month, year);
            monthlySummaries.add(monthlySummary);
        }

        return monthlySummaries;
    }

    /**
     * Get yearly production summaries for the last 5 years
     *
     * @param username The username of the current user
     * @return List of AgentProductionSummaryDto for the last 5 years
     */
    @CacheableWithTTL(cacheName = "productionAgentLast5YearCache",
            key = "#username",
            ttl = 1200, db = 7)
    public List<AgentProductionSummaryDto> getLastFiveYearsProductionSummaries(String username) {
        int currentYear = LocalDate.now().getYear();
        List<AgentProductionSummaryDto> yearlySummaries = new ArrayList<>();

        // Get summaries for the last 5 years
        for (int i = 0; i < 5; i++) {
            int year = currentYear - i;
            AgentProductionSummaryDto yearlySummary = getYearlyProductionSummary(username, year);
            yearlySummaries.add(yearlySummary);
        }

        return yearlySummaries;
    }

    /**
     * Get production summary aggregated by main branch code
     *
     * @param username The username of the current user
     * @param month    The month to get data for (defaults to current month if null)
     * @param year     The year to get data for (defaults to current year if null)
     * @return List of BranchProductionSummaryDto with netApe aggregated by main_branch_code
     */
    @CacheableWithTTL(cacheName = "productionAgentMainBranchCache",
            key = "#username + ':' + " +
                    "#month + ':' + " +
                    "#year",
            ttl = 1200, db = 7)
    public List<BranchProductionSummaryDto> getProductionByMainBranchCode(String username, Integer month, Integer year) {
        // Validate user
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        if (user.getBranches() != null && !user.getBranches().isEmpty()) {
            List<String> branchCodes = user.getBranches().stream()
                    .map(Branch::getBranchCode)
                    .collect(Collectors.toList());

            // Get aggregated netApe by main_branch_code
            return agentProductionPerAgentRepository.aggregateNetApeByMainBranchCode(branchCodes, month, year);
        }

        return Collections.emptyList();
    }

    @CacheableWithTTL(cacheName = "productionAgentBdmByMainBranchCache",
            key = "#username + ':' + " +
                    "#month + ':' + " +
                    "#year",
            ttl = 1200, db = 7)
    public List<BdmProductionSummaryDto> getProductionBdmByMainBranchCode(String username, Integer month, Integer year) {
        // Validate user
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        if (user.getBranches() != null && !user.getBranches().isEmpty()) {
            List<String> branchCodes = user.getBranches().stream()
                    .map(Branch::getBranchCode)
                    .collect(Collectors.toList());

            // Get aggregated netApe by main_branch_code
            return agentProductionPerAgentRepository.aggregateBdmNetApeByMainBranchCode(branchCodes, month, year);
        }

        return Collections.emptyList();
    }

    @CacheableWithTTL(cacheName = "productionAgentAbddByMainBranchCache",
            key = "#username + ':' + " +
                    "#month + ':' + " +
                    "#year",
            ttl = 1200, db = 7)
    public List<AbddProductionSummaryDto> getProductionAbddByMainBranchCode(String username, Integer month, Integer year) {
        // Validate user
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        if (user.getBranches() != null && !user.getBranches().isEmpty()) {
            List<String> branchCodes = user.getBranches().stream()
                    .map(Branch::getBranchCode)
                    .collect(Collectors.toList());

            // Get aggregated netApe by main_branch_code
            return agentProductionPerAgentRepository.aggregateAbddNetApeByMainBranchCode(branchCodes, month, year);
        }

        return Collections.emptyList();
    }

    @CacheableWithTTL(cacheName = "productionAgentBddByMainBranchCache",
            key = "#username + ':' + " +
                    "#month + ':' + " +
                    "#year",
            ttl = 1200, db = 7)
    public List<BddProductionSummaryDto> getProductionBddByMainBranchCode(String username, Integer month, Integer year) {
        // Validate user
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        if (user.getBranches() != null && !user.getBranches().isEmpty()) {
            List<String> branchCodes = user.getBranches().stream()
                    .map(Branch::getBranchCode)
                    .collect(Collectors.toList());

            // Get aggregated netApe by main_branch_code
            return agentProductionPerAgentRepository.aggregateBddNetApeByMainBranchCode(branchCodes, month, year);
        }

        return Collections.emptyList();
    }

    @CacheableWithTTL(cacheName = "productionAgentHosByMainBranchCache",
            key = "#username + ':' + " +
                    "#month + ':' + " +
                    "#year",
            ttl = 1200, db = 7)
    public List<HosProductionSummaryDto> getProductionHosByMainBranchCode(String username, Integer month, Integer year) {
        // Validate user
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        if (user.getBranches() != null && !user.getBranches().isEmpty()) {
            List<String> branchCodes = user.getBranches().stream()
                    .map(Branch::getBranchCode)
                    .collect(Collectors.toList());

            // Get aggregated netApe by main_branch_code
            return agentProductionPerAgentRepository.aggregateHosNetApeByMainBranchCode(branchCodes, month, year);
        }

        return Collections.emptyList();
    }
}
