package id.co.panindaiichilife.superapp.agent.core.retrofit;

import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;

/**
 * HTTP Client SSL Configuration Utility
 * <p>
 * This class provides secure SSL configuration for OkHttpClient instances.
 * By default, it uses proper SSL certificate validation for security.
 * <p>
 * SECURITY NOTICE:
 * - Production environments should ALWAYS use secure SSL validation
 * - The unsafe SSL configuration is only for development/testing with self-signed certificates
 * - All unsafe SSL methods are properly annotated with SonarQube suppressions and warnings
 * <p>
 * Configuration options:
 * 1. Default secure SSL (recommended for production)
 * 2. Custom certificate trust store (for specific certificate requirements)
 * 3. Disabled validation (ONLY for development/testing)
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
public class HttpClientContext {

    private static final Logger log = LoggerFactory.getLogger(HttpClientContext.class);

    /**
     * Configure SSL for OkHttpClient builder.
     * By default, uses proper SSL certificate validation.
     * Only disables validation when explicitly configured via system property.
     *
     * @param builder OkHttpClient.Builder to configure
     */
    public static void configureSsl(OkHttpClient.Builder builder) {
        configureSsl(builder, false);
    }

    /**
     * Configure SSL for OkHttpClient builder with option to disable validation.
     *
     * @param builder           OkHttpClient.Builder to configure
     * @param disableValidation whether to disable SSL certificate validation
     */
    public static void configureSsl(OkHttpClient.Builder builder, boolean disableValidation) {
        if (disableValidation) {
            log.warn("SSL certificate validation is DISABLED. This should only be used in development/testing environments!");
            configureUnsafeSsl(builder);
        } else {
            log.debug("Using default SSL certificate validation");
            // Use default SSL configuration - no custom configuration needed
            // OkHttpClient will use the system's default SSL context and trust store
            configureSecureSsl();
        }
    }

    /**
     * Configure secure SSL with proper certificate validation.
     * This method explicitly sets up secure SSL configuration.
     */
    private static void configureSecureSsl() {
        // Explicitly use the default SSL configuration
        // This ensures proper certificate validation is in place
        log.debug("Configuring secure SSL with proper certificate validation");

        // OkHttpClient uses secure defaults, but we can be explicit about it
        // The default SSLSocketFactory and X509TrustManager will be used
        // which properly validates certificates against the system trust store
    }

    /**
     * Configure SSL with custom trusted certificates.
     * This method allows adding specific certificates to the trust store while maintaining security.
     *
     * @param builder           OkHttpClient.Builder to configure
     * @param certificateInputs Input streams for custom certificates to trust
     */
    @SuppressWarnings("java:S112")
    // Suppress SonarQube warning for RuntimeException - appropriate for SSL configuration failures
    public static void configureSslWithCustomCertificates(OkHttpClient.Builder builder, InputStream... certificateInputs) {
        try {
            log.info("Configuring SSL with custom trusted certificates");

            // Create a KeyStore containing the trusted CAs
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, null);

            // Add custom certificates to the keystore
            for (int i = 0; i < certificateInputs.length; i++) {
                Certificate certificate = certificateFactory.generateCertificate(certificateInputs[i]);
                keyStore.setCertificateEntry("custom-cert-" + i, certificate);
                certificateInputs[i].close();
            }

            // Create a TrustManager that trusts the CAs in our KeyStore
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init(keyStore);

            // Create an SSLContext that uses our TrustManager
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustManagerFactory.getTrustManagers(), null);

            // Configure the OkHttpClient to use our custom SSLContext
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            X509TrustManager trustManager = (X509TrustManager) trustManagerFactory.getTrustManagers()[0];
            builder.sslSocketFactory(sslSocketFactory, trustManager);

            log.info("Successfully configured SSL with {} custom certificate(s)", certificateInputs.length);
        } catch (KeyStoreException | NoSuchAlgorithmException | CertificateException |
                 KeyManagementException | IOException e) {
            log.error("Failed to configure SSL with custom certificates: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to configure SSL with custom certificates: " + e.getMessage(), e);
        }
    }

    /**
     * Configure unsafe SSL that trusts all certificates.
     * WARNING: This should NEVER be used in production!
     * <p>
     * This method intentionally disables SSL certificate validation and hostname verification.
     * It should only be used in development/testing environments where self-signed certificates
     * or invalid hostnames are expected.
     *
     * @param builder OkHttpClient.Builder to configure with unsafe SSL settings
     */
    @SuppressWarnings({
            "java:S5527", // Suppress SonarQube warning for intentionally disabled hostname verification
            "java:S4830", // Suppress SonarQube warning for intentionally disabled certificate validation
            "java:S112"   // Suppress SonarQube warning for RuntimeException - appropriate for SSL configuration failures
    })
    private static void configureUnsafeSsl(OkHttpClient.Builder builder) {
        try {
            // Create a trust manager that does not validate certificate chains
            // SECURITY WARNING: This intentionally bypasses SSL certificate validation
            // This is ONLY acceptable in development/testing environments
            final TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        @SuppressWarnings("java:S4424") // Suppress weak certificate validation warning
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain,
                                                       String authType) throws CertificateException {
                            // Intentionally empty - accepts all client certificates for dev/test only
                        }

                        @Override
                        @SuppressWarnings("java:S4424") // Suppress weak certificate validation warning
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain,
                                                       String authType) throws CertificateException {
                            // Intentionally empty - accepts all server certificates for dev/test only
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };

            // Install the all-trusting trust manager
            final SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            // Create an ssl socket factory with our all-trusting manager
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);

            // INTENTIONALLY UNSAFE: Accept all hostnames (only for development/testing)
            // This line will trigger security warnings in IDEs - this is expected and intentional
            // noinspection ALL (Suppress IDE warnings for this intentionally unsafe line)
            builder.hostnameVerifier((hostname, session) -> true);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            log.error("Failed to configure unsafe SSL: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to configure unsafe SSL: " + e.getMessage(), e);
        }
    }
}
