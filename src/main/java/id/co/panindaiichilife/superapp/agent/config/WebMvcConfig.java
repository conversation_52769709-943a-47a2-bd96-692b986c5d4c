package id.co.panindaiichilife.superapp.agent.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import id.co.panindaiichilife.superapp.agent.core.support.editor.LocalDateRangeEditor;
import id.co.panindaiichilife.superapp.agent.core.support.type.LocalDateRange;
import id.co.panindaiichilife.superapp.agent.core.view.FilterMode;
import id.co.panindaiichilife.superapp.agent.core.view.FilterModeEditor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.propertyeditors.StringTrimmerEditor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@ControllerAdvice
public class WebMvcConfig implements WebMvcConfigurer {

    @Value("${media.dir}")
    private String mediaDir;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // No interceptors are currently configured for this application
        // This method is intentionally left empty as no custom interceptors are needed at this time
    }

    @InitBinder
    public void registerCustomEditors(WebDataBinder binder, WebRequest request) {
        //treat empty strings in forms as NULL values in database
        binder.registerCustomEditor(FilterMode.class, new FilterModeEditor());
        binder.registerCustomEditor(LocalDateRange.class, new LocalDateRangeEditor());
        binder.registerCustomEditor(String.class, new StringTrimmerEditor(true));
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/media/**").addResourceLocations("file:" + mediaDir + "/");
    }

    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper()
                .findAndRegisterModules()
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

}
