package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalLevelDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ApprovalLevelFilter;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ApprovalLevelForm;
import id.co.panindaiichilife.superapp.agent.service.ApprovalLevelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController("cmsApprovalLevelController")
@RequestMapping("/api/cms/approval-level")
@Tag(name = "Approval Level - CMS", description = "API CMS Approval Level")
@Slf4j
@RequiredArgsConstructor
public class ApprovalLevelCmsController {

    private final ApprovalLevelService approvalLevelService;

    @Operation(summary = "List approvalLevels")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.ApprovalLevel', 'view')")
    public Page<ApprovalLevelDto> index(@ParameterObject @ModelAttribute("filter") ApprovalLevelFilter filter,
                                        @ParameterObject @PageableDefault(sort = "id") Pageable pageable) {
        return approvalLevelService.findAll(pageable, filter);
    }

    @Operation(summary = "View specific approvalLevel")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.ApprovalLevel', 'view')")
    public ApprovalLevelDto view(@PathVariable long id) {
        return approvalLevelService.findOne(id);
    }

    @Operation(summary = "Add new approvalLevels")
    @PostMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.ApprovalLevel', 'add')")
    public ApprovalLevelDto insert(@Valid @RequestBody ApprovalLevelForm approvalLevelForm) {
        return approvalLevelService.add(approvalLevelForm);
    }

    @Operation(summary = "Modify existing approvalLevel")
    @PutMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.ApprovalLevel', 'edit')")
    public ApprovalLevelDto edit(@PathVariable long id,
                                 @Valid @RequestBody ApprovalLevelForm approvalLevelForm) {
        return approvalLevelService.update(id, approvalLevelForm);
    }

    @Operation(summary = "Delete existing approvalLevel")
    @DeleteMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.ApprovalLevel', 'delete')")
    public void delete(@PathVariable long id) {
        approvalLevelService.delete(id);
    }
}
