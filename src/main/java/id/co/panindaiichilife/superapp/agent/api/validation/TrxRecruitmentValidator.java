package id.co.panindaiichilife.superapp.agent.api.validation;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.Role;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxRecruitmentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class TrxRecruitmentValidator implements Validator {

    private static final String POSITION_LEVEL_FIELD = "positionLevel";
    private static final String RECRUITER_CODE_FIELD = "recruiterCode";

    private final UserRepository userRepository;
    private final AgentRepository agentRepository;
    private final TrxRecruitmentRepository trxRecruitmentRepository;

    @Override
    public boolean supports(Class<?> clazz) {
        return TrxRecruitmentForm.class.isAssignableFrom(clazz);
    }

    /**
     * Validates the form for draft operations
     *
     * @param target The target object to validate
     * @param errors The errors object to add validation errors to
     */
    public void validateForDraft(Object target, Errors errors) {
        TrxRecruitmentForm form = (TrxRecruitmentForm) target;

        if (!validateExistingRecruitment(form, errors, true)) {
            return;
        }

        User recruiter = validateAndGetRecruiter(form, errors);
        if (recruiter == null) {
            return;
        }

        String recruiterPositionLevel = determineRecruiterPositionLevel(recruiter, errors);
        if (recruiterPositionLevel == null) {
            return;
        }

        String candidatePositionLevel = form.getPositionLevel().name();
        validateHierarchyRecruitment(recruiterPositionLevel, candidatePositionLevel, errors);
    }

    @Override
    public void validate(Object target, Errors errors) {
        TrxRecruitmentForm form = (TrxRecruitmentForm) target;

        if (!validateRequiredFields(form, errors)) {
            return;
        }

        if (!validateExistingRecruitment(form, errors, false)) {
            return;
        }

        User recruiter = validateAndGetRecruiter(form, errors);
        if (recruiter == null) {
            return;
        }

        String recruiterPositionLevel = determineRecruiterPositionLevel(recruiter, errors);
        if (recruiterPositionLevel == null) {
            return;
        }

        String candidatePositionLevel = form.getPositionLevel().name();
        validateHierarchyRecruitment(recruiterPositionLevel, candidatePositionLevel, errors);
    }

    /**
     * Validates the form for revise operations, excluding DIKEMBALIKAN status from existing recruitment check
     *
     * @param target The target object to validate
     * @param errors The errors object to add validation errors to
     */
    public void validateForRevise(Object target, Errors errors) {
        TrxRecruitmentForm form = (TrxRecruitmentForm) target;

        if (!validateRequiredFields(form, errors)) {
            return;
        }

        if (!validateExistingRecruitment(form, errors, true)) {
            return;
        }

        User recruiter = validateAndGetRecruiter(form, errors);
        if (recruiter == null) {
            return;
        }

        String recruiterPositionLevel = determineRecruiterPositionLevel(recruiter, errors);
        if (recruiterPositionLevel == null) {
            return;
        }

        String candidatePositionLevel = form.getPositionLevel().name();
        validateHierarchyRecruitment(recruiterPositionLevel, candidatePositionLevel, errors);
    }

    private boolean validateRequiredFields(TrxRecruitmentForm form, Errors errors) {
        if (form.getPositionLevel() == null) {
            errors.rejectValue(POSITION_LEVEL_FIELD, "positionLevel.required", "Position level is required");
            return false;
        }

        if (StringUtils.isBlank(form.getRecruiterCode())) {
            errors.rejectValue(RECRUITER_CODE_FIELD, "recruiterCode.required", "Recruiter code is required");
            return false;
        }

        if (StringUtils.isBlank(form.getNik())) {
            errors.rejectValue("nik", "nik.required", "NIK is required");
            return false;
        }

        if (StringUtils.isBlank(form.getParaf())) {
            errors.rejectValue("paraf", "paraf.required", "Paraf is required");
            return false;
        }

        if (StringUtils.isBlank(form.getSignature())) {
            errors.rejectValue("signature", "signature.required", "Signature is required");
            return false;
        }

        return true;
    }

    private boolean validateExistingRecruitment(TrxRecruitmentForm form, Errors errors, boolean isReviseContext) {
        List<TrxStatus> excludedStatuses;

        if (isReviseContext) {
            // For revise operations, also exclude DIKEMBALIKAN status
            excludedStatuses = Arrays.asList(TrxStatus.EXPIRED, TrxStatus.REJECTED, TrxStatus.DRAFT, TrxStatus.DIKEMBALIKAN);
        } else {
            // For normal operations, exclude standard statuses
            excludedStatuses = Arrays.asList(TrxStatus.EXPIRED, TrxStatus.REJECTED, TrxStatus.DRAFT);
        }

        boolean existingRecruitment = trxRecruitmentRepository.existsByRecruiterCodeAndNikAndTrxStatusNotIn(
                form.getRecruiterCode(), form.getNik(), excludedStatuses);

        if (existingRecruitment) {
            String message = isReviseContext
                    ? "There is already an active recruitment request for this candidate by this recruiter. "
                    + "Please wait for the existing request to be draft, expired, rejected, or returned."
                    : "There is already an active recruitment request for this candidate by this recruiter. "
                    + "Please wait for the existing request to be draft, expired or rejected.";

            errors.rejectValue("nik", "nik.duplicate", message);
            return false;
        }

        return true;
    }

    private User validateAndGetRecruiter(TrxRecruitmentForm form, Errors errors) {
        try {
            return userRepository.findByUsername(form.getRecruiterCode())
                    .orElseThrow(() -> new NotFoundException("Recruiter not found"));
        } catch (Exception e) {
            errors.rejectValue(RECRUITER_CODE_FIELD, "recruiterCode.invalid", "Invalid recruiter code");
            return null;
        }
    }

    private String determineRecruiterPositionLevel(User recruiter, Errors errors) {
        if (hasSpecialRole(recruiter)) {
            return getSpecialRolePositionLevel(recruiter);
        }

        Agent recruiterAgent = agentRepository.findTopByUser(recruiter).orElse(null);
        if (recruiterAgent == null) {
            errors.rejectValue(RECRUITER_CODE_FIELD, "recruiterCode.noAgent",
                    "Recruiter does not have an associated agent record");
            return null;
        }

        String positionLevel = recruiterAgent.getPositionLevel();
        if (StringUtils.isBlank(positionLevel)) {
            positionLevel = getPositionLevelFromRoles(recruiter);
        }

        if (StringUtils.isBlank(positionLevel)) {
            errors.rejectValue(RECRUITER_CODE_FIELD, "recruiterCode.noPositionLevel",
                    "Cannot determine position level for recruiter");
            return null;
        }

        return positionLevel;
    }

    private boolean hasSpecialRole(User recruiter) {
        if (recruiter.getRoles() == null) {
            return false;
        }

        return recruiter.getRoles().stream()
                .anyMatch(role -> {
                    String roleCode = role.getCode();
                    return roleCode != null && extractSpecialRole(roleCode) != null;
                });
    }

    private String getSpecialRolePositionLevel(User recruiter) {
        if (recruiter.getRoles() == null) {
            return null;
        }

        for (Role role : recruiter.getRoles()) {
            String roleCode = role.getCode();
            if (roleCode != null) {
                String specialRole = extractSpecialRole(roleCode);
                if (specialRole != null) {
                    return specialRole;
                }
            }
        }
        return null;
    }

    private String extractSpecialRole(String roleCode) {
        String[] specialRoles = {"BDM", "ABDD", "BDD", "ARA", "HOS", "CAO"};

        for (String specialRole : specialRoles) {
            if (roleCode.contains(specialRole)) {
                return specialRole;
            }
        }
        return null;
    }

    private String getPositionLevelFromRoles(User recruiter) {
        if (recruiter.getRoles() == null) {
            return null;
        }

        for (Role role : recruiter.getRoles()) {
            String roleCode = role.getCode();
            if (roleCode != null) {
                String positionLevel = extractPositionLevel(roleCode);
                if (positionLevel != null) {
                    return positionLevel;
                }
            }
        }
        return null;
    }

    private String extractPositionLevel(String roleCode) {
        if (roleCode.contains("_BP")) {
            return "BP";
        }
        if (roleCode.contains("_BM")) {
            return "BM";
        }
        if (roleCode.contains("_BD")) {
            return "BD";
        }
        return null;
    }

    /**
     * Validates recruitment based on hierarchy rules:
     * <p>
     * BP:
     * - Can only recruit BP
     * <p>
     * BM (leader):
     * - Can recruit BP and BM
     * <p>
     * BD (leader):
     * - Can recruit all levels (BP, BM, BD)
     * <p>
     * BDM/ABDD/BDD/HOS/CAO:
     * - Can recruit all levels (BP, BM, BD)
     *
     * @param recruiterPositionLevel The position level of the recruiter
     * @param candidatePositionLevel The position level of the candidate being recruited
     * @param errors                 The errors object to add validation errors to
     */
    private void validateHierarchyRecruitment(String recruiterPositionLevel, String candidatePositionLevel, Errors errors) {
        switch (recruiterPositionLevel) {
            case "BP":
                // BP can only recruit BP
                if (!"BP".equals(candidatePositionLevel)) {
                    errors.rejectValue(POSITION_LEVEL_FIELD, "positionLevel.invalidForBP",
                            "BP can only recruit BP level candidates");
                }
                break;

            case "BM":
                // BM can recruit BP and BM
                if (!"BP".equals(candidatePositionLevel) && !"BM".equals(candidatePositionLevel)) {
                    errors.rejectValue(POSITION_LEVEL_FIELD, "positionLevel.invalidForBM",
                            "BM can only recruit BP or BM level candidates");
                }
                break;

            case "BD":
            case "BDM":
            case "ABDD":
            case "ARA":
            case "BDD":
            case "HOS":
            case "CAO":
                // These roles can recruit all levels - no validation needed
                break;

            default:
                errors.rejectValue(RECRUITER_CODE_FIELD, "recruiterCode.unknownPositionLevel",
                        "Unknown recruiter position level: " + recruiterPositionLevel);
                break;
        }
    }
}