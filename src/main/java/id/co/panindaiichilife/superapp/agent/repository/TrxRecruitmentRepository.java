package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAajiStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAasiStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface TrxRecruitmentRepository extends BaseRepository<TrxRecruitment, Long> {

    List<TrxRecruitment> findByRecruiterAndApprovalStatusIn(Agent recruiter, List<ApprovalStatus> approvalStatusList);

    Optional<TrxRecruitment> findByApprovalHeader(TrxApprovalHeader approvalHeader);

    /**
     * Find existing recruitment requests by recruiter code and NIK where the transaction status is not EXPIRED or REJECTED
     *
     * @param recruiterCode    The code of the recruiter
     * @param nik              The NIK (National ID) of the candidate
     * @param excludedStatuses List of transaction statuses to exclude from the search
     * @return List of matching recruitment requests
     */
    List<TrxRecruitment> findByRecruiterCodeAndNikAndTrxStatusNotIn(String recruiterCode, String nik, List<TrxStatus> excludedStatuses);

    /**
     * Check if there are any existing recruitment requests by recruiter code and NIK where the transaction status is not EXPIRED or REJECTED
     *
     * @param recruiterCode    The code of the recruiter
     * @param nik              The NIK (National ID) of the candidate
     * @param excludedStatuses List of transaction statuses to exclude from the search
     * @return true if any matching recruitment requests exist, false otherwise
     */
    boolean existsByRecruiterCodeAndNikAndTrxStatusNotIn(String recruiterCode, String nik, List<TrxStatus> excludedStatuses);

    /**
     * Find a recruitment request by recruiter code and NIK
     *
     * @param recruiterCode The code of the recruiter
     * @param nik           The NIK (National ID) of the candidate
     * @return Optional containing the recruitment request if found, empty otherwise
     */
    Optional<TrxRecruitment> findByRecruiterCodeAndNik(String recruiterCode, String nik);

    /**
     * Find the most recent recruitment request by recruiter code and NIK
     *
     * @param recruiterCode The code of the recruiter
     * @param nik           The NIK (National ID) of the candidate
     * @return Optional containing the most recent recruitment request if found, empty otherwise
     */
    Optional<TrxRecruitment> findTopByRecruiterCodeAndNikOrderByCreatedAtDesc(String recruiterCode, String nik);

    /**
     * Find a recruitment request by UUID
     *
     * @param uuid The UUID of the recruitment
     * @return Optional containing the recruitment request if found, empty otherwise
     */
    Optional<TrxRecruitment> findByUuid(String uuid);

    List<TrxRecruitment> findByUpdatedAtBeforeAndTrxStatusNotInAndDeletedFalse(Instant cutoff, List<TrxStatus> status);

    /**
     * Simple query to find all approved recruitments for debugging
     */
    @Query("""
        SELECT r FROM TrxRecruitment r
        WHERE r.approvalStatus = :approvalStatus
        AND r.deleted = false
        ORDER BY r.updatedAt DESC
        """)
    List<TrxRecruitment> findAllApprovedRecruitments(@Param("approvalStatus") ApprovalStatus approvalStatus);

    /**
     * Find approved recruitments with agent codes for debugging
     */
    @Query("""
        SELECT r FROM TrxRecruitment r
        WHERE r.approvalStatus = :approvalStatus
        AND r.agentCode IS NOT NULL
        AND r.agentCode != ''
        AND r.deleted = false
        ORDER BY r.updatedAt DESC
        """)
    List<TrxRecruitment> findApprovedRecruitmentsWithAgentCode(@Param("approvalStatus") ApprovalStatus approvalStatus);

    /**
     * Find approved recruitments for productivity analysis
     * Filters by approval status DISETUJUI and date range
     *
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @param recruiterIds List of recruiter IDs for filtering (supports team scope)
     * @return List of approved recruitments
     */
    @Query("""
        SELECT r FROM TrxRecruitment r
        WHERE r.approvalStatus = :approvalStatus
        AND r.updatedAt >= :startDate
        AND r.updatedAt <= :endDate
        AND (:recruiterIds IS NULL OR r.recruiter.id IN :recruiterIds)
        AND r.deleted = false
        ORDER BY r.updatedAt DESC
        """)
    List<TrxRecruitment> findApprovedRecruitmentsForProductivity(
            @Param("approvalStatus") ApprovalStatus approvalStatus,
            @Param("startDate") Instant startDate,
            @Param("endDate") Instant endDate,
            @Param("recruiterIds") List<Long> recruiterIds);

    /**
     * Find approved recruitments with agent codes (rekrut berkode agen)
     * These are recruitments where agent code exists but license validation is not ACTIVE
     *
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @param recruiterIds List of recruiter IDs for filtering (supports team scope)
     * @return List of approved recruitments with agent codes but inactive licenses
     */
    @Query("""
        SELECT r FROM TrxRecruitment r
        WHERE r.approvalStatus = :approvalStatus
        AND r.updatedAt >= :startDate
        AND r.updatedAt <= :endDate
        AND (:recruiterIds IS NULL OR r.recruiter.id IN :recruiterIds)
        AND r.agentCode IS NOT NULL
        AND r.agentCode != ''
        AND (r.validationLicenseAajiStatus != :aajiActiveStatus OR r.validationLicenseAajiStatus IS NULL)
        AND (r.validationLicenseAasiStatus != :aasiActiveStatus OR r.validationLicenseAasiStatus IS NULL)
        AND r.deleted = false
        ORDER BY r.updatedAt DESC
        """)
    List<TrxRecruitment> findRekrutBerkodeAgen(
            @Param("approvalStatus") ApprovalStatus approvalStatus,
            @Param("startDate") Instant startDate,
            @Param("endDate") Instant endDate,
            @Param("recruiterIds") List<Long> recruiterIds,
            @Param("aajiActiveStatus") ValidationLicenseAajiStatus aajiActiveStatus,
            @Param("aasiActiveStatus") ValidationLicenseAasiStatus aasiActiveStatus);

    /**
     * Find approved recruitments with active licenses (rekrut baru berlisensi)
     * These are recruitments where agent code exists and either AAJI or AASI license validation is ACTIVE
     *
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @param recruiterIds List of recruiter IDs for filtering (supports team scope)
     * @return List of approved recruitments with active licenses
     */
    @Query("""
        SELECT r FROM TrxRecruitment r
        WHERE r.approvalStatus = :approvalStatus
        AND r.updatedAt >= :startDate
        AND r.updatedAt <= :endDate
        AND (:recruiterIds IS NULL OR r.recruiter.id IN :recruiterIds)
        AND r.agentCode IS NOT NULL
        AND r.agentCode != ''
        AND (r.validationLicenseAajiStatus = :aajiActiveStatus OR r.validationLicenseAasiStatus = :aasiActiveStatus)
        AND r.deleted = false
        ORDER BY r.updatedAt DESC
        """)
    List<TrxRecruitment> findRekrutBaruBerlisensi(
            @Param("approvalStatus") ApprovalStatus approvalStatus,
            @Param("startDate") Instant startDate,
            @Param("endDate") Instant endDate,
            @Param("recruiterIds") List<Long> recruiterIds,
            @Param("aajiActiveStatus") ValidationLicenseAajiStatus aajiActiveStatus,
            @Param("aasiActiveStatus") ValidationLicenseAasiStatus aasiActiveStatus);

    /**
     * Find approved recruitments for productivity analysis filtered by branch codes
     * This is more efficient for STAFF users who need to see data from multiple branches
     *
     * @param approvalStatus The approval status to filter by
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @param branchCodes List of branch codes for filtering
     * @return List of approved recruitments
     */
    @Query("""
        SELECT r FROM TrxRecruitment r
        WHERE r.approvalStatus = :approvalStatus
        AND r.updatedAt >= :startDate
        AND r.updatedAt <= :endDate
        AND (:branchCodes IS NULL OR r.branch.branchCode IN :branchCodes)
        AND r.deleted = false
        ORDER BY r.updatedAt DESC
        """)
    List<TrxRecruitment> findApprovedRecruitmentsForProductivityByBranch(
            @Param("approvalStatus") ApprovalStatus approvalStatus,
            @Param("startDate") Instant startDate,
            @Param("endDate") Instant endDate,
            @Param("branchCodes") List<String> branchCodes);

    /**
     * Find approved recruitments with agent codes (rekrut berkode agen) filtered by branch codes
     *
     * @param approvalStatus The approval status to filter by
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @param branchCodes List of branch codes for filtering
     * @param aajiActiveStatus AAJI active status to exclude
     * @param aasiActiveStatus AASI active status to exclude
     * @return List of approved recruitments with agent codes but inactive licenses
     */
    @Query("""
        SELECT r FROM TrxRecruitment r
        WHERE r.approvalStatus = :approvalStatus
        AND r.updatedAt >= :startDate
        AND r.updatedAt <= :endDate
        AND (:branchCodes IS NULL OR r.branch.branchCode IN :branchCodes)
        AND r.agentCode IS NOT NULL
        AND r.agentCode != ''
        AND (r.validationLicenseAajiStatus != :aajiActiveStatus OR r.validationLicenseAajiStatus IS NULL)
        AND (r.validationLicenseAasiStatus != :aasiActiveStatus OR r.validationLicenseAasiStatus IS NULL)
        AND r.deleted = false
        ORDER BY r.updatedAt DESC
        """)
    List<TrxRecruitment> findRekrutBerkodeAgenByBranch(
            @Param("approvalStatus") ApprovalStatus approvalStatus,
            @Param("startDate") Instant startDate,
            @Param("endDate") Instant endDate,
            @Param("branchCodes") List<String> branchCodes,
            @Param("aajiActiveStatus") ValidationLicenseAajiStatus aajiActiveStatus,
            @Param("aasiActiveStatus") ValidationLicenseAasiStatus aasiActiveStatus);

    /**
     * Find approved recruitments with active licenses (rekrut baru berlisensi) filtered by branch codes
     *
     * @param approvalStatus The approval status to filter by
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @param branchCodes List of branch codes for filtering
     * @param aajiActiveStatus AAJI active status to include
     * @param aasiActiveStatus AASI active status to include
     * @return List of approved recruitments with active licenses
     */
    @Query("""
        SELECT r FROM TrxRecruitment r
        WHERE r.approvalStatus = :approvalStatus
        AND r.updatedAt >= :startDate
        AND r.updatedAt <= :endDate
        AND (:branchCodes IS NULL OR r.branch.branchCode IN :branchCodes)
        AND r.agentCode IS NOT NULL
        AND r.agentCode != ''
        AND (r.validationLicenseAajiStatus = :aajiActiveStatus OR r.validationLicenseAasiStatus = :aasiActiveStatus)
        AND r.deleted = false
        ORDER BY r.updatedAt DESC
        """)
    List<TrxRecruitment> findRekrutBaruBerlisensiBybranch(
            @Param("approvalStatus") ApprovalStatus approvalStatus,
            @Param("startDate") Instant startDate,
            @Param("endDate") Instant endDate,
            @Param("branchCodes") List<String> branchCodes,
            @Param("aajiActiveStatus") ValidationLicenseAajiStatus aajiActiveStatus,
            @Param("aasiActiveStatus") ValidationLicenseAasiStatus aasiActiveStatus);

}
