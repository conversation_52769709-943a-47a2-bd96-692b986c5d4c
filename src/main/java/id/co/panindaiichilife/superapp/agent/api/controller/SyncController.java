package id.co.panindaiichilife.superapp.agent.api.controller;

import id.co.panindaiichilife.superapp.agent.api.dto.batch.JobCreationDto;
import id.co.panindaiichilife.superapp.agent.service.AgentService;
import id.co.panindaiichilife.superapp.agent.service.BranchService;
import id.co.panindaiichilife.superapp.agent.service.SyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController("syncController")
@RequestMapping("/api/public/sync")
@Tag(name = "Sync Data", description = "API Sync Data")
@Slf4j
@RequiredArgsConstructor
public class SyncController {

    private final AgentService agentService;
    private final BranchService branchService;
    private final SyncService syncService;

    @Operation(summary = "Run Job Import Agent")
    @GetMapping(value = "agent")
    public List<JobCreationDto> syncAgent() {
        return agentService.runImportAgent();
    }

    @Operation(summary = "Run Job Import MDRT")
    @GetMapping(value = "mdrt")
    public List<JobCreationDto> syncMdrt() {
        return syncService.runImportMdrt();
    }

    @Operation(summary = "Run Job Import Agent Production Per Agent")
    @GetMapping(value = "agent-production-per-agent")
    public List<JobCreationDto> syncAgentProductionPerAgent() {
        return syncService.runImportAgentProductionPerAgent();
    }


    @Operation(summary = "Run Job Import Agent Production Per Policy")
    @GetMapping(value = "agent-production-per-policy")
    public List<JobCreationDto> syncAgentProductionPerPolicy() {
        return syncService.runImportAgentProductionPerPolicy();
    }

    @Operation(summary = "Run Job Import Allowance")
    @GetMapping(value = "allowance")
    public List<JobCreationDto> syncAllowance() {
        return syncService.runImportAllowance();
    }

    @Operation(summary = "Run Job Import Commission Compensation")
    @GetMapping(value = "commission-compensation")
    public List<JobCreationDto> syncCommissionCompensation() {
        return syncService.runImportCommissionCompensation();
    }

    @Operation(summary = "Run Job Import Validasi Per G1")
    @GetMapping(value = "validasi-per-g1")
    public List<JobCreationDto> syncValidasiPerG1() {
        return syncService.runImportValidasiPerG1();
    }

    @Operation(summary = "Run Job Import Validasi Per Hirarki")
    @GetMapping(value = "validasi-per-hirarki")
    public List<JobCreationDto> syncValidasiPerHirarki() {
        return syncService.runImportValidasiPerHirarki();
    }

    @Operation(summary = "Run Job Import Persistency")
    @GetMapping(value = "persistency")
    public List<JobCreationDto> syncPersistency() {
        return syncService.runImportPersistency();
    }

    @Operation(summary = "Run Job Import Promosi Agent")
    @GetMapping(value = "promosi-agent")
    public List<JobCreationDto> syncPromosiAgent() {
        return syncService.runImportPromosiAgent();
    }

    @Operation(summary = "Run Job Import Promosi Leader")
    @GetMapping(value = "promosi-leader")
    public List<JobCreationDto> syncPromosiLeader() {
        return syncService.runImportPromosiLeader();
    }

    @Operation(summary = "Run Sync Branch")
    @GetMapping(value = "branch")
    public void syncBranch() {
        branchService.syncBranch();
    }
}
