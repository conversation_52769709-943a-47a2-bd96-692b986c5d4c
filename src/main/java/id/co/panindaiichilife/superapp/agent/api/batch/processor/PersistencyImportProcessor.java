package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.PersistencyImportDto;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.Persistency;
import id.co.panindaiichilife.superapp.agent.repository.PersistencyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class PersistencyImportProcessor implements ItemProcessor<PersistencyImportDto, Persistency> {

    private final PersistencyRepository persistencyRepository;

    @Override
    public Persistency process(PersistencyImportDto item) {
        return findOrCreatePersistency(item);
    }

    private Persistency findOrCreatePersistency(PersistencyImportDto item) {
        Persistency persistency = new Persistency();
        BeanUtils.copyProperties(item, persistency);
        persistency.setDistributionCode(DistributionCode.valueOf(item.getDistributionCode()));

        return persistency;
    }
}