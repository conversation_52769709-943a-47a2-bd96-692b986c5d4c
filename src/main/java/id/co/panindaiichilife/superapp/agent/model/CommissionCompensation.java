package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "commission_compensation")
@Data
@ToString(of = {"id", "agentCode"})
public class CommissionCompensation {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "commission_compensation_id_seq")
    @SequenceGenerator(name = "commission_compensation_id_seq", sequenceName = "commission_compensation_id_seq", allocationSize = 1)
    private Long id;

    @Enumerated(EnumType.STRING)
    private DistributionCode distributionCode;

    private String agentCode;

    private String leaderCode;

    private String type;

    private String mainBranchCode;

    private String branchCode;

    private String bdmCode;

    private String bdmName;

    private String abddCode;

    private String abddName;

    private String bddCode;

    private String bddName;

    private String hosCode;

    private String hosName;

    private Integer year;

    private Integer month;

    private String periode;

    private Integer policyYear;

    private Long amount;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

}
