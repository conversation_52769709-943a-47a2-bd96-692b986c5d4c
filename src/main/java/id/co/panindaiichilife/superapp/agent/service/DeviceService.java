package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.DeviceDto;
import id.co.panindaiichilife.superapp.agent.api.filter.DeviceFilter;
import id.co.panindaiichilife.superapp.agent.api.form.DeviceForm;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheEvictWithTTL;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.model.Device;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.DeviceRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeviceService {

    private final DeviceRepository deviceRepository;

    private final UserRepository userRepository;

    private final RedisService redisService;

    public Page<DeviceDto> findAll(Pageable pageable, DeviceFilter filter) {
        Page<Device> devices = deviceRepository.findAll(filter, pageable);
        return BaseDto.of(DeviceDto.class, devices, pageable);
    }

    public DeviceDto findOne(Long id) {
        Device data = deviceRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(DeviceDto.class, data);
    }

    public List<DeviceDto> findByUser(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        List<Device> devices = deviceRepository.findByUserAndStatus(user, Device.Status.Active);

        return BaseDto.of(DeviceDto.class, devices);
    }

    @Transactional
    public DeviceDto register(String username, DeviceForm deviceForm) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Device data = deviceRepository.findByDeviceIdAndUser(deviceForm.getDeviceId(), user).orElse(null);

        if (null == data) {
            data = new Device();
            data.setStatus(Device.Status.Active);
        }

        BeanUtils.copyProperties(deviceForm, data);
        data.setLastLogin(Instant.now());
        data.setUser(user);
        deviceRepository.save(data);
        return BaseDto.of(DeviceDto.class, data);
    }

    @Transactional
    @CacheEvictWithTTL(cacheName = "deviceCache", key = "#deviceId + ':' + #username", db = 7)
    public void revoke(String username, String deviceId) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Device data = deviceRepository.findByDeviceIdAndUser(deviceId, user).orElseThrow(() -> new BadRequestException("Device not found with ID: " + deviceId + " for user: " + user.getUsername()));

        data.setStatus(Device.Status.Inactive);
        data.setLastLogout(Instant.now());
        deviceRepository.save(data);
    }

    @CacheableWithTTL(cacheName = "deviceCache", key = "#deviceId + ':' + #username", ttl = 86400, db = 7)
    public boolean isDeviceActive(String deviceId, String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        boolean isActive = Boolean.TRUE;
        Device data = deviceRepository.findTopByDeviceIdAndUser(deviceId, user).orElse(null);

        if (null != data) {
            isActive = Device.Status.Active == data.getStatus();
        }

        return isActive;
    }

    /**
     * Activate device during login process
     * This method creates or updates a device record and sets it to Active status
     *
     * @param username The username
     * @param deviceId The device ID to activate
     */
    @Transactional
    @CacheEvictWithTTL(cacheName = "deviceCache", key = "#deviceId + ':' + #username", db = 7)
    public void activateDeviceOnLogin(String username, String deviceId) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Device device = deviceRepository.findByDeviceIdAndUser(deviceId, user).orElse(null);

        if (device == null) {
            // Create new device record
            device = new Device();
            device.setDeviceId(deviceId);
            device.setUser(user);
            device.setOsType("UNKNOWN"); // Will be updated when device registers properly
        }

        // Activate the device
        device.setStatus(Device.Status.Active);
        device.setLastLogin(Instant.now());
        deviceRepository.save(device);

        log.info("Device {} activated for user: {} during login", deviceId, username);
    }

    /**
     * Deactivate all devices for a user (typically called after password change)
     * This forces the user to re-login on all devices for security purposes
     *
     * @param user The user whose devices should be deactivated
     * @return Number of devices that were deactivated
     */
    @Transactional
    public int deactivateAllUserDevices(User user) {
        log.info("Deactivating all devices for user: {}", user.getUsername());

        List<Device> activeDevices = deviceRepository.findByUserAndStatus(user, Device.Status.Active);

        if (activeDevices.isEmpty()) {
            log.info("No active devices found for user: {}", user.getUsername());
            return 0;
        }

        Instant logoutTime = Instant.now();
        int deactivatedCount = 0;

        for (Device device : activeDevices) {
            device.setFirebaseToken(null); // Clear firebase token
            device.setStatus(Device.Status.Inactive);
            device.setLastLogout(logoutTime);
            deviceRepository.save(device);
            deactivatedCount++;

            // Clear cache for this specific device using RedisService
            clearDeviceCacheManually(device.getDeviceId(), user.getUsername());

            log.debug("Deactivated device: {} for user: {}. Firebase token cleared.", device.getDeviceId(), user.getUsername());
        }

        log.info("Successfully deactivated {} devices for user: {}", deactivatedCount, user.getUsername());
        return deactivatedCount;
    }

    /**
     * Deactivate all devices for a user by username
     *
     * @param username The username whose devices should be deactivated
     * @return Number of devices that were deactivated
     */
    @Transactional
    public int deactivateAllUserDevices(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(() ->
                new NotFoundException("User not found with username: " + username));
        return deactivateAllUserDevices(user);
    }

    /**
     * Logout from a specific device by clearing firebase token and setting status to Inactive
     *
     * @param username The username
     * @param deviceId The device ID to logout from
     */
    @Transactional
    @CacheEvictWithTTL(cacheName = "deviceCache", key = "#deviceId + ':' + #username", db = 7)
    public void logoutFromDevice(String username, String deviceId) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Device device = deviceRepository.findByDeviceIdAndUser(deviceId, user)
                .orElseThrow(() -> new BadRequestException("Device not found with ID: " + deviceId + " for user: " + username));

        // Clear firebase token and set device to inactive
        device.setFirebaseToken(null);
        device.setLastLogout(Instant.now());
        deviceRepository.save(device);

        log.info("Logged out device: {} for user: {}. Firebase token cleared.", deviceId, username);
    }

    /**
     * Manually clear device cache for a specific device and user combination using RedisService
     * This method works reliably even when called from Kafka consumers or internal method calls
     * where Spring AOP might not be properly applied
     *
     * @param deviceId The device ID
     * @param username The username
     */
    public void clearDeviceCacheManually(String deviceId, String username) {
        String cacheKey = deviceId + ":" + username;
        boolean cleared = redisService.clearCache("deviceCache", cacheKey, 7);
        log.debug("Manually cleared device cache for device: {} and user: {} - Success: {}", deviceId, username, cleared);
    }
}
