package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.PromosiLeaderImportDto;
import id.co.panindaiichilife.superapp.agent.model.PromosiLeader;
import id.co.panindaiichilife.superapp.agent.repository.PromosiLeaderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class PromosiLeaderImportProcessor implements ItemProcessor<PromosiLeaderImportDto, PromosiLeader> {

    private final PromosiLeaderRepository promosiLeaderRepository;


    @Override
    public PromosiLeader process(PromosiLeaderImportDto item) {
        return findOrCreatePromosiLeader(item);
    }

    private PromosiLeader findOrCreatePromosiLeader(PromosiLeaderImportDto item) {
        PromosiLeader promosiLeader = new PromosiLeader();
        BeanUtils.copyProperties(item, promosiLeader);
        return promosiLeader;
    }
}