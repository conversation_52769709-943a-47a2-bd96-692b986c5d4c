package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * DTO for KTP validation with QuantumX integration
 */
@Data
public class PortalValidationKtpDto {
    @SerializedName("trxNum")
    @JsonProperty("trxNum")
    private String trxNum;

    @SerializedName("desc1")
    @JsonProperty("desc1")
    private String desc1;

    @SerializedName("faceImage")
    @JsonProperty("faceImage")
    private String faceImage;

    @SerializedName("ktpImage")
    @JsonProperty("ktpImage")
    private String ktpImage;

    @SerializedName("idNumber")
    @JsonProperty("idNumber")
    private String idNumber;

    @SerializedName("email")
    @JsonProperty("email")
    private String email;

    @SerializedName("phoneNumber")
    @JsonProperty("phoneNumber")
    private String phoneNumber;

    @SerializedName("name")
    @JsonProperty("name")
    private String name;

    @SerializedName("birthDate")
    @JsonProperty("birthDate")
    private String birthDate;

    @SerializedName("caller")
    @JsonProperty("caller")
    private String caller;
}
