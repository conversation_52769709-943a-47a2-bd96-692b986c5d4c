package id.co.panindaiichilife.superapp.agent.core.support;

import id.co.panindaiichilife.superapp.agent.core.security.UserDetailsExtra;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

public final class TimeUtils {

  public static final String TIME_ZONE = "Asia/Jakarta";

  @Deprecated
  public static ZonedDateTime toSystemZone(LocalDateTime temporal) {
    return temporal.atZone(ZoneId.systemDefault());
  }

  @Deprecated
  public static ZonedDateTime toUserZone(LocalDateTime temporal) {
    if (temporal == null) {
      return null;
    }

    ZonedDateTime result = toSystemZone(temporal);
    result = toUserZone(result);
    return result;
  }

  public static ZonedDateTime toUserZone(ZonedDateTime temporal) {
    if (temporal == null) {
      return null;
    }

    return toUserZone(temporal.toInstant());
  }

  public static ZonedDateTime toUserZone(Instant temporal) {
    if (temporal == null) {
      return null;
    }

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null && authentication.getPrincipal() instanceof UserDetailsExtra) {
      UserDetailsExtra ud = (UserDetailsExtra) authentication.getPrincipal();
      return temporal.atZone(ud.getZoneId());
    } else {
      return temporal.atZone(ZoneId.systemDefault());
    }
  }

  public static ZonedDateTime zonedNow() {
    return ZonedDateTime.now(ZoneId.of(TIME_ZONE));
  }
}
