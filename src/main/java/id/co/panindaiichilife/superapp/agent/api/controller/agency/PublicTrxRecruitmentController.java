package id.co.panindaiichilife.superapp.agent.api.controller.agency;

import id.co.panindaiichilife.superapp.agent.api.dto.agency.EmailVerificationResponseDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRecruitmentDto;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentSignForm;
import id.co.panindaiichilife.superapp.agent.api.validation.TrxRecruitmentValidator;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.service.EmailVerificationService;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRecruitmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController("publicTrxAgencyRecruitmentController")
@RequestMapping("/api/public/agency")
@Tag(name = "Public Agency - Recruitment", description = "API Public Agency Recruitment")
@Slf4j
@RequiredArgsConstructor
public class PublicTrxRecruitmentController {

    private final TrxRecruitmentService trxRecruitmentService;

    private final TrxRecruitmentValidator trxRecruitmentValidator;

    private final EmailVerificationService emailVerificationService;

    @Operation(summary = "View specific recruitment")
    @GetMapping(value = "{uuid}")
    public TrxRecruitmentDto view(@PathVariable String uuid) {
        return trxRecruitmentService.findByUuid(uuid);
    }


    @Operation(summary = "Draft Recruitment")
    @PostMapping(value = "recruitment/draft")
    public TrxRecruitmentDto draft(@Valid @RequestBody TrxRecruitmentForm trxRecruitmentForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        trxRecruitmentValidator.validateForDraft(trxRecruitmentForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        TrxRecruitment trxRecruitment = trxRecruitmentService.saveDraft(null, trxRecruitmentForm);
        return BaseDto.of(TrxRecruitmentDto.class, trxRecruitment);
    }

    @Operation(summary = "Submit Sign Recruitment")
    @PatchMapping(value = "recruitment/sign/{uuid}")
    public void submitSign(@PathVariable String uuid,
                           @Valid @RequestBody TrxRecruitmentSignForm trxRecruitmentForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }

        trxRecruitmentService.sign(uuid, trxRecruitmentForm);
    }

    @Operation(summary = "Submit Recruitment")
    @PostMapping(value = "recruitment/submit")
    public void submit(@Valid @RequestBody TrxRecruitmentForm trxRecruitmentForm, BindingResult bindingResult) throws MethodArgumentNotValidException {
        trxRecruitmentValidator.validate(trxRecruitmentForm, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new MethodArgumentNotValidException(null, bindingResult);
        }
        trxRecruitmentService.submit(null, trxRecruitmentForm);
    }

    @Operation(summary = "Upload Documents")
    @PostMapping(value = "upload/{path}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    public FileinputResponse uploadDocument(@PathVariable String path, @RequestParam("file") MultipartFile file) {
        return trxRecruitmentService.upload(null, path, file);
    }

    @Operation(summary = "Send Email Verification Link")
    @PostMapping(value = "recruitment/{uuid}/send-verification-email")
    public ResponseEntity<EmailVerificationResponseDto> sendVerificationEmail(@PathVariable String uuid) {
        try {
            String email = emailVerificationService.sendVerificationEmailByUuid(uuid);
            if (null != email) {
                return ResponseEntity.ok(EmailVerificationResponseDto.success(
                        "Verification email sent successfully", email, uuid));
            } else {
                return ResponseEntity.ok(EmailVerificationResponseDto.error(
                        "Failed to send verification email. Please try again later."));
            }
        } catch (BadRequestException e) {
            return ResponseEntity.ok(EmailVerificationResponseDto.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error sending verification email", e);
            return ResponseEntity.ok(EmailVerificationResponseDto.error(
                    "An unexpected error occurred. Please try again later."));
        }
    }

    @Operation(summary = "Verify Email with Token")
    @GetMapping(value = "recruitment/verify-email")
    public ResponseEntity<EmailVerificationResponseDto> verifyEmail(@RequestParam String token) {
        try {
            TrxRecruitment recruitment = emailVerificationService.verifyEmail(token);
            return ResponseEntity.ok(EmailVerificationResponseDto.success(
                    "Email verified successfully", recruitment.getEmail(), recruitment.getUuid()));
        } catch (BadRequestException e) {
            return ResponseEntity.ok(EmailVerificationResponseDto.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error verifying email", e);
            return ResponseEntity.ok(EmailVerificationResponseDto.error(
                    "An unexpected error occurred. Please try again later."));
        }
    }

    @Operation(summary = "Check Email Verified by UUID")
    @GetMapping(value = "recruitment/check-email-verified")
    public ResponseEntity<Boolean> checkVerifiedEmail(@RequestParam String uuid) {
        return ResponseEntity.ok(emailVerificationService.checkEmailVerified(uuid));
    }
}
