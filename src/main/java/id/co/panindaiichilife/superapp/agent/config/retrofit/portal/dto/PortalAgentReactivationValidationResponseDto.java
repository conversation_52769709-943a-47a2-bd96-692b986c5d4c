package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * DTO for the response from agent reactivation validation API
 */
@Data
public class PortalAgentReactivationValidationResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("agentReactivationValidationDTO")
    @JsonProperty("agentReactivationValidationDTO")
    private ValidationDto agentReactivationValidationDTO;

    @Data
    public static class ValidationDto {
        @SerializedName("remark")
        @JsonProperty("remark")
        private String remark;

        @SerializedName("validLeaderCode")
        @JsonProperty("validLeaderCode")
        private String validLeaderCode;

        @SerializedName("valid")
        @JsonProperty("valid")
        private boolean valid;
    }

}
