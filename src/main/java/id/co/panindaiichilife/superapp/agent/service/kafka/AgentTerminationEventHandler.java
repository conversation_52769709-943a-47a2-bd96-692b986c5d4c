package id.co.panindaiichilife.superapp.agent.service.kafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sendgrid.helpers.mail.objects.Attachments;
import id.co.panindaiichilife.superapp.agent.api.dto.NotificationDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalAgentTerminationRequestDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalAgentTerminationResponseDto;
import id.co.panindaiichilife.superapp.agent.core.service.PdfService;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.DocumentType;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.Branch;
import id.co.panindaiichilife.superapp.agent.model.Role;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalDetail;
import id.co.panindaiichilife.superapp.agent.model.TrxTermination;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.model.event.AgentTerminationEvent;
import id.co.panindaiichilife.superapp.agent.repository.BranchRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.FirebaseService;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxTerminationService;
import id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.TerminationNotificationTemplates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.TerminationNotificationTemplates.Email.Keys.NEW_SUBMISSION_TO_CAS_REVIEWER;
import static id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.TerminationNotificationTemplates.Email.Keys.TO_BDM_ABDD_BDD_APPROVED_BY_CAS;
import static id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.TerminationNotificationTemplates.Email.Keys.TO_BDM_ABDD_BDD_REJECTED_BY_CAS;
import static id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.TerminationNotificationTemplates.Email.Keys.TO_BDM_ABDD_BDD__POLICY_TRANSFER__APPROVED_BY_CAS;
import static id.co.panindaiichilife.superapp.agent.service.agency.notificationTemplates.TerminationNotificationTemplates.Email.Keys.TO_TERMINATION_TARGET_APPROVED_BY_CAS;

@Service
@Slf4j
@RequiredArgsConstructor
public class AgentTerminationEventHandler {
    private final GlobalConfigService globalConfigService;
    private final TrxTerminationService trxTerminationService;
    private final PortalProvider portalProvider;
    private final UserRepository userRepository;
    private final FirebaseService firebaseService;
    private final BranchRepository branchRepository;
    private final PdfService pdfService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final String DEFAULT_TEMPLATE = "mail.template.termination.approved";

    public void handleEvent(AgentTerminationEvent event) {
        Long terminationId = event.getTerminationId();

        // update termination status
        TrxTermination termination = trxTerminationService.resolveTerminationStatusOnEventReceived(
                terminationId, event.getApprovalStatus());

        log.info("Handling termination event for ID: {}", terminationId);

        TrxStatus latestTerminationStatus = termination.getStatus();

        if (event.getApprovalStatus() == ApprovalStatus.MENUNGGU_PERSETUJUAN &&
                event.getNextApproverUserIds() != null && !event.getNextApproverUserIds().isEmpty()) {
            // Send notification to the next approvers
            log.info("Sending notification to {} next approvers for termination ID: {}",
                    event.getNextApproverUserIds().size(), terminationId);
            sendNextApproverNotifications(event);

            sendEmailToCasReviewer(event, termination);
        }

        switch (latestTerminationStatus) {
            case COMPLETE:
                log.info("Submitting termination request {} to portal...", terminationId);

                submitTerminationToPortal(termination);

                termination = trxTerminationService.setEffectiveDate(termination);

                sendInboxNotificationToRequester(event, termination);
                sendEmailToRelatedDirectors(termination);

                break;

            case REJECTED:
                sendInboxNotificationToRequester(event, termination);
                sendEmailToRelatedDirectors(termination);
                log.info("Transaction {} rejected", terminationId);

                return;

            case DIKEMBALIKAN:
                sendInboxNotificationToRequester(event, termination);
                break;

            case CANCELLED:
                log.info("Transaction {} cancelled", terminationId);
                break;

            case EXPIRED:
                log.info("Transaction {} expired", terminationId);
                break;

            default:
                log.info("Status {} of termination {} has no handler yet", latestTerminationStatus, terminationId);
                break;

        }
    }

    private TrxApprovalDetail getLastApprovalDetail(Set<TrxApprovalDetail> approvalDetails) {
        int approvalDetailsSize = approvalDetails.size();
        return approvalDetailsSize > 0 ?
                approvalDetails.stream().toList().get(approvalDetailsSize - 1) : null;
    }

    private Map<String, String> getLastApproverInfo(TrxTermination termination) {
        Set<TrxApprovalDetail> approvalDetails = termination.getApprovalHeader().getApprovalDetails();
        TrxApprovalDetail lastApproval = getLastApprovalDetail(approvalDetails);
        String lastApproverName = "";
        String approvalRemarks = "";
        if (lastApproval != null && lastApproval.getActionBy() != null) {
            lastApproverName = lastApproval.getActionBy().getName();
            approvalRemarks = lastApproval.getRemarks();
        }

        return Map.of(
                "lastApproverName", lastApproverName,
                "approvalRemarks", approvalRemarks
        );
    }

    private void sendNextApproverNotifications(AgentTerminationEvent event) {
        // Get the list of next approver user IDs
        List<String> approverUserIds = event.getNextApproverUserIds();

        if (approverUserIds != null && !approverUserIds.isEmpty()) {
            // For each approver user ID
            for (String userId : approverUserIds) {
                // Find the user by username
                User approver = userRepository.findByUsername(userId).orElse(null);

                if (approver != null) {
                    // Send Firebase notification if the user has a device token
                    NotificationDto notification = new NotificationDto();
                    notification.setData(Map.of(
                            "approvalId", event.getApprovalHeaderId().toString(),
                            "trxId", event.getTerminationId().toString(),
                            "trxType", event.getTrxType().name()));
                    notification.setTitle(event.getNotificationTitle());
                    notification.setBody(event.getNotificationBody());
                    notification.setInboxType(InboxType.INBOX);

                    firebaseService.sendNotification(List.of(approver), notification);

                    log.info("Approval request notification sent to approver: {} ({})", approver.getUsername(),
                            approver.getEmail());
                } else {
                    log.warn("Could not find approver with ID: {} or email is missing", userId);
                }
            }
        } else {
            log.warn("No next approver user IDs found for termination ID: {}", event.getTerminationId());
        }
    }

    private void sendEmailToCasReviewer(AgentTerminationEvent event, TrxTermination termination) {
        List<User> casReviewer = userRepository.findUsersByRoleCode("ROLE_CAS_REVIEW_AGE");
        List<String> recipients = new ArrayList<>();
        for (User user : casReviewer) {
            if (event.getNextApproverUserIds().contains(user.getUsername())) {
                recipients.add(user.getEmail());
            }
        }
        trxTerminationService.sendEmail(recipients, NEW_SUBMISSION_TO_CAS_REVIEWER, DEFAULT_TEMPLATE, Map.of());
    }

    private void sendInboxNotificationToRequester(AgentTerminationEvent event, TrxTermination termination) {
        User target = termination.getTarget();

        NotificationDto notification = NotificationDto.builder()
                .inboxType(InboxType.INBOX)
                .data(Map.of(
                        "approvalId", termination.getApprovalHeader().getId().toString(),
                        "trxId", termination.getId().toString(),
                        "trxType", event.getTrxType().name()))
                .build();

        switch (termination.getStatus()) {
            case COMPLETE -> {
                notification.setTitle("Pengajuan terminasi telah disetujui");
                notification.setBody(
                        String.format(
                                TerminationNotificationTemplates.Inbox.REQUESTER_REQUEST_APPROVED,
                                target.getName(),
                                target.getAgent().getAgentCode()));
            }
            case REJECTED -> {
                Map<String, String> lastApprovalDetails = getLastApproverInfo(termination);
                notification.setTitle("Pengajuan terminasi telah ditolak");
                notification.setBody(
                        String.format(
                                TerminationNotificationTemplates.Inbox.REQUESTER_REQUEST_REJECTED,
                                target.getName(),
                                target.getAgent().getAgentCode(),
                                lastApprovalDetails.get("lastApproverName"),
                                lastApprovalDetails.get("approvalRemarks")));
            }
            case DIKEMBALIKAN -> {
                Map<String, String> lastApprovalDetails = getLastApproverInfo(termination);
                notification.setTitle("Pengajuan terminasi diterima");
                notification.setBody(
                        String.format(
                                TerminationNotificationTemplates.Inbox.REQUESTER_REQUEST_RETURNED,
                                target.getName(),
                                target.getAgent().getAgentCode(),
                                lastApprovalDetails.get("lastApproverName"),
                                lastApprovalDetails.get("approvalRemarks")));
            }
        }

        firebaseService.sendNotification(List.of(termination.getRequester()), notification);
    }

    private void sendEmailToRelatedDirectors(TrxTermination termination) {
        Agent terminationTarget = termination.getTarget().getAgent();
        Agent policyTransferTarget = termination.getTrxPolicyTransfer().getTarget().getAgent();

        List<String> directorsRoleCodes = List.of("ROLE_AGE_BDM", "ROLE_AGE_ABDD", "ROLE_AGE_BDD");
        Set<TrxApprovalDetail> approvalDetails = termination.getApprovalHeader().getApprovalDetails();

        List<String> recipients = approvalDetails.stream()
                .map(TrxApprovalDetail::getActionBy)
                .filter(actionBy -> {
                    for (Role actorRole : actionBy.getRoles()) {
                        if (directorsRoleCodes.contains(actorRole.getCode())) {
                            return true;
                        }
                    }
                    return false;
                })
                .map(User::getEmail)
                .toList();

        if (recipients.isEmpty()) {
            log.info("No related directors found to be notified about termination {} with status {}",
                    termination.getId(), termination.getStatus());
            return;
        }

        switch (termination.getStatus()) {
            case COMPLETE -> {
                trxTerminationService.sendEmail(
                        recipients,
                        TO_BDM_ABDD_BDD_APPROVED_BY_CAS,
                        DEFAULT_TEMPLATE,
                        Map.of(
                                "agent_name", terminationTarget.getAgentName(),
                                "agent_code", terminationTarget.getAgentCode()));
                trxTerminationService.sendEmail(
                        recipients,
                        TO_BDM_ABDD_BDD__POLICY_TRANSFER__APPROVED_BY_CAS,
                        DEFAULT_TEMPLATE,
                        Map.of(
                                "source_agent_name", terminationTarget.getAgentName(),
                                "source_agent_code", terminationTarget.getAgentCode(),
                                "target_agent_name", policyTransferTarget.getAgentName(),
                                "target_agent_code", policyTransferTarget.getAgentCode()));
            }
            case REJECTED -> {
                TrxApprovalDetail lastApproval = getLastApprovalDetail(approvalDetails);
                String lastApproverName = "";
                String remarks = "";
                if (lastApproval != null) {
                    lastApproverName = lastApproval.getActionBy().getName();
                    remarks = lastApproval.getRemarks();
                }
                trxTerminationService.sendEmail(
                        recipients,
                        TO_BDM_ABDD_BDD_REJECTED_BY_CAS,
                        DEFAULT_TEMPLATE,
                        Map.of(
                                "agent_name", terminationTarget.getAgentName(),
                                "agent_code", terminationTarget.getAgentCode(),
                                "approver_name", lastApproverName,
                                "remarks", remarks));
            }
        }
    }

    private void submitTerminationToPortal(TrxTermination termination) {
        User policyTransferTarget = termination.getTrxPolicyTransfer().getTarget();
        User policyTransferCreator = termination.getTrxPolicyTransfer().getAssignedBy();
        User userToBeTerminated = termination.getTarget();
        String targetTerminationAgentCode = userToBeTerminated.getAgent().getAgentCode();

        // check if the target of policy transfer is the creator (means the leader)
        boolean isServicing = !policyTransferTarget.getId().equals(policyTransferCreator.getId());
        String targetPolicyTransferAgentCode = isServicing ? null : policyTransferTarget.getAgent().getAgentCode();

        String channelSystemName = globalConfigService.getGlobalConfig("compass.user", "UAT-QX");

        PortalAgentTerminationRequestDto requestDto = PortalAgentTerminationRequestDto.builder()
                .agentCode(targetTerminationAgentCode)
                .servicingAgent(targetPolicyTransferAgentCode)
                .creby(channelSystemName)
                .isServicing(isServicing)
                .build();

        try {
            Call<PortalAgentTerminationResponseDto> call = portalProvider.terminateAgent(requestDto);
            Response<PortalAgentTerminationResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalAgentTerminationResponseDto body = response.body();
                if (body.getStatusCode().equals("200")) {
                    log.info("Agent {} was successfully terminated in portal system.",
                            targetTerminationAgentCode);
                } else {
                    log.warn("Failed to terminate agent in portal {}",
                            objectMapper.writeValueAsString(body));
                }
            } else {
                log.error("Termination in portal was failed.");
            }
        } catch (Exception e) {
            log.error("Error sending termination request to portal system for termination ID: {}", termination.getId());
        }
    }

    // TODO this is kept if in the future will be used
    private void sendEmailToTerminatedAgent(TrxTermination termination) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM yyyy");
            String formattedEffectiveDate = formatter.format(termination.getEffectiveDate());
            String place = "";
            String letterNumber = "";

            User target = termination.getTarget();
            Agent targetAgent = target.getAgent();

            if (targetAgent == null) {
                throw new IllegalStateException("Target agent is null");
            }

            String address = target.getAgent().getAddress();

            Branch branch = branchRepository.findByBranchCode(targetAgent.getBranchCode())
                    .orElse(null);
            String branchName = branch != null ? branch.getBranchName() : "";

            Map<String, Object> pdfData = Map.of(
                    "placeAndDate", String.format("%s, %s", place, formattedEffectiveDate),
                    "letterNumber", letterNumber,
                    "agentName", target.getName().toUpperCase(),
                    "address1", address,
                    "agentCode", targetAgent.getAgentCode(),
                    "branchName", branchName,
                    "effectiveDate", String.format("%s, %s", place, formattedEffectiveDate)
            );

            byte[] pdfBytes = pdfService.generateDocumentPdfAsBytes(DocumentType.TERMINATION_LETTER, pdfData);

            Attachments attachments = new Attachments();
            attachments.setFilename("Pengakhiran Perjanjian Keagenan Asuransi Jiwa.pdf");
            attachments.setType("application/pdf");
            attachments.setDisposition("attachment");
            attachments.setContent(Base64.getEncoder().encodeToString(pdfBytes));

            Map<String, Object> emailData = Map.of("agent_name", target.getName());
            trxTerminationService.sendEmail(
                    List.of(target.getEmail()), TO_TERMINATION_TARGET_APPROVED_BY_CAS, DEFAULT_TEMPLATE, emailData,
                    attachments);
        } catch (Exception e) {
            log.warn("Unable to generate pdf or sending email for termination: {}", e.getMessage());
        }
    }

}
