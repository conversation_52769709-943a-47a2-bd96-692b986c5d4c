package id.co.panindaiichilife.superapp.agent.model.event;

import id.co.panindaiichilife.superapp.agent.config.kafka.KafkaEvent;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.InboxType;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@KafkaEvent
public class AgentTerminationEvent {
    private String eventId;
    private Long terminationId;
    private String targetAgentCode;
    private TrxType trxType;
    private ApprovalStatus approvalStatus;
    private TrxStatus trxStatus;
    private Instant timestamp;
    private List<String> nextApproverUserIds; // List of user IDs for next approvers
    private Long approvalHeaderId;
    private String notificationTitle;
    private String notificationBody;
    private String reviewer;
    private String notes;
    private InboxType inboxType;
}
