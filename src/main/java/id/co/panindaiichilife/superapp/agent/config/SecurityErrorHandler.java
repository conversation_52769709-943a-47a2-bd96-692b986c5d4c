package id.co.panindaiichilife.superapp.agent.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;

@Component
public class SecurityErrorHandler {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // Custom Authentication Entry Point
    public AuthenticationEntryPoint customAuthenticationEntryPoint() {
        return (HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) -> {
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("error", "UnauthorizedException");
            data.put("errorDescription", "Invalid credentials");

            OBJECT_MAPPER.writeValue(response.getOutputStream(), data);
            response.getOutputStream().flush();
        };
    }

    // Custom Access Denied Handler
    public AccessDeniedHandler customAccessDeniedHandler() {
        return (HttpServletRequest request, HttpServletResponse response, org.springframework.security.access.AccessDeniedException accessDeniedException) -> {
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);

            Map<String, Object> data = new LinkedHashMap<>();
            data.put("error", "ForbiddenException");
            data.put("errorDescription", "Forbidden access");

            OBJECT_MAPPER.writeValue(response.getOutputStream(), data);
            response.getOutputStream().flush();
        };
    }

}
