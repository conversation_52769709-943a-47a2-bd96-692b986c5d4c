package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.model.Training;

import java.util.List;
import java.util.Optional;

public interface TrainingRepository extends BaseRepository<Training, Long> {
    Optional<Training> findByCode(String code);

    List<Training> findByChannel(Channel channel);
}
