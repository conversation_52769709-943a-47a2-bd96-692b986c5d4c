package id.co.panindaiichilife.superapp.agent.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@SuppressWarnings("java:S115") // Suppress naming convention rule - enum values depend on external system
public enum ClaimStatus {
    Proses_Pengam<PERSON>an_Keputusan(ClaimStatusGroup.BERLANGSUNG),
    Proses_Veri<PERSON>i(ClaimStatusGroup.BERLANGSUNG),
    Pending_Dokumen_dan_Pending_Investigasi(ClaimStatusGroup.BERLANGSUNG),
    Pending_Dokumen(ClaimStatusGroup.BERLANGSUNG),
    Pending_Investigasi(ClaimStatusGroup.BERLANGSUNG),
    Paid(ClaimStatusGroup.DISETUJUI),
    Reject(ClaimStatusGroup.DITOLAK);

    @Getter
    private final ClaimStatusGroup group;

    ClaimStatus(ClaimStatusGroup group) {
        this.group = group;
    }

    /**
     * Get all statuses that belong to a specific group
     *
     * @param group The group to filter by
     * @return Array of ClaimStatus that belong to the specified group
     */
    public static ClaimStatus[] getStatusesByGroup(ClaimStatusGroup group) {
        return java.util.Arrays.stream(values())
                .filter(status -> status.getGroup() == group)
                .toArray(ClaimStatus[]::new);
    }

    /**
     * Convert a comma-separated string of status names to an array of ClaimStatus
     *
     * @param statusString Comma-separated string of status names
     * @return Array of ClaimStatus
     */
    public static ClaimStatus[] fromString(String statusString) {
        if (statusString == null || statusString.trim().isEmpty()) {
            return new ClaimStatus[0];
        }

        String[] statusNames = statusString.split("-");
        return java.util.Arrays.stream(statusNames)
                .map(String::trim)
                .map(ClaimStatus::valueOf)
                .toArray(ClaimStatus[]::new);
    }

    /**
     * Convert an array of ClaimStatus to a comma-separated string
     *
     * @param statuses Array of ClaimStatus
     * @return Comma-separated string of status names
     */
    public static String toString(ClaimStatus[] statuses) {
        if (statuses == null || statuses.length == 0) {
            return "";
        }

        return java.util.Arrays.stream(statuses)
                .map(Enum::name)
                .collect(java.util.stream.Collectors.joining("-"));
    }
}