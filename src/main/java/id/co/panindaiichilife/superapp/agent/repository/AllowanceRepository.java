package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.Allowance;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface AllowanceRepository extends BaseRepository<Allowance, Long> {

    Allowance findByAgentCode(String agentCode);

    List<Allowance> findByLeaderCode(String leaderCode);

    List<Allowance> findByAgentCodeAndYearAndMonth(String agentCode, int year, int month);

    @Query("SELECT DISTINCT c.periode FROM Allowance c WHERE c.agentCode = :agentCode AND c.year = :year AND c.month = :month")
    String findPeriodeByAgentAndYearAndMonth(@Param("agentCode") String agentCode,
                                             @Param("year") Integer year,
                                             @Param("month") Integer month);

    @Query("SELECT SUM(c.amount) FROM Allowance c WHERE c.agentCode = :agentCode AND c.year = :year AND c.month = :month AND c.type = :type")
    Double sumAmountByPeriodeAndType(@Param("agentCode") String agentCode,
                                     @Param("year") Integer year,
                                     @Param("month") Integer month,
                                     @Param("type") String type);

    @Modifying
    @Transactional
    @Query("DELETE FROM Allowance a WHERE a.year = :year AND a.month = :month")
    void deleteByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

    long countByYearAndMonth(Integer year, Integer month);

}
