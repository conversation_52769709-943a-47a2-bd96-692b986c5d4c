package id.co.panindaiichilife.superapp.agent.service.agency.validator;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationAmlDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationAmlResponseDto;
import id.co.panindaiichilife.superapp.agent.enums.ValidationAmlStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;

/**
 * Validator for checking AML (Anti-Money Laundering) status
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AmlValidator extends AbstractRecruitmentValidator {

    private final PortalProvider portalProvider;
    private final GlobalConfigService globalConfigService;

    @Override
    public boolean canValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        return StringUtils.isNotBlank(entity.getFullName());
    }

    @Override
    protected TrxRecruitment doValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        PortalValidationAmlResponseDto response = validateAmlStatus(entity.getFullName());

        if (response != null) {
            // Determine AML status based on match count
            if (response.getMatchCount() > 0) {
                entity.setValidationAmlStatus(ValidationAmlStatus.MATCH);
                log.info("AML validation found {} matches for name {}", response.getMatchCount(), entity.getFullName());
            } else {
                entity.setValidationAmlStatus(ValidationAmlStatus.PASS);
                log.info("AML validation found no matches for name {}", entity.getFullName());
            }
        } else {
            log.warn("AML validation skipped or failed for name {}", entity.getFullName());
        }

        return entity;
    }

    @Override
    public String getValidatorName() {
        return "AML Validator";
    }

    /**
     * Validates AML status for a recruitment candidate
     *
     * @param fullName The full name of the candidate
     * @return The AML validation response from the portal
     */
    private PortalValidationAmlResponseDto validateAmlStatus(String fullName) {
        try {
            if (StringUtils.isBlank(fullName)) {
                log.warn("AML validation skipped - missing required data");
                return null;
            }

            String user = globalConfigService.getGlobalConfig("plias.aml.user", "UAT-QX");
            PortalValidationAmlDto request = new PortalValidationAmlDto();
            request.setName(fullName);
            request.setUser(user);

            // Call the portal API to validate AML status
            Call<PortalValidationAmlResponseDto> call = portalProvider.validateAml(request);
            Response<PortalValidationAmlResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                return response.body();
            } else {
                log.error("Failed to validate AML status: {}", response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                // Return an empty response with no matches to avoid blocking the recruitment process
                PortalValidationAmlResponseDto emptyResponse = new PortalValidationAmlResponseDto();
                emptyResponse.setMatchCount(0);
                return emptyResponse;
            }
        } catch (Exception e) {
            log.error("Error validating AML status", e);
            // Return an empty response with no matches to avoid blocking the recruitment process
            PortalValidationAmlResponseDto emptyResponse = new PortalValidationAmlResponseDto();
            emptyResponse.setMatchCount(0);
            return emptyResponse;
        }
    }
}
