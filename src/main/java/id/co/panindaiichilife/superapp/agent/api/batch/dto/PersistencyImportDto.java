package id.co.panindaiichilife.superapp.agent.api.batch.dto;

import id.co.panindaiichilife.superapp.agent.core.support.annotation.Header;
import lombok.Data;

@Data
public class PersistencyImportDto {

    @Header("AgentCode")
    private String agentCode;

    @Header("DistributionCode")
    private String distributionCode;

    @Header("BranchCode")
    private String branchCode;

    @Header("MainBranchCode")
    private String mainBranchCode;

    @Header("BDMCode")
    private String bdmCode;

    @Header("BDMName")
    private String bdmName;

    @Header("ABDDCode")
    private String abddCode;

    @Header("ABDDName")
    private String abddName;

    @Header("BDDCode")
    private String bddCode;

    @Header("BDDName")
    private String bddName;

    @Header("HOSCode")
    private String hosCode;

    @Header("HOSName")
    private String hosName;

    @Header("Year")
    private Integer year;

    @Header("Type")
    private String type;

    @Header("PersistencyType")
    private String persistencyType;

    @Header("Persistency")
    private Double persistency;
}