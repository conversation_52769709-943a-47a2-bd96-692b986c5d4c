package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.ClaimDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ClaimFilter;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalClaimResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ClaimService {

    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    private final PortalProvider portalProvider;

    /**
     * Get claim list for an agent
     *
     * @param username The username of the agent
     * @param filter   The filter parameters
     * @return List of claim DTOs
     */
    @CacheableWithTTL(cacheName = "claimListCache",
            key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.startDate) ? #filter.startDate : 'default') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.endDate) ? #filter.endDate : 'default') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.status) ? #filter.status : '') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.statusGroup) ? #filter.statusGroup : '') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.withDownline) ? #filter.withDownline : 0)",
            ttl = 1600, db = 7)
    public List<ClaimDto> getClaimList(String username, ClaimFilter filter) {
        // Get the agent information from the username
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // If agentCode is not provided in the filter, use the agent's code
        String agentCode = StringUtils.isBlank(filter.getAgentCode())
                ? agent.getAgentCode()
                : filter.getAgentCode();

        // Trim -D suffix from agentCode if present
        agentCode = AgentCodeUtil.trimDSuffix(agentCode);

        String dateFrom = filter.getStartDate().toString();
        String dateTo = filter.getEndDate().toString();

        // Set pagination parameters
        Integer pageIndex = 0;
        Integer pageSize = null;

        // Get status string based on filter
        String status = filter.getStatusString();

        Integer withDownline = filter.getWithDownline() != null ? filter.getWithDownline() : 0;

        // Call the portal API
        Call<PortalClaimResponseDto> call = portalProvider.getClaimList(
                agentCode,
                dateFrom,
                dateTo,
                pageIndex,
                pageSize,
                status,
                withDownline
        );

        try {
            Response<PortalClaimResponseDto> response = call.execute();
            if (response.isSuccessful()) {
                PortalClaimResponseDto responseDto = response.body();
                if (responseDto != null && responseDto.getClaimsTrackings() != null) {
                    // Convert the response data to ClaimDto objects
                    return responseDto.getClaimsTrackings().stream()
                            .map(this::convertToClaimDto)
                            .collect(Collectors.toList());
                }
            }
            // Return empty list if response is not successful or body is null
            return new ArrayList<>();
        } catch (IOException e) {
            log.error("Error occurred while retrieving claim list", e);
            throw new InternalServerErrorException("Error occurred while retrieving claim list");
        }
    }

    /**
     * Convert PortalClaimResponseDto.ClaimTrackingDto to ClaimDto
     */
    private ClaimDto convertToClaimDto(PortalClaimResponseDto.ClaimTrackingDto claimData) {
        ClaimDto claimDto = new ClaimDto();
        BeanUtils.copyProperties(claimData, claimDto);
        return claimDto;
    }
}
