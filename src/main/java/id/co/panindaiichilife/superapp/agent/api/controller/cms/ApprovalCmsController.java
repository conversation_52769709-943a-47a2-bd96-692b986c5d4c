package id.co.panindaiichilife.superapp.agent.api.controller.cms;

import com.fasterxml.jackson.databind.ObjectMapper;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRecruitmentDto;
import id.co.panindaiichilife.superapp.agent.api.dto.agency.TrxRejoinApplicationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.batch.JobCreationDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.ApprovalHeaderDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ApprovalFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.ApprovalHeaderFilter;
import id.co.panindaiichilife.superapp.agent.api.filter.HistoryApprovalFilter;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentValidationForm;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinValidationForm;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ApprovalForm;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.core.service.BatchJobService;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import id.co.panindaiichilife.superapp.agent.service.ApprovalService;
import id.co.panindaiichilife.superapp.agent.service.NotificationService;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRecruitmentService;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRejoinService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@RestController("cmsApprovalController")
@RequestMapping("/api/cms/approval")
@Tag(name = "Approval - CMS", description = "API CMS Approval")
@Slf4j
@RequiredArgsConstructor
public class ApprovalCmsController {

    private final ApprovalService approvalService;

    private final NotificationService notificationService;

    private final AmazonS3Service amazonS3Service;

    private final BatchJobService batchJobService;

    @Qualifier("approvalExportJob")
    private final Job approvalExportJob;

    private final TrxRecruitmentService trxRecruitmentService;

    private final ObjectMapper objectMapper;
    private final TrxRejoinService trxRejoinService;

    @Operation(summary = "List All Approval")
    @GetMapping("all")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'view')")
    public Page<ApprovalHeaderDto> getAllApprovalList(Principal principal,
                                                      @ParameterObject @ModelAttribute("filter") ApprovalHeaderFilter filter,
                                                      @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        return approvalService.findAll(pageable, filter);
    }

    @Operation(summary = "List Pending Approval")
    @GetMapping("")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'view')")
    public Page<ApprovalHeaderDto> getApprovalList(Principal principal,
                                                   @ParameterObject @ModelAttribute("filter") ApprovalFilter filter,
                                                   @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.ASC) Pageable pageable) {
        return approvalService.getPendingApprovalsForUser(principal.getName(), filter, pageable, Boolean.FALSE);
    }

    @Operation(summary = "History Approval")
    @GetMapping("history")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'view')")
    public Page<ApprovalHeaderDto> getHistoryApproval(Principal principal,
                                                      @ParameterObject @ModelAttribute("filter") HistoryApprovalFilter filter,
                                                      @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        if (filter.getUser() == null) {
            filter.setUser(principal.getName());
        }

        return approvalService.getHistoryApproval(filter, pageable);
    }

    @Operation(summary = "View specific Approval")
    @GetMapping(value = "{id}")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'view')")
    public ApprovalHeaderDto view(@PathVariable long id) {
        return approvalService.findOne(id);
    }

    @Operation(summary = "Process Approval")
    @PostMapping(value = "")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'approve')")
    public ApprovalHeaderDto processApproval(Principal principal, @Valid @RequestBody ApprovalForm form) {
        return approvalService.processApproval(form, principal.getName());
    }

    @Operation(summary = "Manually trigger notification for approval header")
    @PostMapping(value = "{id}/send-notification")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'approve')")
    public ResponseEntity<Map<String, String>> sendNotification(@PathVariable Long id) {
        try {
            // Find the approval header
            ApprovalHeaderDto approvalHeader = approvalService.findOne(id);
            if (approvalHeader == null) {
                return ResponseEntity.notFound().build();
            }

            // Get the actual TrxApprovalHeader entity for notification
            TrxApprovalHeader header = approvalService.getTrxApprovalHeaderById(id);

            // Also trigger portal system update for DISETUJUI recruitment transactions
            // If this fails, the entire operation will fail
            if (header.getApprovalStatus() == ApprovalStatus.DISETUJUI && isRecruitmentTransaction(header.getTrxType())) {
                log.info("Triggering portal system update for DISETUJUI recruitment ID: {}", header.getTrxId());
                trxRecruitmentService.handlePortalSystemUpdate(header.getTrxId());
                log.info("Portal system update completed successfully for recruitment ID: {}", header.getTrxId());
            }

            Map<String, String> response = Map.of(
                    "message", "Notification sent successfully" + (header.getApprovalStatus() == ApprovalStatus.DISETUJUI && isRecruitmentTransaction(header.getTrxType()) ? " and portal system update completed" : ""),
                    "approvalId", id.toString(),
                    "status", "success"
            );

            // Trigger manual notification first
            notificationService.sendNotification(header);

            log.info("Manual notification triggered for approval header ID: {}", id);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error sending manual notification for approval header ID: {}", id, e);
            throw new BadRequestException(e.getMessage());
        }
    }

    @Operation(summary = "Update Recruitment Validation Statuses")
    @PatchMapping(value = "recruitment/{uuid}/validation-statuses")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'validate')")
    public TrxRecruitmentDto updateValidationStatuses(
            @PathVariable String uuid,
            Principal principal,
            @Valid @RequestBody TrxRecruitmentValidationForm validationForm) {
        return trxRecruitmentService.updateValidationStatuses(principal.getName(), uuid, validationForm);
    }

    @Operation(summary = "Update Rejoin Validation Statuses")
    @PatchMapping(value = "rejoin/{id}/validation-statuses")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'validate')")
    public TrxRejoinApplicationDto updateValidationStatuses(
            @PathVariable Long id,
            Principal principal,
            @Valid @RequestBody TrxRejoinValidationForm validationForm) {
        return trxRejoinService.updateValidationStatuses(principal.getName(), id, validationForm);
    }

    @PostMapping("/export")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.cms.Approval', 'export')")
    public JobCreationDto export(@ParameterObject @ModelAttribute("filter") ApprovalFilter filter,
                                 @ParameterObject @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) throws Exception {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        String filename = "approval_" + LocalDateTime.now().format(formatter) + ".xlsx";
        String path = "assets/export/approval/";
        String resourceUri = amazonS3Service.getUrl(path + filename);

        // Serialize filter and sort to JSON
        String filterJson = objectMapper.writeValueAsString(filter);
        String sortJson = objectMapper.writeValueAsString(pageable.getSort());

        // Create job parameters
        JobParameters parameters = new JobParametersBuilder()
                .addString("s3FolderPath", path)
                .addString("s3FileName", filename)
                .addString("filter", filterJson) // Pass filter as JSON string
                .addString("sort", sortJson) // Pass sort as JSON string
                .toJobParameters();

        // Run the job
        JobExecution jobExecution = batchJobService.run(approvalExportJob, parameters, true);

        return JobCreationDto.builder()
                .id(jobExecution.getId())
                .status(jobExecution.getStatus())
                .resourceUri(resourceUri)
                .monitorUrl("/api/cms/monitoring/batch/export/" + jobExecution.getId())
                .build();
    }

    /**
     * Checks if the transaction type is a recruitment transaction
     *
     * @param trxType The transaction type
     * @return true if it's a recruitment transaction
     */
    private boolean isRecruitmentTransaction(TrxType trxType) {
        return trxType == TrxType.RECRUITMENT_BP ||
                trxType == TrxType.RECRUITMENT_BM ||
                trxType == TrxType.RECRUITMENT_BD;
    }
}
