package id.co.panindaiichilife.superapp.agent.config;

import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.core.security.AuthenticationSuccessHandlerImpl;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionService;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionSuperAppService;
import id.co.panindaiichilife.superapp.agent.core.security.ForgetPasswordService;
import id.co.panindaiichilife.superapp.agent.core.security.ForgetPasswordServiceImpl;
import id.co.panindaiichilife.superapp.agent.core.service.JwtService;
import id.co.panindaiichilife.superapp.agent.core.service.MailService;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

@Configuration
@RequiredArgsConstructor
public class SecurityConfig {

    private final RegisteredClientRepository registeredClientRepository;
    private final UserService userService;
    private final JwtEncoder jwtEncoder;
    private final EncryptionSuperAppService encryptionService;
    private final PortalProvider portalProvider;
    private final EncryptionService encryptionService2;
    private final UserRepository userRepository;
    private final MessageSource messageSource;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final MailService mailService;
    private final ResourceLoader resourceLoader;
    private final GlobalConfigService globalConfigService;

    @Bean(name = "dbAuthenticationProvider")
    public AuthenticationProvider dbAuthenticationProvider() {
        DbAuthenticationProvider provider = new DbAuthenticationProvider(
                registeredClientRepository,
                userService,
                jwtEncoder,
                encryptionService
        );
        provider.setPasswordEncoder(passwordEncoder);
        provider.setUserDetailsService(userDetailsService());
        return provider;
    }

    @Bean(name = "portalAuthenticationProvider")
    public AuthenticationProvider portalAuthenticationProvider() {
        return new PortalAuthenticationProvider(
            registeredClientRepository,
            jwtEncoder,
            portalProvider,
            userService,
            encryptionService2,
            encryptionService
        );
    }

    @Bean(name = "customAuthenticationProvider")
    public AuthenticationProvider customAuthenticationProvider() {
        return new CustomAuthenticationProvider(
            portalAuthenticationProvider(),
            dbAuthenticationProvider(),
            userService,
            encryptionService
        );
    }

    @Bean
    public AuthenticationSuccessHandler authenticationSuccessHandler() {
        return new AuthenticationSuccessHandlerImpl(userRepository);
    }

    @Bean
    public ForgetPasswordService forgetPasswordService() {
        return new ForgetPasswordServiceImpl(
            jwtService,
            mailService,
            userRepository,
            resourceLoader,
            globalConfigService
        );
    }

    @Bean
    public UserDetailsService userDetailsService() {
        return new DbUserDetailServiceImpl(userRepository, messageSource);
    }

    @Bean
    public AuthenticationFailureHandler authenticationFailureHandler() {
        AuthenticationFailureHandlerImpl aclFailureHandlerImpl = new AuthenticationFailureHandlerImpl();
        aclFailureHandlerImpl.setAllowSessionCreation(true);
        aclFailureHandlerImpl.setUseForward(true);
        return aclFailureHandlerImpl;
    }
}
