package id.co.panindaiichilife.superapp.agent.api.dto;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.RoleDto;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.UserType;
import id.co.panindaiichilife.superapp.agent.model.User;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
public class ProfileDto extends BaseDto<User> {

    private Long id;

    private String username;

    private String name;

    private String email;

    private String phone;

    private String timezone;

    private String picture;

    private UserType userType;

    private Set<RoleDto> roles = new HashSet<>();

    private Instant created;

    private Instant modified;

    @Override
    public void copy(User data) {
        super.copy(data);
        roles = BaseDto.of(RoleDto.class, data.getRoles());
    }
}
