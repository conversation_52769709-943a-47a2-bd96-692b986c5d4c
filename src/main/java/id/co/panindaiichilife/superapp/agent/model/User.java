package id.co.panindaiichilife.superapp.agent.model;

import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.UserType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Set;

@Entity
@Table(name = "users", uniqueConstraints = {
        @UniqueConstraint(name = "uk_users_username", columnNames = "username")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id", "username"})
@SQLDelete(sql = "UPDATE users SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class User extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "users_id_seq")
    @SequenceGenerator(name = "users_id_seq", sequenceName = "users_id_seq", allocationSize = 1)
    private Long id;

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private Agent agent;

    @Audited
    @Column(nullable = false)
    private String username;

    @Column
    private String password;

    @Audited
    @Column(nullable = false)
    private String name;

    @Audited
    private String timezone;

    @Audited
    private String email;

    @Audited
    private String phone;

    private String picture;

    private Boolean isAgent = Boolean.FALSE;

    @Audited
    @Enumerated(EnumType.STRING)
    private Channel channel;

    @Audited
    @Enumerated(EnumType.STRING)
    private UserType userType;

    @ManyToMany
    @JoinTable(
            name = "user_role",
            joinColumns = {
                    @JoinColumn(name = "id_user")},
            inverseJoinColumns = {
                    @JoinColumn(name = "id_role")}
    )
    private Set<Role> roles;

    @ManyToMany
    @JoinTable(
            name = "user_branch",
            joinColumns = {
                    @JoinColumn(name = "id_user")},
            inverseJoinColumns = {
                    @JoinColumn(name = "id_branch")}
    )
    @OrderBy("branchName")
    private Set<Branch> branches;

    @Audited
    @Enumerated(EnumType.STRING)
    private Status status;

    @Audited
    private Instant lastLogin;

    @Audited
    private Instant lastChangePassword;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;

    public ZoneId getZoneId() {
        return ZoneId.of(timezone);
    }

    public LocalDate getCurrentDate() {
        return LocalDate.now(getZoneId());
    }

    public LocalDateTime getCurrentDateTime() {
        return LocalDateTime.now(getZoneId());
    }

    @SuppressWarnings("java:S115") // Suppress naming convention rule - enum values depend on external system
    public enum Status {
        Active, Inactive, Suspend
    }
}
