package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalTrainingResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("trainingList")
    @JsonProperty("trainingList")
    private List<TrainingDto> trainingList;


    @Data
    public static class TrainingDto {

        @SerializedName("code")
        @JsonProperty("code")
        private String code;

        @SerializedName("name")
        @JsonProperty("name")
        private String name;

        @SerializedName("desc")
        @JsonProperty("desc")
        private String desc;
    }
}
