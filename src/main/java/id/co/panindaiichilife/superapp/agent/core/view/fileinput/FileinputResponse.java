package id.co.panindaiichilife.superapp.agent.core.view.fileinput;

import id.co.panindaiichilife.superapp.agent.core.support.HttpUtils;
import lombok.Data;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

@Data
public class FileinputResponse {

    private String error;

    private String[] initialPreview;

    private InitialPreviewConfig[] initialPreviewConfig;

    protected FileinputResponse() {

    }

    public static FileinputResponse error() {
        return error("Upload failed");
    }

    public static FileinputResponse error(String error) {
        FileinputResponse response = new FileinputResponse();
        response.setError(error);
        return response;
    }

    public static FileinputResponse success(String file) {
        FileinputResponse response = new FileinputResponse();

        String previewUrl = file;
        try {
            final URI uri = new URI(file);
            if (!uri.isAbsolute()) {
                previewUrl = HttpUtils.getBaseUrl() + "/" + file;
            }
        } catch (URISyntaxException ex) {
            // If URI parsing fails, use the original file path as preview URL
        }

        response.setInitialPreview(new String[]{previewUrl});
        response.setInitialPreviewConfig(new InitialPreviewConfig[]{
                new InitialPreviewConfig().put("url", file)
        });
        return response;
    }

    @Data
    private static class InitialPreviewConfig {

        private Map<String, Object> extra;

        public InitialPreviewConfig() {
        }

        public InitialPreviewConfig put(String key, Object value) {
            if (extra == null) {
                extra = new HashMap<>();
            }

            extra.put(key, value);
            return this;
        }
    }
}
