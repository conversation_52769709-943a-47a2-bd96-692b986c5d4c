package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.ValidasiPerG1ImportDto;
import id.co.panindaiichilife.superapp.agent.model.ValidasiPerG1;
import id.co.panindaiichilife.superapp.agent.repository.ValidasiPerG1Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class ValidasiPerG1ImportProcessor implements ItemProcessor<ValidasiPerG1ImportDto, ValidasiPerG1> {

    private final ValidasiPerG1Repository validasiPerG1Repository;


    @Override
    public ValidasiPerG1 process(ValidasiPerG1ImportDto item) {
        return findOrCreateValidasiPerG1(item);
    }

    private ValidasiPerG1 findOrCreateValidasiPerG1(ValidasiPerG1ImportDto item) {
        ValidasiPerG1 validasiPerG1 = new ValidasiPerG1();

        // Copy properties
        BeanUtils.copyProperties(item, validasiPerG1);
        validasiPerG1.setGNetApe(item.getGroupNetApe());
        validasiPerG1.setGNetApeTarget(item.getGroupNetApeTarget());
        return validasiPerG1;
    }
}