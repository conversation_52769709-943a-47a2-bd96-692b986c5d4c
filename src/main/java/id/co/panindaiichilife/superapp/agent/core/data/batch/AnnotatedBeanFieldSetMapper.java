package id.co.panindaiichilife.superapp.agent.core.data.batch;

import id.co.panindaiichilife.superapp.agent.core.support.AnnotatedBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.FieldSet;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.NumberFormat;
import org.springframework.util.NumberUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.validation.BindException;
import org.thymeleaf.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.Temporal;
import java.util.Map;

@Slf4j
public class AnnotatedBeanFieldSetMapper<E> implements FieldSetMapper<E> {

    private Class<E> type;

    private Map<String, Field> fieldMapping;

    public AnnotatedBeanFieldSetMapper(Class<E> type) {
        this.type = type;

        fieldMapping = AnnotatedBeanUtils.getImportedFields(type);
    }

    @Override
    public E mapFieldSet(FieldSet fieldSet) throws BindException {
        try {
            E bean = type.getDeclaredConstructor().newInstance();

            for (String name : fieldSet.getNames()) {
                if (!fieldMapping.containsKey(name)) {
                    continue;
                }

                Field field = fieldMapping.get(name);
                String value = fieldSet.readString(name);
                processFieldValue(bean, field, name, value);
            }

            return bean;
        } catch (ReflectiveOperationException ex) {
            ReflectionUtils.handleReflectionException(ex);
            return null;
        }
    }

    private void processFieldValue(E bean, Field field, String name, String value) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        try {
            Object resolvedValue = resolveFieldValue(field, value);
            PropertyUtils.setProperty(bean, field.getName(), resolvedValue);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            log.error("FULL ERROR - Column: {}, Field: {}, Value: {}, Field Type: {}",
                    name, field.getName(), value, field.getType(), e);
            throw e;
        }
    }

    private Object resolveFieldValue(Field field, String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }

        Class<?> fieldType = field.getType();
        try {
            if (String.class.equals(fieldType)) {
                return value;
            } else if (Number.class.isAssignableFrom(fieldType)) {
                // Remove any commas, whitespace, and handle special cases
                String cleanedValue = value.replace(",", "").trim();

                // Handle special numeric cases
                if (cleanedValue.isEmpty() ||
                        cleanedValue.equals("-") ||
                        cleanedValue.equals("N/A") ||
                        cleanedValue.equals("null")) {
                    return null;
                }

                // For Double specifically, use BigDecimal to prevent scientific notation
                if (fieldType == Double.class || fieldType == double.class) {
                    return new java.math.BigDecimal(cleanedValue).doubleValue();
                }

                return parseNumeric(field, fieldType.asSubclass(Number.class), cleanedValue);
            } else if (Temporal.class.isAssignableFrom(fieldType)) {
                return parseTemporal(field, field.getType().asSubclass(Temporal.class), value);
            } else {
                throw new UnsupportedOperationException("Unsupported field type: " + fieldType);
            }
        } catch (Exception e) {
            log.error("Error parsing value '{}' for field {} of type {}",
                    value, field.getName(), fieldType, e);
            throw new IllegalArgumentException("Unable to parse value", e);
        }
    }

    private <T extends Number> T parseNumeric(Field field, Class<T> fieldType, String value) {
        try {
            // Remove any commas and ensure proper decimal format
            String cleanedValue = value.replace(",", "");
            Number number = Double.parseDouble(cleanedValue);
            return NumberUtils.convertNumberToTargetClass(number, fieldType);
        } catch (NumberFormatException ex) {
            try {
                java.text.NumberFormat formatter;
                if (field.isAnnotationPresent(NumberFormat.class)) {
                    NumberFormat numberFormat = field.getAnnotation(NumberFormat.class);
                    formatter = new DecimalFormat(numberFormat.pattern());
                } else {
                    formatter = java.text.NumberFormat.getInstance();
                }

                Number number = formatter.parse(value);
                return NumberUtils.convertNumberToTargetClass(number, fieldType);
            } catch (ParseException parseEx) {
                throw new IllegalArgumentException("Unable to parse value: " + value, parseEx);
            }
        }
    }

    @SuppressWarnings("unchecked")
    private <T extends Temporal> T parseTemporal(Field field, Class<T> fieldType, String value) {
        DateTimeFormatter formatter = getDateTimeFormatter(field);

        try {
            return parseTemporalByType(fieldType, value, formatter);
        } catch (DateTimeParseException ex) {
            return null;
        }
    }

    private DateTimeFormatter getDateTimeFormatter(Field field) {
        if (field.isAnnotationPresent(DateTimeFormat.class)) {
            DateTimeFormat dateTimeFormat = field.getAnnotation(DateTimeFormat.class);
            return DateTimeFormatter.ofPattern(dateTimeFormat.pattern());
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    private <T extends Temporal> T parseTemporalByType(Class<T> fieldType, String value, DateTimeFormatter formatter) {
        if (fieldType.equals(LocalDate.class)) {
            return (T) parseLocalDate(value, formatter);
        } else if (fieldType.equals(LocalTime.class)) {
            return (T) parseLocalTime(value, formatter);
        } else if (fieldType.equals(LocalDateTime.class)) {
            return (T) parseLocalDateTime(value, formatter);
        } else if (fieldType.equals(ZonedDateTime.class)) {
            return (T) parseZonedDateTime(value, formatter);
        } else if (fieldType.equals(OffsetDateTime.class)) {
            return (T) parseOffsetDateTime(value, formatter);
        } else if (fieldType.equals(Instant.class)) {
            return (T) Instant.parse(value);
        } else {
            throw new UnsupportedOperationException("Unsupported temporal type: " + fieldType);
        }
    }

    private LocalDate parseLocalDate(String value, DateTimeFormatter formatter) {
        return formatter == null ? LocalDate.parse(value) : LocalDate.parse(value, formatter);
    }

    private LocalTime parseLocalTime(String value, DateTimeFormatter formatter) {
        return formatter == null ? LocalTime.parse(value) : LocalTime.parse(value, formatter);
    }

    private LocalDateTime parseLocalDateTime(String value, DateTimeFormatter formatter) {
        return formatter == null ? LocalDateTime.parse(value) : LocalDateTime.parse(value, formatter);
    }

    private ZonedDateTime parseZonedDateTime(String value, DateTimeFormatter formatter) {
        return formatter == null ? ZonedDateTime.parse(value) : ZonedDateTime.parse(value, formatter);
    }

    private OffsetDateTime parseOffsetDateTime(String value, DateTimeFormatter formatter) {
        return formatter == null ? OffsetDateTime.parse(value) : OffsetDateTime.parse(value, formatter);
    }
}
