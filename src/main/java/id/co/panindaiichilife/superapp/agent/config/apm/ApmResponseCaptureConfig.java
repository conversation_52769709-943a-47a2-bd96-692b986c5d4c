package id.co.panindaiichilife.superapp.agent.config.apm;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * Configuration for APM Response Capture functionality
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(ApmResponseCaptureProperties.class)
public class ApmResponseCaptureConfig {

    private final ApmResponseCaptureProperties properties;

    /**
     * Register the APM Response Capture Filter
     */
    @Bean
    @ConditionalOnProperty(name = "apm.response.capture.enabled", havingValue = "true", matchIfMissing = true)
    public FilterRegistrationBean<ApmResponseCaptureFilter> apmResponseCaptureFilter(ObjectMapper objectMapper) {
        log.info("Registering APM Response Capture Filter with properties: {}", properties);

        ApmResponseCaptureFilter filter = new ApmResponseCaptureFilter(properties, objectMapper);

        FilterRegistrationBean<ApmResponseCaptureFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(filter);
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(Ordered.LOWEST_PRECEDENCE - 1);
        registrationBean.setName("apmResponseCaptureFilter");

        return registrationBean;
    }
}
