package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalPolicyOverdueResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("totalData")
    @JsonProperty("totalData")
    private int totalData;

    @SerializedName("outstandingDues")
    @JsonProperty("outstandingDues")
    private List<OutstandingDueDto> outstandingDues;

    @Data
    public static class OutstandingDueDto {

        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("agentName")
        @JsonProperty("agentName")
        private String agentName;

        @SerializedName("policyNumber")
        @JsonProperty("policyNumber")
        private String policyNumber;

        @SerializedName("product")
        @JsonProperty("product")
        private String product;

        @SerializedName("policyHolder")
        @JsonProperty("policyHolder")
        private String policyHolder;

        @SerializedName("insuredName")
        @JsonProperty("insuredName")
        private String insuredName;

        @SerializedName("lastPaymentDate")
        @JsonProperty("lastPaymentDate")
        private String lastPaymentDate;

        @SerializedName("dueDate")
        @JsonProperty("dueDate")
        private String dueDate;

        @SerializedName("mobileNumber")
        @JsonProperty("mobileNumber")
        private String mobileNumber;

        @SerializedName("emailAddress")
        @JsonProperty("emailAddress")
        private String emailAddress;

        @SerializedName("paymentFrequency")
        @JsonProperty("paymentFrequency")
        private String paymentFrequency;

        @SerializedName("paymentMethod")
        @JsonProperty("paymentMethod")
        private String paymentMethod;

        @SerializedName("currency")
        @JsonProperty("currency")
        private String currency;

        @SerializedName("billingAmount")
        @JsonProperty("billingAmount")
        private double billingAmount;

        @SerializedName("policyTerm")
        @JsonProperty("policyTerm")
        private String policyTerm;

        @SerializedName("paymentTerm")
        @JsonProperty("paymentTerm")
        private String paymentTerm;

        @SerializedName("backColor")
        @JsonProperty("backColor")
        private String backColor;

        @SerializedName("flagNlg")
        @JsonProperty("flagNlg")
        private String flagNlg;

        @SerializedName("nlgBackColor")
        @JsonProperty("nlgBackColor")
        private String nlgBackColor;
    }
}
