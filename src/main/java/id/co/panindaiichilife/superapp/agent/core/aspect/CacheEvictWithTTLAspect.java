package id.co.panindaiichilife.superapp.agent.core.aspect;

import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheEvictWithTTL;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.Duration;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class CacheEvictWithTTLAspect {

    // Redis connection timeout in seconds
    private static final int REDIS_TIMEOUT_SECONDS = 1;

    private final RedisTemplate<String, Object> redisTemplate;

    private final RedisConnectionFactory redisConnectionFactory;

    @Around("@annotation(cacheEvictWithTTL)")
    public Object handleCacheEvict(ProceedingJoinPoint joinPoint, CacheEvictWithTTL cacheEvictWithTTL) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // Generate cache key to evict
        String cacheKey = generateCacheEvictionKey(method, joinPoint.getArgs(), cacheEvictWithTTL);
        int dbIndex = cacheEvictWithTTL.db();

        try {
            // Switch to specific database
            RedisTemplate<String, Object> dbSpecificTemplate = createDatabaseSpecificTemplate(dbIndex);

            // Attempt to delete the key
            Boolean deleted = dbSpecificTemplate.delete(cacheKey);
            log.info("Cache Eviction - Key: {}, DB: {}, Deleted: {}", cacheKey, dbIndex, deleted);
        } catch (RedisConnectionFailureException e) {
            // Log warning but continue with method execution
            log.warn("Redis connection failure during cache eviction for key: {}. Error: {}",
                    cacheKey, e.getMessage());
        } catch (Exception e) {
            // Handle any other Redis-related exceptions
            log.warn("Error during cache eviction for key: {}. Error: {}",
                    cacheKey, e.getMessage());
        }

        // Always proceed with the original method, even if cache eviction fails
        return joinPoint.proceed();
    }

    private String generateCacheEvictionKey(Method method, Object[] args, CacheEvictWithTTL cacheEvictWithTTL) {
        // If a specific key is provided, parse it
        if (!cacheEvictWithTTL.key().isEmpty()) {
            return cacheEvictWithTTL.cacheName() + "::" + parseKey(cacheEvictWithTTL.key(), method, args);
        }
        return cacheEvictWithTTL.cacheName().isEmpty()
                ? "default"
                : cacheEvictWithTTL.cacheName();
    }

    private String parseKey(String key, Method method, Object[] args) {
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();

        String[] parameterNames = new DefaultParameterNameDiscoverer().getParameterNames(method);

        if (parameterNames != null) {
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
        }

        Expression expression = parser.parseExpression(key);
        return expression.getValue(context, String.class);
    }

    private RedisTemplate<String, Object> createDatabaseSpecificTemplate(int dbIndex) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();

        try {
            LettuceConnectionFactory connectionFactory =
                    (LettuceConnectionFactory) createDatabaseSpecificConnectionFactory(dbIndex);
            connectionFactory.start(); // Initialize the connection factory

            template.setConnectionFactory(connectionFactory);
            template.setKeySerializer(redisTemplate.getKeySerializer());
            template.setValueSerializer(redisTemplate.getValueSerializer());
            template.setHashKeySerializer(redisTemplate.getHashKeySerializer());
            template.setHashValueSerializer(redisTemplate.getHashValueSerializer());
            template.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Failed to create database-specific Redis template for DB: {}. Error: {}",
                    dbIndex, e.getMessage());
            throw e;
        }

        return template;
    }

    private RedisConnectionFactory createDatabaseSpecificConnectionFactory(int dbIndex) {
        RedisConnectionFactory connectionFactory = redisConnectionFactory;
        if (connectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory lettuceFactory =
                    (LettuceConnectionFactory) connectionFactory;

            RedisStandaloneConfiguration configuration =
                    new RedisStandaloneConfiguration(
                            lettuceFactory.getStandaloneConfiguration().getHostName(),
                            lettuceFactory.getStandaloneConfiguration().getPort()
                    );

            // Set any authentication if required
            if (lettuceFactory.getStandaloneConfiguration().getPassword().isPresent()) {
                configuration.setPassword(lettuceFactory.getStandaloneConfiguration().getPassword());
            }

            configuration.setDatabase(dbIndex);

            // Create Lettuce client configuration with timeouts
            LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                    .commandTimeout(Duration.ofSeconds(REDIS_TIMEOUT_SECONDS))
                    .shutdownTimeout(Duration.ofSeconds(REDIS_TIMEOUT_SECONDS))
                    .build();

            // The timeout settings will be inherited from the RedisConfig
            LettuceConnectionFactory newFactory = new LettuceConnectionFactory(configuration, clientConfig);
            newFactory.afterPropertiesSet();
            return newFactory;
        }
        return connectionFactory;
    }
}