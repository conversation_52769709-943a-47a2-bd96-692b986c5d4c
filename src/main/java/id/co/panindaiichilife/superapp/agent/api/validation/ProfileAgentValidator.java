package id.co.panindaiichilife.superapp.agent.api.validation;

import id.co.panindaiichilife.superapp.agent.api.form.ProfileAgentForm;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

@Component
public class ProfileAgentValidator implements Validator {

    private static final String REQUIRED = "Required";

    @Override
    public boolean supports(Class<?> clazz) {
        return ProfileAgentForm.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        ProfileAgentForm form = (ProfileAgentForm) target;

        validateAgentNameChange(form, errors);
        validateBankAccountChange(form, errors);
        validateAddressChange(form, errors);
        validateMaritalStatusChange(form, errors);
    }

    private void validateAgentNameChange(ProfileAgentForm form, Errors errors) {
        if (isFieldNotEmpty(form.getAgentName()) && isFieldEmpty(form.getKtpAttachment())) {
            errors.rejectValue("ktpAttachment", REQUIRED, new Object[]{"KTP Attachment"},
                    "Lampiran KTP diperlukan untuk mengubah nama pengguna.");
        }
    }

    private void validateBankAccountChange(ProfileAgentForm form, Errors errors) {
        if (isFieldNotEmpty(form.getBankAccountNumber()) && isFieldEmpty(form.getBankAttachment())) {
            errors.rejectValue("bankAttachment", REQUIRED, new Object[]{"Bank Attachment"},
                    "Lampiran Buku Rekening diperlukan untuk mengubah nomor rekening bank.");
        }
    }

    private void validateAddressChange(ProfileAgentForm form, Errors errors) {
        if (isFieldNotEmpty(form.getAddress()) && isFieldEmpty(form.getKtpAttachment())) {
            errors.rejectValue("ktpAttachment", REQUIRED, new Object[]{"KTP Attachment"},
                    "Lampiran KTP diperlukan untuk mengubah alamat.");
        }
    }

    private void validateMaritalStatusChange(ProfileAgentForm form, Errors errors) {
        if (isFieldNotEmpty(form.getMaritalStatus()) && isFieldEmpty(form.getKkAttachment())) {
            errors.rejectValue("kkAttachment", REQUIRED, new Object[]{"KK Attachment"},
                    "Lampiran KK diperlukan untuk mengubah status pernikahan.");
        }
    }

    private boolean isFieldNotEmpty(String field) {
        return field != null && !field.isEmpty();
    }

    private boolean isFieldEmpty(String field) {
        return field == null || field.isEmpty();
    }
}