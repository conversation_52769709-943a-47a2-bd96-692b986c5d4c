package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.PersistencyDto;
import id.co.panindaiichilife.superapp.agent.api.filter.PersistencyFilter;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.Branch;
import id.co.panindaiichilife.superapp.agent.model.Persistency;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.PersistencyRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PersistencyService {

    private static final String TYPE_AGENT = "Agent";
    private static final String TYPE_LEADER = "Leader";
    private static final String TYPE_GROUP = "Group";

    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    private final PersistencyRepository persistencyRepository;

    @CacheableWithTTL(cacheName = "persistencyIndividuCache",
            key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.year) ? #filter.year : '')",
            ttl = 1600, db = 7)
    public PersistencyDto getPersistencyIndividual(String username, PersistencyFilter filter) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        if (StringUtils.isBlank(filter.getAgentCode())) {
            filter.setAgentCode(agent.getAgentCode());
        }
        filter.setType(TYPE_AGENT);

        return this.buildIndividualPersistencyDto(persistencyRepository.findAll(filter));
    }

    @CacheableWithTTL(cacheName = "persistencyTeamCache",
            key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.year) ? #filter.year : '')",
            ttl = 1600, db = 7)
    public List<PersistencyDto> getPersistencyTeam(String username, PersistencyFilter filter) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        // Check if user is an agent
        if (user.getIsAgent() != null && user.getIsAgent()) {

            // User is an agent, filter by agent code
            Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

            String agentCode = StringUtils.isBlank(filter.getAgentCode())
                    ? agent.getAgentCode()
                    : filter.getAgentCode();

            // Trim -D suffix from agentCode if present
            final String trimmedAgentCode = AgentCodeUtil.trimDSuffix(agentCode);

            List<Agent> members = agentRepository.findByLeaderCode(trimmedAgentCode);

            // Extract team member codes (excluding the leader)
            List<String> teamMemberCodes = members.stream()
                    .map(Agent::getAgentCode)
                    .filter(code -> !code.equals(trimmedAgentCode))
                    .collect(Collectors.toList());

            // Get leader's persistency data with type "Leader"
            List<Persistency> leaderPersistencies = persistencyRepository.findByAgentCodeAndType(trimmedAgentCode, TYPE_GROUP);

            // Get team members' persistency data with type "Agent"
            List<Persistency> teamPersistencies = persistencyRepository.findByAgentCodeInAndType(teamMemberCodes, TYPE_AGENT);

            // Combine leader and team persistencies
            List<Persistency> allPersistencies = new ArrayList<>();
            allPersistencies.addAll(leaderPersistencies);
            allPersistencies.addAll(teamPersistencies);

            return buildTeamPersistencyDto(allPersistencies, trimmedAgentCode);
        } else {
            // User is not an agent, filter by mainBranchCode
            List<Persistency> teamPersistencies = new ArrayList<>();

            // If user has assigned branches, filter by those branch codes
            if (user.getBranches() != null && !user.getBranches().isEmpty()) {
                // Extract mainBranchCodes from user's assigned branches
                List<String> branchCodes = user.getBranches().stream()
                        .map(Branch::getBranchCode)
                        .toList();

                // For each branch, find agents in that branch and get their persistency records
                for (String branchCode : branchCodes) {
                    // Get persistency records for this branch using mainBranchCode
                    List<Persistency> branchPersistencies = persistencyRepository.findByMainBranchCodeAndType(branchCode, TYPE_GROUP);
                    teamPersistencies.addAll(branchPersistencies);
                }
            }

            return buildTeamPersistencyDto(teamPersistencies, null);
        }
    }

    /**
     * Build an IndividualPersistencyResponse from a list of persistency records
     */
    private PersistencyDto buildIndividualPersistencyDto(List<Persistency> persistencies) {
        if (persistencies == null || persistencies.isEmpty()) {
            return null;
        }

        PersistencyDto.PersistencyDtoBuilder builder = PersistencyDto.builder();

        // Set basic agent info from the first record
        Persistency firstRecord = persistencies.get(0);
        Agent agent = agentRepository.findTopByAgentCode(firstRecord.getAgentCode()).orElse(null);

        builder.agentCode(firstRecord.getAgentCode());
        builder.type(firstRecord.getType());
        builder.year(firstRecord.getYear());
        builder.name(agent != null ? agent.getAgentName() : null);
        builder.agentLevel(agent != null ? agent.getPositionLevel() : null);
        builder.agentPhoto(agent != null ? agent.getPhoto() : null);

        // Set persistency values from each record
        for (Persistency p : persistencies) {
            switch (p.getPersistencyType()) {
                case "Persistency 13":
                    builder.persistency13(p.getPersistency());
                    break;
                case "Persistency 25":
                    builder.persistency25(p.getPersistency());
                    break;
                case "Persistency 37":
                    builder.persistency37(p.getPersistency());
                    break;
                case "Persistency 49":
                    builder.persistency49(p.getPersistency());
                    break;
                case "Persistency 61":
                    builder.persistency61(p.getPersistency());
                    break;
            }
        }

        return builder.build();
    }

    /**
     * Build a TeamPersistencyResponse from a list of team persistency records
     *
     * @param teamPersistencies  List of persistency records
     * @param requestorAgentCode Agent code of the requestor (should appear first), null if no specific ordering needed
     */
    private List<PersistencyDto> buildTeamPersistencyDto(List<Persistency> teamPersistencies, String requestorAgentCode) {
        if (teamPersistencies == null || teamPersistencies.isEmpty()) {
            return new ArrayList<>();
        }

        // Group persistencies by agent code
        Map<String, List<Persistency>> persistenciesByAgent = teamPersistencies.stream()
                .collect(Collectors.groupingBy(Persistency::getAgentCode));

        // Build team member DTOs
        List<PersistencyDto> teamDtos = persistenciesByAgent.entrySet().stream()
                .map(entry -> buildIndividualPersistencyDto(entry.getValue()))
                .filter(dto -> dto != null) // Filter out null DTOs
                .collect(Collectors.toList());

        // If requestorAgentCode is specified, ensure it appears first
        if (requestorAgentCode != null && !teamDtos.isEmpty()) {
            // Find the requestor's DTO and move it to index 0
            PersistencyDto requestorDto = null;
            int requestorIndex = -1;

            for (int i = 0; i < teamDtos.size(); i++) {
                if (requestorAgentCode.equals(teamDtos.get(i).getAgentCode())) {
                    requestorDto = teamDtos.get(i);
                    requestorIndex = i;
                    break;
                }
            }

            // If requestor found and not already at index 0, move to front
            if (requestorDto != null && requestorIndex > 0) {
                teamDtos.remove(requestorIndex);
                teamDtos.add(0, requestorDto);
            }
        }

        return teamDtos;
    }
}
