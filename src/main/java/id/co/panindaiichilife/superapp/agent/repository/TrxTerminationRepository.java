package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxTermination;
import id.co.panindaiichilife.superapp.agent.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface TrxTerminationRepository extends BaseRepository<TrxTermination, Long> {
    Optional<TrxTermination> findByTarget(User target);

    Optional<TrxTermination> findByTargetAndStatusOrderByUpdatedAtDesc(User target, TrxStatus status);

    List<TrxTermination> findAllByRequester(User requester);

    List<TrxTermination> findAllByStatus(TrxStatus status);

    List<TrxTermination> findAllByApprovalStatus(ApprovalStatus approvalStatus);

    List<TrxTermination> findAllByTargetInAndStatusIn(List<User> target, List<TrxStatus> status);

    @Query("""
        SELECT t FROM TrxTermination t
        WHERE 
            t.target.id = :targetId 
            AND t.status in (
                TrxStatus.IN_PROGRESS,
                TrxStatus.DRAFT,
                TrxStatus.DIKEMBALIKAN
            )
    """)
    Optional<TrxTermination> findActiveTerminationByTarget(@Param("targetId") Long targetId);

    @Query("""
        SELECT
            t FROM TrxTermination t
        WHERE 
            t.requester.id = :requesterId 
            AND (:statuses IS NULL OR t.status IN (:statuses))
            AND (:searchQuery IS NULL
                OR (
                    LOWER(t.target.name)  LIKE LOWER(CONCAT('%', :searchQuery, '%'))
                    OR LOWER(t.target.username)  LIKE LOWER(CONCAT('%', :searchQuery, '%'))
                    )
            )
    """)
    Page<TrxTermination> findTerminationRequestsByRequester(
            @Param("requesterId") Long requesterId, @Param("searchQuery") String searchQuery,
            @Param("statuses") List<TrxStatus> statuses, Pageable pageable);

    List<TrxTermination> findByLastActionAtBeforeAndStatusNotIn(Instant cutoff, List<TrxStatus> status);

    Optional<TrxTermination> findTopByTargetOrderByEffectiveDateDesc(User target);

    Optional<TrxTermination> findTopByTrxPolicyTransferId(Long policyTransferId);
}
