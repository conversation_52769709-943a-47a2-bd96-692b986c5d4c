package id.co.panindaiichilife.superapp.agent.service.agency;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import id.co.panindaiichilife.superapp.agent.enums.ValidationBankAccountStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationKtpStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAajiStatus;
import id.co.panindaiichilife.superapp.agent.enums.ValidationLicenseAasiStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import id.co.panindaiichilife.superapp.agent.service.agency.validator.rejoin.RejoinValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
public class RejoinValidationService {
    private final List<RejoinValidator> validators;

    public TrxRejoinApplication validateRejoin(TrxRejoinApplicationForm form, TrxRejoinApplication entity) {
        log.info("Starting validation for rejoin ID: {}", entity.getId());

        // Filter validators that can be applied to this rejoin
        List<RejoinValidator> applicableValidators = validators.stream()
                .filter(validator -> validator.canValidate(form, entity))
                .toList();

        log.info("Running {} validators for rejoin ID: {}", applicableValidators.size(), entity.getId());

        if (applicableValidators.isEmpty()) {
            log.warn("No applicable validators found for rejoin ID: {}", entity.getId());
            return entity;
        }

        try {
            // Run all validators asynchronously
            List<CompletableFuture<TrxRejoinApplication>> futures = applicableValidators.stream()
                    .map(validator -> validator.validate(form, entity))
                    .toList();

            // Wait for all validations to complete
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            // Wait for all validations to complete and get the final entity
            allFutures.join();

            log.info("All validations completed for rejoin ID: {}", entity.getId());

            // defaulted to pass
            entity.setValidationKtpStatus(ValidationKtpStatus.PASS);
            // below defaulted to postponed or pending as per confirmation from BA
            entity.setValidationBankAccountStatus(ValidationBankAccountStatus.POSTPONED);
            entity.setValidationLicenseAajiStatus(ValidationLicenseAajiStatus.PENDING);
            entity.setValidationLicenseAasiStatus(ValidationLicenseAasiStatus.PENDING);

            // The entity has been updated by all validators
            return entity;
        } catch (Exception e) {
            log.error("Error during validation process for rejoin ID: {}", entity.getId(), e);
            return entity;
        }
    }
}
