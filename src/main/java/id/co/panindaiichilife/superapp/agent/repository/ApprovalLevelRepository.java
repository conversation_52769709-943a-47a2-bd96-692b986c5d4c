package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.ApprovalLevel;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ApprovalLevelRepository extends BaseRepository<ApprovalLevel, Long>, ApprovalLevelRepositoryCustom {
    List<ApprovalLevel> findByTrxTypeOrderByLevelNumber(TrxType trxType);

    Optional<ApprovalLevel> findApplicableLevel(
            TrxType trxType,
            Integer levelNumber,
            Channel channel,
            Set<String> requesterRoles,
            Set<String> approverRoles,
            Set<String> branchCodes);

    @Query("SELECT MAX(al.levelNumber) FROM ApprovalLevel al WHERE al.trxType = :trxType AND al.isActive = true")
    Integer findMaxLevelForTransactionType(@Param("trxType") TrxType trxType);

    @Query("SELECT MAX(al.levelNumber) FROM ApprovalLevel al WHERE al.trxType = :trxType AND (al.requesterRole IN :requesterRoles OR al.requesterRole IS NULL) AND al.isActive = true")
    Integer findMaxLevelForTransactionTypeAndRequesterRole(@Param("trxType") TrxType trxType, @Param("requesterRoles") Set<String> requesterRoles);

    List<ApprovalLevel> findByApproverRoleInAndBranchCodeIn(
            Set<String> roles,
            Set<String> branchCodes);
}
