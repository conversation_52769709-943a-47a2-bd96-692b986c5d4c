package id.co.panindaiichilife.superapp.agent.model;


import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;

@Entity
@Table(name = "branches")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id", "branchName"})
@SQLDelete(sql = "UPDATE branches SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class Branch extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "branches_id_seq")
    @SequenceGenerator(name = "branches_id_seq", sequenceName = "branches_id_seq", allocationSize = 1)
    private Long id;

    @Audited
    private String parentBranchCode;

    @Audited
    private String parentBranchName;

    @Audited
    private String branchCode;

    @Audited
    private String branchName;

    @Audited
    private String areaCode;

    @Audited
    private String areaName;

    @Audited
    private String regionCode;

    @Audited
    private String regionName;

    @Audited
    private String subRegionCode;

    @Audited
    private String subRegionName;

    @Audited
    private String hosCode;

    @Audited
    private String hosName;

    @Audited
    private String phoneNumber;

    @Audited
    private Integer staffCount;

    @Audited
    private String city;

    @Audited
    private String address;

    @Audited
    private String secondAddress;

    @Audited
    private String thirdAddress;

    @Audited
    private String googleMapsUrl;

    @Audited
    private Double latitude;

    @Audited
    private Double longitude;

    @Audited
    private Boolean isActive;

    @Audited
    @Enumerated(EnumType.STRING)
    private Channel channel;

    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;
}
