package id.co.panindaiichilife.superapp.agent.core.service;

import id.co.panindaiichilife.superapp.agent.api.dto.pdf.*;
import id.co.panindaiichilife.superapp.agent.enums.DocumentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.OutputStream;

/**
 * Service for generating specific agent-related document types with predefined variable mappings.
 * This service provides convenient methods for agent document generation scenarios.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DocumentGenerationService {

    private final PdfService pdfService;

    // DTO-based methods

    /**
     * Generate PKAJ-AGE document using DTO.
     *
     * @param dto PKAJ-AGE document parameters
     * @return PDF document as byte array
     * @throws Exception if PDF generation fails
     */
    public byte[] generatePkajAgeDocument(PkajAgeDocumentDto dto) throws Exception {
        return pdfService.generateDocumentPdfAsBytes(DocumentType.PKAJ_AGE, dto.toVariablesMap());
    }

    /**
     * Generate PKAJ-AGE document using DTO to output stream.
     *
     * @param dto          PKAJ-AGE document parameters
     * @param outputStream Output stream to write the PDF
     * @throws Exception if PDF generation fails
     */
    public void generatePkajAgeDocument(PkajAgeDocumentDto dto, OutputStream outputStream) throws Exception {
        pdfService.generateDocumentPdf(DocumentType.PKAJ_AGE, dto.toVariablesMap(), outputStream);
    }

    /**
     * Generate PMKAJ-AGE document using DTO.
     *
     * @param dto PMKAJ-AGE document parameters
     * @return PDF document as byte array
     * @throws Exception if PDF generation fails
     */
    public byte[] generatePmkajAgeDocument(PmkajAgeDocumentDto dto) throws Exception {
        return pdfService.generateDocumentPdfAsBytes(DocumentType.PMKAJ_AGE, dto.toVariablesMap());
    }

    /**
     * Generate PMKAJ-AGE document using DTO to output stream.
     *
     * @param dto          PMKAJ-AGE document parameters
     * @param outputStream Output stream to write the PDF
     * @throws Exception if PDF generation fails
     */
    public void generatePmkajAgeDocument(PmkajAgeDocumentDto dto, OutputStream outputStream) throws Exception {
        pdfService.generateDocumentPdf(DocumentType.PMKAJ_AGE, dto.toVariablesMap(), outputStream);
    }

    /**
     * Generate KODE-ETIK-AGE document using DTO.
     *
     * @param dto KODE-ETIK-AGE document parameters
     * @return PDF document as byte array
     * @throws Exception if PDF generation fails
     */
    public byte[] generateKodeEtikAgeDocument(KodeEtikAgeDocumentDto dto) throws Exception {
        return pdfService.generateDocumentPdfAsBytes(DocumentType.KODE_ETIK_AGE, dto.toVariablesMap());
    }

    /**
     * Generate KODE-ETIK-AGE document using DTO to output stream.
     *
     * @param dto          KODE-ETIK-AGE document parameters
     * @param outputStream Output stream to write the PDF
     * @throws Exception if PDF generation fails
     */
    public void generateKodeEtikAgeDocument(KodeEtikAgeDocumentDto dto, OutputStream outputStream) throws Exception {
        pdfService.generateDocumentPdf(DocumentType.KODE_ETIK_AGE, dto.toVariablesMap(), outputStream);
    }

    /**
     * Generate ANTI-TWISTING-AGE document using DTO.
     *
     * @param dto ANTI-TWISTING-AGE document parameters
     * @return PDF document as byte array
     * @throws Exception if PDF generation fails
     */
    public byte[] generateAntiTwistingAgeDocument(AntiTwistingAgeDocumentDto dto) throws Exception {
        return pdfService.generateDocumentPdfAsBytes(DocumentType.ANTI_TWISTING_AGE, dto.toVariablesMap());
    }

    /**
     * Generate ANTI-TWISTING-AGE document using DTO to output stream.
     *
     * @param dto          ANTI-TWISTING-AGE document parameters
     * @param outputStream Output stream to write the PDF
     * @throws Exception if PDF generation fails
     */
    public void generateAntiTwistingAgeDocument(AntiTwistingAgeDocumentDto dto, OutputStream outputStream) throws Exception {
        pdfService.generateDocumentPdf(DocumentType.ANTI_TWISTING_AGE, dto.toVariablesMap(), outputStream);
    }

    /**
     * Generate APGEN-AGE (Aplikasi Keagenan) document using DTO.
     *
     * @param dto APGEN-AGE document parameters
     * @return PDF document as byte array
     * @throws Exception if PDF generation fails
     */
    public byte[] generateApgenAgeDocument(ApgenAgeDocumentDto dto) throws Exception {
        return pdfService.generateDocumentPdfAsBytes(DocumentType.APGEN_AGE, dto.toVariablesMap());
    }

    /**
     * Generate APGEN-AGE (Aplikasi Keagenan) document using DTO to output stream.
     *
     * @param dto          APGEN-AGE document parameters
     * @param outputStream Output stream to write the PDF
     * @throws Exception if PDF generation fails
     */
    public void generateApgenAgeDocument(ApgenAgeDocumentDto dto, OutputStream outputStream) throws Exception {
        pdfService.generateDocumentPdf(DocumentType.APGEN_AGE, dto.toVariablesMap(), outputStream);
    }

    /**
     * Generate TERMINATION-LETTER document using DTO to output stream.
     *
     * @param dto          TERMINATION-LETTER document parameters
     * @param outputStream Output stream to write the PDF
     * @throws Exception if PDF generation fails
     */
    public void generateTerminationLetterDocument(ApgenAgeDocumentDto dto, OutputStream outputStream) throws Exception {
        pdfService.generateDocumentPdf(DocumentType.TERMINATION_LETTER, dto.toVariablesMap(), outputStream);
    }

    /**
     * Generate REJOIN-LETTER document using DTO to output stream.
     *
     * @param dto          REJOIN-LETTER document parameters
     * @param outputStream Output stream to write the PDF
     * @throws Exception if PDF generation fails
     */
    public void generateRejoinLetterDocument(ApgenAgeDocumentDto dto, OutputStream outputStream) throws Exception {
        pdfService.generateDocumentPdf(DocumentType.REJOIN_LETTER, dto.toVariablesMap(), outputStream);
    }

    /**
     * Generate any document type using generic DTO.
     *
     * @param dto Generic document parameters
     * @return PDF document as byte array
     * @throws Exception if PDF generation fails
     */
    public byte[] generateDocument(GenericDocumentDto dto) throws Exception {
        return pdfService.generateDocumentPdfAsBytes(dto.getDocumentType(), dto.toVariablesMap());
    }

    /**
     * Generate any document type using generic DTO to output stream.
     *
     * @param dto          Generic document parameters
     * @param outputStream Output stream to write the PDF
     * @throws Exception if PDF generation fails
     */
    public void generateDocument(GenericDocumentDto dto, OutputStream outputStream) throws Exception {
        pdfService.generateDocumentPdf(dto.getDocumentType(), dto.toVariablesMap(), outputStream);
    }

    /**
     * Check if a document type is supported.
     *
     * @param documentType The document type to check
     * @return true if the document type is supported, false otherwise
     */
    public boolean isDocumentTypeSupported(DocumentType documentType) {
        return pdfService.isTemplateAvailable(documentType);
    }

    /**
     * Check if a document type is supported by name.
     *
     * @param documentTypeName The document type name to check
     * @return true if the document type is supported, false otherwise
     */
    public boolean isDocumentTypeSupported(String documentTypeName) {
        return pdfService.isTemplateAvailable(documentTypeName);
    }

    /**
     * Get all supported agent document types.
     *
     * @return Array of all supported document type names
     */
    public String[] getSupportedDocumentTypes() {
        return DocumentType.getAllTemplateNames();
    }

    /**
     * Get all supported agent document types as enum values.
     *
     * @return Array of all supported DocumentType enum values
     */
    public DocumentType[] getSupportedDocumentTypeEnums() {
        return DocumentType.values();
    }
}
