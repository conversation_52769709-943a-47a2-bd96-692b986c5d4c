package id.co.panindaiichilife.superapp.agent.service.agency.validator;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationBlacklistResponseDto;
import id.co.panindaiichilife.superapp.agent.enums.ValidationBlacklistStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;

import java.time.LocalDate;

/**
 * Validator for checking blacklist status
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BlacklistValidator extends AbstractRecruitmentValidator {

    private final PortalProvider portalProvider;

    @Override
    public boolean canValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        return entity.getBirthDate() != null && StringUtils.isNotBlank(entity.getNik());
    }

    @Override
    protected TrxRecruitment doValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        ValidationBlacklistStatus status = validateBlacklistStatus(entity.getNik(), entity.getBirthDate());

        if (status != null) {
            entity.setValidationBlacklistStatus(status);
            log.info("Blacklist validation result for NIK {}: {}", entity.getNik(), status);
        } else {
            log.warn("Blacklist validation skipped or failed for NIK {}", entity.getNik());
        }

        return entity;
    }

    @Override
    public String getValidatorName() {
        return "Blacklist Validator";
    }

    /**
     * Validates blacklist status for a recruitment candidate
     *
     * @param idNumber    The ID number (NIK) of the candidate
     * @param dateOfBirth The date of birth of the candidate
     * @return The ValidationBlacklistStatus enum value based on the API response
     */
    private ValidationBlacklistStatus validateBlacklistStatus(String idNumber, LocalDate dateOfBirth) {
        try {
            if (dateOfBirth == null || StringUtils.isBlank(idNumber)) {
                log.warn("Blacklist validation skipped - missing required data");
                return null;
            }

            // Format date as yyyy-MM-dd
            String dobFormatted = dateOfBirth.toString();

            // Call the portal API to validate blacklist status
            Call<PortalValidationBlacklistResponseDto> call = portalProvider.validateBlacklist(dobFormatted, idNumber);
            Response<PortalValidationBlacklistResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalValidationBlacklistResponseDto blacklistResponse = response.body();

                // Check if the API call was successful
                if ("200".equals(blacklistResponse.getStatusCode())) {
                    // Convert the string result to ValidationBlacklistStatus enum
                    String result = blacklistResponse.getResult();
                    try {
                        return ValidationBlacklistStatus.valueOf(result.replace("-", ""));
                    } catch (IllegalArgumentException e) {
                        log.error("Unknown blacklist status: {}", result, e);
                        return null;
                    }
                } else {
                    log.error("Error in blacklist validation: {} - {}", blacklistResponse.getStatusCode(), blacklistResponse.getMessage());
                    return null;
                }
            } else {
                log.error("Failed to validate blacklist status: {}", response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                return null;
            }
        } catch (Exception e) {
            log.error("Error validating blacklist status", e);
            return null;
        }
    }
}
