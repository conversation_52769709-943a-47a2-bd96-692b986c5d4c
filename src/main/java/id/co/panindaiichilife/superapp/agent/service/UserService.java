package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccessDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccountDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.UserDto;
import id.co.panindaiichilife.superapp.agent.api.filter.UserFilter;
import id.co.panindaiichilife.superapp.agent.api.form.ChangePasswordForm;
import id.co.panindaiichilife.superapp.agent.api.form.cms.ProfileForm;
import id.co.panindaiichilife.superapp.agent.api.form.cms.UserForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalChangePasswordDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalChangePasswordResponseDto;
import id.co.panindaiichilife.superapp.agent.core.data.association.ManyToManyUtils;
import id.co.panindaiichilife.superapp.agent.core.http.BadRequestException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionService;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.core.view.fileinput.FileinputResponse;
import id.co.panindaiichilife.superapp.agent.enums.UserType;
import id.co.panindaiichilife.superapp.agent.model.Access;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AccessRepository;
import id.co.panindaiichilife.superapp.agent.repository.BranchRepository;
import id.co.panindaiichilife.superapp.agent.repository.RoleRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Response;

import java.time.Instant;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;

    private final EncryptionService encryptionService;

    private final AmazonS3Service amazonS3Service;

    private final AccessRepository accessRepository;

    private final PortalProvider portalProvider;

    private final PasswordEncoder passwordEncoder;

    private final RoleRepository roleRepository;

    private final BranchRepository branchRepository;

    private final DeviceService deviceService;

    private final RedisService redisService;

    @CacheableWithTTL(cacheName = "userCache", key = "#username", ttl = 120, db = 7)
    public AccountDto findByUsername(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        return BaseDto.of(AccountDto.class, user);
    }

    public AccountDto update(String username, ProfileForm profileForm) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);

        BeanUtils.copyProperties(profileForm, user);
        userRepository.save(user);

        // Clear both userCache and userAclCache after update
        redisService.clearCache("userCache", username, 7);
        redisService.clearCache("agentCache", username, 7);
        redisService.clearCache("userAclCache", username, 7);

        return BaseDto.of(AccountDto.class, user);
    }


    @CacheableWithTTL(cacheName = "userAclCache", key = "#username", ttl = 120, db = 7)
    public AccountDto findByUsernameWithAcl(String username) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        AccountDto dto = BaseDto.of(AccountDto.class, user);

        List<Access> accesses = accessRepository.listByUsername(username);
        dto.setAccesses(BaseDto.of(AccessDto.class, accesses));
        return dto;
    }

    public boolean checkIsAgent(String username) {
        return userRepository.existsByUsernameAndIsAgentTrue(username);
    }

    public boolean checkIsUserStaff(String username) {
        return username.contains("@panindai-ichilife.co.id");
    }

    public boolean checkIsUserStaff(User user) {
        return user.getUsername().contains("@panindai-ichilife.co.id") || user.getUserType().equals(UserType.STAFF) || user.getUserType().equals(UserType.CAS);
    }

    public FileinputResponse upload(String username, String folder, MultipartFile file) {
        User data = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        ;
        String filePath = "/assets/users/" + data.getUsername() + folder + UUID.randomUUID() + "."
                + FilenameUtils.getExtension(file.getOriginalFilename());
        if (amazonS3Service.store(file, filePath)) {
            return FileinputResponse.success(amazonS3Service.getUrl(filePath));
        } else {
            throw new BadRequestException("Upload failed");
        }
    }


    public void changePassword(String username, ChangePasswordForm changePasswordApiForm) {
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        if (checkIsUserStaff(user)) {
            throw new BadRequestException("User staff should not able to change password");
        }

        // Decrypt password
        String rawPassword = encryptionService.decrypt(changePasswordApiForm.getPassword());
        String oldRawPassword = encryptionService.decrypt(changePasswordApiForm.getOldPassword());

        // Handle password reset based on user type
        if (user.getIsAgent()) {
            changeAgentPassword(user, oldRawPassword, rawPassword);
        } else {
            changeRegularUserPassword(user, oldRawPassword, rawPassword);
        }
    }

    /**
     * Change password for regular users
     */
    private void changeRegularUserPassword(User user, String oldRawPassword, String rawPassword) {
        String currentPassword = user.getPassword();
        if (!passwordEncoder.matches(oldRawPassword, currentPassword)) {
            throw new BadRequestException("Old password not match");
        }
        user.setPassword(passwordEncoder.encode(rawPassword));
        user.setLastChangePassword(Instant.now());
        userRepository.save(user);

        // Deactivate all devices for security after password change
        deactivateUserDevicesAfterPasswordChange(user);
    }

    /**
     * Change password for agent users through portal service
     */
    private void changeAgentPassword(User user, String oldRawPassword, String newRawPassword) {
        // Second call: Perform password change with auth code
        boolean success = performPasswordChange(user.getUsername(), oldRawPassword, newRawPassword);

        if (success) {
            user.setLastChangePassword(Instant.now());
            userRepository.save(user);

            // Deactivate all devices for security after password change
            deactivateUserDevicesAfterPasswordChange(user);
        }
    }

    /**
     * Perform the actual password change using authorization code
     */
    private boolean performPasswordChange(String username, String oldRawPassword, String newRawPassword) {

        try {
            PortalChangePasswordDto request = new PortalChangePasswordDto();
            request.setUsername(encryptionService.encrypt(username, ""));
            request.setOldPassword(encryptionService.encrypt(oldRawPassword, ""));
            request.setNewPassword(encryptionService.encrypt(newRawPassword, ""));

            Response<PortalChangePasswordResponseDto> response =
                    portalProvider.changePassword(request).execute();

            if (response.isSuccessful() && StringUtils.equals(Objects.requireNonNull(response.body()).getStatusCode(), "200")) {
                return true;
            } else {
                throw new BadRequestException(Objects.requireNonNull(response.body()).getMessage());
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }


    public Page<UserDto> findAll(Pageable pageable, UserFilter filter) {
        Page<User> users = userRepository.findAll(filter, pageable);
        return BaseDto.of(UserDto.class, users, pageable);
    }

    public UserDto findOne(Long id) {
        User data = userRepository.findById(id).orElseThrow(NotFoundException::new);
        return BaseDto.of(UserDto.class, data);
    }

    @Transactional
    public UserDto add(UserForm userForm) {
        User data = new User();
        BeanUtils.copyProperties(userForm, data);

        ManyToManyUtils.save(userForm, data, "roles", roleRepository);
        ManyToManyUtils.save(userForm, data, "branches", branchRepository);
        if (StringUtils.isNotBlank(userForm.getPassword())) {
            String rawPassword = encryptionService.decrypt(userForm.getPassword());
            data.setPassword(passwordEncoder.encode(rawPassword));
        }
        data.setIsAgent(Boolean.FALSE);
        userRepository.save(data);

        return BaseDto.of(UserDto.class, data);
    }

    @Transactional
    public UserDto update(Long id, UserForm userForm) {
        User data = userRepository.findById(id).orElseThrow(NotFoundException::new);
        data.setName(userForm.getName());
        data.setEmail(userForm.getEmail());
        data.setPhone(userForm.getPhone());
        data.setTimezone(userForm.getTimezone());
        data.setChannel(userForm.getChannel());
        data.setUserType(userForm.getUserType());
        data.setStatus(userForm.getStatus());
        data.setPicture(userForm.getPicture());
        data.setUsername(userForm.getUsername());

        ManyToManyUtils.save(userForm, data, "roles", roleRepository);
        ManyToManyUtils.save(userForm, data, "branches", branchRepository);
        if (StringUtils.isNotBlank(userForm.getPassword())) {
            String rawPassword = encryptionService.decrypt(userForm.getPassword());
            data.setPassword(passwordEncoder.encode(rawPassword));
        }
        data.setIsAgent(Boolean.FALSE);
        userRepository.save(data);

        return BaseDto.of(UserDto.class, data);
    }

    public void delete(Long id) {
        userRepository.deleteById(id);
    }

    public List<String> getTimezone() {
        return ZoneId.getAvailableZoneIds().stream()
                .sorted(Comparator.naturalOrder())
                .collect(Collectors.toList());
    }

    /**
     * Helper method to deactivate all user devices after password change
     * This is a security measure to force re-authentication on all devices
     *
     * @param user The user whose devices should be deactivated
     */
    private void deactivateUserDevicesAfterPasswordChange(User user) {
        try {
            int deactivatedCount = deviceService.deactivateAllUserDevices(user);
            log.info("Password changed for user: {}. Deactivated {} devices for security.",
                    user.getUsername(), deactivatedCount);
        } catch (Exception e) {
            // Log the error but don't fail the password change operation
            log.error("Failed to deactivate devices for user: {} after password change. Error: {}",
                    user.getUsername(), e.getMessage(), e);
        }
    }
}
