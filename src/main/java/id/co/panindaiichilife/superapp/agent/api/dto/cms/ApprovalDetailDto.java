package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

@EqualsAndHashCode(callSuper = true)
@Data
public class ApprovalDetailDto extends BaseDto<TrxApprovalDetail> {
    private Long id;
    private ActionByDto actionBy;
    private String remarks;
    private ApprovalStatus approvalStatus;
    private String detailApproval;
    private Integer levelNumber;
    private Instant createdAt;

    @Override
    public void copy(TrxApprovalDetail data) {
        super.copy(data);
        actionBy = BaseDto.of(ActionByDto.class, data.getActionBy());
    }
}
