package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.model.User;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;


@EqualsAndHashCode(callSuper = true)
@Data
public class RequestByDto extends BaseDto<User> {

    private String username;

    private String name;

    private Channel channel;

    private String agentCode;

    private String agentLevel;

    private Collection<BranchDto> branches;

    @Override
    public void copy(User data) {
        super.copy(data);
        branches = BaseDto.of(BranchDto.class, data.getBranches());

        if (null != data.getIsAgent()) {
            if (data.getIsAgent()) {
                agentCode = data.getAgent().getAgentCode();
                agentLevel = data.getAgent().getLevel();
            }
        }
    }
}
