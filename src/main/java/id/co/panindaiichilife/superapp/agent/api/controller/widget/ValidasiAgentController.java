package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.ValidasiAgentDto;
import id.co.panindaiichilife.superapp.agent.api.filter.ValidasiPerG1Filter;
import id.co.panindaiichilife.superapp.agent.api.filter.ValidasiPerHirarkiFilter;
import id.co.panindaiichilife.superapp.agent.service.widget.ValidasiAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.util.List;

@RestController("widgetValidasiAgentController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class ValidasiAgentController {

    private final ValidasiAgentService validasiAgentService;


    @Operation(summary = "Get widget Validasi Hirarki Agent")
    @GetMapping(value = "validasi-hirarki")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ValidasiHirarki', 'view')")
    public List<ValidasiAgentDto> getValidasiHirarki(Principal principal,
                                                     @ParameterObject @ModelAttribute("filter") ValidasiPerHirarkiFilter filter) {
        return validasiAgentService.getValidasiHirarki(principal.getName(), filter);
    }

    @Operation(summary = "Get widget Validasi G1")
    @GetMapping(value = "validasi-g1")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.ValidasiG1', 'view')")
    public List<ValidasiAgentDto> getValidasiG1(Principal principal,
                                                @ParameterObject @ModelAttribute("filter") ValidasiPerG1Filter filter) {
        return validasiAgentService.getValidasiG1(principal.getName(), filter);
    }
}
