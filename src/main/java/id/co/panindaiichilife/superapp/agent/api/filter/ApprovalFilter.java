package id.co.panindaiichilife.superapp.agent.api.filter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import id.co.panindaiichilife.superapp.agent.core.data.filter.FieldFilter;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.Branch;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import id.co.panindaiichilife.superapp.agent.util.ApprovalRoleUtil;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.persistence.criteria.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
@Slf4j
public class ApprovalFilter extends FieldFilter<TrxApprovalHeader> {

    // Constants to avoid string literal duplication
    private static final String APPROVER_ROLE_FIELD = "approverRole";
    private static final String LAST_APPROVER_ROLE_FIELD = "lastApproverRole";
    private static final String TRX_ID_FIELD = "trxId";
    private static final String AGENT_CODE_FIELD = "agentCode";
    private static final String AGENT_FIELD = "agent";

    private String q;

    private List<TrxType> trxType;

    private ApprovalStatus approvalStatus;

    @Parameter(hidden = true)
    private List<ApprovalStatus> approvalStatusList;

    @Parameter(hidden = true)
    private List<Integer> eligibleLevelNumbers;

    @Parameter(hidden = true)
    private Set<String> approverRoles;

    @Parameter(hidden = true)
    private Set<String> lastApproverRoles;

    @Parameter(hidden = true)
    private List<Integer> lastLevelNumbers;

    private LocalDate date;

    @Parameter(hidden = true)
    private boolean includeLastApprover = false;

    @Parameter(hidden = true)
    private String uplineAgentCode;

    @Parameter(hidden = true)
    private String recruiterUsername;

    @Parameter(hidden = true)
    private Set<String> branchCodes;

    @Override
    protected Predicate getBasePredicate(Root<TrxApprovalHeader> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        List<Predicate> predicates = new ArrayList<>();

        addDatePredicate(predicates, root, cb);
        addTransactionTypePredicate(predicates, root);
        addApprovalStatusPredicate(predicates, root, cb);
        addBranchFilteringPredicate(predicates, root);
        addSearchPredicate(predicates, root, query, cb);
        addApproverPredicates(predicates, root, cb);

        return predicates.isEmpty() ? cb.conjunction() : cb.and(predicates.toArray(new Predicate[0]));
    }

    private void addDatePredicate(List<Predicate> predicates, Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        if (date != null) {
            LocalDate start = date;
            LocalDate end = date.plusDays(1);
            predicates.add(cb.and(
                    cb.greaterThanOrEqualTo(root.get("createdAt"), start.atStartOfDay()),
                    cb.lessThan(root.get("createdAt"), end.atStartOfDay())
            ));
        }
    }

    private void addTransactionTypePredicate(List<Predicate> predicates, Root<TrxApprovalHeader> root) {
        if (trxType != null && !trxType.isEmpty()) {
            predicates.add(root.get("trxType").in(trxType));
        }
    }

    private void addApprovalStatusPredicate(List<Predicate> predicates, Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        if (approvalStatus != null) {
            predicates.add(cb.equal(root.get("approvalStatus"), approvalStatus));
        } else if (approvalStatusList != null && !approvalStatusList.isEmpty()) {
            predicates.add(root.get("approvalStatus").in(approvalStatusList));
        }
    }

    private void addBranchFilteringPredicate(List<Predicate> predicates, Root<TrxApprovalHeader> root) {
        if (branchCodes != null && !branchCodes.isEmpty()) {
            Join<TrxApprovalHeader, Branch> branchJoin = root.join("branch", JoinType.LEFT);
            predicates.add(branchJoin.get("branchCode").in(branchCodes));
        }
    }

    private void addSearchPredicate(List<Predicate> predicates, Root<TrxApprovalHeader> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        if (q != null && !q.trim().isEmpty()) {
            predicates.add(createSearchPredicate(root, query, cb));
        }
    }

    private void addApproverPredicates(List<Predicate> predicates, Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        Predicate currentApproverPredicate = createCurrentApproverPredicate(root, cb);
        Predicate lastApproverPredicate = createLastApproverPredicate(root, cb);

        if (currentApproverPredicate != null && lastApproverPredicate != null) {
            predicates.add(cb.or(currentApproverPredicate, lastApproverPredicate));
        } else if (currentApproverPredicate != null) {
            predicates.add(currentApproverPredicate);
        } else if (lastApproverPredicate != null) {
            predicates.add(lastApproverPredicate);
        }
    }

    private Predicate createCurrentApproverPredicate(Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        List<Predicate> approverPredicates = new ArrayList<>();

        addCurrentLevelPredicate(approverPredicates, root);
        addApproverRolesPredicate(approverPredicates, root, cb);

        return approverPredicates.isEmpty() ? null : cb.and(approverPredicates.toArray(new Predicate[0]));
    }

    private void addCurrentLevelPredicate(List<Predicate> predicates, Root<TrxApprovalHeader> root) {
        if (eligibleLevelNumbers != null && !eligibleLevelNumbers.isEmpty()) {
            predicates.add(root.get("currentLevel").in(eligibleLevelNumbers));
        }
    }

    private void addApproverRolesPredicate(List<Predicate> predicates, Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        Predicate rolePredicate = createStandardRolePredicate(root, cb);
        Predicate uplinePredicate = createUplinePredicate(root, cb);
        Predicate recruiterPredicate = createRecruiterPredicate(root, cb);

        Predicate combinedRolePredicate = combineRolePredicates(cb, rolePredicate, uplinePredicate, recruiterPredicate);
        if (combinedRolePredicate != null) {
            predicates.add(combinedRolePredicate);
        }
    }

    private Predicate createStandardRolePredicate(Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        if (approverRoles == null || approverRoles.isEmpty()) {
            return null;
        }

        List<Predicate> rolePredicates = new ArrayList<>();
        for (String userRole : approverRoles) {
            addRoleMatchPredicates(rolePredicates, root, cb, userRole);
        }

        return cb.or(rolePredicates.toArray(new Predicate[0]));
    }

    private void addRoleMatchPredicates(List<Predicate> predicates, Root<TrxApprovalHeader> root,
                                       CriteriaBuilder cb, String userRole) {
        // Check for exact match (single role case)
        predicates.add(cb.equal(root.get(APPROVER_ROLE_FIELD), userRole));

        // Check for comma-separated roles (multiple roles case)
        predicates.add(cb.like(root.get(APPROVER_ROLE_FIELD), userRole + ",%"));
        predicates.add(cb.like(root.get(APPROVER_ROLE_FIELD), "%," + userRole + ",%"));
        predicates.add(cb.like(root.get(APPROVER_ROLE_FIELD), "%," + userRole));
    }

    private Predicate createUplinePredicate(Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        if (uplineAgentCode == null || uplineAgentCode.isEmpty()) {
            return null;
        }
        return cb.equal(root.get(APPROVER_ROLE_FIELD), "UPLINE:" + uplineAgentCode);
    }

    private Predicate createRecruiterPredicate(Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        if (recruiterUsername == null || recruiterUsername.isEmpty()) {
            return null;
        }
        return cb.equal(root.get(APPROVER_ROLE_FIELD), "RECRUITER:" + recruiterUsername);
    }

    private Predicate combineRolePredicates(CriteriaBuilder cb, Predicate rolePredicate,
                                          Predicate uplinePredicate, Predicate recruiterPredicate) {
        List<Predicate> combinedPredicates = new ArrayList<>();

        if (rolePredicate != null) combinedPredicates.add(rolePredicate);
        if (uplinePredicate != null) combinedPredicates.add(uplinePredicate);
        if (recruiterPredicate != null) combinedPredicates.add(recruiterPredicate);

        return combinedPredicates.isEmpty() ? null : cb.or(combinedPredicates.toArray(new Predicate[0]));
    }

    private Predicate createLastApproverPredicate(Root<TrxApprovalHeader> root, CriteriaBuilder cb) {
        // Only include last approver predicates if explicitly requested or if it's the only set of criteria provided
        if (!includeLastApprover && (approverRoles != null || eligibleLevelNumbers != null)) {
            return null;
        }

        List<Predicate> lastApproverPredicates = new ArrayList<>();

        // Last level predicate
        if (lastLevelNumbers != null && !lastLevelNumbers.isEmpty()) {
            lastApproverPredicates.add(root.get("lastLevel").in(lastLevelNumbers));
        }

        // Last approver role predicate - handle comma-separated roles
        if (lastApproverRoles != null && !lastApproverRoles.isEmpty()) {
            List<Predicate> lastRolePredicates = new ArrayList<>();

            // For each user role, check if it matches any role in the lastApproverRole field
            for (String userRole : lastApproverRoles) {
                // Check for exact match (single role case)
                lastRolePredicates.add(cb.equal(root.get(LAST_APPROVER_ROLE_FIELD), userRole));

                // Check for comma-separated roles (multiple roles case)
                lastRolePredicates.add(cb.like(root.get(LAST_APPROVER_ROLE_FIELD), userRole + ",%"));
                lastRolePredicates.add(cb.like(root.get(LAST_APPROVER_ROLE_FIELD), "%," + userRole + ",%"));
                lastRolePredicates.add(cb.like(root.get(LAST_APPROVER_ROLE_FIELD), "%," + userRole));
            }

            lastApproverPredicates.add(cb.or(lastRolePredicates.toArray(new Predicate[0])));
        }

        if (lastApproverPredicates.isEmpty()) {
            return null;
        }

        return cb.and(lastApproverPredicates.toArray(new Predicate[0]));
    }

    private Predicate createSearchPredicate(Root<TrxApprovalHeader> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        String searchTerm = "%" + q.trim().toLowerCase() + "%";
        List<Predicate> searchPredicates = new ArrayList<>();

        // Determine which transaction types to search
        List<TrxType> typesToSearch = new ArrayList<>();
        if (trxType != null && !trxType.isEmpty()) {
            // Transaction types specified
            typesToSearch.addAll(trxType);
        } else {
            // No transaction type specified, search across all types
            typesToSearch.addAll(List.of(TrxType.values()));
        }

        // Create search predicates for each transaction type
        for (TrxType type : typesToSearch) {
            Predicate searchPredicate = createSearchPredicateForTrxType(root, query, cb, type, searchTerm);
            if (searchPredicate != null) {
                searchPredicates.add(cb.and(
                        cb.equal(root.get("trxType"), type),
                        searchPredicate
                ));
            }
        }

        return searchPredicates.isEmpty() ? cb.conjunction() : cb.or(searchPredicates.toArray(new Predicate[0]));
    }

    private Predicate createSearchPredicateForTrxType(Root<TrxApprovalHeader> root, CriteriaQuery<?> query,
                                                      CriteriaBuilder cb, TrxType trxType, String searchTerm) {
        switch (trxType) {
            case RECRUITMENT_BP:
            case RECRUITMENT_BM:
            case RECRUITMENT_BD:
                return createRecruitmentSearchPredicate(root, query, cb, searchTerm);
            case EDIT_PROFILE:
                return createEditProfileSearchPredicate(root, query, cb, searchTerm);
            case TERMINASI_BP:
            case TERMINASI_BM:
            case TERMINASI_BD:
                return createTerminationSearchPredicate(root, query, cb, searchTerm);
            case REJOIN_BP:
            case REJOIN_BM:
                return createRejoinSearchPredicate(root, query, cb, searchTerm);
            default:
                return null;
        }
    }

    private Predicate createRecruitmentSearchPredicate(Root<TrxApprovalHeader> root, CriteriaQuery<?> query,
                                                       CriteriaBuilder cb, String searchTerm) {
        // Create subquery to search in TrxRecruitment
        var subquery = query.subquery(Long.class);
        var recruitmentRoot = subquery.from(id.co.panindaiichilife.superapp.agent.model.TrxRecruitment.class);

        subquery.select(recruitmentRoot.get("id"))
                .where(cb.and(
                        cb.equal(recruitmentRoot.get("id"), root.get(TRX_ID_FIELD)),
                        cb.or(
                                cb.like(cb.lower(recruitmentRoot.get("fullName")), searchTerm),
                                cb.like(cb.lower(recruitmentRoot.get(AGENT_CODE_FIELD)), searchTerm)
                        )
                ));

        return cb.exists(subquery);
    }

    private Predicate createEditProfileSearchPredicate(Root<TrxApprovalHeader> root, CriteriaQuery<?> query,
                                                       CriteriaBuilder cb, String searchTerm) {
        // Create subquery to search in TrxEditProfile -> Agent
        var subquery = query.subquery(Long.class);
        var editProfileRoot = subquery.from(id.co.panindaiichilife.superapp.agent.model.TrxEditProfile.class);
        var agentJoin = editProfileRoot.join(AGENT_FIELD);

        subquery.select(editProfileRoot.get("id"))
                .where(cb.and(
                        cb.equal(editProfileRoot.get("id"), root.get(TRX_ID_FIELD)),
                        cb.or(
                                cb.like(cb.lower(agentJoin.get("agentName")), searchTerm),
                                cb.like(cb.lower(agentJoin.get(AGENT_CODE_FIELD)), searchTerm)
                        )
                ));

        return cb.exists(subquery);
    }

    private Predicate createTerminationSearchPredicate(Root<TrxApprovalHeader> root, CriteriaQuery<?> query,
                                                       CriteriaBuilder cb, String searchTerm) {
        // Create subquery to search in TrxTermination -> target User and Agent
        var subquery = query.subquery(Long.class);
        var terminationRoot = subquery.from(id.co.panindaiichilife.superapp.agent.model.TrxTermination.class);
        var targetUserJoin = terminationRoot.join("target");
        var targetAgentJoin = targetUserJoin.join(AGENT_FIELD, JoinType.LEFT);

        subquery.select(terminationRoot.get("id"))
                .where(cb.and(
                        cb.equal(terminationRoot.get("id"), root.get(TRX_ID_FIELD)),
                        cb.or(
                                cb.like(cb.lower(targetUserJoin.get("name")), searchTerm),
                                cb.like(cb.lower(targetAgentJoin.get(AGENT_CODE_FIELD)), searchTerm)
                        )
                ));

        return cb.exists(subquery);
    }

    private Predicate createRejoinSearchPredicate(Root<TrxApprovalHeader> root, CriteriaQuery<?> query,
                                                  CriteriaBuilder cb, String searchTerm) {
        // Create subquery to search in TrxRejoinApplication -> Agent
        var subquery = query.subquery(Long.class);
        var rejoinRoot = subquery.from(id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication.class);
        var agentJoin = rejoinRoot.join(AGENT_FIELD);

        subquery.select(rejoinRoot.get("id"))
                .where(cb.and(
                        cb.equal(rejoinRoot.get("id"), root.get(TRX_ID_FIELD)),
                        cb.or(
                                cb.like(cb.lower(agentJoin.get("agentName")), searchTerm),
                                cb.like(cb.lower(agentJoin.get(AGENT_CODE_FIELD)), searchTerm)
                        )
                ));

        return cb.exists(subquery);
    }
}
