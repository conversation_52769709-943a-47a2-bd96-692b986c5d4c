package id.co.panindaiichilife.superapp.agent.service.agency.validator;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRecruitmentForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationAdminAgentResponseDto;
import id.co.panindaiichilife.superapp.agent.enums.ValidationAdministrationAgentStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;

/**
 * Validator for checking administration agent status
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AdminAgentValidator extends AbstractRecruitmentValidator {

    private final PortalProvider portalProvider;

    @Override
    public boolean canValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        return StringUtils.isNotBlank(entity.getNik());
    }

    @Override
    protected TrxRecruitment doValidate(TrxRecruitmentForm form, TrxRecruitment entity) {
        ValidationAdministrationAgentStatus status = validateAdminAgentStatus(entity.getNik());

        if (status != null) {
            entity.setValidationAdministrationAgentStatus(status);
            log.info("Admin agent validation result for NIK {}: {}", entity.getNik(), status);
        } else {
            log.warn("Admin agent validation skipped or failed for NIK {}", entity.getNik());
        }

        return entity;
    }

    @Override
    public String getValidatorName() {
        return "Admin Agent Validator";
    }

    /**
     * Validates administration agent status for a recruitment candidate
     *
     * @param idNumber The ID number (NIK) of the candidate
     * @return The ValidationAdministrationAgentStatus enum value based on the API response
     */
    private ValidationAdministrationAgentStatus validateAdminAgentStatus(String idNumber) {
        try {
            if (StringUtils.isBlank(idNumber)) {
                log.warn("Administration agent validation skipped - missing required data");
                return null;
            }

            // Call the portal API to validate administration agent status
            // Using 'A' as the status parameter to check for active agents
            Call<PortalValidationAdminAgentResponseDto> call = portalProvider.validateAdminAgent(idNumber, "A");
            Response<PortalValidationAdminAgentResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalValidationAdminAgentResponseDto adminResponse = response.body();

                // Check if the API call was successful
                if ("200".equals(adminResponse.getStatusCode())) {
                    // If the response is successful, it means the candidate is an administration agent
                    // According to the requirements, this should be POSTPONED
                    log.info("Administration agent validation found active admin agent for NIK: {}", idNumber);
                    return ValidationAdministrationAgentStatus.POSTPONED;
                } else if ("404".equals(adminResponse.getStatusCode())) {
                    // If the response is 404 with the specific message, it means the candidate is not an administration agent
                    // According to the requirements, this should be PASS
                    log.info("Administration agent validation did not find admin agent for NIK: {}", idNumber);
                    return ValidationAdministrationAgentStatus.PASS;
                } else {
                    log.error("Error in administration agent validation: {} - {}", adminResponse.getStatusCode(), adminResponse.getMessage());
                    return null;
                }
            } else {
                log.error("Failed to validate administration agent status: {}", response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                return null;
            }
        } catch (Exception e) {
            log.error("Error validating administration agent status", e);
            return null;
        }
    }
}
