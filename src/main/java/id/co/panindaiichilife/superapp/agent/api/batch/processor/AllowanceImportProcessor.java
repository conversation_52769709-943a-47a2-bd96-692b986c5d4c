package id.co.panindaiichilife.superapp.agent.api.batch.processor;

import id.co.panindaiichilife.superapp.agent.api.batch.dto.AllowanceImportDto;
import id.co.panindaiichilife.superapp.agent.enums.DistributionCode;
import id.co.panindaiichilife.superapp.agent.model.Allowance;
import id.co.panindaiichilife.superapp.agent.repository.AllowanceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;


@Slf4j
@RequiredArgsConstructor
public class AllowanceImportProcessor implements ItemProcessor<AllowanceImportDto, Allowance> {

    private final AllowanceRepository allowanceRepository;

    @Override
    public Allowance process(AllowanceImportDto item) {
        return findOrCreateAllowance(item);
    }

    private Allowance findOrCreateAllowance(AllowanceImportDto item) {
        Allowance allowance = new Allowance();
        BeanUtils.copyProperties(item, allowance);
        allowance.setDistributionCode(DistributionCode.valueOf(item.getDistributionCode()));
        return allowance;
    }
}