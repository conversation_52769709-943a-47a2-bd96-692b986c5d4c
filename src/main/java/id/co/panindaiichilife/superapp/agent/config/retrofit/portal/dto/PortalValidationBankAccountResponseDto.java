package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * Response DTO for bank account validation with QuantumX integration
 */
@Data
public class PortalValidationBankAccountResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("code")
    @JsonProperty("code")
    private String code;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("extra")
    @JsonProperty("extra")
    private Object extra;

    @SerializedName("transactionId")
    @JsonProperty("transactionId")
    private String transactionId;

    @SerializedName("pricingStrategy")
    @JsonProperty("pricingStrategy")
    private String pricingStrategy;

    @SerializedName("data")
    @JsonProperty("data")
    private BankAccountData data;

    @Data
    public static class BankAccountData {
        @SerializedName("accountExists")
        @JsonProperty("accountExists")
        private Boolean accountExists;

        @SerializedName("accountName")
        @JsonProperty("accountName")
        private String accountName;
    }
}
