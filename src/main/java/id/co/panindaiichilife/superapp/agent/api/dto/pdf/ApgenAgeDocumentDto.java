package id.co.panindaiichilife.superapp.agent.api.dto.pdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * DTO for APGEN-AGE (Aplikasi Keagenan) document generation parameters.
 * This DTO contains all fields from the agency application form.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApgenAgeDocumentDto {

    // Header Information
    private String distributionChannel;
    private String groupBD;
    private String salesOfficeGA;
    private String recruiterAgent;
    private String agentCode;
    private String agentLevel;

    // Personal Information
    private String fullName;
    private String birthPlace;
    private String birthDate;
    private String gender; // Pria/Wanita
    private String ktpNumber;
    private String ktpValidityDate;
    private String maritalStatus; // Belum Menikah/Menikah/Cerai
    private String homeAddress;
    private String city;
    private String postalCode;
    private String homePhone;
    private String mobilePhone;
    private String faxNumber;
    private String email;
    private String numberOfDependents;
    private String occupation;
    private String npwpNumber;
    private String incomeSource;
    private String npkpAddress;

    // Education Information
    private String lastEducation; // SD/SMP/SMA/SMK/Akademi/Universitas/Lain-lain
    private String educationCity;
    private String educationPostalCode;

    // Education Level Checkboxes
    private Boolean isEducationSD;
    private Boolean isEducationSMP;
    private Boolean isEducationSMA;
    private Boolean isEducationAkademi;
    private Boolean isEducationUniversitas;
    private Boolean isEducationLainLain;
    private String educationLainLainDetail;

    // Insurance Experience
    private String hasInsuranceExperience; // Ya/Tidak
    private Boolean hasInsuranceExperienceYa;
    private Boolean hasInsuranceExperienceTidak;

    // Family Information (Emergency Contact)
    private String fatherName;
    private String motherName;
    private String spouseName;
    private String fatherBirthDate;
    private String motherBirthDate;
    private String spouseBirthDate;
    private String fatherAddress;
    private String motherAddress;
    private String spouseAddress;
    private String fatherPhone;
    private String motherPhone;
    private String spousePhone;

    // Banking Information
    private String bankName;
    private String accountNumber;
    private String accountHolderName;
    private String branchLocation;

    // Photo Information
    private String passPhoto;

    // Signature Information
    private String candidateSignature;
    private String candidateParaf;
    private String casSignature;
    private String casParaf;
    private String casName;
    private String casRole;

    // Additional Information
    private String applicationDate;
    private String applicationLocation;


    /**
     * Additional custom variables for the template.
     */
    private Map<String, Object> additionalVariables;

    /**
     * Get Indonesian day name from DayOfWeek.
     *
     * @param dayOfWeek the day of week
     * @return Indonesian day name
     */
    private String getIndonesianDayName(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY:
                return "Senin";
            case TUESDAY:
                return "Selasa";
            case WEDNESDAY:
                return "Rabu";
            case THURSDAY:
                return "Kamis";
            case FRIDAY:
                return "Jumat";
            case SATURDAY:
                return "Sabtu";
            case SUNDAY:
                return "Minggu";
            default:
                return "";
        }
    }

    /**
     * Get Indonesian month name from Month.
     *
     * @param month the month
     * @return Indonesian month name
     */
    private String getIndonesianMonthName(Month month) {
        switch (month) {
            case JANUARY:
                return "Januari";
            case FEBRUARY:
                return "Februari";
            case MARCH:
                return "Maret";
            case APRIL:
                return "April";
            case MAY:
                return "Mei";
            case JUNE:
                return "Juni";
            case JULY:
                return "Juli";
            case AUGUST:
                return "Agustus";
            case SEPTEMBER:
                return "September";
            case OCTOBER:
                return "Oktober";
            case NOVEMBER:
                return "November";
            case DECEMBER:
                return "Desember";
            default:
                return "";
        }
    }

    /**
     * Convert DTO to variables map for template processing.
     *
     * @return Map of variables for template injection
     */
    public Map<String, Object> toVariablesMap() {
        Map<String, Object> variables = new HashMap<>();

        addDateTimeVariables(variables);
        addHeaderVariables(variables);
        addPersonalVariables(variables);
        addEducationVariables(variables);
        addInsuranceVariables(variables);
        addFamilyVariables(variables);
        addBankingVariables(variables);
        addPhotoVariables(variables);
        addSignatureVariables(variables);
        addAdditionalVariables(variables);

        return variables;
    }

    /**
     * Add date and time variables to the map.
     */
    private void addDateTimeVariables(Map<String, Object> variables) {
        LocalDate now = LocalDate.now();
        variables.put("dayName", getIndonesianDayName(now.getDayOfWeek()));
        variables.put("date", now.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        variables.put("month", getIndonesianMonthName(now.getMonth()));
        variables.put("year", String.valueOf(now.getYear()));
        variables.put("applicationDate", getStringOrDefault(applicationDate, now.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))));
        variables.put("applicationLocation", getStringOrEmpty(applicationLocation));
    }

    /**
     * Add header information variables to the map.
     */
    private void addHeaderVariables(Map<String, Object> variables) {
        variables.put("distributionChannel", getStringOrEmpty(distributionChannel));
        variables.put("groupBD", getStringOrEmpty(groupBD));
        variables.put("salesOfficeGA", getStringOrEmpty(salesOfficeGA));
        variables.put("recruiterAgent", getStringOrEmpty(recruiterAgent));
        variables.put("agentCode", getStringOrEmpty(agentCode));
        variables.put("agentLevel", getStringOrEmpty(agentLevel));
    }

    /**
     * Add personal information variables to the map.
     */
    private void addPersonalVariables(Map<String, Object> variables) {
        variables.put("fullName", getStringOrEmpty(fullName));
        variables.put("birthPlace", getStringOrEmpty(birthPlace));
        variables.put("birthDate", getStringOrEmpty(birthDate));
        variables.put("gender", getStringOrEmpty(gender));
        variables.put("ktpNumber", getStringOrEmpty(ktpNumber));
        variables.put("ktpValidityDate", getStringOrEmpty(ktpValidityDate));
        variables.put("maritalStatus", getStringOrEmpty(maritalStatus));
        variables.put("homeAddress", getStringOrEmpty(homeAddress));
        variables.put("city", getStringOrEmpty(city));
        variables.put("postalCode", getStringOrEmpty(postalCode));
        variables.put("homePhone", getStringOrEmpty(homePhone));
        variables.put("mobilePhone", getStringOrEmpty(mobilePhone));
        variables.put("faxNumber", getStringOrEmpty(faxNumber));
        variables.put("email", getStringOrEmpty(email));
        variables.put("numberOfDependents", getStringOrEmpty(numberOfDependents));
        variables.put("occupation", getStringOrEmpty(occupation));
        variables.put("npwpNumber", getStringOrEmpty(npwpNumber));
        variables.put("incomeSource", getStringOrEmpty(incomeSource));
        variables.put("npkpAddress", getStringOrEmpty(npkpAddress));
    }

    /**
     * Add education information variables to the map.
     */
    private void addEducationVariables(Map<String, Object> variables) {
        variables.put("lastEducation", getStringOrEmpty(lastEducation));
        variables.put("educationCity", getStringOrEmpty(educationCity));
        variables.put("educationPostalCode", getStringOrEmpty(educationPostalCode));

        // Education Level Checkboxes
        variables.put("isEducationSD", getBooleanOrFalse(isEducationSD));
        variables.put("isEducationSMP", getBooleanOrFalse(isEducationSMP));
        variables.put("isEducationSMA", getBooleanOrFalse(isEducationSMA));
        variables.put("isEducationAkademi", getBooleanOrFalse(isEducationAkademi));
        variables.put("isEducationUniversitas", getBooleanOrFalse(isEducationUniversitas));
        variables.put("isEducationLainLain", getBooleanOrFalse(isEducationLainLain));
        variables.put("educationLainLainDetail", getStringOrEmpty(educationLainLainDetail));
    }

    /**
     * Add insurance experience variables to the map.
     */
    private void addInsuranceVariables(Map<String, Object> variables) {
        variables.put("hasInsuranceExperience", getStringOrEmpty(hasInsuranceExperience));
        variables.put("hasInsuranceExperienceYa", getBooleanOrFalse(hasInsuranceExperienceYa));
        variables.put("hasInsuranceExperienceTidak", getBooleanOrFalse(hasInsuranceExperienceTidak));
    }

    /**
     * Add family information variables to the map.
     */
    private void addFamilyVariables(Map<String, Object> variables) {
        variables.put("fatherName", getStringOrEmpty(fatherName));
        variables.put("motherName", getStringOrEmpty(motherName));
        variables.put("spouseName", getStringOrEmpty(spouseName));
        variables.put("fatherBirthDate", getStringOrEmpty(fatherBirthDate));
        variables.put("motherBirthDate", getStringOrEmpty(motherBirthDate));
        variables.put("spouseBirthDate", getStringOrEmpty(spouseBirthDate));
        variables.put("fatherAddress", getStringOrEmpty(fatherAddress));
        variables.put("motherAddress", getStringOrEmpty(motherAddress));
        variables.put("spouseAddress", getStringOrEmpty(spouseAddress));
        variables.put("fatherPhone", getStringOrEmpty(fatherPhone));
        variables.put("motherPhone", getStringOrEmpty(motherPhone));
        variables.put("spousePhone", getStringOrEmpty(spousePhone));
    }

    /**
     * Add banking information variables to the map.
     */
    private void addBankingVariables(Map<String, Object> variables) {
        variables.put("bankName", getStringOrEmpty(bankName));
        variables.put("accountNumber", getStringOrEmpty(accountNumber));
        variables.put("accountHolderName", getStringOrEmpty(accountHolderName));
        variables.put("branchLocation", getStringOrEmpty(branchLocation));
    }

    /**
     * Add photo information variables to the map.
     */
    private void addPhotoVariables(Map<String, Object> variables) {
        variables.put("passPhoto", getStringOrEmpty(passPhoto));
    }

    /**
     * Add signature information variables to the map.
     */
    private void addSignatureVariables(Map<String, Object> variables) {
        variables.put("candidateSignature", getStringOrEmpty(candidateSignature));
        variables.put("candidateParaf", getStringOrEmpty(candidateParaf));
        variables.put("casSignature", getStringOrEmpty(casSignature));
        variables.put("casParaf", getStringOrEmpty(casParaf));
        variables.put("casName", getStringOrEmpty(casName));
        variables.put("casRole", getStringOrEmpty(casRole));
    }

    /**
     * Add additional variables if provided.
     */
    private void addAdditionalVariables(Map<String, Object> variables) {
        if (additionalVariables != null) {
            variables.putAll(additionalVariables);
        }
    }

    /**
     * Utility method to get string value or empty string if null.
     */
    private String getStringOrEmpty(String value) {
        return value != null ? value : "";
    }

    /**
     * Utility method to get string value or default if null.
     */
    private String getStringOrDefault(String value, String defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * Utility method to get boolean value or false if null.
     */
    private boolean getBooleanOrFalse(Boolean value) {
        return Boolean.TRUE.equals(value);
    }
}
