package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.batch.*;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.launch.JobOperator;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.nio.charset.Charset;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BatchService {

    private final JobExplorer jobExplorer;

    private final JobOperator jobOperator;

    private final AmazonS3Service amazonS3Service;

    public BatchJobBasicDto getBasicJob(JobExecution data) {
        String jobType = data.getJobInstance().getJobName().endsWith("ImportJob") ? "import" : "export";

        BatchJobBasicDto response = BatchJobBasicDto.builder()
                .id(data.getId())
                .jobType(jobType)
                .redirectUrl("/api/monitoring/batch/" + jobType + "/" + data.getId())
                .build();

        if (jobType.equals("import")) {
            response.setImportJobDto(getImportJob(data.getId()));
        } else {
            response.setExportJobDto(getExportJob(data.getId()));
        }

        return response;
    }

    public BatchJobBasicDto getBasicJob(Long id) {
        JobExecution data = jobExplorer.getJobExecution(id);
        if (data == null) {
            throw new NotFoundException("Job execution not found");
        }

        String jobType = data.getJobInstance().getJobName().endsWith("ImportJob") ? "import" : "export";

        BatchJobBasicDto response = BatchJobBasicDto.builder()
                .id(id)
                .jobType(jobType)
                .redirectUrl("/api/monitoring/batch/" + jobType + "/" + id)
                .build();

        if (jobType.equals("import")) {
            response.setImportJobDto(getImportJob(id));
        } else {
            response.setExportJobDto(getExportJob(id));
        }

        return response;
    }

    public ExportJobDto getExportJob(Long id) {
        JobExecution data = jobExplorer.getJobExecution(id);

        if (data == null) {
            throw new NotFoundException("Job execution not found");
        }

        ExportJobDto.ExportJobDtoBuilder responseBuilder = ExportJobDto.builder()
                .jobId(data.getId())
                .jobName(data.getJobInstance().getJobName())
                .status(data.getStatus().toString())
                .startTime(data.getStartTime() != null ?
                        Date.from(data.getStartTime().atZone(ZoneId.systemDefault()).toInstant()) : null)
                .endTime(data.getEndTime() != null ?
                        Date.from(data.getEndTime().atZone(ZoneId.systemDefault()).toInstant()) : null);

        // Retrieve steps
        Iterator<StepExecution> it = data.getStepExecutions().iterator();
        StepExecution exportStep = it.hasNext() ? it.next() : null;

        if (exportStep != null) {
            StepExecutionDto exportStepDto = StepExecutionDto.builder()
                    .name(exportStep.getStepName())
                    .status(exportStep.getStatus().toString())
                    .readCount(exportStep.getReadCount())
                    .writeCount(exportStep.getWriteCount())
                    .skipCount(exportStep.getSkipCount())
                    .build();
            responseBuilder.exportStep(exportStepDto);
        }

        // Calculate duration
        Date start = data.getStartTime() != null ?
                Date.from(data.getStartTime().atZone(ZoneId.systemDefault()).toInstant()) : null;
        Date end = data.getEndTime() != null ?
                Date.from(data.getEndTime().atZone(ZoneId.systemDefault()).toInstant()) : null;
        if (start != null && end != null) {
            long diff = end.getTime() - start.getTime();
            responseBuilder.duration(DurationFormatUtils.formatDurationWords(diff, true, true));
        }

        // Result file info
        String path = data.getJobParameters().getString("s3FolderPath");
        String fileName = data.getJobParameters().getString("s3FileName");
        String resultFile = amazonS3Service.getUrl(path + fileName);
        responseBuilder.resultFilePath(resultFile);
        return responseBuilder.build();
    }

    public ImportJobDto getImportJob(Long id) {
        JobExecution data = jobExplorer.getJobExecution(id);

        if (data == null) {
            throw new NotFoundException("Job execution not found");
        }

        ImportJobDto.ImportJobDtoBuilder responseBuilder = ImportJobDto.builder()
                .jobId(data.getId())
                .jobName(data.getJobInstance().getJobName())
                .status(data.getStatus().toString())
                .startTime(data.getStartTime() != null ?
                        Date.from(data.getStartTime().atZone(ZoneId.systemDefault()).toInstant()) : null)
                .endTime(data.getEndTime() != null ?
                        Date.from(data.getEndTime().atZone(ZoneId.systemDefault()).toInstant()) : null);

        // Retrieve and map all steps
        List<StepExecutionDto> stepExecutionDtos = data.getStepExecutions().stream()
                .map(step -> StepExecutionDto.builder()
                        .name(step.getStepName())
                        .status(step.getStatus().toString())
                        .readCount(step.getReadCount())
                        .writeCount(step.getWriteCount())
                        .skipCount(step.getSkipCount())
                        .startTime(step.getStartTime() != null ?
                                Date.from(step.getStartTime().atZone(ZoneId.systemDefault()).toInstant()) : null)
                        .endTime(step.getEndTime() != null ?
                                Date.from(step.getEndTime().atZone(ZoneId.systemDefault()).toInstant()) : null)
                        .build())
                .collect(Collectors.toList());

        responseBuilder.steps(stepExecutionDtos);

        // Calculate duration
        Date start = data.getStartTime() != null ?
                Date.from(data.getStartTime().atZone(ZoneId.systemDefault()).toInstant()) : null;
        Date end = data.getEndTime() != null ?
                Date.from(data.getEndTime().atZone(ZoneId.systemDefault()).toInstant()) : null;
        if (start != null && end != null) {
            long diff = end.getTime() - start.getTime();
            responseBuilder.duration(DurationFormatUtils.formatDurationWords(diff, true, true));
        }

        // Original file info
        String path = data.getJobParameters().getString("s3FolderPath");
        String fileName = data.getJobParameters().getString("s3FileName");
        if (null != fileName) {
            String resultFile = amazonS3Service.getUrl(path + fileName);
            responseBuilder.originalFilePath(resultFile);
        }

        // Error log info
        // Assuming you want to check for error logs in any step with write count > 0
        Optional<StepExecutionDto> stepWithErrors = stepExecutionDtos.stream()
                .filter(step -> step.getWriteCount() > 0 && step.getWriteCount() != step.getReadCount())
                .findFirst();

        if (stepWithErrors.isPresent()) {
            String errorLog = amazonS3Service.getUrl(path + fileName + ".log");
            responseBuilder.errorLogPath(errorLog);

            try {
                String errorContent = IOUtils.toString(new URI(path + fileName + ".log"), Charset.defaultCharset());
                responseBuilder.errorContent(errorContent);
            } catch (Exception ex) {
                responseBuilder.errorReadFailed(true);
            }
        }

        return responseBuilder.build();
    }

    public JobOperationDto stop(Long id) {
        try {
            jobOperator.stop(id);
            JobOperationDto response = JobOperationDto.builder()
                    .status("success")
                    .message("Job stop request submitted")
                    .build();
            return response;
        } catch (JobExecutionException ex) {
            log.warn("An error happened while trying to stop batch job", ex);
            JobOperationDto response = JobOperationDto.builder()
                    .status("error")
                    .message("Failed to stop job: " + ex.getMessage())
                    .build();
            return response;
        }
    }

    public JobStatusDto getJobStatus(Long id) {
        JobExecution data = jobExplorer.getJobExecution(id);

        if (data == null) {
            throw new NotFoundException("Job execution not found");
        }

        JobStatusDto response = JobStatusDto.builder()
                .status(data.getStatus().toString())
                .build();
        return response;
    }

    public List<BatchJobBasicDto> getLast30JobExecutions() {
        // Get all job names
        List<String> jobNames = jobExplorer.getJobNames();

        List<JobExecution> last30Executions = new ArrayList<>();

        for (String jobName : jobNames) {
            // Retrieve job instances, starting from the most recent
            List<JobInstance> jobInstances = jobExplorer.getJobInstances(jobName, 0, 30);

            for (JobInstance jobInstance : jobInstances) {
                List<JobExecution> executions = jobExplorer.getJobExecutions(jobInstance);
                last30Executions.addAll(executions);
            }
        }

        // Sort executions by creation time in descending order
        Collections.sort(last30Executions, (e1, e2) ->
                e2.getCreateTime().compareTo(e1.getCreateTime()));

        // Return only the top 30 converted to DTOs
        return last30Executions.stream()
                .limit(30)
                .map(this::getBasicJob)
                .collect(Collectors.toList());
    }

    // Get job names
    public List<String> getAllJobNames() {
        return jobExplorer.getJobNames();
    }
}
