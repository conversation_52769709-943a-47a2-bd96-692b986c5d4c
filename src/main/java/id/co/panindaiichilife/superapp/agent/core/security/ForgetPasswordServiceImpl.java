package id.co.panindaiichilife.superapp.agent.core.security;

import com.sendgrid.helpers.mail.Mail;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.service.JwtService;
import id.co.panindaiichilife.superapp.agent.core.service.MailService;
import id.co.panindaiichilife.superapp.agent.core.support.HttpUtils;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import id.co.panindaiichilife.superapp.agent.service.GlobalConfigService;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public class ForgetPasswordServiceImpl implements ForgetPasswordService {

    private final JwtService jwtService;
    private final MailService mailService;
    private final UserRepository userRepository;
    private final ResourceLoader resourceLoader;
    private final GlobalConfigService globalConfigService;

    @Value("${jwt.expiry.forget-password}")
    private Long expiry;

    @Value("${mail.base-url:}")
    private String baseUrl;

    protected String getResetPasswordUrl(String token) {
        return "/forget-password/reset?token=" + token;
    }

    protected String getForgetPasswordTemplate() {
        return globalConfigService.getGlobalConfig("mail.forgot-password.template.id", "d-97f659a4611e4ab59ed4c3c794e42810");
    }

    public void requestResetPassword(User user) {
        if (StringUtils.isBlank(baseUrl)) {
            baseUrl = HttpUtils.getBaseUrl();
        }

        long expiryMillis = expiry * 1000;
        String token = jwtService.encodeUser(new UserDetailsExtraImpl(user), expiryMillis);

        Map<String, Object> data = new HashMap<>();
        data.put("name", user.getName());
        data.put("email", user.getEmail());
        data.put("url", baseUrl + getResetPasswordUrl(token));

        try {
            String template = getForgetPasswordTemplate();
            String emailAddress = ObjectUtils.firstNonNull(user.getEmail(), user.getUsername());
            Mail email = mailService.createEmail(template, data, emailAddress);
            mailService.sendEmail(email);
        } catch (MessagingException ex) {
            log.error("Error sending email", ex);
        }
    }

    public User readUserFromToken(String token) {
        UserDetails ud = jwtService.decodeUser(token);
        return ud == null ? null : userRepository.findByUsername(ud.getUsername()).orElseThrow(NotFoundException::new);
    }

}
