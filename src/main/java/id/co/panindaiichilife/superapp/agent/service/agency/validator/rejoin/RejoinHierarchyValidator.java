package id.co.panindaiichilife.superapp.agent.service.agency.validator.rejoin;

import com.google.gson.Gson;
import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxRejoinApplicationForm;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalValidationHirarkiResponseDto;
import id.co.panindaiichilife.superapp.agent.enums.ValidationHirarkiStatus;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;

@Slf4j
@Component
@RequiredArgsConstructor
public class RejoinHierarchyValidator extends AbstractRejoinValidator {
    private final PortalProvider portalProvider;

    @Override
    public boolean canValidate(TrxRejoinApplicationForm form, TrxRejoinApplication entity) {
        return StringUtils.isNotBlank(form.getProposedLeaderCode()) && entity.getProposedLevel() != null;
    }

    @Override
    protected TrxRejoinApplication doValidate(TrxRejoinApplicationForm form, TrxRejoinApplication entity) {
        PortalValidationHirarkiResponseDto response = validateHirarki(entity.getProposedLeaderCode(),
                entity.getProposedLevel());

        if (response != null) {
            // Store the response as JSON
            entity.setResultValidationHirarki(new Gson().toJson(response.getUplineDTO()));
            entity.setValidationHirarkiStatus(ValidationHirarkiStatus.PASS);
            log.info("Hierarchy validation passed for leader code: {}", form.getProposedLeaderCode());
        } else {
            entity.setValidationHirarkiStatus(ValidationHirarkiStatus.POSTPONED);
            log.warn("Hierarchy validation skipped or failed for leader code: {}", form.getProposedLeaderCode());
        }

        return entity;
    }

    @Override
    public String getValidatorName() {
        return "Rejoin Hierarchy Validator";
    }

    private PortalValidationHirarkiResponseDto validateHirarki(String proposedLeaderCode, String proposedLevel) {
        try {
            if (StringUtils.isBlank(proposedLeaderCode) || StringUtils.isBlank(proposedLevel)) {
                log.warn("Hierarchy validation skipped - missing required data");
                return null;
            }

            // Call the portal API to validate hierarchy
            Call<PortalValidationHirarkiResponseDto> call = portalProvider.getAgentUpline(proposedLeaderCode, proposedLevel);
            Response<PortalValidationHirarkiResponseDto> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                PortalValidationHirarkiResponseDto hirarkiResponse = response.body();

                // Check if the API call was successful
                if ("200".equals(hirarkiResponse.getStatusCode())) {
                    log.info("Successfully retrieved upline information for leader code: {}", proposedLeaderCode);
                    return hirarkiResponse;
                } else {
                    log.error("Error in hierarchy validation: {} - {}", hirarkiResponse.getStatusCode(), hirarkiResponse.getMessage());
                    return null;
                }
            } else {
                log.error("Failed to validate hierarchy: {}", response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                return null;
            }
        } catch (Exception e) {
            log.error("Error validating hierarchy", e);
            return null;
        }
    }
}
