package id.co.panindaiichilife.superapp.agent.api.dto.cms;

import id.co.panindaiichilife.superapp.agent.core.view.BaseDto;
import id.co.panindaiichilife.superapp.agent.enums.ApprovalStatus;
import id.co.panindaiichilife.superapp.agent.enums.TrxType;
import id.co.panindaiichilife.superapp.agent.model.TrxApprovalHeader;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
public class ApprovalHeaderDto extends BaseDto<TrxApprovalHeader> {
    private Long id;
    private String requestId;
    private RequestByDto requestBy;
    private TrxType trxType;
    private Long trxId;
    private ApprovalStatus approvalStatus;
    private Integer currentLevel;
    private String approverRole;
    private Integer maxLevel;
    private String remarks;
    private Set<ApprovalDetailDto> approvalDetails;
    private String detailApproval;
    private String lastApproverRole;
    private Object detailData;
    private Integer lastLevel;
    private Instant createdAt;
    private Instant updatedAt;

    @Override
    public void copy(TrxApprovalHeader data) {
        super.copy(data);
        requestBy = BaseDto.of(RequestByDto.class, data.getRequestBy());
        approvalDetails = BaseDto.of(ApprovalDetailDto.class, data.getApprovalDetails());
    }
}
