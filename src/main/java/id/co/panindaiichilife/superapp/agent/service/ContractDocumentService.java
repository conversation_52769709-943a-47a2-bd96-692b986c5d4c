package id.co.panindaiichilife.superapp.agent.service;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.RoleDto;
import id.co.panindaiichilife.superapp.agent.api.dto.cms.SignatureDto;
import id.co.panindaiichilife.superapp.agent.api.dto.pdf.*;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.core.service.DocumentGenerationService;
import id.co.panindaiichilife.superapp.agent.enums.Channel;
import id.co.panindaiichilife.superapp.agent.enums.PositionLevel;
import id.co.panindaiichilife.superapp.agent.model.TrxRecruitment;
import id.co.panindaiichilife.superapp.agent.service.agency.TrxRecruitmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for generating contract documents (PKAJ, PMKAJ, ANTI TWISTING, KODE ETIK)
 * with proper contract number formatting, S3 storage, and database linking.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ContractDocumentService {

    private final DocumentGenerationService documentGenerationService;
    private final AmazonS3Service amazonS3Service;
    private final TrxRecruitmentService trxRecruitmentService;
    private final SignatureService signatureService;

    /**
     * Generate all contract documents for an approved recruitment.
     * This includes APGEN-AGE, PKAJ, PMKAJ (only for BM and BD), ANTI TWISTING, and KODE ETIK.
     *
     * @param recruitment The approved recruitment
     * @return Map containing the S3 URLs of generated documents
     */
    @Transactional
    public Map<String, String> generateAllContractDocuments(TrxRecruitment recruitment) {
        log.info("Starting contract document generation for recruitment ID: {}, agent code: {}",
                recruitment.getId(), recruitment.getAgentCode());

        Map<String, String> documentUrls = new HashMap<>();

        try {
            // Generate APGEN-AGE document (agency application form - required for all)
            String apgenUrl = generateApgenAgeDocument(recruitment);
            if (apgenUrl != null) {
                documentUrls.put("APGEN_AGE", apgenUrl);
                log.info("APGEN-AGE document generated and stored: {}", apgenUrl);
            }

            // Generate PKAJ document (required for all)
            String pkajUrl = generatePkajDocument(recruitment);
            if (pkajUrl != null) {
                documentUrls.put("PKAJ", pkajUrl);
                log.info("PKAJ document generated and stored: {}", pkajUrl);
            }

            // Generate PMKAJ document (only for BM and BD)
            if (shouldGeneratePmkaj(recruitment.getPositionLevel())) {
                String pmkajUrl = generatePmkajDocument(recruitment);
                if (pmkajUrl != null) {
                    documentUrls.put("PMKAJ", pmkajUrl);
                    log.info("PMKAJ document generated and stored: {}", pmkajUrl);
                }
            } else {
                log.info("PMKAJ document not generated - position level {} does not require PMKAJ",
                        recruitment.getPositionLevel());
            }

            // Generate ANTI TWISTING document
            String antiTwistingUrl = generateAntiTwistingDocument(recruitment);
            if (antiTwistingUrl != null) {
                documentUrls.put("ANTI_TWISTING", antiTwistingUrl);
                log.info("ANTI TWISTING document generated and stored: {}", antiTwistingUrl);
            }

            // Generate KODE ETIK document
            String kodeEtikUrl = generateKodeEtikDocument(recruitment);
            if (kodeEtikUrl != null) {
                documentUrls.put("KODE_ETIK", kodeEtikUrl);
                log.info("KODE ETIK document generated and stored: {}", kodeEtikUrl);
            }

            // Update recruitment record with document URLs
            updateRecruitmentWithDocumentUrls(recruitment, documentUrls);

            log.info("All contract documents generated successfully for recruitment ID: {}", recruitment.getId());
            return documentUrls;

        } catch (Exception e) {
            log.error("Error generating contract documents for recruitment ID: {}", recruitment.getId(), e);
            throw new RuntimeException("Failed to generate contract documents", e);
        }
    }

    /**
     * Generate APGEN-AGE (Aplikasi Keagenan) document.
     */
    private String generateApgenAgeDocument(TrxRecruitment recruitment) {
        try {
            // Get CAS signature information from Signature Management module
            CasSignatureInfo casInfo = getCasSignatureInfo(recruitment.getChannel(), "APGEN");

            // Determine checkbox states based on available data
            EducationCheckboxes educationCheckboxes = determineEducationCheckboxes(recruitment);
            InsuranceExperienceCheckboxes insuranceCheckboxes = determineInsuranceExperienceCheckboxes(recruitment);

            // Build comprehensive DTO with all recruitment data
            ApgenAgeDocumentDto dto = ApgenAgeDocumentDto.builder()
                    // Header Information
                    .distributionChannel(recruitment.getChannel() != null ? mapChannelToCode(recruitment.getChannel()) : "")
                    .groupBD("") // This might need to be retrieved from another source
                    .salesOfficeGA("") // This might need to be retrieved from another source
                    .recruiterAgent(recruitment.getRecruiter() != null ? recruitment.getRecruiter().getName() : "")
                    .agentCode(recruitment.getAgentCode() != null ? recruitment.getAgentCode() : "")
                    .agentLevel(recruitment.getPositionLevel() != null ? recruitment.getPositionLevel().name() : "")

                    // Personal Information
                    .fullName(recruitment.getFullName() != null ? recruitment.getFullName() : "")
                    .birthPlace(recruitment.getBirthPlace() != null ? recruitment.getBirthPlace() : "")
                    .birthDate(recruitment.getBirthDate() != null ? recruitment.getBirthDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "")
                    .gender(recruitment.getGender() != null ? recruitment.getGender().name() : "")
                    .ktpNumber(recruitment.getNik() != null ? recruitment.getNik() : "")
                    .ktpValidityDate("") // Not available in recruitment data
                    .maritalStatus(recruitment.getMaritalStatus() != null ? recruitment.getMaritalStatus().name() : "")
                    .homeAddress(recruitment.getDomicileAddress() != null ? recruitment.getDomicileAddress() : "")
                    .city(recruitment.getDomicileCity() != null ? recruitment.getDomicileCity() : "")
                    .postalCode("")
                    .homePhone("")
                    .mobilePhone(recruitment.getPhoneNumber() != null ? recruitment.getPhoneNumber() : "")
                    .faxNumber("") // Not available in recruitment data
                    .email(recruitment.getEmail() != null ? recruitment.getEmail() : "")
                    .numberOfDependents("") // Not available in recruitment data
                    .occupation(recruitment.getOccupation() != null ? recruitment.getOccupation() : "")
                    .npwpNumber("")
                    .incomeSource("") // Not available in recruitment data
                    .npkpAddress("") // Not available in recruitment data

                    // Education Information
                    .lastEducation("")
                    .educationCity("") // Not available in recruitment data
                    .educationPostalCode("") // Not available in recruitment data

                    // Education Level Checkboxes (determined based on occupation and other data)
                    .isEducationSD(educationCheckboxes.isEducationSD)
                    .isEducationSMP(educationCheckboxes.isEducationSMP)
                    .isEducationSMA(educationCheckboxes.isEducationSMA)
                    .isEducationAkademi(educationCheckboxes.isEducationAkademi)
                    .isEducationUniversitas(educationCheckboxes.isEducationUniversitas)
                    .isEducationLainLain(educationCheckboxes.isEducationLainLain)
                    .educationLainLainDetail(educationCheckboxes.educationLainLainDetail)

                    // Insurance Experience (determined based on job history)
                    .hasInsuranceExperience(insuranceCheckboxes.hasInsuranceExperienceYa ? "Ya" : "Tidak")
                    .hasInsuranceExperienceYa(insuranceCheckboxes.hasInsuranceExperienceYa)
                    .hasInsuranceExperienceTidak(insuranceCheckboxes.hasInsuranceExperienceTidak)

                    // Family Information (use emergency contact data where available)
                    .fatherName("") // Not available in recruitment data
                    .motherName("") // Not available in recruitment data
                    .spouseName("") // Not available in recruitment data
                    .fatherBirthDate("") // Not available in recruitment data
                    .motherBirthDate("") // Not available in recruitment data
                    .spouseBirthDate("") // Not available in recruitment data
                    .fatherAddress("") // Not available in recruitment data
                    .motherAddress("") // Not available in recruitment data
                    .spouseAddress("") // Not available in recruitment data
                    .fatherPhone("") // Not available in recruitment data
                    .motherPhone("") // Not available in recruitment data
                    .spousePhone("") // Not available in recruitment data

                    // Banking Information
                    .bankName(recruitment.getBank() != null ? recruitment.getBank().getBankName() : "")
                    .accountNumber(recruitment.getBankAccountNumber() != null ? recruitment.getBankAccountNumber() : "")
                    .accountHolderName(recruitment.getBankAccountName() != null ? recruitment.getBankAccountName() : "")
                    .branchLocation("") // Not available in recruitment data

                    // Photo Information
                    .passPhoto(recruitment.getPassPhoto() != null ? recruitment.getPassPhoto() : "")

                    // Signature Information
                    .candidateSignature(recruitment.getSignature() != null ? recruitment.getSignature() : "")
                    .candidateParaf(recruitment.getParaf() != null ? recruitment.getParaf() : "")
                    .casSignature(casInfo.signature)
                    .casParaf(casInfo.paraf)
                    .casName(casInfo.name)
                    .casRole(casInfo.role)

                    // Additional Information
                    .applicationDate(LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")))
                    .applicationLocation(recruitment.getDomicileCity() != null ? recruitment.getDomicileCity() : "")
                    .build();

            // Generate PDF
            byte[] pdfBytes = documentGenerationService.generateApgenAgeDocument(dto);

            // Store in S3
            String fileName = String.format("APGEN_AGE_%s_%s.pdf",
                    recruitment.getAgentCode(),
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            String s3Path = String.format("/assets/recruitment/contracts/apgen-age/%s", fileName);

            boolean stored = amazonS3Service.storeBytes(pdfBytes, s3Path, "application/pdf");
            if (stored) {
                return amazonS3Service.getUrl(s3Path);
            }

        } catch (Exception e) {
            log.error("Error generating APGEN-AGE document for recruitment ID: {}", recruitment.getId(), e);
        }
        return null;
    }

    /**
     * Generate PKAJ document with proper contract number format.
     * Format: Kodeagent/SPK/HO/bulanbergabung/tahunbergabung
     */
    private String generatePkajDocument(TrxRecruitment recruitment) {
        try {
            // Generate contract number
            String contractNumber = generatePkajContractNumber(recruitment);

            // Get CAS signature information from Signature Management module
            CasSignatureInfo casInfo = getCasSignatureInfo(recruitment.getChannel(), "PKAJ");

            // Build DTO
            PkajAgeDocumentDto dto = PkajAgeDocumentDto.builder()
                    .pkajNumber(contractNumber)
                    .address(recruitment.getDomicileAddress())
                    .candidateName(recruitment.getFullName())
                    .candidateBirthOfPlace(recruitment.getBirthPlace())
                    .candidateDomicileCity(recruitment.getDomicileCity())
                    .candidateKtp(recruitment.getNik())
                    .candidateSignature(recruitment.getSignature())
                    .candidateParaf(recruitment.getParaf())
                    .casSignature(casInfo.signature)
                    .casParaf(casInfo.paraf)
                    .casName(casInfo.name)
                    .casRole(casInfo.role)
                    .build();

            // Generate PDF
            byte[] pdfBytes = documentGenerationService.generatePkajAgeDocument(dto);

            // Store in S3
            String fileName = String.format("PKAJ_%s_%s.pdf",
                    recruitment.getAgentCode(),
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            String s3Path = String.format("/assets/recruitment/contracts/pkaj/%s", fileName);

            boolean stored = amazonS3Service.storeBytes(pdfBytes, s3Path, "application/pdf");
            if (stored) {
                return amazonS3Service.getUrl(s3Path);
            }

        } catch (Exception e) {
            log.error("Error generating PKAJ document for recruitment ID: {}", recruitment.getId(), e);
        }
        return null;
    }

    /**
     * Generate PMKAJ document with proper contract number format.
     * Format: Kodeagent/MNGR/bulanpromosi/tahunpromosi
     * Only for BM and BD position levels.
     */
    private String generatePmkajDocument(TrxRecruitment recruitment) {
        try {
            // Generate contract number
            String contractNumber = generatePmkajContractNumber(recruitment);

            // Get CAS signature information from Signature Management module
            CasSignatureInfo casInfo = getCasSignatureInfo(recruitment.getChannel(), "PMKAJ");

            // Build DTO
            PmkajAgeDocumentDto dto = PmkajAgeDocumentDto.builder()
                    .pmkajNumber(contractNumber)
                    .pkajNumber(generatePkajContractNumber(recruitment)) // Reference to PKAJ
                    .address(recruitment.getDomicileAddress())
                    .candidateName(recruitment.getFullName())
                    .candidateBirthOfPlace(recruitment.getBirthPlace())
                    .candidateDomicileCity(recruitment.getDomicileCity())
                    .candidateKtp(recruitment.getNik())
                    .candidateSignature(recruitment.getSignature())
                    .candidateParaf(recruitment.getParaf())
                    .casSignature(casInfo.signature)
                    .casParaf(casInfo.paraf)
                    .casName(casInfo.name)
                    .casRole(casInfo.role)
                    .build();

            log.debug("PMKAJ DTO: {}", dto);

            // Generate PDF
            byte[] pdfBytes = documentGenerationService.generatePmkajAgeDocument(dto);

            // Store in S3
            String fileName = String.format("PMKAJ_%s_%s.pdf",
                    recruitment.getAgentCode(),
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            String s3Path = String.format("/assets/recruitment/contracts/pmkaj/%s", fileName);

            boolean stored = amazonS3Service.storeBytes(pdfBytes, s3Path, "application/pdf");
            if (stored) {
                return amazonS3Service.getUrl(s3Path);
            }

        } catch (Exception e) {
            log.error("Error generating PMKAJ document for recruitment ID: {}", recruitment.getId(), e);
        }
        return null;
    }

    /**
     * Generate ANTI TWISTING document.
     */
    private String generateAntiTwistingDocument(TrxRecruitment recruitment) {
        try {
            // Build DTO
            AntiTwistingAgeDocumentDto dto = AntiTwistingAgeDocumentDto.builder()
                    .agentName(recruitment.getFullName())
                    .ktpNumber(recruitment.getNik())
                    .placeOfBirth(recruitment.getBirthPlace())
                    .dateOfBirth(recruitment.getBirthDate() != null ?
                            recruitment.getBirthDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "")
                    .address(recruitment.getDomicileAddress())
                    .location(recruitment.getBranch() != null ? recruitment.getBranch().getCity() : "")
                    .signature(recruitment.getSignature())
                    .build();

            // Generate PDF
            byte[] pdfBytes = documentGenerationService.generateAntiTwistingAgeDocument(dto);

            // Store in S3
            String fileName = String.format("ANTI_TWISTING_%s_%s.pdf",
                    recruitment.getAgentCode(),
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            String s3Path = String.format("/assets/recruitment/contracts/anti-twisting/%s", fileName);

            boolean stored = amazonS3Service.storeBytes(pdfBytes, s3Path, "application/pdf");
            if (stored) {
                return amazonS3Service.getUrl(s3Path);
            }

        } catch (Exception e) {
            log.error("Error generating ANTI TWISTING document for recruitment ID: {}", recruitment.getId(), e);
        }
        return null;
    }

    /**
     * Generate KODE ETIK document.
     */
    private String generateKodeEtikDocument(TrxRecruitment recruitment) {
        try {
            // Build DTO
            KodeEtikAgeDocumentDto dto = KodeEtikAgeDocumentDto.builder()
                    .agentName(recruitment.getFullName())
                    .company("PT Panin Dai-ichi Life")
                    .ktpNumber(recruitment.getNik())
                    .signature(recruitment.getSignature())
                    .build();

            // Generate PDF
            byte[] pdfBytes = documentGenerationService.generateKodeEtikAgeDocument(dto);

            // Store in S3
            String fileName = String.format("KODE_ETIK_%s_%s.pdf",
                    recruitment.getAgentCode(),
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            String s3Path = String.format("/assets/recruitment/contracts/kode-etik/%s", fileName);

            boolean stored = amazonS3Service.storeBytes(pdfBytes, s3Path, "application/pdf");
            if (stored) {
                return amazonS3Service.getUrl(s3Path);
            }

        } catch (Exception e) {
            log.error("Error generating KODE ETIK document for recruitment ID: {}", recruitment.getId(), e);
        }
        return null;
    }

    /**
     * Generate PKAJ contract number.
     * Format: Kodeagent/SPK/HO/bulanbergabung/tahunbergabung
     */
    private String generatePkajContractNumber(TrxRecruitment recruitment) {
        LocalDate joinDate = LocalDate.now(); // Use current date as join date
        String month = String.format("%02d", joinDate.getMonthValue());
        String year = String.valueOf(joinDate.getYear());

        return String.format("%s/SPK/HO/%s/%s",
                recruitment.getAgentCode(), month, year);
    }

    /**
     * Generate PMKAJ contract number.
     * Format: Kodeagent/MNGR/bulanpromosi/tahunpromosi
     */
    private String generatePmkajContractNumber(TrxRecruitment recruitment) {
        LocalDate promotionDate = LocalDate.now(); // Use current date as promotion date
        String month = String.format("%02d", promotionDate.getMonthValue());
        String year = String.valueOf(promotionDate.getYear());

        return String.format("%s/MNGR/%s/%s",
                recruitment.getAgentCode(), month, year);
    }

    /**
     * Check if PMKAJ document should be generated based on position level.
     * Only BM (Branch Manager) and BD (Business Development) require PMKAJ.
     */
    private boolean shouldGeneratePmkaj(PositionLevel positionLevel) {
        return positionLevel == PositionLevel.BM || positionLevel == PositionLevel.BD;
    }

    /**
     * Get CAS signature information from Signature Management module.
     *
     * @param channel      The channel (AGE, BAN)
     * @param documentType The document type (PKAJ, PMKAJ, etc.)
     * @return CasSignatureInfo containing signature, paraf, name, and role
     */
    private CasSignatureInfo getCasSignatureInfo(Channel channel, String documentType) {
        log.info("Retrieving CAS signature for channel: {} and document type: {}", channel, documentType);

        try {
            SignatureDto signatureDto = signatureService.findByChannelAndDocumentType(channel, documentType);

            if (signatureDto != null) {
                CasSignatureInfo casInfo = new CasSignatureInfo();
                casInfo.signature = signatureDto.getSignature() != null ? signatureDto.getSignature() : "";
                casInfo.paraf = signatureDto.getParaf() != null ? signatureDto.getParaf() : "";

                // Get user information for name and role
                if (signatureDto.getUser() != null) {
                    casInfo.name = signatureDto.getUser().getName() != null ? signatureDto.getUser().getName() : "";

                    // Extract role from user roles - select the most appropriate role
                    if (signatureDto.getUser().getRoles() != null && !signatureDto.getUser().getRoles().isEmpty()) {
                        casInfo.role = selectBestRole(signatureDto.getUser().getRoles());
                        log.debug("Retrieved role from user: {}", casInfo.role);
                    } else {
                        casInfo.role = "CAS"; // Default role when no roles found
                        log.debug("No roles found for user, using default role: CAS");
                    }
                } else {
                    casInfo.name = "";
                    casInfo.role = "CAS";
                }

                log.info("Successfully retrieved CAS signature for channel: {} and document type: {}", channel, documentType);
                return casInfo;
            } else {
                log.warn("No CAS signature found for channel: {} and document type: {}, using empty values", channel, documentType);
                return new CasSignatureInfo(); // Returns empty values
            }
        } catch (Exception e) {
            log.error("Error retrieving CAS signature for channel: {} and document type: {}", channel, documentType, e);
            return new CasSignatureInfo(); // Returns empty values on error
        }
    }

    /**
     * Update recruitment record with generated document URLs.
     */
    private void updateRecruitmentWithDocumentUrls(TrxRecruitment recruitment, Map<String, String> documentUrls) {
        try {
            // Generate contract number
            String pkajContractNumber = generatePkajContractNumber(recruitment);
            // Generate contract number
            String pmkajContractNumber = generatePmkajContractNumber(recruitment);

            trxRecruitmentService.updateDocumentUrls(
                    recruitment.getId(),
                    documentUrls.get("APGEN_AGE"),
                    documentUrls.get("PKAJ"),
                    documentUrls.get("PMKAJ"),
                    documentUrls.get("ANTI_TWISTING"),
                    documentUrls.get("KODE_ETIK"),
                    pkajContractNumber,
                    pmkajContractNumber
            );
            log.info("Updated recruitment {} with document URLs", recruitment.getId());
        } catch (Exception e) {
            log.error("Error updating recruitment {} with document URLs", recruitment.getId(), e);
        }
    }

    /**
     * Map channel enum to appropriate letter code.
     * AGE = A, BAN = L
     *
     * @param channel The channel enum
     * @return The corresponding letter code
     */
    private String mapChannelToCode(Channel channel) {
        switch (channel) {
            case AGE:
                return "A";
            case BAN:
                return "L";
            default:
                log.warn("Unknown channel: {}, returning channel name", channel);
                return channel.name();
        }
    }

    /**
     * Select the best role from user roles based on priority.
     * Prioritizes management and leadership roles over general roles.
     *
     * @param roles Collection of user roles
     * @return The most appropriate role name
     */
    private String selectBestRole(Collection<RoleDto> roles) {
        if (roles == null || roles.isEmpty()) {
            return "CAS";
        }

        // Define role priority (higher priority roles first)
        String[] rolePriority = {
                "DIRECTOR", "DIREKTUR", "HEAD", "KEPALA", "MANAGER", "MANAJER",
                "SUPERVISOR", "LEADER", "PEMIMPIN", "CAS", "ADMIN", "USER"
        };

        // Look for roles in priority order
        for (String priorityRole : rolePriority) {
            for (RoleDto role : roles) {
                if (role.getName() != null &&
                        role.getName().toUpperCase().contains(priorityRole)) {
                    log.debug("Selected role '{}' based on priority match with '{}'", role.getName(), priorityRole);
                    return role.getName();
                }
            }
        }

        // If no priority match found, return the first role
        RoleDto firstRole = roles.iterator().next();
        String roleName = firstRole.getName() != null ? firstRole.getName() : "CAS";
        log.debug("No priority match found, using first role: {}", roleName);
        return roleName;
    }

    /**
     * Determine education level checkboxes based on occupation and other available data.
     * This is a heuristic approach since education level is not directly stored in recruitment.
     *
     * @param recruitment The recruitment data
     * @return EducationCheckboxes object with appropriate checkboxes set
     */
    private EducationCheckboxes determineEducationCheckboxes(TrxRecruitment recruitment) {
        EducationCheckboxes checkboxes = new EducationCheckboxes();

        // Default all to false
        checkboxes.isEducationSD = false;
        checkboxes.isEducationSMP = false;
        checkboxes.isEducationSMA = false;
        checkboxes.isEducationAkademi = false;
        checkboxes.isEducationUniversitas = false;
        checkboxes.isEducationLainLain = false;
        checkboxes.educationLainLainDetail = "";

        // Use occupation to make educated guess about education level
        String occupation = recruitment.getOccupation();
        if (occupation != null) {
            String occupationLower = occupation.toLowerCase();

            // Professional occupations typically require university education
            if (occupationLower.contains("dokter") || occupationLower.contains("engineer") ||
                    occupationLower.contains("lawyer") || occupationLower.contains("manager") ||
                    occupationLower.contains("consultant") || occupationLower.contains("analyst") ||
                    occupationLower.contains("architect") || occupationLower.contains("professor") ||
                    occupationLower.contains("dosen") || occupationLower.contains("guru")) {
                checkboxes.isEducationUniversitas = true;
            }
            // Technical/vocational occupations might require academy/diploma
            else if (occupationLower.contains("technician") || occupationLower.contains("teknisi") ||
                    occupationLower.contains("operator") || occupationLower.contains("supervisor")) {
                checkboxes.isEducationAkademi = true;
            }
            // Default to SMA for most other occupations
            else {
                checkboxes.isEducationSMA = true;
            }
        } else {
            // If no occupation data, default to SMA
            checkboxes.isEducationSMA = true;
        }

        return checkboxes;
    }

    /**
     * Determine insurance experience checkboxes based on last job and experience data.
     *
     * @param recruitment The recruitment data
     * @return InsuranceExperienceCheckboxes object with appropriate checkboxes set
     */
    private InsuranceExperienceCheckboxes determineInsuranceExperienceCheckboxes(TrxRecruitment recruitment) {
        InsuranceExperienceCheckboxes checkboxes = new InsuranceExperienceCheckboxes();

        // Check if last job or any job history indicates insurance experience
        String lastJob = recruitment.getLastJob();
        boolean hasInsuranceExperience = false;

        if (lastJob != null) {
            String lastJobLower = lastJob.toLowerCase();
            if (lastJobLower.equals("sales asuransi jiwa")) {
                hasInsuranceExperience = true;
            }
        }

        checkboxes.hasInsuranceExperienceYa = hasInsuranceExperience;
        checkboxes.hasInsuranceExperienceTidak = !hasInsuranceExperience;

        return checkboxes;
    }

    /**
     * Example method showing how to manually create an ApgenAgeDocumentDto with specific checkbox values.
     * This can be used for testing or when you need to override the automatic checkbox determination.
     *
     * @param recruitment The recruitment data
     * @return ApgenAgeDocumentDto with manually set checkbox values
     */
    public ApgenAgeDocumentDto createManualApgenAgeDto(TrxRecruitment recruitment) {
        // Get CAS signature information
        CasSignatureInfo casInfo = getCasSignatureInfo(recruitment.getChannel(), "APGEN");

        return ApgenAgeDocumentDto.builder()
                // Header Information
                .distributionChannel(recruitment.getChannel() != null ? mapChannelToCode(recruitment.getChannel()) : "")
                .groupBD("")
                .salesOfficeGA("")
                .recruiterAgent(recruitment.getRecruiter() != null ? recruitment.getRecruiter().getName() : "")
                .agentCode(recruitment.getAgentCode() != null ? recruitment.getAgentCode() : "")
                .agentLevel(recruitment.getPositionLevel() != null ? recruitment.getPositionLevel().name() : "")

                // Personal Information
                .fullName(recruitment.getFullName() != null ? recruitment.getFullName() : "")
                .birthPlace(recruitment.getBirthPlace() != null ? recruitment.getBirthPlace() : "")
                .birthDate(recruitment.getBirthDate() != null ? recruitment.getBirthDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "")
                .gender(recruitment.getGender() != null ? recruitment.getGender().name() : "")
                .ktpNumber(recruitment.getNik() != null ? recruitment.getNik() : "")
                .ktpValidityDate("")
                .maritalStatus(recruitment.getMaritalStatus() != null ? recruitment.getMaritalStatus().name() : "")
                .homeAddress(recruitment.getDomicileAddress() != null ? recruitment.getDomicileAddress() : "")
                .city(recruitment.getDomicileCity() != null ? recruitment.getDomicileCity() : "")
                .postalCode("")
                .homePhone(recruitment.getPhoneNumber() != null ? recruitment.getPhoneNumber() : "")
                .mobilePhone(recruitment.getPhoneNumber() != null ? recruitment.getPhoneNumber() : "")
                .faxNumber("")
                .email(recruitment.getEmail() != null ? recruitment.getEmail() : "")
                .numberOfDependents("")
                .occupation(recruitment.getOccupation() != null ? recruitment.getOccupation() : "")
                .npwpNumber("")
                .incomeSource("")
                .npkpAddress("")

                // Education Information - MANUALLY SET THESE AS NEEDED
                .lastEducation("Universitas") // Example: set to specific education level
                .educationCity("")
                .educationPostalCode("")
                .isEducationSD(false)
                .isEducationSMP(false)
                .isEducationSMA(false)
                .isEducationAkademi(false)
                .isEducationUniversitas(true) // Example: check Universitas
                .isEducationLainLain(false)
                .educationLainLainDetail("")

                // Insurance Experience - MANUALLY SET THESE AS NEEDED
                .hasInsuranceExperience("Tidak") // Example: set to "Tidak"
                .hasInsuranceExperienceYa(false)
                .hasInsuranceExperienceTidak(true) // Example: check "Tidak"

                // Family Information
                .fatherName("")
                .motherName("")
                .spouseName("")
                .fatherBirthDate("")
                .motherBirthDate("")
                .spouseBirthDate("")
                .fatherAddress("")
                .motherAddress("")
                .spouseAddress("")
                .fatherPhone("")
                .motherPhone("")
                .spousePhone("")

                // Banking Information
                .bankName(recruitment.getBank() != null ? recruitment.getBank().getBankName() : "")
                .accountNumber(recruitment.getBankAccountNumber() != null ? recruitment.getBankAccountNumber() : "")
                .accountHolderName(recruitment.getBankAccountName() != null ? recruitment.getBankAccountName() : "")
                .branchLocation("")

                // Signature Information
                .candidateSignature(recruitment.getSignature() != null ? recruitment.getSignature() : "")
                .candidateParaf(recruitment.getParaf() != null ? recruitment.getParaf() : "")
                .casSignature(casInfo.signature)
                .casParaf(casInfo.paraf)
                .casName(casInfo.name)
                .casRole(casInfo.role)

                // Additional Information
                .applicationDate(LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")))
                .applicationLocation(recruitment.getBranch() != null ? recruitment.getBranch().getBranchName() : "")
                .build();
    }

    /**
     * Helper class to hold education checkbox states.
     */
    private static class EducationCheckboxes {
        Boolean isEducationSD = false;
        Boolean isEducationSMP = false;
        Boolean isEducationSMA = false;
        Boolean isEducationAkademi = false;
        Boolean isEducationUniversitas = false;
        Boolean isEducationLainLain = false;
        String educationLainLainDetail = "";
    }

    /**
     * Helper class to hold insurance experience checkbox states.
     */
    private static class InsuranceExperienceCheckboxes {
        Boolean hasInsuranceExperienceYa = false;
        Boolean hasInsuranceExperienceTidak = true;
    }

    /**
     * Helper class to hold CAS signature information.
     */
    private static class CasSignatureInfo {
        String signature = "";
        String paraf = "";
        String name = "";
        String role = "";
    }
}
