package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalBirthdayCustomerDto {

    @SerializedName("agentCode")
    @JsonProperty("agentCode")
    private String agentCode;

    @SerializedName("dateFrom")
    @JsonProperty("dateFrom")
    private String dateFrom;

    @SerializedName("dateTo")
    @JsonProperty("dateTo")
    private String dateTo;
}
