package id.co.panindaiichilife.superapp.agent.config;

import id.co.panindaiichilife.superapp.agent.api.dto.cms.AccountDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalLoginDto;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalLoginResponseDto;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionService;
import id.co.panindaiichilife.superapp.agent.core.security.EncryptionSuperAppService;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2RefreshToken;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoderParameters;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AccessTokenAuthenticationToken;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import retrofit2.Call;
import retrofit2.Response;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@RequiredArgsConstructor
public class PortalAuthenticationProvider implements AuthenticationProvider, MessageSourceAware {

    private static final String BAD_CREDENTIALS_MESSAGE_KEY = "AbstractUserDetailsAuthenticationProvider.badCredentials";
    private static final String BAD_CREDENTIALS_DEFAULT_MESSAGE = "Bad credentials";
    private final RegisteredClientRepository registeredClientRepository;
    private final JwtEncoder jwtEncoder;
    private final PortalProvider portalProvider;
    private final UserService userService;
    private final EncryptionService encryptionService;
    private final EncryptionSuperAppService encryptionSuperAppService;
    protected MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();
    @Value("${rest.security.client-id}")
    private String clientId;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        try {
            String username = encryptionSuperAppService.decrypt(authentication.getName());
            String password = encryptionSuperAppService.decrypt(authentication.getCredentials().toString());
            
            AccountDto user = userService.findByUsername(username);
            if (user == null) {
                throw new BadCredentialsException(messages.getMessage(
                        BAD_CREDENTIALS_MESSAGE_KEY,
                        BAD_CREDENTIALS_DEFAULT_MESSAGE));
            }
            List<String> roles = user.getRoles().stream()
                    .map(role -> {
                        String code = role.getCode();
                        return code.startsWith("ROLE_") ? code.substring(5) : code;
                    })
                    .toList();

            PortalLoginDto request = new PortalLoginDto();
            request.setUsername(encryptionService.encrypt(username, "SaltData1!"));
            request.setPassword(encryptionService.encrypt(password, "SaltData2@"));

            Call<PortalLoginResponseDto> call = portalProvider.login(request);

            Response<PortalLoginResponseDto> response = call.execute();
            if (response.isSuccessful() && StringUtils.equals(response.body().getStatusCode(), "200")) {
                // Retrieve RegisteredClient
                RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
                if (registeredClient == null) {
                    throw new IllegalArgumentException("RegisteredClient cannot be null");
                }

                // Generate JWT Access Token
                Instant now = Instant.now();

                // Calculate expiration based on remember me
                Instant tokenExpiration = calculateEndOfDayInstant();
                JwtClaimsSet accessTokenClaims = JwtClaimsSet.builder()
                        .subject(username)
                        .audience(Collections.singletonList(clientId))
                        .issuedAt(now)
                        .expiresAt(tokenExpiration)
                        .claim("roles", roles)
                        .claim("scope", "read write")
                        .build();
                String accessTokenValue = jwtEncoder.encode(JwtEncoderParameters.from(accessTokenClaims)).getTokenValue();

                OAuth2AccessToken accessToken = new OAuth2AccessToken(
                        OAuth2AccessToken.TokenType.BEARER,
                        accessTokenValue,
                        now,
                        tokenExpiration
                );

                // Generate JWT Refresh Token
                //in seconds
                int refreshTokenDuration = 604800;
                JwtClaimsSet refreshTokenClaims = JwtClaimsSet.builder()
                        .subject(username)
                        .audience(Collections.singletonList(clientId))
                        .issuedAt(now)
                        .expiresAt(now.plusSeconds(refreshTokenDuration))
                        .claim("roles", roles)
                        .claim("scope", "read write")
                        .build();
                String refreshTokenValue = jwtEncoder.encode(JwtEncoderParameters.from(refreshTokenClaims)).getTokenValue();

                OAuth2RefreshToken refreshToken = new OAuth2RefreshToken(
                        refreshTokenValue,
                        now,
                        now.plusSeconds(refreshTokenDuration)
                );

                Map<String, Object> additionalData = new HashMap<>();
                additionalData.put("needPassReset", response.body().getNeedPassReset());
                additionalData.put("remainingDays", response.body().getRemainingDays());
                // Return authentication token with both access and refresh tokens
                return new OAuth2AccessTokenAuthenticationToken(
                        registeredClient,
                        authentication,
                        accessToken,
                        refreshToken,
                        additionalData
                );
            }
        } catch (Exception ex) {
            log.error("Authentication error", ex);
            throw new BadCredentialsException(messages.getMessage(
                    BAD_CREDENTIALS_MESSAGE_KEY,
                    BAD_CREDENTIALS_DEFAULT_MESSAGE));
        }

        throw new BadCredentialsException(messages.getMessage(
                BAD_CREDENTIALS_MESSAGE_KEY,
                BAD_CREDENTIALS_DEFAULT_MESSAGE));
    }

    /**
     * Calculate Instant for end of day (23:59:59) GMT+7 timezone
     */
    private Instant calculateEndOfDayInstant() {
        ZoneId gmt7 = ZoneId.of("GMT+7");
        LocalDate today = LocalDate.now(gmt7);
        return today.plusDays(1).atStartOfDay(gmt7).toInstant().minusNanos(1);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }

    @Override
    public void setMessageSource(MessageSource messageSource) {
        this.messages = new MessageSourceAccessor(messageSource);
    }
}
