package id.co.panindaiichilife.superapp.agent.config;

import id.co.panindaiichilife.superapp.agent.core.security.EncryptionSuperAppService;
import id.co.panindaiichilife.superapp.agent.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import jakarta.annotation.PostConstruct;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2RefreshToken;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoderParameters;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AccessTokenAuthenticationToken;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@RequiredArgsConstructor
public class DbAuthenticationProvider extends DaoAuthenticationProvider {

    private final RegisteredClientRepository registeredClientRepository;
    private final UserService userService;
    private final JwtEncoder jwtEncoder;
    private final EncryptionSuperAppService encryptionService;
    @Value("${rest.security.client-id}")
    private String clientId;
    @Value("${rest.security.access-token.duration}")
    private int accessTokenDuration = 3600; //in seconds
    @Value("${rest.security.refresh-token.duration}")
    private int refreshTokenDuration = 604800; //in seconds

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        // Step 1: Decrypt the authentication object
        String decryptedUsername = encryptionService.decrypt(authentication.getName());
        String decryptedPassword = encryptionService.decrypt(authentication.getCredentials().toString());

        // Check if this is a remember me authentication
        boolean isRememberMe = false;
        if (authentication instanceof RememberMeUsernamePasswordAuthenticationToken) {
            isRememberMe = ((RememberMeUsernamePasswordAuthenticationToken) authentication).isRememberMe();
        }

        // Create a new Authentication object with the decrypted data
        Authentication decryptedAuthentication = new UsernamePasswordAuthenticationToken(
                decryptedUsername,
                decryptedPassword,
                authentication.getAuthorities()
        );

        Authentication baseAuth = super.authenticate(decryptedAuthentication);

        if (baseAuth.isAuthenticated()) {
            // Retrieve RegisteredClient
            RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
            if (registeredClient == null) {
                throw new IllegalArgumentException("RegisteredClient cannot be null");
            }

            List<String> roles = userService.findByUsername(decryptedUsername).getRoles().stream()
                    .map(role -> {
                        String code = role.getCode();
                        return code.startsWith("ROLE_") ? code.substring(5) : code;
                    })
                    .collect(Collectors.toList());

            // Generate JWT Access Token
            Instant now = Instant.now();

            // Calculate expiration based on remember me
            Instant tokenExpiration;
            if (isRememberMe) {
                tokenExpiration = calculateEndOfDayInstant();
            } else {
                tokenExpiration = now.plusSeconds(accessTokenDuration);
            }

            JwtClaimsSet accessTokenClaims = JwtClaimsSet.builder()
                    .subject(decryptedUsername)
                    .audience(Collections.singletonList(clientId))
                    .issuedAt(now)
                    .expiresAt(tokenExpiration)
                    .claim("roles", roles)
                    .claim("scope", "read write") // Example scope
                    .build();
            String accessTokenValue = jwtEncoder.encode(JwtEncoderParameters.from(accessTokenClaims)).getTokenValue();

            OAuth2AccessToken accessToken = new OAuth2AccessToken(
                    OAuth2AccessToken.TokenType.BEARER,
                    accessTokenValue,
                    now,
                    tokenExpiration
            );

            // Generate JWT Refresh Token
            JwtClaimsSet refreshTokenClaims = JwtClaimsSet.builder()
                    .subject(decryptedUsername)
                    .audience(Collections.singletonList(clientId))
                    .issuedAt(now)
                    .expiresAt(now.plusSeconds(refreshTokenDuration))
                    .claim("roles", roles)
                    .claim("scope", "read write")
                    .build();
            String refreshTokenValue = jwtEncoder.encode(JwtEncoderParameters.from(refreshTokenClaims)).getTokenValue();

            OAuth2RefreshToken refreshToken = new OAuth2RefreshToken(
                    refreshTokenValue,
                    now,
                    now.plusSeconds(refreshTokenDuration)
            );

            // Return authentication token with both access and refresh tokens
            return new OAuth2AccessTokenAuthenticationToken(
                    registeredClient,
                    baseAuth,
                    accessToken,
                    refreshToken
            );
        }
        return baseAuth;
    }

    /**
     * Calculate Instant for end of day (23:59:59)
     */
    private Instant calculateEndOfDayInstant() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endOfDay = now.toLocalDate().atTime(LocalTime.of(23, 59, 59));

        // If current time is already past 23:59:59, set to next day's end
        if (now.isAfter(endOfDay)) {
            endOfDay = endOfDay.plusDays(1);
        }

        return endOfDay.atZone(ZoneId.systemDefault()).toInstant();
    }
}
