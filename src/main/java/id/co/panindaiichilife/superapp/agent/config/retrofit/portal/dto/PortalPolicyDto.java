package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PortalPolicyDto {

    @SerializedName("agentCode")
    @JsonProperty("agentCode")
    private String agentCode;

    @SerializedName("valueStatus")
    @JsonProperty("valueStatus")
    private String valueStatus;

    @SerializedName("twist")
    @JsonProperty("twist")
    private String twist;

    @SerializedName("bitFilter")
    @JsonProperty("bitFilter")
    private int bitFilter;

    @SerializedName("valFilter")
    @JsonProperty("valFilter")
    private String valFilter;

    @SerializedName("pageIndex")
    @JsonProperty("pageIndex")
    private int pageIndex;

    @SerializedName("pageSize")
    @JsonProperty("pageSize")
    private int pageSize;

    @SerializedName("countRow")
    @JsonProperty("countRow")
    private boolean countRow;

    @SerializedName("sortIndex")
    @JsonProperty("sortIndex")
    private int sortIndex;

    @SerializedName("sortBy")
    @JsonProperty("sortBy")
    private int sortBy;

    @SerializedName("brCode")
    @JsonProperty("brCode")
    private String brCode;

    @SerializedName("channel")
    @JsonProperty("channel")
    private String channel;

    @SerializedName("nlg")
    @JsonProperty("nlg")
    private String nlg;

    @SerializedName("withDownline")
    @JsonProperty("withDownline")
    private Boolean withDownline;

    @SerializedName("yearFrom")
    @JsonProperty("yearFrom")
    private Integer yearFrom;

    @SerializedName("yearTo")
    @JsonProperty("yearTo")
    private Integer yearTo;

    @SerializedName("monthFrom")
    @JsonProperty("monthFrom")
    private Integer monthFrom;

    @SerializedName("monthTo")
    @JsonProperty("monthTo")
    private Integer monthTo;

}
