package id.co.panindaiichilife.superapp.agent.model;


import id.co.panindaiichilife.superapp.agent.core.data.entity.BaseEntity;
import id.co.panindaiichilife.superapp.agent.enums.*;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "trx_recruitments")
@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@SQLDelete(sql = "UPDATE trx_recruitments SET deleted = true, deleted_at = NOW() WHERE id = ?")
@Where(clause = "deleted = false")
public class TrxRecruitment extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "trx_recruitments_id_seq")
    @SequenceGenerator(name = "trx_recruitments_id_seq", sequenceName = "trx_recruitments_id_seq", allocationSize = 1)
    private Long id;

    @Column(name = "uuid", unique = true, updatable = false)
    private String uuid = UUID.randomUUID().toString();

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recruiter_id", nullable = false)
    private User recruiter;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bank_id")
    private Bank bank;

    @Audited
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "branch_id")
    private Branch branch;

    @Audited
    @OneToOne(targetEntity = TrxApprovalHeader.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "trx_approval_header_id")
    private TrxApprovalHeader approvalHeader;

    @Audited
    @Enumerated(EnumType.STRING)
    private Channel channel;

    @Audited
    @Enumerated(EnumType.STRING)
    private ApprovalStatus approvalStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private TrxStatus trxStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private Gender gender;

    @Audited
    private String recruiterCode;

    @Audited
    private String leaderCode;

    @Audited
    private String agentCode;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String ktpPhoto;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String selfiePhoto;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String passPhoto;

    @Audited
    @Enumerated(EnumType.STRING)
    private PositionLevel positionLevel;

    @Audited
    private String branchCode;

    @Audited
    private String nik;

    @Audited
    private String fullName;

    @Audited
    private String birthPlace;

    @Audited
    private LocalDate birthDate;

    @Audited
    private String ktpProvince;

    @Audited
    private String ktpCity;

    @Audited
    private String ktpDistrict;

    @Audited
    private String ktpSubDistrict;

    @Audited
    private String ktpRt;

    @Audited
    private String ktpRw;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String ktpAddress;

    @Audited
    private Boolean isDomicileSameAsKtp;

    @Audited
    private String domicileProvince;

    @Audited
    private String domicileCity;

    @Audited
    private String domicileDistrict;

    @Audited
    private String domicileSubDistrict;

    @Audited
    private String domicileRt;

    @Audited
    private String domicileRw;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String domicileAddress;

    @Audited
    private String phoneNumber;

    @Audited
    @Enumerated(EnumType.STRING)
    private MaritalStatus maritalStatus;

    @Audited
    private String occupation;

    @Audited
    private String occupationCode;

    @Audited
    private String email;

    @Audited
    private String emergencyContactName;

    @Audited
    private String emergencyContactRelation;

    @Audited
    private String emergencyContactPhone;

    @Audited
    private String bankAccountName;

    @Audited
    private String bankAccountNumber;

    @Audited
    private String lastJob;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String last5YearJob;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String last2YearProduction;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String lastCompanyManPower;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String rewardInfo;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String pkajFile;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String pmkajFile;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String kodeEtikFile;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String apgenFile;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String antiTwistingFile;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String resultInterview;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String signature;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String paraf;

    @Audited
    private Boolean isEmailVerified = Boolean.FALSE;

    @Audited
    private Boolean isPublic = Boolean.FALSE;

    @Audited
    private LocalDateTime emailVerifiedDate;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationBlacklistStatus validationBlacklistStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationKtpStatus validationKtpStatus;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String resulValidationKtp;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationBankAccountStatus validationBankAccountStatus;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String resultValidationBankAccount;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationHirarkiStatus validationHirarkiStatus;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String resultValidationHirarki;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationAmlStatus validationAmlStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationAdministrationAgentStatus validationAdministrationAgentStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationLicenseAajiStatus validationLicenseAajiStatus;

    @Audited
    @Enumerated(EnumType.STRING)
    private ValidationLicenseAasiStatus validationLicenseAasiStatus;

    @Audited
    private String pmkajNumber;

    @Audited
    private String pkajNumber;

    @Audited
    @Column(columnDefinition = "TEXT")
    private String notes;

    @Audited
    private LocalDate effectiveDate;


    @CreationTimestamp
    private Instant createdAt;

    @UpdateTimestamp
    private Instant updatedAt;
}
