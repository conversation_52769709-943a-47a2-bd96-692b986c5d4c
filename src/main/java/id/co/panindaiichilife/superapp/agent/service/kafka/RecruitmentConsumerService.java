package id.co.panindaiichilife.superapp.agent.service.kafka;

import id.co.panindaiichilife.superapp.agent.config.kafka.KafkaConfig;
import id.co.panindaiichilife.superapp.agent.model.event.RecruitmentStatusEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RecruitmentConsumerService {

    private final RecruitmentEventHandler recruitmentEventHandler;

    /**
     * Listens for recruitment events on the recruitment topic
     *
     * @param event The recruitment event received from Kafka
     */
    @KafkaListener(
            topics = KafkaConfig.RECRUITMENT_TOPIC,
            groupId = KafkaConfig.RECRUITMENT_GROUP,
            containerFactory = "recruitmentKafkaListenerContainerFactory")
    public void consumeRecruitmentEvent(RecruitmentStatusEvent event) {
        log.info("Received recruitment event: {}", event);

        try {
            recruitmentEventHandler.handleRecruitmentEvent(event);
        } catch (Exception e) {
            log.error("Error processing recruitment event: {}", e.getMessage(), e);
        }
    }
}
