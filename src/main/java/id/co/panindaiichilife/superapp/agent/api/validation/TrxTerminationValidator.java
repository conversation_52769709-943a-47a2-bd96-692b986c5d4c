package id.co.panindaiichilife.superapp.agent.api.validation;

import id.co.panindaiichilife.superapp.agent.api.form.agency.TrxTerminationForm;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.TrxTerminationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

@Component
@Slf4j
@RequiredArgsConstructor
public class TrxTerminationValidator implements Validator {
    private final AgentRepository agentRepository;
    private final TrxTerminationRepository trxTerminationRepository;

    @Override
    public boolean supports(@NotNull Class<?> clazz) {
        return TrxTerminationForm.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(@NotNull Object target, @NotNull Errors errors) {
        TrxTerminationForm form = (TrxTerminationForm) target;

        if (form.getReason() == null || form.getReason().trim().isEmpty()) {
            errors.rejectValue("reason", "reason.required", "Reason is required");
        }

        if (form.getTargetAgentCode() == null || form.getTargetAgentCode().trim().isEmpty()) {
            errors.rejectValue("targetAgentCode", "targetAgentCode.required", "Target agent code is required");
        }

        // find active termination request if any
        Agent terminationTarget = agentRepository.findTopByAgentCode(form.getTargetAgentCode()).orElseThrow(NotFoundException::new);
        User targetUser = terminationTarget.getUser();
        if (targetUser == null) {
            throw new InternalServerErrorException("Invalid agent data");
        }

        trxTerminationRepository.findActiveTerminationByTarget(targetUser.getId())
                .ifPresent(activeTerminationRequest ->
                        errors.rejectValue("targetAgentCode", "targetAgentCode.invalid", "There is an active termination request for this user"));
    }
}
