package id.co.panindaiichilife.superapp.agent.service;


import id.co.panindaiichilife.superapp.agent.api.dto.batch.JobCreationDto;
import id.co.panindaiichilife.superapp.agent.core.service.AmazonS3Service;
import id.co.panindaiichilife.superapp.agent.core.service.BatchJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class SyncService {

    private final BatchJobService batchJobService;
    private final AmazonS3Service amazonS3Service;
    @Qualifier("mdrtImportJob")
    private final Job mdrtImportJob;
    @Qualifier("agentProductionPerAgentImportJob")
    private final Job agentProductionPerAgentImportJob;
    @Qualifier("agentProductionPerPolicyImportJob")
    private final Job agentProductionPerPolicyImportJob;
    @Qualifier("allowanceImportJob")
    private final Job allowanceImportJob;
    @Qualifier("commissionCompensationImportJob")
    private final Job commissionCompensationImportJob;
    @Qualifier("validasiPerG1ImportJob")
    private final Job validasiPerG1ImportJob;
    @Qualifier("validasiPerHirarkiImportJob")
    private final Job validasiPerHirarkiImportJob;
    @Qualifier("persistencyImportJob")
    private final Job persistencyImportJob;
    @Qualifier("promosiAgentImportJob")
    private final Job promosiAgentImportJob;
    @Qualifier("promosiLeaderImportJob")
    private final Job promosiLeaderImportJob;

    public List<JobCreationDto> runImportMdrt() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "MDRT");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(mdrtImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportAgentProductionPerAgent() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "AgentProductionPerAgent");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(agentProductionPerAgentImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportAgentProductionPerPolicy() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "AgentProductionPerPolicy");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(agentProductionPerPolicyImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportAllowance() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "Allowance");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(allowanceImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportCommissionCompensation() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "CommissionCompensation");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(commissionCompensationImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportValidasiPerG1() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "ValidasiPerG1");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(validasiPerG1ImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportValidasiPerHirarki() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "ValidasiPerHirarki");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(validasiPerHirarkiImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportPersistency() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "Persistency");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(persistencyImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportPromosiAgent() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "PromosiAgent");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(promosiAgentImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }

    public List<JobCreationDto> runImportPromosiLeader() {
        List<JobCreationDto> jobCreationDtos = new ArrayList<>();
        List<String> fileNames = amazonS3Service.scanFiles("csv/initial/", "PromosiLeader");

        fileNames.forEach((fileName) -> {
            // Create job parameters
            JobParameters jobParameters = new JobParametersBuilder()
                    .addString("key", fileName)
                    .addLong("time", System.currentTimeMillis())
                    .toJobParameters();

            // Run the job
            JobExecution jobExecution = batchJobService.run(promosiLeaderImportJob, jobParameters, true);

            jobCreationDtos.add(JobCreationDto.builder()
                    .id(jobExecution.getId())
                    .status(jobExecution.getStatus())
                    .monitorUrl("/api/cms/monitoring/batch/import/" + jobExecution.getId())
                    .build());
        });
        return jobCreationDtos;
    }
}
