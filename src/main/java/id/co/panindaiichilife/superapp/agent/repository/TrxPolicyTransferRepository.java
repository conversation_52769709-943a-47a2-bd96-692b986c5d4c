package id.co.panindaiichilife.superapp.agent.repository;

import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.model.TrxPolicyTransfer;
import id.co.panindaiichilife.superapp.agent.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface TrxPolicyTransferRepository extends BaseRepository<TrxPolicyTransfer, Long> {
    Optional<TrxPolicyTransfer> findByTarget(User target);

    @Query("""
        SELECT p FROM TrxPolicyTransfer p 
        WHERE p.target.agent.agentCode = :agentCode
            AND (:searchText IS NULL
                OR (
                    LOWER(p.source.agent.agentCode)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    OR LOWER(p.source.agent.agentName)  LIKE LOWER(CONCAT('%', :searchText, '%'))
                    ) 
            )
    """)
    Page<TrxPolicyTransfer> findPolicyTransferByTargetAgentCode(
            @Param("agentCode") String agentCode, @Param("searchText") String searchText,
            Pageable pageable);
}
