package id.co.panindaiichilife.superapp.agent.repository;


import id.co.panindaiichilife.superapp.agent.core.data.repository.BaseRepository;
import id.co.panindaiichilife.superapp.agent.enums.TrxStatus;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.TrxRejoinApplication;
import id.co.panindaiichilife.superapp.agent.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface TrxRejoinApplicationRepository extends BaseRepository<TrxRejoinApplication, Long> {
    List<TrxRejoinApplication> findAllByStatusInAndSubmittedBy(List<TrxStatus> status, User user);

    List<TrxRejoinApplication> findAllByUpdatedAtBeforeAndStatusNotIn(Instant updatedAt, List<TrxStatus> status);

    Optional<TrxRejoinApplication> findTopByAgentAndStatusInOrderByUpdatedAtDesc(Agent agent, List<TrxStatus> status);

    @Query("""
        SELECT r
        FROM TrxRejoinApplication r
        WHERE
            r.submittedBy.id = :submitterId
            AND (:statuses IS NULL OR r.status IN (:statuses))
            AND (:searchQuery IS NULL OR
                (LOWER(r.agent.agentCode) LIKE LOWER(CONCAT('%', :searchQuery, '%'))
                OR LOWER(r.agent.agentName) LIKE LOWER(CONCAT('%', :searchQuery, '%')))
            )
    """)
    Page<TrxRejoinApplication> findRejoinApplicationsBySubmitter(
            @Param("submitterId") Long submitterId, @Param("searchQuery") String searchQuery,
            @Param("statuses") List<TrxStatus> statuses, Pageable pageable);
}
