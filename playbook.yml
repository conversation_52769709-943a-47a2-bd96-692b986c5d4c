- name: Deploy New ECS Task Definition via Terraform
  hosts: localhost
  vars:
    terraform_dir: "./terraform"
    service_arn: "superapp-ecs/keagenan"
  tasks:
    - name: Change to Terraform directory (validate path)
      ansible.builtin.shell: cd {{ terraform_dir }} && pwd
      register: terraform_dir_path
      changed_when: false

    - name: Ensure Terraform is initialized
      ansible.builtin.shell: terraform init
      args:
        chdir: "{{ terraform_dir }}"
      register: terraform_init
      changed_when: "'already initialized' not in terraform_init.stdout"

    - name: Get full ECS task definition ARN (with revision)
      ansible.builtin.command: >
        aws ecs describe-task-definition --task-definition {{ task_family }} --query 'taskDefinition.taskDefinitionArn' --output text
      register: current_task_arn
      ignore_errors: true

    - name: Import ECS task definition into Terraform state
      ansible.builtin.shell: >
        terraform import -var="image_name={{ new_image }}" aws_ecs_task_definition.task {{ current_task_arn.stdout }}
      args:
        chdir: "{{ terraform_dir }}"
      ignore_errors: true

    - name: Import ECS service into Terraform state
      ansible.builtin.shell: >
        terraform import -var="image_name={{ new_image }}" aws_ecs_service.services {{ service_arn }}
      args:
        chdir: "{{ terraform_dir }}"
      ignore_errors: true

    - name: Apply Terraform with new image
      ansible.builtin.shell: >
        terraform apply -var="image_name={{ new_image }}" -auto-approve
      args:
        chdir: "{{ terraform_dir }}"
